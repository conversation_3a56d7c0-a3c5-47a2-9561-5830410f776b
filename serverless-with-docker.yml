service: laravel-dockers

provider:
  name: aws
  region: us-east-2
  vpc:
    securityGroupIds:
      - sg-090edc2d9cb3e7985  # Replace with your Lambda function's security group ID
    subnetIds:
      - subnet-242dbe4f # Subnet IDs from your RDS instance
      - subnet-3da56d40
      - subnet-03e2d24f
  ecr:
    images:
      apimio-docker:
        path: ./
  iam:
    role:
      statements:
        - Effect: Allow
          Action:
            - ec2:CreateNetworkInterface
            - ec2:DeleteNetworkInterface
            - ec2:DescribeNetworkInterfaces
          Resource:
            - "*"
        - Effect: Allow
          Action:
            - cloudwatch:GetMetricStatistics
            - logs:CreateLogGroup
            - logs:CreateLogStream
            - logs:FilterLogEvents
            - logs:PutLogEvents
            - kms:Decrypt
            - secretsmanager:GetSecretValue
            - ssm:GetParameters
            - ssm:GetParameter
            - lambda:invokeFunction
            - s3:*
            - ses:*
            - sqs:*
            - dynamodb:*
            - ecr:*
          Resource:
          - arn:aws:sqs:us-east-2:574295680953:UserQueue-*
          - "*"

      managedPolicies:
        - arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole


  environment:
    EXCEL_TEMP_PATH : /tmp
    APP_URL: https://phpstack-1322452-4837507.cloudwaysapps.com
    APP_NAME: Apimio
    APP_ENV: lambda
    APP_VERSION: 11.0
    DB_CONNECTION: mysql
    DB_HOST: apimio1.clsqeeg2qimy.us-east-2.rds.amazonaws.com
    DB_PORT: 3306
    DB_DATABASE: apimio_test
    DB_USERNAME: admin
    DB_PASSWORD: Apimiodevs321!
    BROADCAST_DRIVER: pusher
    CACHE_DRIVER: file
    QUEUE_CONNECTION: sqs
    SESSION_DRIVER: database
    SESSION_LIFETIME: 120
    MAIL_DRIVER: smtp
    MAIL_HOST: smtp.sendgrid.net
    MAIL_PORT: 587
    MAIL_USERNAME: apikey
    MAIL_PASSWORD: *********************************************************************
    MAIL_ENCRYPTION: tls
    MAIL_FROM_ADDRESS: <EMAIL>
    MAIL_FROM_NAME: "${APP_NAME}"
    AWS_BUCKET: apimio-staging
    AWS_MAIN_URL: https://apimio-staging.s3.amazonaws.com/
    SQS_PREFIX: https://sqs.us-east-2.amazonaws.com/574295680953/
    STRIPE_KEY: pk_test_aXkxuuqQz8k881VU3C1Jvehg
    STRIPE_SECRET: sk_test_bW7sPjB0LmgvOZkXaxFftHSm
#    DYNAMODB_CACHE_TABLE: !Ref CacheTable
#    BREF_LARAVEL_CACHE_CONFIG: false
    DQL_URL: DLQ-fail-jobs.fifo
    LAMBDA_FUNCTION_NAME: laravel-dockers-dev-queueWorker
    LOG_CHANNEL: stderr

package:
  patterns:
    - '!node_modules/**'
    - '!public/storage'
    - '!resources/assets/**'
    - '!storage/**'
    - '!tests/**'
    - '!serverless/**'
    - '!.env'
    - '.md'
    - '!.git/**'
    - '!vendor/bin/**'
    - 'bootstrap/cache/**'
    - 'app/Classes/**'

functions:
  queueWorker:
    image:
      name: apimio-docker
    timeout: 840
    environment:
      SQS_QUEUE: "UserQueue-*"


plugins:
  - ./vendor/bref/bref
