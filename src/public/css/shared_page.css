body {
    padding-left: 40px ;
    padding-right: 40px;
}
.share-collection {
    display: grid;
    grid-auto-columns: max-content;
    grid-auto-flow: dense;
    /*grid-auto-rows: minmax(18px, auto);*/
    grid-gap: 8px;
    grid-template-columns: repeat(14, 1fr);
    /*height: 103px;*/
    /*overflow: hidden;*/
}
.share-collection-card {
    display: grid;
    grid-auto-columns: max-content;
    grid-auto-flow: dense;
    grid-auto-rows: minmax(18px, auto);
    grid-gap: 16px;
    grid-template-columns: repeat(10, 1fr);
}
#folder-main > .dropdown  {
    display: none;
}
.card-width {
    min-width: 335px !important;
    max-height: initial;
}
.folder-card {
    min-width: 244px;
}
.drop_right {
    display: none;
}
.folder {
    position: relative;
}
.download-btn {
    position: absolute;
    margin-top: 5px;
    right: 10px;
    color:#5B5B5B;
    font-size: 16px;
    cursor: pointer;

}
.emoji-icon {
    display: none;
}
.trash-icon {
    display: none !important;
}

.modal-aside,
.modal.right .modal-aside {
    width: 346px;
    position: fixed;
    height: 100vh;
    right: 0;
    top: 0;
    -webkit-transform: translate3d(0%, 0, 0);
    -ms-transform: translate3d(0%, 0, 0);
    -o-transform: translate3d(0%, 0, 0);
    transform: translate3d(0%, 0, 0);
}

.modal_content,
.modal.right .modal_content {
    height: 100%;
    overflow-y: auto;
}

.modal_body,
.modal.right .modal_body {
    padding: 15px 15px 80px;
}

/*Right*/
.modal.right.fade .modal-aside {
    right: -320px;
    -webkit-transition: opacity 0.3s linear, right 0.3s ease-out;
    -moz-transition: opacity 0.3s linear, right 0.3s ease-out;
    -o-transition: opacity 0.3s linear, right 0.3s ease-out;
    transition: opacity 0.3s linear, right 0.3s ease-out;
}

.modal.right.fade.in .modal-aside {
    right: 0;
}

/* ----- MODAL STYLE ----- */
.modal_content {
    border-radius: 0;
    border: none;
}

.modal_header {
    border-bottom-color: #EEEEEE;
    background-color: #FAFAFA;
}
