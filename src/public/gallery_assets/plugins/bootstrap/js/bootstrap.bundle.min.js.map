{"version": 3, "sources": ["../../js/src/util/index.js", "../../js/src/dom/event-handler.js", "../../js/src/dom/data.js", "../../js/src/base-component.js", "../../js/src/util/component-functions.js", "../../js/src/alert.js", "../../js/src/button.js", "../../js/src/dom/manipulator.js", "../../js/src/dom/selector-engine.js", "../../js/src/carousel.js", "../../js/src/collapse.js", "../../node_modules/@popperjs/core/lib/enums.js", "../../node_modules/@popperjs/core/lib/dom-utils/getNodeName.js", "../../node_modules/@popperjs/core/lib/dom-utils/getWindow.js", "../../node_modules/@popperjs/core/lib/dom-utils/instanceOf.js", "../../node_modules/@popperjs/core/lib/modifiers/applyStyles.js", "../../node_modules/@popperjs/core/lib/utils/getBasePlacement.js", "../../node_modules/@popperjs/core/lib/dom-utils/getBoundingClientRect.js", "../../node_modules/@popperjs/core/lib/dom-utils/getLayoutRect.js", "../../node_modules/@popperjs/core/lib/dom-utils/contains.js", "../../node_modules/@popperjs/core/lib/dom-utils/getComputedStyle.js", "../../node_modules/@popperjs/core/lib/dom-utils/isTableElement.js", "../../node_modules/@popperjs/core/lib/dom-utils/getDocumentElement.js", "../../node_modules/@popperjs/core/lib/dom-utils/getParentNode.js", "../../node_modules/@popperjs/core/lib/dom-utils/getOffsetParent.js", "../../node_modules/@popperjs/core/lib/utils/getMainAxisFromPlacement.js", "../../node_modules/@popperjs/core/lib/utils/math.js", "../../node_modules/@popperjs/core/lib/utils/within.js", "../../node_modules/@popperjs/core/lib/utils/mergePaddingObject.js", "../../node_modules/@popperjs/core/lib/utils/getFreshSideObject.js", "../../node_modules/@popperjs/core/lib/utils/expandToHashMap.js", "../../node_modules/@popperjs/core/lib/modifiers/arrow.js", "../../node_modules/@popperjs/core/lib/utils/getVariation.js", "../../node_modules/@popperjs/core/lib/modifiers/computeStyles.js", "../../node_modules/@popperjs/core/lib/modifiers/eventListeners.js", "../../node_modules/@popperjs/core/lib/utils/getOppositePlacement.js", "../../node_modules/@popperjs/core/lib/utils/getOppositeVariationPlacement.js", "../../node_modules/@popperjs/core/lib/dom-utils/getWindowScroll.js", "../../node_modules/@popperjs/core/lib/dom-utils/getWindowScrollBarX.js", "../../node_modules/@popperjs/core/lib/dom-utils/isScrollParent.js", "../../node_modules/@popperjs/core/lib/dom-utils/getScrollParent.js", "../../node_modules/@popperjs/core/lib/dom-utils/listScrollParents.js", "../../node_modules/@popperjs/core/lib/utils/rectToClientRect.js", "../../node_modules/@popperjs/core/lib/dom-utils/getClippingRect.js", "../../node_modules/@popperjs/core/lib/dom-utils/getViewportRect.js", "../../node_modules/@popperjs/core/lib/dom-utils/getDocumentRect.js", "../../node_modules/@popperjs/core/lib/utils/computeOffsets.js", "../../node_modules/@popperjs/core/lib/utils/detectOverflow.js", "../../node_modules/@popperjs/core/lib/utils/computeAutoPlacement.js", "../../node_modules/@popperjs/core/lib/modifiers/flip.js", "../../node_modules/@popperjs/core/lib/modifiers/hide.js", "../../node_modules/@popperjs/core/lib/modifiers/offset.js", "../../node_modules/@popperjs/core/lib/modifiers/popperOffsets.js", "../../node_modules/@popperjs/core/lib/modifiers/preventOverflow.js", "../../node_modules/@popperjs/core/lib/utils/getAltAxis.js", "../../node_modules/@popperjs/core/lib/dom-utils/getCompositeRect.js", "../../node_modules/@popperjs/core/lib/dom-utils/getNodeScroll.js", "../../node_modules/@popperjs/core/lib/dom-utils/getHTMLElementScroll.js", "../../node_modules/@popperjs/core/lib/utils/orderModifiers.js", "../../node_modules/@popperjs/core/lib/createPopper.js", "../../node_modules/@popperjs/core/lib/utils/debounce.js", "../../node_modules/@popperjs/core/lib/utils/mergeByName.js", "../../node_modules/@popperjs/core/lib/popper-lite.js", "../../node_modules/@popperjs/core/lib/popper.js", "../../js/src/dropdown.js", "../../js/src/util/scrollbar.js", "../../js/src/util/backdrop.js", "../../js/src/util/focustrap.js", "../../js/src/modal.js", "../../js/src/offcanvas.js", "../../js/src/util/sanitizer.js", "../../js/src/tooltip.js", "../../js/src/popover.js", "../../js/src/scrollspy.js", "../../js/src/tab.js", "../../js/src/toast.js", "../../js/index.umd.js"], "names": ["TRANSITION_END", "getSelector", "element", "selector", "getAttribute", "hrefAttr", "includes", "startsWith", "split", "trim", "getSelectorFromElement", "document", "querySelector", "getElementFromSelector", "triggerTransitionEnd", "dispatchEvent", "Event", "isElement", "obj", "j<PERSON>y", "nodeType", "getElement", "length", "typeCheckConfig", "componentName", "config", "configTypes", "Object", "keys", "for<PERSON>ach", "property", "expectedTypes", "value", "valueType", "toString", "call", "match", "toLowerCase", "RegExp", "test", "TypeError", "toUpperCase", "isVisible", "getClientRects", "getComputedStyle", "getPropertyValue", "isDisabled", "Node", "ELEMENT_NODE", "classList", "contains", "disabled", "hasAttribute", "findShadowRoot", "documentElement", "attachShadow", "getRootNode", "root", "ShadowRoot", "parentNode", "noop", "reflow", "offsetHeight", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "j<PERSON><PERSON><PERSON>", "window", "body", "DOMContentLoadedCallbacks", "isRTL", "dir", "defineJQueryPlugin", "plugin", "callback", "$", "name", "NAME", "JQUERY_NO_CONFLICT", "fn", "jQueryInterface", "<PERSON><PERSON><PERSON><PERSON>", "noConflict", "readyState", "addEventListener", "push", "execute", "executeAfterTransition", "transitionElement", "waitForTransition", "emulatedDuration", "transitionDuration", "transitionDelay", "floatTransitionDuration", "Number", "parseFloat", "floatTransitionDelay", "getTransitionDurationFromElement", "called", "handler", "target", "removeEventListener", "setTimeout", "getNextActiveElement", "list", "activeElement", "shouldGetNext", "isCycleAllowed", "index", "indexOf", "listLength", "Math", "max", "min", "namespaceRegex", "stripNameRegex", "stripUidRegex", "eventRegistry", "uidEvent", "customEvents", "mouseenter", "mouseleave", "customEventsRegex", "nativeEvents", "Set", "getUidEvent", "uid", "getEvent", "<PERSON><PERSON><PERSON><PERSON>", "events", "delegationSelector", "uidEventList", "i", "len", "event", "<PERSON><PERSON><PERSON><PERSON>", "normalizeParams", "originalTypeEvent", "delegationFn", "delegation", "typeEvent", "getTypeEvent", "has", "add<PERSON><PERSON><PERSON>", "oneOff", "wrapFn", "relatedTarget", "<PERSON><PERSON><PERSON><PERSON>", "this", "handlers", "previousFn", "replace", "dom<PERSON><PERSON>s", "querySelectorAll", "EventHandler", "off", "type", "apply", "bootstrapDelegationHandler", "bootstrapHandler", "<PERSON><PERSON><PERSON><PERSON>", "Boolean", "on", "one", "inNamespace", "isNamespace", "elementEvent", "namespace", "storeElementEvent", "handler<PERSON><PERSON>", "removeNamespacedHandlers", "slice", "keyHandlers", "trigger", "args", "isNative", "jQueryEvent", "bubbles", "nativeDispatch", "defaultPrevented", "evt", "isPropagationStopped", "isImmediatePropagationStopped", "isDefaultPrevented", "createEvent", "initEvent", "CustomEvent", "cancelable", "key", "defineProperty", "get", "preventDefault", "elementMap", "Map", "Data", "set", "instance", "instanceMap", "size", "console", "error", "Array", "from", "remove", "delete", "BaseComponent", "constructor", "_element", "DATA_KEY", "dispose", "EVENT_KEY", "getOwnPropertyNames", "propertyName", "_queueCallback", "isAnimated", "static", "getInstance", "VERSION", "Error", "enableDismissTrigger", "component", "method", "clickEvent", "tagName", "closest", "getOrCreateInstance", "<PERSON><PERSON>", "close", "_destroyElement", "each", "data", "undefined", "SELECTOR_DATA_TOGGLE", "<PERSON><PERSON>", "toggle", "setAttribute", "normalizeData", "val", "normalizeDataKey", "chr", "button", "Manipulator", "setDataAttribute", "removeDataAttribute", "removeAttribute", "getDataAttributes", "attributes", "dataset", "filter", "pureKey", "char<PERSON>t", "getDataAttribute", "offset", "rect", "getBoundingClientRect", "top", "pageYOffset", "left", "pageXOffset", "position", "offsetTop", "offsetLeft", "SelectorEngine", "find", "concat", "Element", "prototype", "findOne", "children", "child", "matches", "parents", "ancestor", "prev", "previous", "previousElementSibling", "next", "nextElement<PERSON><PERSON>ling", "focusableC<PERSON><PERSON>n", "focusables", "map", "join", "el", "<PERSON><PERSON><PERSON>", "interval", "keyboard", "slide", "pause", "wrap", "touch", "DefaultType", "ORDER_NEXT", "ORDER_PREV", "DIRECTION_LEFT", "DIRECTION_RIGHT", "KEY_TO_DIRECTION", "ArrowLeft", "ArrowRight", "EVENT_SLID", "CLASS_NAME_ACTIVE", "SELECTOR_ACTIVE_ITEM", "Carousel", "super", "_items", "_interval", "_activeElement", "_isPaused", "_isSliding", "touchTimeout", "touchStartX", "touchDeltaX", "_config", "_getConfig", "_indicatorsElement", "_touchSupported", "navigator", "maxTouchPoints", "_pointerEvent", "PointerEvent", "_addEventListeners", "_slide", "nextWhenVisible", "hidden", "cycle", "clearInterval", "_updateInterval", "setInterval", "visibilityState", "bind", "to", "activeIndex", "_getItemIndex", "order", "_handleSwipe", "absDeltax", "abs", "direction", "_keydown", "_addTouchEventListeners", "has<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pointerType", "start", "clientX", "touches", "move", "end", "clearTimeout", "itemImg", "add", "_getItemByOrder", "isNext", "_triggerSlideEvent", "eventDirectionName", "targetIndex", "fromIndex", "_setActiveIndicatorElement", "activeIndicator", "indicators", "parseInt", "elementInterval", "defaultInterval", "directionOrOrder", "_directionToOrder", "activeElementIndex", "nextElement", "nextElementIndex", "isCycling", "directionalClassName", "orderClassName", "_orderToDirection", "triggerSlidEvent", "completeCallBack", "action", "ride", "carouselInterface", "slideIndex", "dataApiClickHandler", "carousels", "parent", "CLASS_NAME_SHOW", "CLASS_NAME_COLLAPSE", "CLASS_NAME_COLLAPSING", "CLASS_NAME_COLLAPSED", "CLASS_NAME_DEEPER_CHILDREN", "Collapse", "_isTransitioning", "_triggerArray", "toggleList", "elem", "filterElement", "foundElem", "_selector", "_initializeC<PERSON><PERSON>n", "_addAriaAndCollapsedClass", "_isShown", "hide", "show", "activesData", "actives", "container", "tempActiveData", "elemActive", "dimension", "_getDimension", "style", "scrollSize", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "selected", "trigger<PERSON><PERSON>y", "isOpen", "bottom", "right", "auto", "basePlacements", "clippingParents", "viewport", "popper", "reference", "variationPlacements", "reduce", "acc", "placement", "placements", "beforeRead", "read", "afterRead", "<PERSON><PERSON><PERSON>", "main", "<PERSON><PERSON><PERSON>", "beforeWrite", "write", "afterWrite", "modifierPhases", "getNodeName", "nodeName", "getWindow", "node", "ownerDocument", "defaultView", "isHTMLElement", "HTMLElement", "isShadowRoot", "applyStyles$1", "enabled", "phase", "_ref", "state", "elements", "styles", "assign", "effect", "_ref2", "initialStyles", "options", "strategy", "margin", "arrow", "hasOwnProperty", "attribute", "requires", "getBasePlacement", "includeScale", "width", "height", "x", "y", "getLayoutRect", "clientRect", "offsetWidth", "rootNode", "isSameNode", "host", "isTableElement", "getDocumentElement", "getParentNode", "assignedSlot", "getTrueOffsetParent", "offsetParent", "getOffsetParent", "isFirefox", "userAgent", "currentNode", "css", "transform", "perspective", "contain", "<PERSON><PERSON><PERSON><PERSON>", "getContainingBlock", "getMainAxisFromPlacement", "round", "within", "mathMax", "mathMin", "mergePaddingObject", "paddingObject", "expandToHashMap", "hashMap", "arrow$1", "_state$modifiersData$", "arrowElement", "popperOffsets", "modifiersData", "basePlacement", "axis", "padding", "rects", "toPaddingObject", "arrowRect", "minProp", "maxProp", "endDiff", "startDiff", "arrowOffsetParent", "clientSize", "clientHeight", "clientWidth", "centerToReference", "center", "axisProp", "centerOffset", "_options$element", "requiresIfExists", "getVariation", "unsetSides", "mapToStyles", "_Object$assign2", "popperRect", "variation", "offsets", "gpuAcceleration", "adaptive", "roundOffsets", "_ref3", "dpr", "devicePixelRatio", "roundOffsetsByDPR", "_ref3$x", "_ref3$y", "hasX", "hasY", "sideX", "sideY", "win", "heightProp", "widthProp", "_Object$assign", "commonStyles", "computeStyles$1", "_ref4", "_options$gpuAccelerat", "_options$adaptive", "_options$roundOffsets", "passive", "eventListeners", "_options$scroll", "scroll", "_options$resize", "resize", "scrollParents", "scrollParent", "update", "hash", "getOppositePlacement", "matched", "getOppositeVariationPlacement", "getWindowScroll", "scrollLeft", "scrollTop", "getWindowScrollBarX", "isScrollParent", "_getComputedStyle", "overflow", "overflowX", "overflowY", "getScrollParent", "listScrollParents", "_element$ownerDocumen", "isBody", "visualViewport", "updatedList", "rectToClientRect", "getClientRectFromMixedType", "clippingParent", "html", "getViewportRect", "clientTop", "clientLeft", "getInnerBoundingClientRect", "winScroll", "scrollWidth", "scrollHeight", "getDocumentRect", "computeOffsets", "commonX", "commonY", "mainAxis", "detectOverflow", "_options", "_options$placement", "_options$boundary", "boundary", "_options$rootBoundary", "rootBoundary", "_options$elementConte", "elementContext", "_options$altBoundary", "altBoundary", "_options$padding", "altContext", "clippingClientRect", "mainClippingParents", "clipperElement", "getClippingParents", "firstClippingParent", "clippingRect", "accRect", "getClippingRect", "contextElement", "referenceClientRect", "popperClientRect", "elementClientRect", "overflowOffsets", "offsetData", "multiply", "computeAutoPlacement", "flipVariations", "_options$allowedAutoP", "allowedAutoPlacements", "allPlacements", "allowedPlacements", "overflows", "sort", "a", "b", "flip$1", "_skip", "_options$mainAxis", "checkMainAxis", "_options$altAxis", "altAxis", "checkAltAxis", "specifiedFallbackPlacements", "fallbackPlacements", "_options$flipVariatio", "preferredPlacement", "oppositePlacement", "getExpandedFallbackPlacements", "referenceRect", "checksMap", "makeFallbackChecks", "firstFittingPlacement", "_basePlacement", "isStartVariation", "isVertical", "mainVariationSide", "altVariationSide", "checks", "every", "check", "_loop", "_i", "fittingPlacement", "reset", "getSideOffsets", "preventedOffsets", "isAnySideFullyClipped", "some", "side", "hide$1", "preventOverflow", "referenceOverflow", "popperAltOverflow", "referenceClippingOffsets", "popperEscapeOffsets", "isReferenceHidden", "hasPopperEscaped", "offset$1", "_options$offset", "invertDistance", "skidding", "distance", "distanceAndSkiddingToXY", "_data$state$placement", "popperOffsets$1", "preventOverflow$1", "_options$tether", "tether", "_options$tetherOffset", "tetherOffset", "isBasePlacement", "tetherOffsetValue", "mainSide", "altSide", "additive", "minLen", "maxLen", "arrowPaddingObject", "arrowPaddingMin", "arrowPaddingMax", "arrowLen", "minOffset", "maxOffset", "clientOffset", "offsetModifierValue", "tetherMin", "tetherMax", "preventedOffset", "_mainSide", "_altSide", "_offset", "_min", "_max", "_preventedOffset", "getCompositeRect", "elementOrVirtualElement", "isFixed", "isOffsetParentAnElement", "isElementScaled", "modifiers", "visited", "result", "modifier", "dep", "depModifier", "DEFAULT_OPTIONS", "areValidElements", "_len", "arguments", "_key", "popperGenerator", "generatorOptions", "_generatorOptions", "_generatorOptions$def", "defaultModifiers", "_generatorOptions$def2", "defaultOptions", "pending", "orderedModifiers", "effectCleanupFns", "isDestroyed", "setOptions", "setOptionsAction", "cleanupModifierEffects", "merged", "orderModifiers", "current", "existing", "m", "_ref3$options", "cleanupFn", "forceUpdate", "_state$elements", "_state$orderedModifie", "_state$orderedModifie2", "Promise", "resolve", "then", "destroy", "onFirstUpdate", "createPopper", "computeStyles", "applyStyles", "flip", "ESCAPE_KEY", "SPACE_KEY", "ARROW_UP_KEY", "ARROW_DOWN_KEY", "REGEXP_KEYDOWN", "EVENT_CLICK_DATA_API", "EVENT_KEYDOWN_DATA_API", "SELECTOR_MENU", "PLACEMENT_TOP", "PLACEMENT_TOPEND", "PLACEMENT_BOTTOM", "PLACEMENT_BOTTOMEND", "PLACEMENT_RIGHT", "PLACEMENT_LEFT", "display", "popperConfig", "autoClose", "Dropdown", "_popper", "_menu", "_getMenuElement", "_inNavbar", "_detectNavbar", "getParentFromElement", "_createPopper", "focus", "_completeHide", "<PERSON><PERSON>", "referenceElement", "_getPopperConfig", "isDisplayStatic", "_getPlacement", "parentDropdown", "isEnd", "_getOffset", "popperData", "defaultBsPopperConfig", "_selectMenuItem", "items", "toggles", "context", "<PERSON><PERSON><PERSON>", "isMenuTarget", "isActive", "stopPropagation", "getToggleButton", "clearMenus", "dataApiKeydownHandler", "SELECTOR_FIXED_CONTENT", "SELECTOR_STICKY_CONTENT", "ScrollBarHelper", "getWidth", "documentWidth", "innerWidth", "_disableOver<PERSON>low", "_setElementAttributes", "calculatedValue", "_saveInitialAttribute", "styleProp", "scrollbarWidth", "_applyManipulationCallback", "_resetElementAttributes", "actualValue", "removeProperty", "callBack", "isOverflowing", "className", "rootElement", "clickCallback", "EVENT_MOUSEDOWN", "Backdrop", "_isAppended", "_append", "_getElement", "_emulateAnimation", "backdrop", "createElement", "append", "trapElement", "autofocus", "TAB_NAV_BACKWARD", "FocusTrap", "_isActive", "_lastTabNavDirection", "activate", "_handleFocusin", "_handleKeydown", "deactivate", "shift<PERSON>ey", "EVENT_HIDDEN", "EVENT_SHOW", "EVENT_RESIZE", "EVENT_CLICK_DISMISS", "EVENT_KEYDOWN_DISMISS", "EVENT_MOUSEDOWN_DISMISS", "CLASS_NAME_OPEN", "CLASS_NAME_STATIC", "Modal", "_dialog", "_backdrop", "_initializeBackDrop", "_focustrap", "_initializeFocusTrap", "_ignoreBackdropClick", "_scrollBar", "_isAnimated", "_adjustDialog", "_setEscapeEvent", "_setResizeEvent", "_showBackdrop", "_showElement", "_hideModal", "htmlElement", "handleUpdate", "modalBody", "_triggerBackdropTransition", "_resetAdjustments", "currentTarget", "isModalOverflowing", "isBodyOverflowing", "paddingLeft", "paddingRight", "showEvent", "allReadyOpen", "OPEN_SELECTOR", "<PERSON><PERSON><PERSON>", "visibility", "blur", "uriAttributes", "SAFE_URL_PATTERN", "DATA_URL_PATTERN", "allowedAttribute", "allowedAttributeList", "attributeName", "nodeValue", "regExp", "attributeRegex", "sanitizeHtml", "unsafeHtml", "allowList", "sanitizeFn", "createdDocument", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parseFromString", "elementName", "attributeList", "allowedAttributes", "innerHTML", "DISALLOWED_ATTRIBUTES", "animation", "template", "title", "delay", "customClass", "sanitize", "AttachmentMap", "AUTO", "TOP", "RIGHT", "BOTTOM", "LEFT", "area", "br", "col", "code", "div", "em", "hr", "h1", "h2", "h3", "h4", "h5", "h6", "img", "li", "ol", "p", "pre", "s", "small", "span", "sub", "sup", "strong", "u", "ul", "HIDE", "HIDDEN", "SHOW", "SHOWN", "INSERTED", "CLICK", "FOCUSIN", "FOCUSOUT", "MOUSEENTER", "MOUSELEAVE", "CLASS_NAME_FADE", "HOVER_STATE_SHOW", "HOVER_STATE_OUT", "SELECTOR_TOOLTIP_INNER", "SELECTOR_MODAL", "EVENT_MODAL_HIDE", "TRIGGER_HOVER", "TRIGGER_FOCUS", "<PERSON><PERSON><PERSON>", "_isEnabled", "_timeout", "_hoverState", "_activeTrigger", "tip", "_setListeners", "enable", "disable", "toggle<PERSON>nabled", "_initializeOnDelegatedTarget", "click", "_isWithActiveTrigger", "_enter", "_leave", "getTipElement", "_hideModalHandler", "_disposePopper", "isWithContent", "shadowRoot", "isInTheDom", "getTitle", "tipId", "prefix", "floor", "random", "getElementById", "getUID", "attachment", "_getAttachment", "_addAttachmentClass", "_resolvePossibleFunction", "prevHoverState", "_cleanTipClass", "<PERSON><PERSON><PERSON><PERSON>", "_sanitizeAndSetContent", "content", "templateElement", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "textContent", "updateAttachment", "_getDelegateConfig", "_handlePopperPlacementChange", "_getBasicClassPrefix", "eventIn", "eventOut", "_fixTitle", "originalTitleType", "dataAttributes", "dataAttr", "basicClassPrefixRegex", "tabClass", "token", "tClass", "Popover", "_getContent", "SELECTOR_LINK_ITEMS", "METHOD_POSITION", "ScrollSpy", "_scrollElement", "_offsets", "_targets", "_activeTarget", "_scrollHeight", "_process", "refresh", "autoMethod", "offsetMethod", "offsetBase", "_getScrollTop", "_getScrollHeight", "targetSelector", "targetBCR", "item", "_getOffsetHeight", "innerHeight", "maxScroll", "_activate", "_clear", "queries", "link", "listGroup", "navItem", "spy", "SELECTOR_ACTIVE", "SELECTOR_ACTIVE_UL", "Tab", "listElement", "itemSelector", "hideEvent", "complete", "active", "isTransitioning", "_transitionComplete", "dropdown<PERSON><PERSON>d", "dropdownElement", "dropdown", "CLASS_NAME_HIDE", "CLASS_NAME_SHOWING", "autohide", "Toast", "_hasMouseInteraction", "_hasKeyboardInteraction", "_clearTimeout", "_maybeScheduleHide", "_onInteraction", "isInteracting"], "mappings": ";;;;;0OAOA,MAEMA,EAAiB,gBAyBjBC,EAAcC,IAClB,IAAIC,EAAWD,EAAQE,aAAa,kBAEpC,IAAKD,GAAyB,MAAbA,EAAkB,CACjC,IAAIE,EAAWH,EAAQE,aAAa,QAMpC,IAAKC,IAAcA,EAASC,SAAS,OAASD,EAASE,WAAW,KAChE,OAAO,KAILF,EAASC,SAAS,OAASD,EAASE,WAAW,OACjDF,EAAY,IAAGA,EAASG,MAAM,KAAK,MAGrCL,EAAWE,GAAyB,MAAbA,EAAmBA,EAASI,OAAS,KAG9D,OAAON,GAGHO,EAAyBR,IAC7B,MAAMC,EAAWF,EAAYC,GAE7B,OAAIC,GACKQ,SAASC,cAAcT,GAAYA,EAGrC,MAGHU,EAAyBX,IAC7B,MAAMC,EAAWF,EAAYC,GAE7B,OAAOC,EAAWQ,SAASC,cAAcT,GAAY,MA0BjDW,EAAuBZ,IAC3BA,EAAQa,cAAc,IAAIC,MAAMhB,KAG5BiB,EAAYC,MACXA,GAAsB,iBAARA,UAIO,IAAfA,EAAIC,SACbD,EAAMA,EAAI,SAGmB,IAAjBA,EAAIE,UAGdC,EAAaH,GACbD,EAAUC,GACLA,EAAIC,OAASD,EAAI,GAAKA,EAGZ,iBAARA,GAAoBA,EAAII,OAAS,EACnCX,SAASC,cAAcM,GAGzB,KAGHK,EAAkB,CAACC,EAAeC,EAAQC,KAC9CC,OAAOC,KAAKF,GAAaG,SAAQC,IAC/B,MAAMC,EAAgBL,EAAYI,GAC5BE,EAAQP,EAAOK,GACfG,EAAYD,GAASf,EAAUe,GAAS,UArH5Cd,OADSA,EAsHsDc,GApHzD,GAAEd,IAGL,GAAGgB,SAASC,KAAKjB,GAAKkB,MAAM,eAAe,GAAGC,cALxCnB,IAAAA,EAwHX,IAAK,IAAIoB,OAAOP,GAAeQ,KAAKN,GAClC,MAAM,IAAIO,UACP,GAAEhB,EAAciB,0BAA0BX,qBAA4BG,yBAAiCF,WAM1GW,EAAYxC,MACXe,EAAUf,IAAgD,IAApCA,EAAQyC,iBAAiBrB,SAIgB,YAA7DsB,iBAAiB1C,GAAS2C,iBAAiB,cAG9CC,EAAa5C,IACZA,GAAWA,EAAQkB,WAAa2B,KAAKC,gBAItC9C,EAAQ+C,UAAUC,SAAS,mBAIC,IAArBhD,EAAQiD,SACVjD,EAAQiD,SAGVjD,EAAQkD,aAAa,aAAoD,UAArClD,EAAQE,aAAa,aAG5DiD,EAAiBnD,IACrB,IAAKS,SAAS2C,gBAAgBC,aAC5B,OAAO,KAIT,GAAmC,mBAAxBrD,EAAQsD,YAA4B,CAC7C,MAAMC,EAAOvD,EAAQsD,cACrB,OAAOC,aAAgBC,WAAaD,EAAO,KAG7C,OAAIvD,aAAmBwD,WACdxD,EAIJA,EAAQyD,WAINN,EAAenD,EAAQyD,YAHrB,MAMLC,EAAO,OAUPC,EAAS3D,IAEbA,EAAQ4D,cAGJC,EAAY,KAChB,MAAMC,OAAEA,GAAWC,OAEnB,OAAID,IAAWrD,SAASuD,KAAKd,aAAa,qBACjCY,EAGF,MAGHG,EAA4B,GAiB5BC,EAAQ,IAAuC,QAAjCzD,SAAS2C,gBAAgBe,IAEvCC,EAAqBC,IAjBAC,IAAAA,EAAAA,EAkBN,KACjB,MAAMC,EAAIV,IAEV,GAAIU,EAAG,CACL,MAAMC,EAAOH,EAAOI,KACdC,EAAqBH,EAAEI,GAAGH,GAChCD,EAAEI,GAAGH,GAAQH,EAAOO,gBACpBL,EAAEI,GAAGH,GAAMK,YAAcR,EACzBE,EAAEI,GAAGH,GAAMM,WAAa,KACtBP,EAAEI,GAAGH,GAAQE,EACNL,EAAOO,mBA3BQ,YAAxBnE,SAASsE,YAENd,EAA0B7C,QAC7BX,SAASuE,iBAAiB,oBAAoB,KAC5Cf,EAA0BtC,SAAQ2C,GAAYA,SAIlDL,EAA0BgB,KAAKX,IAE/BA,KAuBEY,EAAUZ,IACU,mBAAbA,GACTA,KAIEa,EAAyB,CAACb,EAAUc,EAAmBC,GAAoB,KAC/E,IAAKA,EAEH,YADAH,EAAQZ,GAIV,MACMgB,EA1LiCtF,CAAAA,IACvC,IAAKA,EACH,OAAO,EAIT,IAAIuF,mBAAEA,EAAFC,gBAAsBA,GAAoBzB,OAAOrB,iBAAiB1C,GAEtE,MAAMyF,EAA0BC,OAAOC,WAAWJ,GAC5CK,EAAuBF,OAAOC,WAAWH,GAG/C,OAAKC,GAA4BG,GAKjCL,EAAqBA,EAAmBjF,MAAM,KAAK,GACnDkF,EAAkBA,EAAgBlF,MAAM,KAAK,GArFf,KAuFtBoF,OAAOC,WAAWJ,GAAsBG,OAAOC,WAAWH,KAPzD,GA6KgBK,CAAiCT,GADlC,EAGxB,IAAIU,GAAS,EAEb,MAAMC,EAAU,EAAGC,OAAAA,MACbA,IAAWZ,IAIfU,GAAS,EACTV,EAAkBa,oBAAoBnG,EAAgBiG,GACtDb,EAAQZ,KAGVc,EAAkBJ,iBAAiBlF,EAAgBiG,GACnDG,YAAW,KACJJ,GACHlF,EAAqBwE,KAEtBE,IAYCa,EAAuB,CAACC,EAAMC,EAAeC,EAAeC,KAChE,IAAIC,EAAQJ,EAAKK,QAAQJ,GAGzB,IAAe,IAAXG,EACF,OAAOJ,GAAME,GAAiBC,EAAiBH,EAAKhF,OAAS,EAAI,GAGnE,MAAMsF,EAAaN,EAAKhF,OAQxB,OANAoF,GAASF,EAAgB,GAAK,EAE1BC,IACFC,GAASA,EAAQE,GAAcA,GAG1BN,EAAKO,KAAKC,IAAI,EAAGD,KAAKE,IAAIL,EAAOE,EAAa,MCrSjDI,EAAiB,qBACjBC,EAAiB,OACjBC,EAAgB,SAChBC,EAAgB,GACtB,IAAIC,EAAW,EACf,MAAMC,EAAe,CACnBC,WAAY,YACZC,WAAY,YAERC,EAAoB,4BACpBC,EAAe,IAAIC,IAAI,CAC3B,QACA,WACA,UACA,YACA,cACA,aACA,iBACA,YACA,WACA,YACA,cACA,YACA,UACA,WACA,QACA,oBACA,aACA,YACA,WACA,cACA,cACA,cACA,YACA,eACA,gBACA,eACA,gBACA,aACA,QACA,OACA,SACA,QACA,SACA,SACA,UACA,WACA,OACA,SACA,eACA,SACA,OACA,mBACA,mBACA,QACA,QACA,WASF,SAASC,EAAYzH,EAAS0H,GAC5B,OAAQA,GAAQ,GAAEA,MAAQR,OAAiBlH,EAAQkH,UAAYA,IAGjE,SAASS,EAAS3H,GAChB,MAAM0H,EAAMD,EAAYzH,GAKxB,OAHAA,EAAQkH,SAAWQ,EACnBT,EAAcS,GAAOT,EAAcS,IAAQ,GAEpCT,EAAcS,GAsCvB,SAASE,EAAYC,EAAQ9B,EAAS+B,EAAqB,MACzD,MAAMC,EAAetG,OAAOC,KAAKmG,GAEjC,IAAK,IAAIG,EAAI,EAAGC,EAAMF,EAAa3G,OAAQ4G,EAAIC,EAAKD,IAAK,CACvD,MAAME,EAAQL,EAAOE,EAAaC,IAElC,GAAIE,EAAMC,kBAAoBpC,GAAWmC,EAAMJ,qBAAuBA,EACpE,OAAOI,EAIX,OAAO,KAGT,SAASE,EAAgBC,EAAmBtC,EAASuC,GACnD,MAAMC,EAAgC,iBAAZxC,EACpBoC,EAAkBI,EAAaD,EAAevC,EAEpD,IAAIyC,EAAYC,EAAaJ,GAO7B,OANiBd,EAAamB,IAAIF,KAGhCA,EAAYH,GAGP,CAACE,EAAYJ,EAAiBK,GAGvC,SAASG,EAAW3I,EAASqI,EAAmBtC,EAASuC,EAAcM,GACrE,GAAiC,iBAAtBP,IAAmCrI,EAC5C,OAUF,GAPK+F,IACHA,EAAUuC,EACVA,EAAe,MAKbhB,EAAkBjF,KAAKgG,GAAoB,CAC7C,MAAMQ,EAASlE,GACN,SAAUuD,GACf,IAAKA,EAAMY,eAAkBZ,EAAMY,gBAAkBZ,EAAMa,iBAAmBb,EAAMa,eAAe/F,SAASkF,EAAMY,eAChH,OAAOnE,EAAG1C,KAAK+G,KAAMd,IAKvBI,EACFA,EAAeO,EAAOP,GAEtBvC,EAAU8C,EAAO9C,GAIrB,MAAOwC,EAAYJ,EAAiBK,GAAaJ,EAAgBC,EAAmBtC,EAASuC,GACvFT,EAASF,EAAS3H,GAClBiJ,EAAWpB,EAAOW,KAAeX,EAAOW,GAAa,IACrDU,EAAatB,EAAYqB,EAAUd,EAAiBI,EAAaxC,EAAU,MAEjF,GAAImD,EAGF,YAFAA,EAAWN,OAASM,EAAWN,QAAUA,GAK3C,MAAMlB,EAAMD,EAAYU,EAAiBE,EAAkBc,QAAQrC,EAAgB,KAC7EnC,EAAK4D,EA3Fb,SAAoCvI,EAASC,EAAU0E,GACrD,OAAO,SAASoB,EAAQmC,GACtB,MAAMkB,EAAcpJ,EAAQqJ,iBAAiBpJ,GAE7C,IAAK,IAAI+F,OAAEA,GAAWkC,EAAOlC,GAAUA,IAAWgD,KAAMhD,EAASA,EAAOvC,WACtE,IAAK,IAAIuE,EAAIoB,EAAYhI,OAAQ4G,KAC/B,GAAIoB,EAAYpB,KAAOhC,EAOrB,OANAkC,EAAMa,eAAiB/C,EAEnBD,EAAQ6C,QACVU,EAAaC,IAAIvJ,EAASkI,EAAMsB,KAAMvJ,EAAU0E,GAG3CA,EAAG8E,MAAMzD,EAAQ,CAACkC,IAM/B,OAAO,MAyEPwB,CAA2B1J,EAAS+F,EAASuC,GAxGjD,SAA0BtI,EAAS2E,GACjC,OAAO,SAASoB,EAAQmC,GAOtB,OANAA,EAAMa,eAAiB/I,EAEnB+F,EAAQ6C,QACVU,EAAaC,IAAIvJ,EAASkI,EAAMsB,KAAM7E,GAGjCA,EAAG8E,MAAMzJ,EAAS,CAACkI,KAiG1ByB,CAAiB3J,EAAS+F,GAE5BpB,EAAGmD,mBAAqBS,EAAaxC,EAAU,KAC/CpB,EAAGwD,gBAAkBA,EACrBxD,EAAGiE,OAASA,EACZjE,EAAGuC,SAAWQ,EACduB,EAASvB,GAAO/C,EAEhB3E,EAAQgF,iBAAiBwD,EAAW7D,EAAI4D,GAG1C,SAASqB,EAAc5J,EAAS6H,EAAQW,EAAWzC,EAAS+B,GAC1D,MAAMnD,EAAKiD,EAAYC,EAAOW,GAAYzC,EAAS+B,GAE9CnD,IAIL3E,EAAQiG,oBAAoBuC,EAAW7D,EAAIkF,QAAQ/B,WAC5CD,EAAOW,GAAW7D,EAAGuC,WAe9B,SAASuB,EAAaP,GAGpB,OADAA,EAAQA,EAAMiB,QAAQpC,EAAgB,IAC/BI,EAAae,IAAUA,EAGhC,MAAMoB,EAAe,CACnBQ,GAAG9J,EAASkI,EAAOnC,EAASuC,GAC1BK,EAAW3I,EAASkI,EAAOnC,EAASuC,GAAc,IAGpDyB,IAAI/J,EAASkI,EAAOnC,EAASuC,GAC3BK,EAAW3I,EAASkI,EAAOnC,EAASuC,GAAc,IAGpDiB,IAAIvJ,EAASqI,EAAmBtC,EAASuC,GACvC,GAAiC,iBAAtBD,IAAmCrI,EAC5C,OAGF,MAAOuI,EAAYJ,EAAiBK,GAAaJ,EAAgBC,EAAmBtC,EAASuC,GACvF0B,EAAcxB,IAAcH,EAC5BR,EAASF,EAAS3H,GAClBiK,EAAc5B,EAAkBhI,WAAW,KAEjD,QAA+B,IAApB8H,EAAiC,CAE1C,IAAKN,IAAWA,EAAOW,GACrB,OAIF,YADAoB,EAAc5J,EAAS6H,EAAQW,EAAWL,EAAiBI,EAAaxC,EAAU,MAIhFkE,GACFxI,OAAOC,KAAKmG,GAAQlG,SAAQuI,KAhDlC,SAAkClK,EAAS6H,EAAQW,EAAW2B,GAC5D,MAAMC,EAAoBvC,EAAOW,IAAc,GAE/C/G,OAAOC,KAAK0I,GAAmBzI,SAAQ0I,IACrC,GAAIA,EAAWjK,SAAS+J,GAAY,CAClC,MAAMjC,EAAQkC,EAAkBC,GAEhCT,EAAc5J,EAAS6H,EAAQW,EAAWN,EAAMC,gBAAiBD,EAAMJ,wBA0CrEwC,CAAyBtK,EAAS6H,EAAQqC,EAAc7B,EAAkBkC,MAAM,OAIpF,MAAMH,EAAoBvC,EAAOW,IAAc,GAC/C/G,OAAOC,KAAK0I,GAAmBzI,SAAQ6I,IACrC,MAAMH,EAAaG,EAAYrB,QAAQnC,EAAe,IAEtD,IAAKgD,GAAe3B,EAAkBjI,SAASiK,GAAa,CAC1D,MAAMnC,EAAQkC,EAAkBI,GAEhCZ,EAAc5J,EAAS6H,EAAQW,EAAWN,EAAMC,gBAAiBD,EAAMJ,yBAK7E2C,QAAQzK,EAASkI,EAAOwC,GACtB,GAAqB,iBAAVxC,IAAuBlI,EAChC,OAAO,KAGT,MAAMuE,EAAIV,IACJ2E,EAAYC,EAAaP,GACzB8B,EAAc9B,IAAUM,EACxBmC,EAAWpD,EAAamB,IAAIF,GAElC,IAAIoC,EACAC,GAAU,EACVC,GAAiB,EACjBC,GAAmB,EACnBC,EAAM,KA4CV,OA1CIhB,GAAezF,IACjBqG,EAAcrG,EAAEzD,MAAMoH,EAAOwC,GAE7BnG,EAAEvE,GAASyK,QAAQG,GACnBC,GAAWD,EAAYK,uBACvBH,GAAkBF,EAAYM,gCAC9BH,EAAmBH,EAAYO,sBAG7BR,GACFK,EAAMvK,SAAS2K,YAAY,cAC3BJ,EAAIK,UAAU7C,EAAWqC,GAAS,IAElCG,EAAM,IAAIM,YAAYpD,EAAO,CAC3B2C,QAAAA,EACAU,YAAY,SAKI,IAATb,GACTjJ,OAAOC,KAAKgJ,GAAM/I,SAAQ6J,IACxB/J,OAAOgK,eAAeT,EAAKQ,EAAK,CAC9BE,IAAG,IACMhB,EAAKc,QAMhBT,GACFC,EAAIW,iBAGFb,GACF9K,EAAQa,cAAcmK,GAGpBA,EAAID,uBAA2C,IAAhBH,GACjCA,EAAYe,iBAGPX,IC1ULY,EAAa,IAAIC,IAEvBC,EAAe,CACbC,IAAI/L,EAASwL,EAAKQ,GACXJ,EAAWlD,IAAI1I,IAClB4L,EAAWG,IAAI/L,EAAS,IAAI6L,KAG9B,MAAMI,EAAcL,EAAWF,IAAI1L,GAI9BiM,EAAYvD,IAAI8C,IAA6B,IAArBS,EAAYC,KAMzCD,EAAYF,IAAIP,EAAKQ,GAJnBG,QAAQC,MAAO,+EAA8EC,MAAMC,KAAKL,EAAYvK,QAAQ,QAOhIgK,IAAG,CAAC1L,EAASwL,IACPI,EAAWlD,IAAI1I,IACV4L,EAAWF,IAAI1L,GAAS0L,IAAIF,IAG9B,KAGTe,OAAOvM,EAASwL,GACd,IAAKI,EAAWlD,IAAI1I,GAClB,OAGF,MAAMiM,EAAcL,EAAWF,IAAI1L,GAEnCiM,EAAYO,OAAOhB,GAGM,IAArBS,EAAYC,MACdN,EAAWY,OAAOxM,KC/BxB,MAAMyM,EACJC,YAAY1M,IACVA,EAAUmB,EAAWnB,MAMrBgJ,KAAK2D,SAAW3M,EAChB8L,EAAKC,IAAI/C,KAAK2D,SAAU3D,KAAK0D,YAAYE,SAAU5D,OAGrD6D,UACEf,EAAKS,OAAOvD,KAAK2D,SAAU3D,KAAK0D,YAAYE,UAC5CtD,EAAaC,IAAIP,KAAK2D,SAAU3D,KAAK0D,YAAYI,WAEjDrL,OAAOsL,oBAAoB/D,MAAMrH,SAAQqL,IACvChE,KAAKgE,GAAgB,QAIzBC,eAAe3I,EAAUtE,EAASkN,GAAa,GAC7C/H,EAAuBb,EAAUtE,EAASkN,GAK1BC,mBAACnN,GACjB,OAAO8L,EAAKJ,IAAIvK,EAAWnB,GAAUgJ,KAAK4D,UAGlBO,2BAACnN,EAASuB,EAAS,IAC3C,OAAOyH,KAAKoE,YAAYpN,IAAY,IAAIgJ,KAAKhJ,EAA2B,iBAAXuB,EAAsBA,EAAS,MAGnF8L,qBACT,MAtCY,QAyCH5I,kBACT,MAAM,IAAI6I,MAAM,uEAGPV,sBACT,MAAQ,MAAK5D,KAAKvE,OAGTqI,uBACT,MAAQ,IAAG9D,KAAK4D,YC5DpB,MAAMW,EAAuB,CAACC,EAAWC,EAAS,UAChD,MAAMC,EAAc,gBAAeF,EAAUV,YACvCtI,EAAOgJ,EAAU/I,KAEvB6E,EAAaQ,GAAGrJ,SAAUiN,EAAa,qBAAoBlJ,OAAU,SAAU0D,GAK7E,GAJI,CAAC,IAAK,QAAQ9H,SAAS4I,KAAK2E,UAC9BzF,EAAMyD,iBAGJ/I,EAAWoG,MACb,OAGF,MAAMhD,EAASrF,EAAuBqI,OAASA,KAAK4E,QAAS,IAAGpJ,KAC/CgJ,EAAUK,oBAAoB7H,GAGtCyH,SCMb,MAAMK,UAAcrB,EAGPhI,kBACT,MAnBS,QAwBXsJ,QAGE,GAFmBzE,EAAamB,QAAQzB,KAAK2D,SArB5B,kBAuBF5B,iBACb,OAGF/B,KAAK2D,SAAS5J,UAAUwJ,OAxBJ,QA0BpB,MAAMW,EAAalE,KAAK2D,SAAS5J,UAAUC,SA3BvB,QA4BpBgG,KAAKiE,gBAAe,IAAMjE,KAAKgF,mBAAmBhF,KAAK2D,SAAUO,GAInEc,kBACEhF,KAAK2D,SAASJ,SACdjD,EAAamB,QAAQzB,KAAK2D,SAnCR,mBAoClB3D,KAAK6D,UAKeM,uBAAC5L,GACrB,OAAOyH,KAAKiF,MAAK,WACf,MAAMC,EAAOJ,EAAMD,oBAAoB7E,MAEvC,GAAsB,iBAAXzH,EAAX,CAIA,QAAqB4M,IAAjBD,EAAK3M,IAAyBA,EAAOlB,WAAW,MAAmB,gBAAXkB,EAC1D,MAAM,IAAIe,UAAW,oBAAmBf,MAG1C2M,EAAK3M,GAAQyH,WAWnBuE,EAAqBO,EAAO,SAS5B1J,EAAmB0J,GC/EnB,MAOMM,EAAuB,4BAU7B,MAAMC,UAAe5B,EAGRhI,kBACT,MArBS,SA0BX6J,SAEEtF,KAAK2D,SAAS4B,aAAa,eAAgBvF,KAAK2D,SAAS5J,UAAUuL,OAvB7C,WA4BFnB,uBAAC5L,GACrB,OAAOyH,KAAKiF,MAAK,WACf,MAAMC,EAAOG,EAAOR,oBAAoB7E,MAEzB,WAAXzH,GACF2M,EAAK3M,SChDb,SAASiN,EAAcC,GACrB,MAAY,SAARA,GAIQ,UAARA,IAIAA,IAAQ/I,OAAO+I,GAAKzM,WACf0D,OAAO+I,GAGJ,KAARA,GAAsB,SAARA,EACT,KAGFA,GAGT,SAASC,EAAiBlD,GACxB,OAAOA,EAAIrC,QAAQ,UAAUwF,GAAQ,IAAGA,EAAIxM,kBDuC9CmH,EAAaQ,GAAGrJ,SAzCc,2BAyCkB2N,GAAsBlG,IACpEA,EAAMyD,iBAEN,MAAMiD,EAAS1G,EAAMlC,OAAO4H,QAAQQ,GACvBC,EAAOR,oBAAoBe,GAEnCN,YAUPlK,EAAmBiK,GCpDnB,MAAMQ,EAAc,CAClBC,iBAAiB9O,EAASwL,EAAK1J,GAC7B9B,EAAQuO,aAAc,WAAUG,EAAiBlD,KAAQ1J,IAG3DiN,oBAAoB/O,EAASwL,GAC3BxL,EAAQgP,gBAAiB,WAAUN,EAAiBlD,OAGtDyD,kBAAkBjP,GAChB,IAAKA,EACH,MAAO,GAGT,MAAMkP,EAAa,GAUnB,OARAzN,OAAOC,KAAK1B,EAAQmP,SACjBC,QAAO5D,GAAOA,EAAInL,WAAW,QAC7BsB,SAAQ6J,IACP,IAAI6D,EAAU7D,EAAIrC,QAAQ,MAAO,IACjCkG,EAAUA,EAAQC,OAAO,GAAGnN,cAAgBkN,EAAQ9E,MAAM,EAAG8E,EAAQjO,QACrE8N,EAAWG,GAAWb,EAAcxO,EAAQmP,QAAQ3D,OAGjD0D,GAGTK,iBAAgB,CAACvP,EAASwL,IACjBgD,EAAcxO,EAAQE,aAAc,WAAUwO,EAAiBlD,OAGxEgE,OAAOxP,GACL,MAAMyP,EAAOzP,EAAQ0P,wBAErB,MAAO,CACLC,IAAKF,EAAKE,IAAM5L,OAAO6L,YACvBC,KAAMJ,EAAKI,KAAO9L,OAAO+L,cAI7BC,SAAS/P,IACA,CACL2P,IAAK3P,EAAQgQ,UACbH,KAAM7P,EAAQiQ,cCzDdC,EAAiB,CACrBC,KAAI,CAAClQ,EAAUD,EAAUS,SAAS2C,kBACzB,GAAGgN,UAAUC,QAAQC,UAAUjH,iBAAiBpH,KAAKjC,EAASC,IAGvEsQ,QAAO,CAACtQ,EAAUD,EAAUS,SAAS2C,kBAC5BiN,QAAQC,UAAU5P,cAAcuB,KAAKjC,EAASC,GAGvDuQ,SAAQ,CAACxQ,EAASC,IACT,GAAGmQ,UAAUpQ,EAAQwQ,UACzBpB,QAAOqB,GAASA,EAAMC,QAAQzQ,KAGnC0Q,QAAQ3Q,EAASC,GACf,MAAM0Q,EAAU,GAEhB,IAAIC,EAAW5Q,EAAQyD,WAEvB,KAAOmN,GAAYA,EAAS1P,WAAa2B,KAAKC,cArBhC,IAqBgD8N,EAAS1P,UACjE0P,EAASF,QAAQzQ,IACnB0Q,EAAQ1L,KAAK2L,GAGfA,EAAWA,EAASnN,WAGtB,OAAOkN,GAGTE,KAAK7Q,EAASC,GACZ,IAAI6Q,EAAW9Q,EAAQ+Q,uBAEvB,KAAOD,GAAU,CACf,GAAIA,EAASJ,QAAQzQ,GACnB,MAAO,CAAC6Q,GAGVA,EAAWA,EAASC,uBAGtB,MAAO,IAGTC,KAAKhR,EAASC,GACZ,IAAI+Q,EAAOhR,EAAQiR,mBAEnB,KAAOD,GAAM,CACX,GAAIA,EAAKN,QAAQzQ,GACf,MAAO,CAAC+Q,GAGVA,EAAOA,EAAKC,mBAGd,MAAO,IAGTC,kBAAkBlR,GAChB,MAAMmR,EAAa,CACjB,IACA,SACA,QACA,WACA,SACA,UACA,aACA,4BACAC,KAAInR,GAAa,GAAEA,2BAAiCoR,KAAK,MAE3D,OAAOrI,KAAKmH,KAAKgB,EAAYnR,GAASoP,QAAOkC,IAAO1O,EAAW0O,IAAO9O,EAAU8O,OC3D9E7M,EAAO,WAUP8M,EAAU,CACdC,SAAU,IACVC,UAAU,EACVC,OAAO,EACPC,MAAO,QACPC,MAAM,EACNC,OAAO,GAGHC,EAAc,CAClBN,SAAU,mBACVC,SAAU,UACVC,MAAO,mBACPC,MAAO,mBACPC,KAAM,UACNC,MAAO,WAGHE,EAAa,OACbC,EAAa,OACbC,EAAiB,OACjBC,EAAkB,QAElBC,GAAmB,CACvBC,UAAkBF,EAClBG,WAAmBJ,GAIfK,GAAc,mBAcdC,GAAoB,SASpBC,GAAuB,wBAiB7B,MAAMC,WAAiBhG,EACrBC,YAAY1M,EAASuB,GACnBmR,MAAM1S,GAENgJ,KAAK2J,OAAS,KACd3J,KAAK4J,UAAY,KACjB5J,KAAK6J,eAAiB,KACtB7J,KAAK8J,WAAY,EACjB9J,KAAK+J,YAAa,EAClB/J,KAAKgK,aAAe,KACpBhK,KAAKiK,YAAc,EACnBjK,KAAKkK,YAAc,EAEnBlK,KAAKmK,QAAUnK,KAAKoK,WAAW7R,GAC/ByH,KAAKqK,mBAAqBnD,EAAeK,QA3BjB,uBA2B8CvH,KAAK2D,UAC3E3D,KAAKsK,gBAAkB,iBAAkB7S,SAAS2C,iBAAmBmQ,UAAUC,eAAiB,EAChGxK,KAAKyK,cAAgB5J,QAAQ9F,OAAO2P,cAEpC1K,KAAK2K,qBAKIpC,qBACT,OAAOA,EAGE9M,kBACT,OAAOA,EAKTuM,OACEhI,KAAK4K,OAAO7B,GAGd8B,mBAGOpT,SAASqT,QAAUtR,EAAUwG,KAAK2D,WACrC3D,KAAKgI,OAITH,OACE7H,KAAK4K,OAAO5B,GAGdL,MAAMzJ,GACCA,IACHc,KAAK8J,WAAY,GAGf5C,EAAeK,QApEI,2CAoEwBvH,KAAK2D,YAClD/L,EAAqBoI,KAAK2D,UAC1B3D,KAAK+K,OAAM,IAGbC,cAAchL,KAAK4J,WACnB5J,KAAK4J,UAAY,KAGnBmB,MAAM7L,GACCA,IACHc,KAAK8J,WAAY,GAGf9J,KAAK4J,YACPoB,cAAchL,KAAK4J,WACnB5J,KAAK4J,UAAY,MAGf5J,KAAKmK,SAAWnK,KAAKmK,QAAQ3B,WAAaxI,KAAK8J,YACjD9J,KAAKiL,kBAELjL,KAAK4J,UAAYsB,aACdzT,SAAS0T,gBAAkBnL,KAAK6K,gBAAkB7K,KAAKgI,MAAMoD,KAAKpL,MACnEA,KAAKmK,QAAQ3B,WAKnB6C,GAAG7N,GACDwC,KAAK6J,eAAiB3C,EAAeK,QAAQiC,GAAsBxJ,KAAK2D,UACxE,MAAM2H,EAActL,KAAKuL,cAAcvL,KAAK6J,gBAE5C,GAAIrM,EAAQwC,KAAK2J,OAAOvR,OAAS,GAAKoF,EAAQ,EAC5C,OAGF,GAAIwC,KAAK+J,WAEP,YADAzJ,EAAaS,IAAIf,KAAK2D,SAAU2F,IAAY,IAAMtJ,KAAKqL,GAAG7N,KAI5D,GAAI8N,IAAgB9N,EAGlB,OAFAwC,KAAK2I,aACL3I,KAAK+K,QAIP,MAAMS,EAAQhO,EAAQ8N,EACpBvC,EACAC,EAEFhJ,KAAK4K,OAAOY,EAAOxL,KAAK2J,OAAOnM,IAKjC4M,WAAW7R,GAOT,OANAA,EAAS,IACJgQ,KACA1C,EAAYI,kBAAkBjG,KAAK2D,aAChB,iBAAXpL,EAAsBA,EAAS,IAE5CF,EAAgBoD,EAAMlD,EAAQuQ,GACvBvQ,EAGTkT,eACE,MAAMC,EAAY/N,KAAKgO,IAAI3L,KAAKkK,aAEhC,GAAIwB,GAnMgB,GAoMlB,OAGF,MAAME,EAAYF,EAAY1L,KAAKkK,YAEnClK,KAAKkK,YAAc,EAEd0B,GAIL5L,KAAK4K,OAAOgB,EAAY,EAAI1C,EAAkBD,GAGhD0B,qBACM3K,KAAKmK,QAAQ1B,UACfnI,EAAaQ,GAAGd,KAAK2D,SApLJ,uBAoL6BzE,GAASc,KAAK6L,SAAS3M,KAG5C,UAAvBc,KAAKmK,QAAQxB,QACfrI,EAAaQ,GAAGd,KAAK2D,SAvLD,0BAuL6BzE,GAASc,KAAK2I,MAAMzJ,KACrEoB,EAAaQ,GAAGd,KAAK2D,SAvLD,0BAuL6BzE,GAASc,KAAK+K,MAAM7L,MAGnEc,KAAKmK,QAAQtB,OAAS7I,KAAKsK,iBAC7BtK,KAAK8L,0BAITA,0BACE,MAAMC,EAAqB7M,GAClBc,KAAKyK,gBAnKO,QAoKhBvL,EAAM8M,aArKY,UAqKwB9M,EAAM8M,aAG/CC,EAAQ/M,IACR6M,EAAmB7M,GACrBc,KAAKiK,YAAc/K,EAAMgN,QACflM,KAAKyK,gBACfzK,KAAKiK,YAAc/K,EAAMiN,QAAQ,GAAGD,UAIlCE,EAAOlN,IAEXc,KAAKkK,YAAchL,EAAMiN,SAAWjN,EAAMiN,QAAQ/T,OAAS,EACzD,EACA8G,EAAMiN,QAAQ,GAAGD,QAAUlM,KAAKiK,aAG9BoC,EAAMnN,IACN6M,EAAmB7M,KACrBc,KAAKkK,YAAchL,EAAMgN,QAAUlM,KAAKiK,aAG1CjK,KAAKyL,eACsB,UAAvBzL,KAAKmK,QAAQxB,QASf3I,KAAK2I,QACD3I,KAAKgK,cACPsC,aAAatM,KAAKgK,cAGpBhK,KAAKgK,aAAe9M,YAAWgC,GAASc,KAAK+K,MAAM7L,IA3Q5B,IA2Q6Dc,KAAKmK,QAAQ3B,YAIrGtB,EAAeC,KAtNO,qBAsNiBnH,KAAK2D,UAAUhL,SAAQ4T,IAC5DjM,EAAaQ,GAAGyL,EAvOI,yBAuOuBrN,GAASA,EAAMyD,sBAGxD3C,KAAKyK,eACPnK,EAAaQ,GAAGd,KAAK2D,SA7OA,2BA6O6BzE,GAAS+M,EAAM/M,KACjEoB,EAAaQ,GAAGd,KAAK2D,SA7OF,yBA6O6BzE,GAASmN,EAAInN,KAE7Dc,KAAK2D,SAAS5J,UAAUyS,IAnOG,mBAqO3BlM,EAAaQ,GAAGd,KAAK2D,SArPD,0BAqP6BzE,GAAS+M,EAAM/M,KAChEoB,EAAaQ,GAAGd,KAAK2D,SArPF,yBAqP6BzE,GAASkN,EAAKlN,KAC9DoB,EAAaQ,GAAGd,KAAK2D,SArPH,wBAqP6BzE,GAASmN,EAAInN,MAIhE2M,SAAS3M,GACP,GAAI,kBAAkB7F,KAAK6F,EAAMlC,OAAO2H,SACtC,OAGF,MAAMiH,EAAYzC,GAAiBjK,EAAMsD,KACrCoJ,IACF1M,EAAMyD,iBACN3C,KAAK4K,OAAOgB,IAIhBL,cAAcvU,GAKZ,OAJAgJ,KAAK2J,OAAS3S,GAAWA,EAAQyD,WAC/ByM,EAAeC,KArPC,iBAqPmBnQ,EAAQyD,YAC3C,GAEKuF,KAAK2J,OAAOlM,QAAQzG,GAG7ByV,gBAAgBjB,EAAOnO,GACrB,MAAMqP,EAASlB,IAAUzC,EACzB,OAAO5L,EAAqB6C,KAAK2J,OAAQtM,EAAeqP,EAAQ1M,KAAKmK,QAAQvB,MAG/E+D,mBAAmB7M,EAAe8M,GAChC,MAAMC,EAAc7M,KAAKuL,cAAczL,GACjCgN,EAAY9M,KAAKuL,cAAcrE,EAAeK,QAAQiC,GAAsBxJ,KAAK2D,WAEvF,OAAOrD,EAAamB,QAAQzB,KAAK2D,SA7RhB,oBA6RuC,CACtD7D,cAAAA,EACA8L,UAAWgB,EACXtJ,KAAMwJ,EACNzB,GAAIwB,IAIRE,2BAA2B/V,GACzB,GAAIgJ,KAAKqK,mBAAoB,CAC3B,MAAM2C,EAAkB9F,EAAeK,QAhRrB,UAgR8CvH,KAAKqK,oBAErE2C,EAAgBjT,UAAUwJ,OAAOgG,IACjCyD,EAAgBhH,gBAAgB,gBAEhC,MAAMiH,EAAa/F,EAAeC,KA/Qb,mBA+QsCnH,KAAKqK,oBAEhE,IAAK,IAAIrL,EAAI,EAAGA,EAAIiO,EAAW7U,OAAQ4G,IACrC,GAAItC,OAAOwQ,SAASD,EAAWjO,GAAG9H,aAAa,oBAAqB,MAAQ8I,KAAKuL,cAAcvU,GAAU,CACvGiW,EAAWjO,GAAGjF,UAAUyS,IAAIjD,IAC5B0D,EAAWjO,GAAGuG,aAAa,eAAgB,QAC3C,QAMR0F,kBACE,MAAMjU,EAAUgJ,KAAK6J,gBAAkB3C,EAAeK,QAAQiC,GAAsBxJ,KAAK2D,UAEzF,IAAK3M,EACH,OAGF,MAAMmW,EAAkBzQ,OAAOwQ,SAASlW,EAAQE,aAAa,oBAAqB,IAE9EiW,GACFnN,KAAKmK,QAAQiD,gBAAkBpN,KAAKmK,QAAQiD,iBAAmBpN,KAAKmK,QAAQ3B,SAC5ExI,KAAKmK,QAAQ3B,SAAW2E,GAExBnN,KAAKmK,QAAQ3B,SAAWxI,KAAKmK,QAAQiD,iBAAmBpN,KAAKmK,QAAQ3B,SAIzEoC,OAAOyC,EAAkBrW,GACvB,MAAMwU,EAAQxL,KAAKsN,kBAAkBD,GAC/BhQ,EAAgB6J,EAAeK,QAAQiC,GAAsBxJ,KAAK2D,UAClE4J,EAAqBvN,KAAKuL,cAAclO,GACxCmQ,EAAcxW,GAAWgJ,KAAKyM,gBAAgBjB,EAAOnO,GAErDoQ,EAAmBzN,KAAKuL,cAAciC,GACtCE,EAAY7M,QAAQb,KAAK4J,WAEzB8C,EAASlB,IAAUzC,EACnB4E,EAAuBjB,EAjUR,sBADF,oBAmUbkB,EAAiBlB,EAjUH,qBACA,qBAiUdE,EAAqB5M,KAAK6N,kBAAkBrC,GAElD,GAAIgC,GAAeA,EAAYzT,UAAUC,SAASuP,IAEhD,YADAvJ,KAAK+J,YAAa,GAIpB,GAAI/J,KAAK+J,WACP,OAIF,GADmB/J,KAAK2M,mBAAmBa,EAAaZ,GACzC7K,iBACb,OAGF,IAAK1E,IAAkBmQ,EAErB,OAGFxN,KAAK+J,YAAa,EAEd2D,GACF1N,KAAK2I,QAGP3I,KAAK+M,2BAA2BS,GAChCxN,KAAK6J,eAAiB2D,EAEtB,MAAMM,EAAmB,KACvBxN,EAAamB,QAAQzB,KAAK2D,SAAU2F,GAAY,CAC9CxJ,cAAe0N,EACf5B,UAAWgB,EACXtJ,KAAMiK,EACNlC,GAAIoC,KAIR,GAAIzN,KAAK2D,SAAS5J,UAAUC,SA5WP,SA4WmC,CACtDwT,EAAYzT,UAAUyS,IAAIoB,GAE1BjT,EAAO6S,GAEPnQ,EAActD,UAAUyS,IAAImB,GAC5BH,EAAYzT,UAAUyS,IAAImB,GAE1B,MAAMI,EAAmB,KACvBP,EAAYzT,UAAUwJ,OAAOoK,EAAsBC,GACnDJ,EAAYzT,UAAUyS,IAAIjD,IAE1BlM,EAActD,UAAUwJ,OAAOgG,GAAmBqE,EAAgBD,GAElE3N,KAAK+J,YAAa,EAElB7M,WAAW4Q,EAAkB,IAG/B9N,KAAKiE,eAAe8J,EAAkB1Q,GAAe,QAErDA,EAActD,UAAUwJ,OAAOgG,IAC/BiE,EAAYzT,UAAUyS,IAAIjD,IAE1BvJ,KAAK+J,YAAa,EAClB+D,IAGEJ,GACF1N,KAAK+K,QAITuC,kBAAkB1B,GAChB,MAAK,CAAC1C,EAAiBD,GAAgB7R,SAASwU,GAI5C1Q,IACK0Q,IAAc3C,EAAiBD,EAAaD,EAG9C6C,IAAc3C,EAAiBF,EAAaC,EAP1C4C,EAUXiC,kBAAkBrC,GAChB,MAAK,CAACzC,EAAYC,GAAY5R,SAASoU,GAInCtQ,IACKsQ,IAAUxC,EAAaC,EAAiBC,EAG1CsC,IAAUxC,EAAaE,EAAkBD,EAPvCuC,EAYarH,yBAACnN,EAASuB,GAChC,MAAM2M,EAAOuE,GAAS5E,oBAAoB7N,EAASuB,GAEnD,IAAI4R,QAAEA,GAAYjF,EACI,iBAAX3M,IACT4R,EAAU,IACLA,KACA5R,IAIP,MAAMyV,EAA2B,iBAAXzV,EAAsBA,EAAS4R,EAAQzB,MAE7D,GAAsB,iBAAXnQ,EACT2M,EAAKmG,GAAG9S,QACH,GAAsB,iBAAXyV,EAAqB,CACrC,QAA4B,IAAjB9I,EAAK8I,GACd,MAAM,IAAI1U,UAAW,oBAAmB0U,MAG1C9I,EAAK8I,UACI7D,EAAQ3B,UAAY2B,EAAQ8D,OACrC/I,EAAKyD,QACLzD,EAAK6F,SAIa5G,uBAAC5L,GACrB,OAAOyH,KAAKiF,MAAK,WACfwE,GAASyE,kBAAkBlO,KAAMzH,MAIX4L,2BAACjF,GACzB,MAAMlC,EAASrF,EAAuBqI,MAEtC,IAAKhD,IAAWA,EAAOjD,UAAUC,SA7cT,YA8ctB,OAGF,MAAMzB,EAAS,IACVsN,EAAYI,kBAAkBjJ,MAC9B6I,EAAYI,kBAAkBjG,OAE7BmO,EAAanO,KAAK9I,aAAa,oBAEjCiX,IACF5V,EAAOiQ,UAAW,GAGpBiB,GAASyE,kBAAkBlR,EAAQzE,GAE/B4V,GACF1E,GAASrF,YAAYpH,GAAQqO,GAAG8C,GAGlCjP,EAAMyD,kBAUVrC,EAAaQ,GAAGrJ,SA7ec,6BAkBF,sCA2dyCgS,GAAS2E,qBAE9E9N,EAAaQ,GAAG/F,OAhfa,6BAgfgB,KAC3C,MAAMsT,EAAYnH,EAAeC,KA7dR,6BA+dzB,IAAK,IAAInI,EAAI,EAAGC,EAAMoP,EAAUjW,OAAQ4G,EAAIC,EAAKD,IAC/CyK,GAASyE,kBAAkBG,EAAUrP,GAAIyK,GAASrF,YAAYiK,EAAUrP,QAW5E5D,EAAmBqO,ICjjBnB,MAAMhO,GAAO,WAKP8M,GAAU,CACdjD,QAAQ,EACRgJ,OAAQ,MAGJxF,GAAc,CAClBxD,OAAQ,UACRgJ,OAAQ,kBASJC,GAAkB,OAClBC,GAAsB,WACtBC,GAAwB,aACxBC,GAAuB,YACvBC,GAA8B,6BAO9BvJ,GAAuB,8BAQ7B,MAAMwJ,WAAiBnL,EACrBC,YAAY1M,EAASuB,GACnBmR,MAAM1S,GAENgJ,KAAK6O,kBAAmB,EACxB7O,KAAKmK,QAAUnK,KAAKoK,WAAW7R,GAC/ByH,KAAK8O,cAAgB,GAErB,MAAMC,EAAa7H,EAAeC,KAAK/B,IAEvC,IAAK,IAAIpG,EAAI,EAAGC,EAAM8P,EAAW3W,OAAQ4G,EAAIC,EAAKD,IAAK,CACrD,MAAMgQ,EAAOD,EAAW/P,GAClB/H,EAAWO,EAAuBwX,GAClCC,EAAgB/H,EAAeC,KAAKlQ,GACvCmP,QAAO8I,GAAaA,IAAclP,KAAK2D,WAEzB,OAAb1M,GAAqBgY,EAAc7W,SACrC4H,KAAKmP,UAAYlY,EACjB+I,KAAK8O,cAAc7S,KAAK+S,IAI5BhP,KAAKoP,sBAEApP,KAAKmK,QAAQmE,QAChBtO,KAAKqP,0BAA0BrP,KAAK8O,cAAe9O,KAAKsP,YAGtDtP,KAAKmK,QAAQ7E,QACftF,KAAKsF,SAMEiD,qBACT,OAAOA,GAGE9M,kBACT,OAAOA,GAKT6J,SACMtF,KAAKsP,WACPtP,KAAKuP,OAELvP,KAAKwP,OAITA,OACE,GAAIxP,KAAK6O,kBAAoB7O,KAAKsP,WAChC,OAGF,IACIG,EADAC,EAAU,GAGd,GAAI1P,KAAKmK,QAAQmE,OAAQ,CACvB,MAAM9G,EAAWN,EAAeC,KAAKwH,GAA4B3O,KAAKmK,QAAQmE,QAC9EoB,EAAUxI,EAAeC,KAxEN,uCAwE6BnH,KAAKmK,QAAQmE,QAAQlI,QAAO4I,IAASxH,EAASpQ,SAAS4X,KAGzG,MAAMW,EAAYzI,EAAeK,QAAQvH,KAAKmP,WAC9C,GAAIO,EAAQtX,OAAQ,CAClB,MAAMwX,EAAiBF,EAAQvI,MAAK6H,GAAQW,IAAcX,IAG1D,GAFAS,EAAcG,EAAiBhB,GAASxK,YAAYwL,GAAkB,KAElEH,GAAeA,EAAYZ,iBAC7B,OAKJ,GADmBvO,EAAamB,QAAQzB,KAAK2D,SArG7B,oBAsGD5B,iBACb,OAGF2N,EAAQ/W,SAAQkX,IACVF,IAAcE,GAChBjB,GAAS/J,oBAAoBgL,EAAY,CAAEvK,QAAQ,IAASiK,OAGzDE,GACH3M,EAAKC,IAAI8M,EA9HA,cA8HsB,SAInC,MAAMC,EAAY9P,KAAK+P,gBAEvB/P,KAAK2D,SAAS5J,UAAUwJ,OAAOiL,IAC/BxO,KAAK2D,SAAS5J,UAAUyS,IAAIiC,IAE5BzO,KAAK2D,SAASqM,MAAMF,GAAa,EAEjC9P,KAAKqP,0BAA0BrP,KAAK8O,eAAe,GACnD9O,KAAK6O,kBAAmB,EAExB,MAYMoB,EAAc,SADSH,EAAU,GAAGvW,cAAgBuW,EAAUvO,MAAM,KAG1EvB,KAAKiE,gBAdY,KACfjE,KAAK6O,kBAAmB,EAExB7O,KAAK2D,SAAS5J,UAAUwJ,OAAOkL,IAC/BzO,KAAK2D,SAAS5J,UAAUyS,IAAIgC,GAAqBD,IAEjDvO,KAAK2D,SAASqM,MAAMF,GAAa,GAEjCxP,EAAamB,QAAQzB,KAAK2D,SArIX,uBA2Ia3D,KAAK2D,UAAU,GAC7C3D,KAAK2D,SAASqM,MAAMF,GAAc,GAAE9P,KAAK2D,SAASsM,OAGpDV,OACE,GAAIvP,KAAK6O,mBAAqB7O,KAAKsP,WACjC,OAIF,GADmBhP,EAAamB,QAAQzB,KAAK2D,SAnJ7B,oBAoJD5B,iBACb,OAGF,MAAM+N,EAAY9P,KAAK+P,gBAEvB/P,KAAK2D,SAASqM,MAAMF,GAAc,GAAE9P,KAAK2D,SAAS+C,wBAAwBoJ,OAE1EnV,EAAOqF,KAAK2D,UAEZ3D,KAAK2D,SAAS5J,UAAUyS,IAAIiC,IAC5BzO,KAAK2D,SAAS5J,UAAUwJ,OAAOiL,GAAqBD,IAEpD,MAAM2B,EAAqBlQ,KAAK8O,cAAc1W,OAC9C,IAAK,IAAI4G,EAAI,EAAGA,EAAIkR,EAAoBlR,IAAK,CAC3C,MAAMyC,EAAUzB,KAAK8O,cAAc9P,GAC7BgQ,EAAOrX,EAAuB8J,GAEhCuN,IAAShP,KAAKsP,SAASN,IACzBhP,KAAKqP,0BAA0B,CAAC5N,IAAU,GAI9CzB,KAAK6O,kBAAmB,EASxB7O,KAAK2D,SAASqM,MAAMF,GAAa,GAEjC9P,KAAKiE,gBATY,KACfjE,KAAK6O,kBAAmB,EACxB7O,KAAK2D,SAAS5J,UAAUwJ,OAAOkL,IAC/BzO,KAAK2D,SAAS5J,UAAUyS,IAAIgC,IAC5BlO,EAAamB,QAAQzB,KAAK2D,SAhLV,wBAqLY3D,KAAK2D,UAAU,GAG/C2L,SAAStY,EAAUgJ,KAAK2D,UACtB,OAAO3M,EAAQ+C,UAAUC,SAASuU,IAKpCnE,WAAW7R,GAST,OARAA,EAAS,IACJgQ,MACA1C,EAAYI,kBAAkBjG,KAAK2D,aACnCpL,IAEE+M,OAASzE,QAAQtI,EAAO+M,QAC/B/M,EAAO+V,OAASnW,EAAWI,EAAO+V,QAClCjW,EAAgBoD,GAAMlD,EAAQuQ,IACvBvQ,EAGTwX,gBACE,OAAO/P,KAAK2D,SAAS5J,UAAUC,SAnML,uBAEhB,QACC,SAmMboV,sBACE,IAAKpP,KAAKmK,QAAQmE,OAChB,OAGF,MAAM9G,EAAWN,EAAeC,KAAKwH,GAA4B3O,KAAKmK,QAAQmE,QAC9EpH,EAAeC,KAAK/B,GAAsBpF,KAAKmK,QAAQmE,QAAQlI,QAAO4I,IAASxH,EAASpQ,SAAS4X,KAC9FrW,SAAQ3B,IACP,MAAMmZ,EAAWxY,EAAuBX,GAEpCmZ,GACFnQ,KAAKqP,0BAA0B,CAACrY,GAAUgJ,KAAKsP,SAASa,OAKhEd,0BAA0Be,EAAcC,GACjCD,EAAahY,QAIlBgY,EAAazX,SAAQqW,IACfqB,EACFrB,EAAKjV,UAAUwJ,OAAOmL,IAEtBM,EAAKjV,UAAUyS,IAAIkC,IAGrBM,EAAKzJ,aAAa,gBAAiB8K,MAMjBlM,uBAAC5L,GACrB,OAAOyH,KAAKiF,MAAK,WACf,MAAMkF,EAAU,GACM,iBAAX5R,GAAuB,YAAYc,KAAKd,KACjD4R,EAAQ7E,QAAS,GAGnB,MAAMJ,EAAO0J,GAAS/J,oBAAoB7E,KAAMmK,GAEhD,GAAsB,iBAAX5R,EAAqB,CAC9B,QAA4B,IAAjB2M,EAAK3M,GACd,MAAM,IAAIe,UAAW,oBAAmBf,MAG1C2M,EAAK3M,UAYb+H,EAAaQ,GAAGrJ,SAzQc,6BAyQkB2N,IAAsB,SAAUlG,IAEjD,MAAzBA,EAAMlC,OAAO2H,SAAoBzF,EAAMa,gBAAmD,MAAjCb,EAAMa,eAAe4E,UAChFzF,EAAMyD,iBAGR,MAAM1L,EAAWO,EAAuBwI,MACfkH,EAAeC,KAAKlQ,GAE5B0B,SAAQ3B,IACvB4X,GAAS/J,oBAAoB7N,EAAS,CAAEsO,QAAQ,IAASA,eAW7DlK,EAAmBwT,IC5UZ,IAAIjI,GAAM,MACN2J,GAAS,SACTC,GAAQ,QACR1J,GAAO,OACP2J,GAAO,OACPC,GAAiB,CAAC9J,GAAK2J,GAAQC,GAAO1J,IACtCoF,GAAQ,QACRI,GAAM,MACNqE,GAAkB,kBAClBC,GAAW,WACXC,GAAS,SACTC,GAAY,YACZC,GAAmCL,GAAeM,QAAO,SAAUC,EAAKC,GACjF,OAAOD,EAAI5J,OAAO,CAAC6J,EAAY,IAAMhF,GAAOgF,EAAY,IAAM5E,OAC7D,IACQ6E,GAA0B,GAAG9J,OAAOqJ,GAAgB,CAACD,KAAOO,QAAO,SAAUC,EAAKC,GAC3F,OAAOD,EAAI5J,OAAO,CAAC6J,EAAWA,EAAY,IAAMhF,GAAOgF,EAAY,IAAM5E,OACxE,IAEQ8E,GAAa,aACbC,GAAO,OACPC,GAAY,YAEZC,GAAa,aACbC,GAAO,OACPC,GAAY,YAEZC,GAAc,cACdC,GAAQ,QACRC,GAAa,aACbC,GAAiB,CAACT,GAAYC,GAAMC,GAAWC,GAAYC,GAAMC,GAAWC,GAAaC,GAAOC,IC9B5F,SAASE,GAAY7a,GAClC,OAAOA,GAAWA,EAAQ8a,UAAY,IAAI3Y,cAAgB,KCD7C,SAAS4Y,GAAUC,GAChC,GAAY,MAARA,EACF,OAAOjX,OAGT,GAAwB,oBAApBiX,EAAKhZ,WAAkC,CACzC,IAAIiZ,EAAgBD,EAAKC,cACzB,OAAOA,GAAgBA,EAAcC,aAAwBnX,OAG/D,OAAOiX,ECRT,SAASja,GAAUia,GAEjB,OAAOA,aADUD,GAAUC,GAAM3K,SACI2K,aAAgB3K,QAGvD,SAAS8K,GAAcH,GAErB,OAAOA,aADUD,GAAUC,GAAMI,aACIJ,aAAgBI,YAGvD,SAASC,GAAaL,GAEpB,MAA0B,oBAAfxX,aAKJwX,aADUD,GAAUC,GAAMxX,YACIwX,aAAgBxX,YCyDvD,MAAA8X,GAAe,CACb9W,KAAM,cACN+W,SAAS,EACTC,MAAO,QACP7W,GA5EF,SAAqB8W,GACnB,IAAIC,EAAQD,EAAKC,MACjBja,OAAOC,KAAKga,EAAMC,UAAUha,SAAQ,SAAU6C,GAC5C,IAAIwU,EAAQ0C,EAAME,OAAOpX,IAAS,GAC9B0K,EAAawM,EAAMxM,WAAW1K,IAAS,GACvCxE,EAAU0b,EAAMC,SAASnX,GAExB2W,GAAcnb,IAAa6a,GAAY7a,KAO5CyB,OAAOoa,OAAO7b,EAAQgZ,MAAOA,GAC7BvX,OAAOC,KAAKwN,GAAYvN,SAAQ,SAAU6C,GACxC,IAAI1C,EAAQoN,EAAW1K,IAET,IAAV1C,EACF9B,EAAQgP,gBAAgBxK,GAExBxE,EAAQuO,aAAa/J,GAAgB,IAAV1C,EAAiB,GAAKA,WAwDvDga,OAlDF,SAAgBC,GACd,IAAIL,EAAQK,EAAML,MACdM,EAAgB,CAClBpC,OAAQ,CACN7J,SAAU2L,EAAMO,QAAQC,SACxBrM,KAAM,IACNF,IAAK,IACLwM,OAAQ,KAEVC,MAAO,CACLrM,SAAU,YAEZ8J,UAAW,IASb,OAPApY,OAAOoa,OAAOH,EAAMC,SAAS/B,OAAOZ,MAAOgD,EAAcpC,QACzD8B,EAAME,OAASI,EAEXN,EAAMC,SAASS,OACjB3a,OAAOoa,OAAOH,EAAMC,SAASS,MAAMpD,MAAOgD,EAAcI,OAGnD,WACL3a,OAAOC,KAAKga,EAAMC,UAAUha,SAAQ,SAAU6C,GAC5C,IAAIxE,EAAU0b,EAAMC,SAASnX,GACzB0K,EAAawM,EAAMxM,WAAW1K,IAAS,GAGvCwU,EAFkBvX,OAAOC,KAAKga,EAAME,OAAOS,eAAe7X,GAAQkX,EAAME,OAAOpX,GAAQwX,EAAcxX,IAE7EuV,QAAO,SAAUf,EAAOpX,GAElD,OADAoX,EAAMpX,GAAY,GACXoX,IACN,IAEEmC,GAAcnb,IAAa6a,GAAY7a,KAI5CyB,OAAOoa,OAAO7b,EAAQgZ,MAAOA,GAC7BvX,OAAOC,KAAKwN,GAAYvN,SAAQ,SAAU2a,GACxCtc,EAAQgP,gBAAgBsN,YAa9BC,SAAU,CAAC,kBCjFE,SAASC,GAAiBvC,GACvC,OAAOA,EAAU3Z,MAAM,KAAK,GCDf,SAASoP,GAAsB1P,EAC9Cyc,GAKE,IAAIhN,EAAOzP,EAAQ0P,wBAoBnB,MAAO,CACLgN,MAAOjN,EAAKiN,MApBD,EAqBXC,OAAQlN,EAAKkN,OApBF,EAqBXhN,IAAKF,EAAKE,IArBC,EAsBX4J,MAAO9J,EAAK8J,MAvBD,EAwBXD,OAAQ7J,EAAK6J,OAvBF,EAwBXzJ,KAAMJ,EAAKI,KAzBA,EA0BX+M,EAAGnN,EAAKI,KA1BG,EA2BXgN,EAAGpN,EAAKE,IA1BG,GCNA,SAASmN,GAAc9c,GACpC,IAAI+c,EAAarN,GAAsB1P,GAGnC0c,EAAQ1c,EAAQgd,YAChBL,EAAS3c,EAAQ4D,aAUrB,OARI+C,KAAKgO,IAAIoI,EAAWL,MAAQA,IAAU,IACxCA,EAAQK,EAAWL,OAGjB/V,KAAKgO,IAAIoI,EAAWJ,OAASA,IAAW,IAC1CA,EAASI,EAAWJ,QAGf,CACLC,EAAG5c,EAAQiQ,WACX4M,EAAG7c,EAAQgQ,UACX0M,MAAOA,EACPC,OAAQA,GCrBG,SAAS3Z,GAASsU,EAAQ7G,GACvC,IAAIwM,EAAWxM,EAAMnN,aAAemN,EAAMnN,cAE1C,GAAIgU,EAAOtU,SAASyN,GAClB,OAAO,EAEJ,GAAIwM,GAAY5B,GAAa4B,GAAW,CACzC,IAAIjM,EAAOP,EAEX,EAAG,CACD,GAAIO,GAAQsG,EAAO4F,WAAWlM,GAC5B,OAAO,EAITA,EAAOA,EAAKvN,YAAcuN,EAAKmM,WACxBnM,GAIb,OAAO,ECpBM,SAAStO,GAAiB1C,GACvC,OAAO+a,GAAU/a,GAAS0C,iBAAiB1C,GCD9B,SAASod,GAAepd,GACrC,MAAO,CAAC,QAAS,KAAM,MAAMyG,QAAQoU,GAAY7a,KAAa,ECDjD,SAASqd,GAAmBrd,GAEzC,QAASe,GAAUf,GAAWA,EAAQib,cACtCjb,EAAQS,WAAasD,OAAOtD,UAAU2C,gBCDzB,SAASka,GAActd,GACpC,MAA6B,SAAzB6a,GAAY7a,GACPA,EAMPA,EAAQud,cACRvd,EAAQyD,aACR4X,GAAarb,GAAWA,EAAQmd,KAAO,OAEvCE,GAAmBrd,GCRvB,SAASwd,GAAoBxd,GAC3B,OAAKmb,GAAcnb,IACoB,UAAvC0C,GAAiB1C,GAAS+P,SAInB/P,EAAQyd,aAHN,KAwCI,SAASC,GAAgB1d,GAItC,IAHA,IAAI+D,EAASgX,GAAU/a,GACnByd,EAAeD,GAAoBxd,GAEhCyd,GAAgBL,GAAeK,IAA6D,WAA5C/a,GAAiB+a,GAAc1N,UACpF0N,EAAeD,GAAoBC,GAGrC,OAAIA,IAA+C,SAA9B5C,GAAY4C,IAA0D,SAA9B5C,GAAY4C,IAAwE,WAA5C/a,GAAiB+a,GAAc1N,UAC3HhM,EAGF0Z,GA5CT,SAA4Bzd,GAC1B,IAAI2d,GAAsE,IAA1DpK,UAAUqK,UAAUzb,cAAcsE,QAAQ,WAG1D,IAFuD,IAA5C8M,UAAUqK,UAAUnX,QAAQ,YAE3B0U,GAAcnb,IAII,UAFX0C,GAAiB1C,GAEnB+P,SACb,OAAO,KAMX,IAFA,IAAI8N,EAAcP,GAActd,GAEzBmb,GAAc0C,IAAgB,CAAC,OAAQ,QAAQpX,QAAQoU,GAAYgD,IAAgB,GAAG,CAC3F,IAAIC,EAAMpb,GAAiBmb,GAI3B,GAAsB,SAAlBC,EAAIC,WAA4C,SAApBD,EAAIE,aAA0C,UAAhBF,EAAIG,UAAiF,IAA1D,CAAC,YAAa,eAAexX,QAAQqX,EAAII,aAAsBP,GAAgC,WAAnBG,EAAII,YAA2BP,GAAaG,EAAI1O,QAAyB,SAAf0O,EAAI1O,OACjO,OAAOyO,EAEPA,EAAcA,EAAYpa,WAI9B,OAAO,KAiBgB0a,CAAmBne,IAAY+D,EC9DzC,SAASqa,GAAyBnE,GAC/C,MAAO,CAAC,MAAO,UAAUxT,QAAQwT,IAAc,EAAI,IAAM,ICDpD,IAAIrT,GAAMD,KAAKC,IACXC,GAAMF,KAAKE,IACXwX,GAAQ1X,KAAK0X,MCDT,SAASC,GAAOzX,EAAK/E,EAAO8E,GACzC,OAAO2X,GAAQ1X,EAAK2X,GAAQ1c,EAAO8E,ICDtB,SAAS6X,GAAmBC,GACzC,OAAOjd,OAAOoa,OAAO,GCDd,CACLlM,IAAK,EACL4J,MAAO,EACPD,OAAQ,EACRzJ,KAAM,GDHuC6O,GEFlC,SAASC,GAAgB7c,EAAOJ,GAC7C,OAAOA,EAAKqY,QAAO,SAAU6E,EAASpT,GAEpC,OADAoT,EAAQpT,GAAO1J,EACR8c,IACN,ICwFL,MAAAC,GAAe,CACbra,KAAM,QACN+W,SAAS,EACTC,MAAO,OACP7W,GA9EF,SAAe8W,GACb,IAAIqD,EAEApD,EAAQD,EAAKC,MACblX,EAAOiX,EAAKjX,KACZyX,EAAUR,EAAKQ,QACf8C,EAAerD,EAAMC,SAASS,MAC9B4C,EAAgBtD,EAAMuD,cAAcD,cACpCE,EAAgB1C,GAAiBd,EAAMzB,WACvCkF,EAAOf,GAAyBc,GAEhCjX,EADa,CAAC4H,GAAM0J,IAAO9S,QAAQyY,IAAkB,EAClC,SAAW,QAElC,GAAKH,GAAiBC,EAAtB,CAIA,IAAIN,EAxBgB,SAAyBU,EAAS1D,GAItD,OAAO+C,GAAsC,iBAH7CW,EAA6B,mBAAZA,EAAyBA,EAAQ3d,OAAOoa,OAAO,GAAIH,EAAM2D,MAAO,CAC/EpF,UAAWyB,EAAMzB,aACbmF,GACkDA,EAAUT,GAAgBS,EAAS3F,KAoBvE6F,CAAgBrD,EAAQmD,QAAS1D,GACjD6D,EAAYzC,GAAciC,GAC1BS,EAAmB,MAATL,EAAexP,GAAME,GAC/B4P,EAAmB,MAATN,EAAe7F,GAASC,GAClCmG,EAAUhE,EAAM2D,MAAMxF,UAAU5R,GAAOyT,EAAM2D,MAAMxF,UAAUsF,GAAQH,EAAcG,GAAQzD,EAAM2D,MAAMzF,OAAO3R,GAC9G0X,EAAYX,EAAcG,GAAQzD,EAAM2D,MAAMxF,UAAUsF,GACxDS,EAAoBlC,GAAgBqB,GACpCc,EAAaD,EAA6B,MAATT,EAAeS,EAAkBE,cAAgB,EAAIF,EAAkBG,aAAe,EAAI,EAC3HC,EAAoBN,EAAU,EAAIC,EAAY,EAG9C9Y,EAAM6X,EAAcc,GACpB5Y,EAAMiZ,EAAaN,EAAUtX,GAAOyW,EAAce,GAClDQ,EAASJ,EAAa,EAAIN,EAAUtX,GAAO,EAAI+X,EAC/CxQ,EAAS8O,GAAOzX,EAAKoZ,EAAQrZ,GAE7BsZ,EAAWf,EACfzD,EAAMuD,cAAcza,KAASsa,EAAwB,IAA0BoB,GAAY1Q,EAAQsP,EAAsBqB,aAAe3Q,EAASyQ,EAAQnB,KA6CzJhD,OA1CF,SAAgBC,GACd,IAAIL,EAAQK,EAAML,MAEd0E,EADUrE,EAAME,QACWjc,QAC3B+e,OAAoC,IAArBqB,EAA8B,sBAAwBA,EAErD,MAAhBrB,IAKwB,iBAAjBA,IACTA,EAAerD,EAAMC,SAAS/B,OAAOlZ,cAAcqe,MAahD/b,GAAS0Y,EAAMC,SAAS/B,OAAQmF,KAQrCrD,EAAMC,SAASS,MAAQ2C,IAUvBxC,SAAU,CAAC,iBACX8D,iBAAkB,CAAC,oBCnGN,SAASC,GAAarG,GACnC,OAAOA,EAAU3Z,MAAM,KAAK,GCQ9B,IAAIigB,GAAa,CACf5Q,IAAK,OACL4J,MAAO,OACPD,OAAQ,OACRzJ,KAAM,QAgBD,SAAS2Q,GAAYzE,GAC1B,IAAI0E,EAEA7G,EAASmC,EAAMnC,OACf8G,EAAa3E,EAAM2E,WACnBzG,EAAY8B,EAAM9B,UAClB0G,EAAY5E,EAAM4E,UAClBC,EAAU7E,EAAM6E,QAChB7Q,EAAWgM,EAAMhM,SACjB8Q,EAAkB9E,EAAM8E,gBACxBC,EAAW/E,EAAM+E,SACjBC,EAAehF,EAAMgF,aAErBC,GAAyB,IAAjBD,EAxBd,SAA2BtF,GACzB,IAAImB,EAAInB,EAAKmB,EACTC,EAAIpB,EAAKoB,EAEToE,EADMld,OACImd,kBAAoB,EAClC,MAAO,CACLtE,EAAGyB,GAAMA,GAAMzB,EAAIqE,GAAOA,IAAQ,EAClCpE,EAAGwB,GAAMA,GAAMxB,EAAIoE,GAAOA,IAAQ,GAiBAE,CAAkBP,GAAmC,mBAAjBG,EAA8BA,EAAaH,GAAWA,EAC1HQ,EAAUJ,EAAMpE,EAChBA,OAAgB,IAAZwE,EAAqB,EAAIA,EAC7BC,EAAUL,EAAMnE,EAChBA,OAAgB,IAAZwE,EAAqB,EAAIA,EAE7BC,EAAOV,EAAQvE,eAAe,KAC9BkF,EAAOX,EAAQvE,eAAe,KAC9BmF,EAAQ3R,GACR4R,EAAQ9R,GACR+R,EAAM3d,OAEV,GAAI+c,EAAU,CACZ,IAAIrD,EAAeC,GAAgB9D,GAC/B+H,EAAa,eACbC,EAAY,cAEZnE,IAAiB1C,GAAUnB,IAGmB,WAA5ClX,GAFJ+a,EAAeJ,GAAmBzD,IAEC7J,UAAsC,aAAbA,IAC1D4R,EAAa,eACbC,EAAY,eAKhBnE,EAAeA,EAEXxD,IAActK,KAAQsK,IAAcpK,IAAQoK,IAAcV,IAAUoH,IAActL,MACpFoM,EAAQnI,GAERuD,GAAKY,EAAakE,GAAcjB,EAAW/D,OAC3CE,GAAKgE,EAAkB,GAAK,GAG1B5G,IAAcpK,KAASoK,IAActK,IAAOsK,IAAcX,IAAWqH,IAActL,MACrFmM,EAAQjI,GAERqD,GAAKa,EAAamE,GAAalB,EAAWhE,MAC1CE,GAAKiE,EAAkB,GAAK,GAIhC,IAKMgB,EALFC,EAAergB,OAAOoa,OAAO,CAC/B9L,SAAUA,GACT+Q,GAAYP,IAEf,OAAIM,EAGKpf,OAAOoa,OAAO,GAAIiG,IAAeD,EAAiB,IAAmBJ,GAASF,EAAO,IAAM,GAAIM,EAAeL,GAASF,EAAO,IAAM,GAAIO,EAAe9D,WAAa2D,EAAIR,kBAAoB,IAAM,EAAI,aAAetE,EAAI,OAASC,EAAI,MAAQ,eAAiBD,EAAI,OAASC,EAAI,SAAUgF,IAG5RpgB,OAAOoa,OAAO,GAAIiG,IAAerB,EAAkB,IAAoBgB,GAASF,EAAO1E,EAAI,KAAO,GAAI4D,EAAgBe,GAASF,EAAO1E,EAAI,KAAO,GAAI6D,EAAgB1C,UAAY,GAAI0C,IAuD9L,MAAAsB,GAAe,CACbvd,KAAM,gBACN+W,SAAS,EACTC,MAAO,cACP7W,GAxDF,SAAuBqd,GACrB,IAAItG,EAAQsG,EAAMtG,MACdO,EAAU+F,EAAM/F,QAChBgG,EAAwBhG,EAAQ4E,gBAChCA,OAA4C,IAA1BoB,GAA0CA,EAC5DC,EAAoBjG,EAAQ6E,SAC5BA,OAAiC,IAAtBoB,GAAsCA,EACjDC,EAAwBlG,EAAQ8E,aAChCA,OAAyC,IAA1BoB,GAA0CA,EAYzDL,EAAe,CACjB7H,UAAWuC,GAAiBd,EAAMzB,WAClC0G,UAAWL,GAAa5E,EAAMzB,WAC9BL,OAAQ8B,EAAMC,SAAS/B,OACvB8G,WAAYhF,EAAM2D,MAAMzF,OACxBiH,gBAAiBA,GAGsB,MAArCnF,EAAMuD,cAAcD,gBACtBtD,EAAME,OAAOhC,OAASnY,OAAOoa,OAAO,GAAIH,EAAME,OAAOhC,OAAQ4G,GAAY/e,OAAOoa,OAAO,GAAIiG,EAAc,CACvGlB,QAASlF,EAAMuD,cAAcD,cAC7BjP,SAAU2L,EAAMO,QAAQC,SACxB4E,SAAUA,EACVC,aAAcA,OAIe,MAA7BrF,EAAMuD,cAAc7C,QACtBV,EAAME,OAAOQ,MAAQ3a,OAAOoa,OAAO,GAAIH,EAAME,OAAOQ,MAAOoE,GAAY/e,OAAOoa,OAAO,GAAIiG,EAAc,CACrGlB,QAASlF,EAAMuD,cAAc7C,MAC7BrM,SAAU,WACV+Q,UAAU,EACVC,aAAcA,OAIlBrF,EAAMxM,WAAW0K,OAASnY,OAAOoa,OAAO,GAAIH,EAAMxM,WAAW0K,OAAQ,CACnE,wBAAyB8B,EAAMzB,aAUjC/L,KAAM,IC1JR,IAAIkU,GAAU,CACZA,SAAS,GAsCX,MAAAC,GAAe,CACb7d,KAAM,iBACN+W,SAAS,EACTC,MAAO,QACP7W,GAAI,aACJmX,OAxCF,SAAgBL,GACd,IAAIC,EAAQD,EAAKC,MACb1P,EAAWyP,EAAKzP,SAChBiQ,EAAUR,EAAKQ,QACfqG,EAAkBrG,EAAQsG,OAC1BA,OAA6B,IAApBD,GAAoCA,EAC7CE,EAAkBvG,EAAQwG,OAC1BA,OAA6B,IAApBD,GAAoCA,EAC7Cze,EAASgX,GAAUW,EAAMC,SAAS/B,QAClC8I,EAAgB,GAAGtS,OAAOsL,EAAMgH,cAAc7I,UAAW6B,EAAMgH,cAAc9I,QAYjF,OAVI2I,GACFG,EAAc/gB,SAAQ,SAAUghB,GAC9BA,EAAa3d,iBAAiB,SAAUgH,EAAS4W,OAAQR,OAIzDK,GACF1e,EAAOiB,iBAAiB,SAAUgH,EAAS4W,OAAQR,IAG9C,WACDG,GACFG,EAAc/gB,SAAQ,SAAUghB,GAC9BA,EAAa1c,oBAAoB,SAAU+F,EAAS4W,OAAQR,OAI5DK,GACF1e,EAAOkC,oBAAoB,SAAU+F,EAAS4W,OAAQR,MAY1DlU,KAAM,IC/CR,IAAI2U,GAAO,CACThT,KAAM,QACN0J,MAAO,OACPD,OAAQ,MACR3J,IAAK,UAEQ,SAASmT,GAAqB7I,GAC3C,OAAOA,EAAU9Q,QAAQ,0BAA0B,SAAU4Z,GAC3D,OAAOF,GAAKE,MCRhB,IAAIF,GAAO,CACT5N,MAAO,MACPI,IAAK,SAEQ,SAAS2N,GAA8B/I,GACpD,OAAOA,EAAU9Q,QAAQ,cAAc,SAAU4Z,GAC/C,OAAOF,GAAKE,MCLD,SAASE,GAAgBjI,GACtC,IAAI0G,EAAM3G,GAAUC,GAGpB,MAAO,CACLkI,WAHexB,EAAI5R,YAInBqT,UAHczB,EAAI9R,aCDP,SAASwT,GAAoBpjB,GAQ1C,OAAO0P,GAAsB2N,GAAmBrd,IAAU6P,KAAOoT,GAAgBjjB,GAASkjB,WCV7E,SAASG,GAAerjB,GAErC,IAAIsjB,EAAoB5gB,GAAiB1C,GACrCujB,EAAWD,EAAkBC,SAC7BC,EAAYF,EAAkBE,UAC9BC,EAAYH,EAAkBG,UAElC,MAAO,6BAA6BphB,KAAKkhB,EAAWE,EAAYD,GCJnD,SAASE,GAAgB1I,GACtC,MAAI,CAAC,OAAQ,OAAQ,aAAavU,QAAQoU,GAAYG,KAAU,EAEvDA,EAAKC,cAAcjX,KAGxBmX,GAAcH,IAASqI,GAAerI,GACjCA,EAGF0I,GAAgBpG,GAActC,ICHxB,SAAS2I,GAAkB3jB,EAASoG,GACjD,IAAIwd,OAES,IAATxd,IACFA,EAAO,IAGT,IAAIuc,EAAee,GAAgB1jB,GAC/B6jB,EAASlB,KAAqE,OAAlDiB,EAAwB5jB,EAAQib,oBAAyB,EAAS2I,EAAsB5f,MACpH0d,EAAM3G,GAAU4H,GAChB3c,EAAS6d,EAAS,CAACnC,GAAKtR,OAAOsR,EAAIoC,gBAAkB,GAAIT,GAAeV,GAAgBA,EAAe,IAAMA,EAC7GoB,EAAc3d,EAAKgK,OAAOpK,GAC9B,OAAO6d,EAASE,EAChBA,EAAY3T,OAAOuT,GAAkBrG,GAActX,KCxBtC,SAASge,GAAiBvU,GACvC,OAAOhO,OAAOoa,OAAO,GAAIpM,EAAM,CAC7BI,KAAMJ,EAAKmN,EACXjN,IAAKF,EAAKoN,EACVtD,MAAO9J,EAAKmN,EAAInN,EAAKiN,MACrBpD,OAAQ7J,EAAKoN,EAAIpN,EAAKkN,SCuB1B,SAASsH,GAA2BjkB,EAASkkB,GAC3C,OAAOA,IAAmBvK,GAAWqK,GC1BxB,SAAyBhkB,GACtC,IAAI0hB,EAAM3G,GAAU/a,GAChBmkB,EAAO9G,GAAmBrd,GAC1B8jB,EAAiBpC,EAAIoC,eACrBpH,EAAQyH,EAAKpE,YACbpD,EAASwH,EAAKrE,aACdlD,EAAI,EACJC,EAAI,EAuBR,OAjBIiH,IACFpH,EAAQoH,EAAepH,MACvBC,EAASmH,EAAenH,OASnB,iCAAiCta,KAAKkR,UAAUqK,aACnDhB,EAAIkH,EAAe7T,WACnB4M,EAAIiH,EAAe9T,YAIhB,CACL0M,MAAOA,EACPC,OAAQA,EACRC,EAAGA,EAAIwG,GAAoBpjB,GAC3B6c,EAAGA,GDRiDuH,CAAgBpkB,IAAYmb,GAAc+I,GAdlG,SAAoClkB,GAClC,IAAIyP,EAAOC,GAAsB1P,GASjC,OARAyP,EAAKE,IAAMF,EAAKE,IAAM3P,EAAQqkB,UAC9B5U,EAAKI,KAAOJ,EAAKI,KAAO7P,EAAQskB,WAChC7U,EAAK6J,OAAS7J,EAAKE,IAAM3P,EAAQ8f,aACjCrQ,EAAK8J,MAAQ9J,EAAKI,KAAO7P,EAAQ+f,YACjCtQ,EAAKiN,MAAQ1c,EAAQ+f,YACrBtQ,EAAKkN,OAAS3c,EAAQ8f,aACtBrQ,EAAKmN,EAAInN,EAAKI,KACdJ,EAAKoN,EAAIpN,EAAKE,IACPF,EAI2G8U,CAA2BL,GAAkBF,GEtBlJ,SAAyBhkB,GACtC,IAAI4jB,EAEAO,EAAO9G,GAAmBrd,GAC1BwkB,EAAYvB,GAAgBjjB,GAC5BgE,EAA0D,OAAlD4f,EAAwB5jB,EAAQib,oBAAyB,EAAS2I,EAAsB5f,KAChG0Y,EAAQ9V,GAAIud,EAAKM,YAAaN,EAAKpE,YAAa/b,EAAOA,EAAKygB,YAAc,EAAGzgB,EAAOA,EAAK+b,YAAc,GACvGpD,EAAS/V,GAAIud,EAAKO,aAAcP,EAAKrE,aAAc9b,EAAOA,EAAK0gB,aAAe,EAAG1gB,EAAOA,EAAK8b,aAAe,GAC5GlD,GAAK4H,EAAUtB,WAAaE,GAAoBpjB,GAChD6c,GAAK2H,EAAUrB,UAMnB,MAJiD,QAA7CzgB,GAAiBsB,GAAQmgB,GAAMvP,YACjCgI,GAAKhW,GAAIud,EAAKpE,YAAa/b,EAAOA,EAAK+b,YAAc,GAAKrD,GAGrD,CACLA,MAAOA,EACPC,OAAQA,EACRC,EAAGA,EACHC,EAAGA,GFG2K8H,CAAgBtH,GAAmBrd,KGzBtM,SAAS4kB,GAAenJ,GACrC,IAOImF,EAPA/G,EAAY4B,EAAK5B,UACjB7Z,EAAUyb,EAAKzb,QACfia,EAAYwB,EAAKxB,UACjBiF,EAAgBjF,EAAYuC,GAAiBvC,GAAa,KAC1D0G,EAAY1G,EAAYqG,GAAarG,GAAa,KAClD4K,EAAUhL,EAAU+C,EAAI/C,EAAU6C,MAAQ,EAAI1c,EAAQ0c,MAAQ,EAC9DoI,EAAUjL,EAAUgD,EAAIhD,EAAU8C,OAAS,EAAI3c,EAAQ2c,OAAS,EAGpE,OAAQuC,GACN,KAAKvP,GACHiR,EAAU,CACRhE,EAAGiI,EACHhI,EAAGhD,EAAUgD,EAAI7c,EAAQ2c,QAE3B,MAEF,KAAKrD,GACHsH,EAAU,CACRhE,EAAGiI,EACHhI,EAAGhD,EAAUgD,EAAIhD,EAAU8C,QAE7B,MAEF,KAAKpD,GACHqH,EAAU,CACRhE,EAAG/C,EAAU+C,EAAI/C,EAAU6C,MAC3BG,EAAGiI,GAEL,MAEF,KAAKjV,GACH+Q,EAAU,CACRhE,EAAG/C,EAAU+C,EAAI5c,EAAQ0c,MACzBG,EAAGiI,GAEL,MAEF,QACElE,EAAU,CACRhE,EAAG/C,EAAU+C,EACbC,EAAGhD,EAAUgD,GAInB,IAAIkI,EAAW7F,EAAgBd,GAAyBc,GAAiB,KAEzE,GAAgB,MAAZ6F,EAAkB,CACpB,IAAI9c,EAAmB,MAAb8c,EAAmB,SAAW,QAExC,OAAQpE,GACN,KAAK1L,GACH2L,EAAQmE,GAAYnE,EAAQmE,IAAalL,EAAU5R,GAAO,EAAIjI,EAAQiI,GAAO,GAC7E,MAEF,KAAKoN,GACHuL,EAAQmE,GAAYnE,EAAQmE,IAAalL,EAAU5R,GAAO,EAAIjI,EAAQiI,GAAO,IAOnF,OAAO2Y,EC1DM,SAASoE,GAAetJ,EAAOO,QAC5B,IAAZA,IACFA,EAAU,IAGZ,IAAIgJ,EAAWhJ,EACXiJ,EAAqBD,EAAShL,UAC9BA,OAAmC,IAAvBiL,EAAgCxJ,EAAMzB,UAAYiL,EAC9DC,EAAoBF,EAASG,SAC7BA,OAAiC,IAAtBD,EAA+BzL,GAAkByL,EAC5DE,EAAwBJ,EAASK,aACjCA,OAAyC,IAA1BD,EAAmC1L,GAAW0L,EAC7DE,EAAwBN,EAASO,eACjCA,OAA2C,IAA1BD,EAAmC3L,GAAS2L,EAC7DE,EAAuBR,EAASS,YAChCA,OAAuC,IAAzBD,GAA0CA,EACxDE,EAAmBV,EAAS7F,QAC5BA,OAA+B,IAArBuG,EAA8B,EAAIA,EAC5CjH,EAAgBD,GAAsC,iBAAZW,EAAuBA,EAAUT,GAAgBS,EAAS3F,KACpGmM,EAAaJ,IAAmB5L,GAASC,GAAYD,GACrD8G,EAAahF,EAAM2D,MAAMzF,OACzB5Z,EAAU0b,EAAMC,SAAS+J,EAAcE,EAAaJ,GACpDK,EJoBS,SAAyB7lB,EAASolB,EAAUE,GACzD,IAAIQ,EAAmC,oBAAbV,EAlB5B,SAA4BplB,GAC1B,IAAI0Z,EAAkBiK,GAAkBrG,GAActd,IAElD+lB,EADoB,CAAC,WAAY,SAAStf,QAAQ/D,GAAiB1C,GAAS+P,WAAa,GACnDoL,GAAcnb,GAAW0d,GAAgB1d,GAAWA,EAE9F,OAAKe,GAAUglB,GAKRrM,EAAgBtK,QAAO,SAAU8U,GACtC,OAAOnjB,GAAUmjB,IAAmBlhB,GAASkhB,EAAgB6B,IAAmD,SAAhClL,GAAYqJ,MALrF,GAYkD8B,CAAmBhmB,GAAW,GAAGoQ,OAAOgV,GAC/F1L,EAAkB,GAAGtJ,OAAO0V,EAAqB,CAACR,IAClDW,EAAsBvM,EAAgB,GACtCwM,EAAexM,EAAgBK,QAAO,SAAUoM,EAASjC,GAC3D,IAAIzU,EAAOwU,GAA2BjkB,EAASkkB,GAK/C,OAJAiC,EAAQxW,IAAM/I,GAAI6I,EAAKE,IAAKwW,EAAQxW,KACpCwW,EAAQ5M,MAAQ1S,GAAI4I,EAAK8J,MAAO4M,EAAQ5M,OACxC4M,EAAQ7M,OAASzS,GAAI4I,EAAK6J,OAAQ6M,EAAQ7M,QAC1C6M,EAAQtW,KAAOjJ,GAAI6I,EAAKI,KAAMsW,EAAQtW,MAC/BsW,IACNlC,GAA2BjkB,EAASimB,IAKvC,OAJAC,EAAaxJ,MAAQwJ,EAAa3M,MAAQ2M,EAAarW,KACvDqW,EAAavJ,OAASuJ,EAAa5M,OAAS4M,EAAavW,IACzDuW,EAAatJ,EAAIsJ,EAAarW,KAC9BqW,EAAarJ,EAAIqJ,EAAavW,IACvBuW,EIpCkBE,CAAgBrlB,GAAUf,GAAWA,EAAUA,EAAQqmB,gBAAkBhJ,GAAmB3B,EAAMC,SAAS/B,QAASwL,EAAUE,GACnJgB,EAAsB5W,GAAsBgM,EAAMC,SAAS9B,WAC3DmF,EAAgB4F,GAAe,CACjC/K,UAAWyM,EACXtmB,QAAS0gB,EACTxE,SAAU,WACVjC,UAAWA,IAETsM,EAAmBvC,GAAiBviB,OAAOoa,OAAO,GAAI6E,EAAY1B,IAClEwH,EAAoBhB,IAAmB5L,GAAS2M,EAAmBD,EAGnEG,EAAkB,CACpB9W,IAAKkW,EAAmBlW,IAAM6W,EAAkB7W,IAAM+O,EAAc/O,IACpE2J,OAAQkN,EAAkBlN,OAASuM,EAAmBvM,OAASoF,EAAcpF,OAC7EzJ,KAAMgW,EAAmBhW,KAAO2W,EAAkB3W,KAAO6O,EAAc7O,KACvE0J,MAAOiN,EAAkBjN,MAAQsM,EAAmBtM,MAAQmF,EAAcnF,OAExEmN,EAAahL,EAAMuD,cAAczP,OAErC,GAAIgW,IAAmB5L,IAAU8M,EAAY,CAC3C,IAAIlX,EAASkX,EAAWzM,GACxBxY,OAAOC,KAAK+kB,GAAiB9kB,SAAQ,SAAU6J,GAC7C,IAAImb,EAAW,CAACpN,GAAOD,IAAQ7S,QAAQ+E,IAAQ,EAAI,GAAK,EACpD2T,EAAO,CAACxP,GAAK2J,IAAQ7S,QAAQ+E,IAAQ,EAAI,IAAM,IACnDib,EAAgBjb,IAAQgE,EAAO2P,GAAQwH,KAI3C,OAAOF,ECzDM,SAASG,GAAqBlL,EAAOO,QAClC,IAAZA,IACFA,EAAU,IAGZ,IAAIgJ,EAAWhJ,EACXhC,EAAYgL,EAAShL,UACrBmL,EAAWH,EAASG,SACpBE,EAAeL,EAASK,aACxBlG,EAAU6F,EAAS7F,QACnByH,EAAiB5B,EAAS4B,eAC1BC,EAAwB7B,EAAS8B,sBACjCA,OAAkD,IAA1BD,EAAmCE,GAAgBF,EAC3EnG,EAAYL,GAAarG,GACzBC,EAAayG,EAAYkG,EAAiB/M,GAAsBA,GAAoB1K,QAAO,SAAU6K,GACvG,OAAOqG,GAAarG,KAAe0G,KAChClH,GACDwN,EAAoB/M,EAAW9K,QAAO,SAAU6K,GAClD,OAAO8M,EAAsBtgB,QAAQwT,IAAc,KAGpB,IAA7BgN,EAAkB7lB,SACpB6lB,EAAoB/M,GAQtB,IAAIgN,EAAYD,EAAkBlN,QAAO,SAAUC,EAAKC,GAOtD,OANAD,EAAIC,GAAa+K,GAAetJ,EAAO,CACrCzB,UAAWA,EACXmL,SAAUA,EACVE,aAAcA,EACdlG,QAASA,IACR5C,GAAiBvC,IACbD,IACN,IACH,OAAOvY,OAAOC,KAAKwlB,GAAWC,MAAK,SAAUC,EAAGC,GAC9C,OAAOH,EAAUE,GAAKF,EAAUG,MC6FpC,MAAAC,GAAe,CACb9iB,KAAM,OACN+W,SAAS,EACTC,MAAO,OACP7W,GA5HF,SAAc8W,GACZ,IAAIC,EAAQD,EAAKC,MACbO,EAAUR,EAAKQ,QACfzX,EAAOiX,EAAKjX,KAEhB,IAAIkX,EAAMuD,cAAcza,GAAM+iB,MAA9B,CAoCA,IAhCA,IAAIC,EAAoBvL,EAAQ8I,SAC5B0C,OAAsC,IAAtBD,GAAsCA,EACtDE,EAAmBzL,EAAQ0L,QAC3BC,OAAoC,IAArBF,GAAqCA,EACpDG,EAA8B5L,EAAQ6L,mBACtC1I,EAAUnD,EAAQmD,QAClBgG,EAAWnJ,EAAQmJ,SACnBE,EAAerJ,EAAQqJ,aACvBI,EAAczJ,EAAQyJ,YACtBqC,EAAwB9L,EAAQ4K,eAChCA,OAA2C,IAA1BkB,GAA0CA,EAC3DhB,EAAwB9K,EAAQ8K,sBAChCiB,EAAqBtM,EAAMO,QAAQhC,UACnCiF,EAAgB1C,GAAiBwL,GAEjCF,EAAqBD,IADH3I,IAAkB8I,GACqCnB,EAjC/E,SAAuC5M,GACrC,GAAIuC,GAAiBvC,KAAeT,GAClC,MAAO,GAGT,IAAIyO,EAAoBnF,GAAqB7I,GAC7C,MAAO,CAAC+I,GAA8B/I,GAAYgO,EAAmBjF,GAA8BiF,IA2BwCC,CAA8BF,GAA3E,CAAClF,GAAqBkF,KAChH9N,EAAa,CAAC8N,GAAoB5X,OAAO0X,GAAoB/N,QAAO,SAAUC,EAAKC,GACrF,OAAOD,EAAI5J,OAAOoM,GAAiBvC,KAAeT,GAAOoN,GAAqBlL,EAAO,CACnFzB,UAAWA,EACXmL,SAAUA,EACVE,aAAcA,EACdlG,QAASA,EACTyH,eAAgBA,EAChBE,sBAAuBA,IACpB9M,KACJ,IACCkO,EAAgBzM,EAAM2D,MAAMxF,UAC5B6G,EAAahF,EAAM2D,MAAMzF,OACzBwO,EAAY,IAAIvc,IAChBwc,GAAqB,EACrBC,EAAwBpO,EAAW,GAE9BlS,EAAI,EAAGA,EAAIkS,EAAW9Y,OAAQ4G,IAAK,CAC1C,IAAIiS,EAAYC,EAAWlS,GAEvBugB,EAAiB/L,GAAiBvC,GAElCuO,EAAmBlI,GAAarG,KAAehF,GAC/CwT,EAAa,CAAC9Y,GAAK2J,IAAQ7S,QAAQ8hB,IAAmB,EACtDtgB,EAAMwgB,EAAa,QAAU,SAC7BlF,EAAWyB,GAAetJ,EAAO,CACnCzB,UAAWA,EACXmL,SAAUA,EACVE,aAAcA,EACdI,YAAaA,EACbtG,QAASA,IAEPsJ,EAAoBD,EAAaD,EAAmBjP,GAAQ1J,GAAO2Y,EAAmBlP,GAAS3J,GAE/FwY,EAAclgB,GAAOyY,EAAWzY,KAClCygB,EAAoB5F,GAAqB4F,IAG3C,IAAIC,EAAmB7F,GAAqB4F,GACxCE,EAAS,GAUb,GARInB,GACFmB,EAAO3jB,KAAKse,EAASgF,IAAmB,GAGtCX,GACFgB,EAAO3jB,KAAKse,EAASmF,IAAsB,EAAGnF,EAASoF,IAAqB,GAG1EC,EAAOC,OAAM,SAAUC,GACzB,OAAOA,KACL,CACFR,EAAwBrO,EACxBoO,GAAqB,EACrB,MAGFD,EAAUrc,IAAIkO,EAAW2O,GAG3B,GAAIP,EAqBF,IAnBA,IAEIU,EAAQ,SAAeC,GACzB,IAAIC,EAAmB/O,EAAW/J,MAAK,SAAU8J,GAC/C,IAAI2O,EAASR,EAAU1c,IAAIuO,GAE3B,GAAI2O,EACF,OAAOA,EAAOre,MAAM,EAAGye,GAAIH,OAAM,SAAUC,GACzC,OAAOA,QAKb,GAAIG,EAEF,OADAX,EAAwBW,EACjB,SAIFD,EAnBYnC,EAAiB,EAAI,EAmBZmC,EAAK,GAGpB,UAFFD,EAAMC,GADmBA,KAOpCtN,EAAMzB,YAAcqO,IACtB5M,EAAMuD,cAAcza,GAAM+iB,OAAQ,EAClC7L,EAAMzB,UAAYqO,EAClB5M,EAAMwN,OAAQ,KAUhB7I,iBAAkB,CAAC,UACnBnS,KAAM,CACJqZ,OAAO,IC7IX,SAAS4B,GAAe5F,EAAU9T,EAAM2Z,GAQtC,YAPyB,IAArBA,IACFA,EAAmB,CACjBxM,EAAG,EACHC,EAAG,IAIA,CACLlN,IAAK4T,EAAS5T,IAAMF,EAAKkN,OAASyM,EAAiBvM,EACnDtD,MAAOgK,EAAShK,MAAQ9J,EAAKiN,MAAQ0M,EAAiBxM,EACtDtD,OAAQiK,EAASjK,OAAS7J,EAAKkN,OAASyM,EAAiBvM,EACzDhN,KAAM0T,EAAS1T,KAAOJ,EAAKiN,MAAQ0M,EAAiBxM,GAIxD,SAASyM,GAAsB9F,GAC7B,MAAO,CAAC5T,GAAK4J,GAAOD,GAAQzJ,IAAMyZ,MAAK,SAAUC,GAC/C,OAAOhG,EAASgG,IAAS,KAiC7B,MAAAC,GAAe,CACbhlB,KAAM,OACN+W,SAAS,EACTC,MAAO,OACP6E,iBAAkB,CAAC,mBACnB1b,GAlCF,SAAc8W,GACZ,IAAIC,EAAQD,EAAKC,MACblX,EAAOiX,EAAKjX,KACZ2jB,EAAgBzM,EAAM2D,MAAMxF,UAC5B6G,EAAahF,EAAM2D,MAAMzF,OACzBwP,EAAmB1N,EAAMuD,cAAcwK,gBACvCC,EAAoB1E,GAAetJ,EAAO,CAC5C8J,eAAgB,cAEdmE,EAAoB3E,GAAetJ,EAAO,CAC5CgK,aAAa,IAEXkE,EAA2BT,GAAeO,EAAmBvB,GAC7D0B,EAAsBV,GAAeQ,EAAmBjJ,EAAY0I,GACpEU,EAAoBT,GAAsBO,GAC1CG,EAAmBV,GAAsBQ,GAC7CnO,EAAMuD,cAAcza,GAAQ,CAC1BolB,yBAA0BA,EAC1BC,oBAAqBA,EACrBC,kBAAmBA,EACnBC,iBAAkBA,GAEpBrO,EAAMxM,WAAW0K,OAASnY,OAAOoa,OAAO,GAAIH,EAAMxM,WAAW0K,OAAQ,CACnE,+BAAgCkQ,EAChC,sBAAuBC,MCH3BC,GAAe,CACbxlB,KAAM,SACN+W,SAAS,EACTC,MAAO,OACPe,SAAU,CAAC,iBACX5X,GA5BF,SAAgBoX,GACd,IAAIL,EAAQK,EAAML,MACdO,EAAUF,EAAME,QAChBzX,EAAOuX,EAAMvX,KACbylB,EAAkBhO,EAAQzM,OAC1BA,OAA6B,IAApBya,EAA6B,CAAC,EAAG,GAAKA,EAC/C/b,EAAOgM,GAAWH,QAAO,SAAUC,EAAKC,GAE1C,OADAD,EAAIC,GA5BD,SAAiCA,EAAWoF,EAAO7P,GACxD,IAAI0P,EAAgB1C,GAAiBvC,GACjCiQ,EAAiB,CAACra,GAAMF,IAAKlJ,QAAQyY,IAAkB,GAAK,EAAI,EAEhEzD,EAAyB,mBAAXjM,EAAwBA,EAAO/N,OAAOoa,OAAO,GAAIwD,EAAO,CACxEpF,UAAWA,KACPzK,EACF2a,EAAW1O,EAAK,GAChB2O,EAAW3O,EAAK,GAIpB,OAFA0O,EAAWA,GAAY,EACvBC,GAAYA,GAAY,GAAKF,EACtB,CAACra,GAAM0J,IAAO9S,QAAQyY,IAAkB,EAAI,CACjDtC,EAAGwN,EACHvN,EAAGsN,GACD,CACFvN,EAAGuN,EACHtN,EAAGuN,GAWcC,CAAwBpQ,EAAWyB,EAAM2D,MAAO7P,GAC1DwK,IACN,IACCsQ,EAAwBpc,EAAKwN,EAAMzB,WACnC2C,EAAI0N,EAAsB1N,EAC1BC,EAAIyN,EAAsBzN,EAEW,MAArCnB,EAAMuD,cAAcD,gBACtBtD,EAAMuD,cAAcD,cAAcpC,GAAKA,EACvClB,EAAMuD,cAAcD,cAAcnC,GAAKA,GAGzCnB,EAAMuD,cAAcza,GAAQ0J,ICxB9Bqc,GAAe,CACb/lB,KAAM,gBACN+W,SAAS,EACTC,MAAO,OACP7W,GApBF,SAAuB8W,GACrB,IAAIC,EAAQD,EAAKC,MACblX,EAAOiX,EAAKjX,KAKhBkX,EAAMuD,cAAcza,GAAQogB,GAAe,CACzC/K,UAAW6B,EAAM2D,MAAMxF,UACvB7Z,QAAS0b,EAAM2D,MAAMzF,OACrBsC,SAAU,WACVjC,UAAWyB,EAAMzB,aAUnB/L,KAAM,IC6FRsc,GAAe,CACbhmB,KAAM,kBACN+W,SAAS,EACTC,MAAO,OACP7W,GA5GF,SAAyB8W,GACvB,IAAIC,EAAQD,EAAKC,MACbO,EAAUR,EAAKQ,QACfzX,EAAOiX,EAAKjX,KACZgjB,EAAoBvL,EAAQ8I,SAC5B0C,OAAsC,IAAtBD,GAAsCA,EACtDE,EAAmBzL,EAAQ0L,QAC3BC,OAAoC,IAArBF,GAAsCA,EACrDtC,EAAWnJ,EAAQmJ,SACnBE,EAAerJ,EAAQqJ,aACvBI,EAAczJ,EAAQyJ,YACtBtG,EAAUnD,EAAQmD,QAClBqL,EAAkBxO,EAAQyO,OAC1BA,OAA6B,IAApBD,GAAoCA,EAC7CE,EAAwB1O,EAAQ2O,aAChCA,OAAyC,IAA1BD,EAAmC,EAAIA,EACtDpH,EAAWyB,GAAetJ,EAAO,CACnC0J,SAAUA,EACVE,aAAcA,EACdlG,QAASA,EACTsG,YAAaA,IAEXxG,EAAgB1C,GAAiBd,EAAMzB,WACvC0G,EAAYL,GAAa5E,EAAMzB,WAC/B4Q,GAAmBlK,EACnBoE,EAAW3G,GAAyBc,GACpCyI,ECrCY,MDqCS5C,ECrCH,IAAM,IDsCxB/F,EAAgBtD,EAAMuD,cAAcD,cACpCmJ,EAAgBzM,EAAM2D,MAAMxF,UAC5B6G,EAAahF,EAAM2D,MAAMzF,OACzBkR,EAA4C,mBAAjBF,EAA8BA,EAAanpB,OAAOoa,OAAO,GAAIH,EAAM2D,MAAO,CACvGpF,UAAWyB,EAAMzB,aACb2Q,EACF1c,EAAO,CACT0O,EAAG,EACHC,EAAG,GAGL,GAAKmC,EAAL,CAIA,GAAIyI,GAAiBG,EAAc,CACjC,IAAImD,EAAwB,MAAbhG,EAAmBpV,GAAME,GACpCmb,EAAuB,MAAbjG,EAAmBzL,GAASC,GACtCtR,EAAmB,MAAb8c,EAAmB,SAAW,QACpCvV,EAASwP,EAAc+F,GACvBle,EAAMmY,EAAc+F,GAAYxB,EAASwH,GACzCnkB,EAAMoY,EAAc+F,GAAYxB,EAASyH,GACzCC,EAAWP,GAAUhK,EAAWzY,GAAO,EAAI,EAC3CijB,EAASvK,IAAc1L,GAAQkT,EAAclgB,GAAOyY,EAAWzY,GAC/DkjB,EAASxK,IAAc1L,IAASyL,EAAWzY,IAAQkgB,EAAclgB,GAGjE8W,EAAerD,EAAMC,SAASS,MAC9BmD,EAAYmL,GAAU3L,EAAejC,GAAciC,GAAgB,CACrErC,MAAO,EACPC,OAAQ,GAENyO,EAAqB1P,EAAMuD,cAAc,oBAAsBvD,EAAMuD,cAAc,oBAAoBG,QxBtEtG,CACLzP,IAAK,EACL4J,MAAO,EACPD,OAAQ,EACRzJ,KAAM,GwBmEFwb,EAAkBD,EAAmBL,GACrCO,EAAkBF,EAAmBJ,GAMrCO,EAAWjN,GAAO,EAAG6J,EAAclgB,GAAMsX,EAAUtX,IACnDujB,EAAYX,EAAkB1C,EAAclgB,GAAO,EAAIgjB,EAAWM,EAAWF,EAAkBP,EAAoBI,EAASK,EAAWF,EAAkBP,EACzJW,EAAYZ,GAAmB1C,EAAclgB,GAAO,EAAIgjB,EAAWM,EAAWD,EAAkBR,EAAoBK,EAASI,EAAWD,EAAkBR,EAC1JlL,EAAoBlE,EAAMC,SAASS,OAASsB,GAAgBhC,EAAMC,SAASS,OAC3EsP,EAAe9L,EAAiC,MAAbmF,EAAmBnF,EAAkByE,WAAa,EAAIzE,EAAkB0E,YAAc,EAAI,EAC7HqH,EAAsBjQ,EAAMuD,cAAczP,OAASkM,EAAMuD,cAAczP,OAAOkM,EAAMzB,WAAW8K,GAAY,EAC3G6G,EAAY5M,EAAc+F,GAAYyG,EAAYG,EAAsBD,EACxEG,EAAY7M,EAAc+F,GAAY0G,EAAYE,EAEtD,GAAIlE,EAAe,CACjB,IAAIqE,EAAkBxN,GAAOoM,EAASlM,GAAQ3X,EAAK+kB,GAAa/kB,EAAK2I,EAAQkb,EAASnM,GAAQ3X,EAAKilB,GAAajlB,GAChHoY,EAAc+F,GAAY+G,EAC1B5d,EAAK6W,GAAY+G,EAAkBtc,EAGrC,GAAIoY,EAAc,CAChB,IAAImE,EAAyB,MAAbhH,EAAmBpV,GAAME,GAErCmc,EAAwB,MAAbjH,EAAmBzL,GAASC,GAEvC0S,EAAUjN,EAAc2I,GAExBuE,EAAOD,EAAU1I,EAASwI,GAE1BI,GAAOF,EAAU1I,EAASyI,GAE1BI,GAAmB9N,GAAOoM,EAASlM,GAAQ0N,EAAMN,GAAaM,EAAMD,EAASvB,EAASnM,GAAQ4N,GAAMN,GAAaM,IAErHnN,EAAc2I,GAAWyE,GACzBle,EAAKyZ,GAAWyE,GAAmBH,GAIvCvQ,EAAMuD,cAAcza,GAAQ0J,IAS5BmS,iBAAkB,CAAC,WExGN,SAASgM,GAAiBC,EAAyB7O,EAAc8O,QAC9D,IAAZA,IACFA,GAAU,GAGZ,IAAIC,EAA0BrR,GAAcsC,GACjBtC,GAAcsC,IAf3C,SAAyBzd,GACvB,IAAIyP,EAAOzP,EAAQ0P,wBACND,EAAKiN,MAAQ1c,EAAQgd,YACrBvN,EAAKkN,OAAS3c,EAAQ4D,aAYuB6oB,CAAgBhP,GAC1E,ICpBoCzC,ECJOhb,EFwBvCoD,EAAkBia,GAAmBI,GACrChO,EAAOC,GAAsB4c,GAC7B/J,EAAS,CACXW,WAAY,EACZC,UAAW,GAETvC,EAAU,CACZhE,EAAG,EACHC,EAAG,GAkBL,OAfI2P,IAA4BA,IAA4BD,MACxB,SAA9B1R,GAAY4C,IAChB4F,GAAejgB,MACbmf,GClCgCvH,EDkCTyC,KCjCd1C,GAAUC,IAAUG,GAAcH,GCJxC,CACLkI,YAFyCljB,EDQbgb,GCNRkI,WACpBC,UAAWnjB,EAAQmjB,WDGZF,GAAgBjI,IDmCnBG,GAAcsC,KAChBmD,EAAUlR,GAAsB+N,IACxBb,GAAKa,EAAa6G,WAC1B1D,EAAQ/D,GAAKY,EAAa4G,WACjBjhB,IACTwd,EAAQhE,EAAIwG,GAAoBhgB,KAI7B,CACLwZ,EAAGnN,EAAKI,KAAO0S,EAAOW,WAAatC,EAAQhE,EAC3CC,EAAGpN,EAAKE,IAAM4S,EAAOY,UAAYvC,EAAQ/D,EACzCH,MAAOjN,EAAKiN,MACZC,OAAQlN,EAAKkN,QGpDjB,SAASnI,GAAMkY,GACb,IAAItb,EAAM,IAAIvF,IACV8gB,EAAU,IAAInlB,IACdolB,EAAS,GAKb,SAASzF,EAAK0F,GACZF,EAAQnX,IAAIqX,EAASroB,MACN,GAAG4L,OAAOyc,EAAStQ,UAAY,GAAIsQ,EAASxM,kBAAoB,IACtE1e,SAAQ,SAAUmrB,GACzB,IAAKH,EAAQjkB,IAAIokB,GAAM,CACrB,IAAIC,EAAc3b,EAAI1F,IAAIohB,GAEtBC,GACF5F,EAAK4F,OAIXH,EAAO3nB,KAAK4nB,GASd,OAzBAH,EAAU/qB,SAAQ,SAAUkrB,GAC1Bzb,EAAIrF,IAAI8gB,EAASroB,KAAMqoB,MAkBzBH,EAAU/qB,SAAQ,SAAUkrB,GACrBF,EAAQjkB,IAAImkB,EAASroB,OAExB2iB,EAAK0F,MAGFD,ECfT,IAAII,GAAkB,CACpB/S,UAAW,SACXyS,UAAW,GACXxQ,SAAU,YAGZ,SAAS+Q,KACP,IAAK,IAAIC,EAAOC,UAAU/rB,OAAQsJ,EAAO,IAAI2B,MAAM6gB,GAAOE,EAAO,EAAGA,EAAOF,EAAME,IAC/E1iB,EAAK0iB,GAAQD,UAAUC,GAGzB,OAAQ1iB,EAAK4e,MAAK,SAAUtpB,GAC1B,QAASA,GAAoD,mBAAlCA,EAAQ0P,0BAIhC,SAAS2d,GAAgBC,QACL,IAArBA,IACFA,EAAmB,IAGrB,IAAIC,EAAoBD,EACpBE,EAAwBD,EAAkBE,iBAC1CA,OAA6C,IAA1BD,EAAmC,GAAKA,EAC3DE,EAAyBH,EAAkBI,eAC3CA,OAA4C,IAA3BD,EAAoCV,GAAkBU,EAC3E,OAAO,SAAsB7T,EAAWD,EAAQqC,QAC9B,IAAZA,IACFA,EAAU0R,GAGZ,IC/C6BhpB,EAC3BipB,ED8CElS,EAAQ,CACVzB,UAAW,SACX4T,iBAAkB,GAClB5R,QAASxa,OAAOoa,OAAO,GAAImR,GAAiBW,GAC5C1O,cAAe,GACftD,SAAU,CACR9B,UAAWA,EACXD,OAAQA,GAEV1K,WAAY,GACZ0M,OAAQ,IAENkS,EAAmB,GACnBC,GAAc,EACd/hB,EAAW,CACb0P,MAAOA,EACPsS,WAAY,SAAoBC,GAC9B,IAAIhS,EAAsC,mBAArBgS,EAAkCA,EAAiBvS,EAAMO,SAAWgS,EACzFC,IACAxS,EAAMO,QAAUxa,OAAOoa,OAAO,GAAI8R,EAAgBjS,EAAMO,QAASA,GACjEP,EAAMgH,cAAgB,CACpB7I,UAAW9Y,GAAU8Y,GAAa8J,GAAkB9J,GAAaA,EAAUwM,eAAiB1C,GAAkB9J,EAAUwM,gBAAkB,GAC1IzM,OAAQ+J,GAAkB/J,IAI5B,IEzE4B8S,EAC9ByB,EFwEMN,EDvCG,SAAwBnB,GAErC,IAAImB,EAAmBrZ,GAAMkY,GAE7B,OAAO9R,GAAeb,QAAO,SAAUC,EAAKwB,GAC1C,OAAOxB,EAAI5J,OAAOyd,EAAiBze,QAAO,SAAUyd,GAClD,OAAOA,EAASrR,QAAUA,QAE3B,IC+B0B4S,EEzEK1B,EFyEsB,GAAGtc,OAAOqd,EAAkB/R,EAAMO,QAAQyQ,WExE9FyB,EAASzB,EAAU3S,QAAO,SAAUoU,EAAQE,GAC9C,IAAIC,EAAWH,EAAOE,EAAQ7pB,MAK9B,OAJA2pB,EAAOE,EAAQ7pB,MAAQ8pB,EAAW7sB,OAAOoa,OAAO,GAAIyS,EAAUD,EAAS,CACrEpS,QAASxa,OAAOoa,OAAO,GAAIyS,EAASrS,QAASoS,EAAQpS,SACrD/N,KAAMzM,OAAOoa,OAAO,GAAIyS,EAASpgB,KAAMmgB,EAAQngB,QAC5CmgB,EACEF,IACN,IAEI1sB,OAAOC,KAAKysB,GAAQ/c,KAAI,SAAU5F,GACvC,OAAO2iB,EAAO3iB,QFuGV,OAvCAkQ,EAAMmS,iBAAmBA,EAAiBze,QAAO,SAAUmf,GACzD,OAAOA,EAAEhT,WAqJbG,EAAMmS,iBAAiBlsB,SAAQ,SAAUqf,GACvC,IAAIxc,EAAOwc,EAAMxc,KACbgqB,EAAgBxN,EAAM/E,QACtBA,OAA4B,IAAlBuS,EAA2B,GAAKA,EAC1C1S,EAASkF,EAAMlF,OAEnB,GAAsB,mBAAXA,EAAuB,CAChC,IAAI2S,EAAY3S,EAAO,CACrBJ,MAAOA,EACPlX,KAAMA,EACNwH,SAAUA,EACViQ,QAASA,IAKX6R,EAAiB7oB,KAAKwpB,GAFT,kBA7HRziB,EAAS4W,UAOlB8L,YAAa,WACX,IAAIX,EAAJ,CAIA,IAAIY,EAAkBjT,EAAMC,SACxB9B,EAAY8U,EAAgB9U,UAC5BD,EAAS+U,EAAgB/U,OAG7B,GAAKqT,GAAiBpT,EAAWD,GAAjC,CASA8B,EAAM2D,MAAQ,CACZxF,UAAWwS,GAAiBxS,EAAW6D,GAAgB9D,GAAoC,UAA3B8B,EAAMO,QAAQC,UAC9EtC,OAAQkD,GAAclD,IAOxB8B,EAAMwN,OAAQ,EACdxN,EAAMzB,UAAYyB,EAAMO,QAAQhC,UAKhCyB,EAAMmS,iBAAiBlsB,SAAQ,SAAUkrB,GACvC,OAAOnR,EAAMuD,cAAc4N,EAASroB,MAAQ/C,OAAOoa,OAAO,GAAIgR,EAAS3e,SAIzE,IAAK,IAAI1H,EAAQ,EAAGA,EAAQkV,EAAMmS,iBAAiBzsB,OAAQoF,IAUzD,IAAoB,IAAhBkV,EAAMwN,MAAV,CAMA,IAAI0F,EAAwBlT,EAAMmS,iBAAiBrnB,GAC/C7B,EAAKiqB,EAAsBjqB,GAC3BkqB,EAAyBD,EAAsB3S,QAC/CgJ,OAAsC,IAA3B4J,EAAoC,GAAKA,EACpDrqB,EAAOoqB,EAAsBpqB,KAEf,mBAAPG,IACT+W,EAAQ/W,EAAG,CACT+W,MAAOA,EACPO,QAASgJ,EACTzgB,KAAMA,EACNwH,SAAUA,KACN0P,QAjBNA,EAAMwN,OAAQ,EACd1iB,GAAS,KAsBfoc,QClM2Bje,EDkMV,WACf,OAAO,IAAImqB,SAAQ,SAAUC,GAC3B/iB,EAAS0iB,cACTK,EAAQrT,OCnMT,WAUL,OATKkS,IACHA,EAAU,IAAIkB,SAAQ,SAAUC,GAC9BD,QAAQC,UAAUC,MAAK,WACrBpB,OAAUzf,EACV4gB,EAAQpqB,YAKPipB,ID4LLqB,QAAS,WACPf,IACAH,GAAc,IAIlB,IAAKd,GAAiBpT,EAAWD,GAK/B,OAAO5N,EAmCT,SAASkiB,IACPJ,EAAiBnsB,SAAQ,SAAUgD,GACjC,OAAOA,OAETmpB,EAAmB,GAGrB,OAvCA9hB,EAASgiB,WAAW/R,GAAS+S,MAAK,SAAUtT,IACrCqS,GAAe9R,EAAQiT,eAC1BjT,EAAQiT,cAAcxT,MAqCnB1P,GAGJ,IAAImjB,GAA4B9B,KG1PnC8B,GAA4B9B,GAAgB,CAC9CI,iBAFqB,CAACpL,GAAgBrD,GAAeoQ,GAAeC,MCMlEF,GAA4B9B,GAAgB,CAC9CI,iBAFqB,CAACpL,GAAgBrD,GAAeoQ,GAAeC,GAAa7f,GAAQ8f,GAAM7F,GAAiBrN,GAAO7D,0iBCsBnH9T,GAAO,WAKP8qB,GAAa,SACbC,GAAY,QAEZC,GAAe,UACfC,GAAiB,YAGjBC,GAAiB,IAAIvtB,OAAQ,4BAM7BwtB,GAAwB,6BACxBC,GAA0B,+BAG1BtY,GAAkB,OAMlBnJ,GAAuB,8BACvB0hB,GAAgB,iBAIhBC,GAAgB7rB,IAAU,UAAY,YACtC8rB,GAAmB9rB,IAAU,YAAc,UAC3C+rB,GAAmB/rB,IAAU,aAAe,eAC5CgsB,GAAsBhsB,IAAU,eAAiB,aACjDisB,GAAkBjsB,IAAU,aAAe,cAC3CksB,GAAiBlsB,IAAU,cAAgB,aAE3CqN,GAAU,CACd/B,OAAQ,CAAC,EAAG,GACZ4V,SAAU,kBACVvL,UAAW,SACXwW,QAAS,UACTC,aAAc,KACdC,WAAW,GAGPze,GAAc,CAClBtC,OAAQ,0BACR4V,SAAU,mBACVvL,UAAW,0BACXwW,QAAS,SACTC,aAAc,yBACdC,UAAW,oBASb,MAAMC,WAAiB/jB,EACrBC,YAAY1M,EAASuB,GACnBmR,MAAM1S,GAENgJ,KAAKynB,QAAU,KACfznB,KAAKmK,QAAUnK,KAAKoK,WAAW7R,GAC/ByH,KAAK0nB,MAAQ1nB,KAAK2nB,kBAClB3nB,KAAK4nB,UAAY5nB,KAAK6nB,gBAKbtf,qBACT,OAAOA,GAGEO,yBACT,OAAOA,GAGErN,kBACT,OAAOA,GAKT6J,SACE,OAAOtF,KAAKsP,WAAatP,KAAKuP,OAASvP,KAAKwP,OAG9CA,OACE,GAAI5V,EAAWoG,KAAK2D,WAAa3D,KAAKsP,SAAStP,KAAK0nB,OAClD,OAGF,MAAM5nB,EAAgB,CACpBA,cAAeE,KAAK2D,UAKtB,GAFkBrD,EAAamB,QAAQzB,KAAK2D,SAvF5B,mBAuFkD7D,GAEpDiC,iBACZ,OAGF,MAAMuM,EAASkZ,GAASM,qBAAqB9nB,KAAK2D,UAE9C3D,KAAK4nB,UACP/hB,EAAYC,iBAAiB9F,KAAK0nB,MAAO,SAAU,QAEnD1nB,KAAK+nB,cAAczZ,GAOjB,iBAAkB7W,SAAS2C,kBAC5BkU,EAAO1J,QA5Fc,gBA6FtB,GAAGwC,UAAU3P,SAASuD,KAAKwM,UACxB7O,SAAQqW,GAAQ1O,EAAaQ,GAAGkO,EAAM,YAAatU,KAGxDsF,KAAK2D,SAASqkB,QACdhoB,KAAK2D,SAAS4B,aAAa,iBAAiB,GAE5CvF,KAAK0nB,MAAM3tB,UAAUyS,IAAI+B,IACzBvO,KAAK2D,SAAS5J,UAAUyS,IAAI+B,IAC5BjO,EAAamB,QAAQzB,KAAK2D,SAnHT,oBAmHgC7D,GAGnDyP,OACE,GAAI3V,EAAWoG,KAAK2D,YAAc3D,KAAKsP,SAAStP,KAAK0nB,OACnD,OAGF,MAAM5nB,EAAgB,CACpBA,cAAeE,KAAK2D,UAGtB3D,KAAKioB,cAAcnoB,GAGrB+D,UACM7D,KAAKynB,SACPznB,KAAKynB,QAAQxB,UAGfvc,MAAM7F,UAGR+V,SACE5Z,KAAK4nB,UAAY5nB,KAAK6nB,gBAClB7nB,KAAKynB,SACPznB,KAAKynB,QAAQ7N,SAMjBqO,cAAcnoB,GACMQ,EAAamB,QAAQzB,KAAK2D,SAvJ5B,mBAuJkD7D,GACpDiC,mBAMV,iBAAkBtK,SAAS2C,iBAC7B,GAAGgN,UAAU3P,SAASuD,KAAKwM,UACxB7O,SAAQqW,GAAQ1O,EAAaC,IAAIyO,EAAM,YAAatU,KAGrDsF,KAAKynB,SACPznB,KAAKynB,QAAQxB,UAGfjmB,KAAK0nB,MAAM3tB,UAAUwJ,OAAOgL,IAC5BvO,KAAK2D,SAAS5J,UAAUwJ,OAAOgL,IAC/BvO,KAAK2D,SAAS4B,aAAa,gBAAiB,SAC5CM,EAAYE,oBAAoB/F,KAAK0nB,MAAO,UAC5CpnB,EAAamB,QAAQzB,KAAK2D,SA1KR,qBA0KgC7D,IAGpDsK,WAAW7R,GAST,GARAA,EAAS,IACJyH,KAAK0D,YAAY6E,WACjB1C,EAAYI,kBAAkBjG,KAAK2D,aACnCpL,GAGLF,EAAgBoD,GAAMlD,EAAQyH,KAAK0D,YAAYoF,aAEf,iBAArBvQ,EAAOsY,YAA2B9Y,EAAUQ,EAAOsY,YACV,mBAA3CtY,EAAOsY,UAAUnK,sBAGxB,MAAM,IAAIpN,UAAW,GAAEmC,GAAKlC,+GAG9B,OAAOhB,EAGTwvB,cAAczZ,GACZ,QAAsB,IAAX4Z,GACT,MAAM,IAAI5uB,UAAU,gEAGtB,IAAI6uB,EAAmBnoB,KAAK2D,SAEG,WAA3B3D,KAAKmK,QAAQ0G,UACfsX,EAAmB7Z,EACVvW,EAAUiI,KAAKmK,QAAQ0G,WAChCsX,EAAmBhwB,EAAW6H,KAAKmK,QAAQ0G,WACA,iBAA3B7Q,KAAKmK,QAAQ0G,YAC7BsX,EAAmBnoB,KAAKmK,QAAQ0G,WAGlC,MAAMyW,EAAetnB,KAAKooB,mBACpBC,EAAkBf,EAAa5D,UAAUvc,MAAK0c,GAA8B,gBAAlBA,EAASroB,OAA+C,IAArBqoB,EAAStR,UAE5GvS,KAAKynB,QAAUS,GAAoBC,EAAkBnoB,KAAK0nB,MAAOJ,GAE7De,GACFxiB,EAAYC,iBAAiB9F,KAAK0nB,MAAO,SAAU,UAIvDpY,SAAStY,EAAUgJ,KAAK2D,UACtB,OAAO3M,EAAQ+C,UAAUC,SAASuU,IAGpCoZ,kBACE,OAAOzgB,EAAec,KAAKhI,KAAK2D,SAAUmjB,IAAe,GAG3DwB,gBACE,MAAMC,EAAiBvoB,KAAK2D,SAASlJ,WAErC,GAAI8tB,EAAexuB,UAAUC,SA3NN,WA4NrB,OAAOmtB,GAGT,GAAIoB,EAAexuB,UAAUC,SA9NJ,aA+NvB,OAAOotB,GAIT,MAAMoB,EAAkF,QAA1E9uB,iBAAiBsG,KAAK0nB,OAAO/tB,iBAAiB,iBAAiBpC,OAE7E,OAAIgxB,EAAexuB,UAAUC,SAvOP,UAwObwuB,EAAQxB,GAAmBD,GAG7ByB,EAAQtB,GAAsBD,GAGvCY,gBACE,OAA0D,OAAnD7nB,KAAK2D,SAASiB,QAAS,WAGhC6jB,aACE,MAAMjiB,OAAEA,GAAWxG,KAAKmK,QAExB,MAAsB,iBAAX3D,EACFA,EAAOlP,MAAM,KAAK8Q,KAAI3C,GAAO/I,OAAOwQ,SAASzH,EAAK,MAGrC,mBAAXe,EACFkiB,GAAcliB,EAAOkiB,EAAY1oB,KAAK2D,UAGxC6C,EAGT4hB,mBACE,MAAMO,EAAwB,CAC5B1X,UAAWjR,KAAKsoB,gBAChB5E,UAAW,CAAC,CACVloB,KAAM,kBACNyX,QAAS,CACPmJ,SAAUpc,KAAKmK,QAAQiS,WAG3B,CACE5gB,KAAM,SACNyX,QAAS,CACPzM,OAAQxG,KAAKyoB,iBAanB,MAP6B,WAAzBzoB,KAAKmK,QAAQkd,UACfsB,EAAsBjF,UAAY,CAAC,CACjCloB,KAAM,cACN+W,SAAS,KAIN,IACFoW,KACsC,mBAA9B3oB,KAAKmK,QAAQmd,aAA8BtnB,KAAKmK,QAAQmd,aAAaqB,GAAyB3oB,KAAKmK,QAAQmd,cAI1HsB,iBAAgBpmB,IAAEA,EAAFxF,OAAOA,IACrB,MAAM6rB,EAAQ3hB,EAAeC,KAxRF,8DAwR+BnH,KAAK0nB,OAAOthB,OAAO5M,GAExEqvB,EAAMzwB,QAMX+E,EAAqB0rB,EAAO7rB,EAAQwF,IAAQkkB,IAAiBmC,EAAMzxB,SAAS4F,IAASgrB,QAKjE7jB,uBAAC5L,GACrB,OAAOyH,KAAKiF,MAAK,WACf,MAAMC,EAAOsiB,GAAS3iB,oBAAoB7E,KAAMzH,GAEhD,GAAsB,iBAAXA,EAAX,CAIA,QAA4B,IAAjB2M,EAAK3M,GACd,MAAM,IAAIe,UAAW,oBAAmBf,MAG1C2M,EAAK3M,SAIQ4L,kBAACjF,GAChB,GAAIA,IA3UmB,IA2UTA,EAAM0G,QAAiD,UAAf1G,EAAMsB,MA9UhD,QA8UoEtB,EAAMsD,KACpF,OAGF,MAAMsmB,EAAU5hB,EAAeC,KAAK/B,IAEpC,IAAK,IAAIpG,EAAI,EAAGC,EAAM6pB,EAAQ1wB,OAAQ4G,EAAIC,EAAKD,IAAK,CAClD,MAAM+pB,EAAUvB,GAASpjB,YAAY0kB,EAAQ9pB,IAC7C,IAAK+pB,IAAyC,IAA9BA,EAAQ5e,QAAQod,UAC9B,SAGF,IAAKwB,EAAQzZ,WACX,SAGF,MAAMxP,EAAgB,CACpBA,cAAeipB,EAAQplB,UAGzB,GAAIzE,EAAO,CACT,MAAM8pB,EAAe9pB,EAAM8pB,eACrBC,EAAeD,EAAa5xB,SAAS2xB,EAAQrB,OACnD,GACEsB,EAAa5xB,SAAS2xB,EAAQplB,WACC,WAA9BolB,EAAQ5e,QAAQod,YAA2B0B,GACb,YAA9BF,EAAQ5e,QAAQod,WAA2B0B,EAE5C,SAIF,GAAIF,EAAQrB,MAAM1tB,SAASkF,EAAMlC,UAA4B,UAAfkC,EAAMsB,MA9W5C,QA8WgEtB,EAAMsD,KAAoB,qCAAqCnJ,KAAK6F,EAAMlC,OAAO2H,UACvJ,SAGiB,UAAfzF,EAAMsB,OACRV,EAAc4E,WAAaxF,GAI/B6pB,EAAQd,cAAcnoB,IAICqE,4BAACnN,GAC1B,OAAOW,EAAuBX,IAAYA,EAAQyD,WAGxB0J,6BAACjF,GAQ3B,GAAI,kBAAkB7F,KAAK6F,EAAMlC,OAAO2H,SACtCzF,EAAMsD,MAAQgkB,IAActnB,EAAMsD,MAAQ+jB,KACxCrnB,EAAMsD,MAAQkkB,IAAkBxnB,EAAMsD,MAAQikB,IAC9CvnB,EAAMlC,OAAO4H,QAAQkiB,MACtBH,GAAettB,KAAK6F,EAAMsD,KAC3B,OAGF,MAAM0mB,EAAWlpB,KAAKjG,UAAUC,SAASuU,IAEzC,IAAK2a,GAAYhqB,EAAMsD,MAAQ+jB,GAC7B,OAMF,GAHArnB,EAAMyD,iBACNzD,EAAMiqB,kBAEFvvB,EAAWoG,MACb,OAGF,MAAMopB,EAAkBppB,KAAK0H,QAAQtC,IAAwBpF,KAAOkH,EAAeW,KAAK7H,KAAMoF,IAAsB,GAC9GpC,EAAWwkB,GAAS3iB,oBAAoBukB,GAE9C,GAAIlqB,EAAMsD,MAAQ+jB,GAKlB,OAAIrnB,EAAMsD,MAAQikB,IAAgBvnB,EAAMsD,MAAQkkB,IACzCwC,GACHlmB,EAASwM,YAGXxM,EAAS4lB,gBAAgB1pB,SAItBgqB,GAAYhqB,EAAMsD,MAAQgkB,IAC7BgB,GAAS6B,cAdTrmB,EAASuM,QAyBfjP,EAAaQ,GAAGrJ,SAAUovB,GAAwBzhB,GAAsBoiB,GAAS8B,uBACjFhpB,EAAaQ,GAAGrJ,SAAUovB,GAAwBC,GAAeU,GAAS8B,uBAC1EhpB,EAAaQ,GAAGrJ,SAAUmvB,GAAsBY,GAAS6B,YACzD/oB,EAAaQ,GAAGrJ,SA/ac,6BA+akB+vB,GAAS6B,YACzD/oB,EAAaQ,GAAGrJ,SAAUmvB,GAAsBxhB,IAAsB,SAAUlG,GAC9EA,EAAMyD,iBACN6kB,GAAS3iB,oBAAoB7E,MAAMsF,YAUrClK,EAAmBosB,ICrenB,MAAM+B,GAAyB,oDACzBC,GAA0B,cAEhC,MAAMC,GACJ/lB,cACE1D,KAAK2D,SAAWlM,SAASuD,KAG3B0uB,WAEE,MAAMC,EAAgBlyB,SAAS2C,gBAAgB2c,YAC/C,OAAOpZ,KAAKgO,IAAI5Q,OAAO6uB,WAAaD,GAGtCpa,OACE,MAAMmE,EAAQ1T,KAAK0pB,WACnB1pB,KAAK6pB,mBAEL7pB,KAAK8pB,sBAAsB9pB,KAAK2D,SAAU,gBAAgBomB,GAAmBA,EAAkBrW,IAE/F1T,KAAK8pB,sBAAsBP,GAAwB,gBAAgBQ,GAAmBA,EAAkBrW,IACxG1T,KAAK8pB,sBAAsBN,GAAyB,eAAeO,GAAmBA,EAAkBrW,IAG1GmW,mBACE7pB,KAAKgqB,sBAAsBhqB,KAAK2D,SAAU,YAC1C3D,KAAK2D,SAASqM,MAAMuK,SAAW,SAGjCuP,sBAAsB7yB,EAAUgzB,EAAW3uB,GACzC,MAAM4uB,EAAiBlqB,KAAK0pB,WAW5B1pB,KAAKmqB,2BAA2BlzB,GAVHD,IAC3B,GAAIA,IAAYgJ,KAAK2D,UAAY5I,OAAO6uB,WAAa5yB,EAAQ+f,YAAcmT,EACzE,OAGFlqB,KAAKgqB,sBAAsBhzB,EAASizB,GACpC,MAAMF,EAAkBhvB,OAAOrB,iBAAiB1C,GAASizB,GACzDjzB,EAAQgZ,MAAMia,GAAc,GAAE3uB,EAASoB,OAAOC,WAAWotB,WAM7D7J,QACElgB,KAAKoqB,wBAAwBpqB,KAAK2D,SAAU,YAC5C3D,KAAKoqB,wBAAwBpqB,KAAK2D,SAAU,gBAC5C3D,KAAKoqB,wBAAwBb,GAAwB,gBACrDvpB,KAAKoqB,wBAAwBZ,GAAyB,eAGxDQ,sBAAsBhzB,EAASizB,GAC7B,MAAMI,EAAcrzB,EAAQgZ,MAAMia,GAC9BI,GACFxkB,EAAYC,iBAAiB9O,EAASizB,EAAWI,GAIrDD,wBAAwBnzB,EAAUgzB,GAWhCjqB,KAAKmqB,2BAA2BlzB,GAVHD,IAC3B,MAAM8B,EAAQ+M,EAAYU,iBAAiBvP,EAASizB,QAC/B,IAAVnxB,EACT9B,EAAQgZ,MAAMsa,eAAeL,IAE7BpkB,EAAYE,oBAAoB/O,EAASizB,GACzCjzB,EAAQgZ,MAAMia,GAAanxB,MAOjCqxB,2BAA2BlzB,EAAUszB,GAC/BxyB,EAAUd,GACZszB,EAAStzB,GAETiQ,EAAeC,KAAKlQ,EAAU+I,KAAK2D,UAAUhL,QAAQ4xB,GAIzDC,gBACE,OAAOxqB,KAAK0pB,WAAa,GClF7B,MAAMnhB,GAAU,CACdkiB,UAAW,iBACXjxB,WAAW,EACX0K,YAAY,EACZwmB,YAAa,OACbC,cAAe,MAGX7hB,GAAc,CAClB2hB,UAAW,SACXjxB,UAAW,UACX0K,WAAY,UACZwmB,YAAa,mBACbC,cAAe,mBAIXpc,GAAkB,OAElBqc,GAAmB,wBAEzB,MAAMC,GACJnnB,YAAYnL,GACVyH,KAAKmK,QAAUnK,KAAKoK,WAAW7R,GAC/ByH,KAAK8qB,aAAc,EACnB9qB,KAAK2D,SAAW,KAGlB6L,KAAKlU,GACE0E,KAAKmK,QAAQ3Q,WAKlBwG,KAAK+qB,UAED/qB,KAAKmK,QAAQjG,YACfvJ,EAAOqF,KAAKgrB,eAGdhrB,KAAKgrB,cAAcjxB,UAAUyS,IAAI+B,IAEjCvO,KAAKirB,mBAAkB,KACrB/uB,EAAQZ,OAbRY,EAAQZ,GAiBZiU,KAAKjU,GACE0E,KAAKmK,QAAQ3Q,WAKlBwG,KAAKgrB,cAAcjxB,UAAUwJ,OAAOgL,IAEpCvO,KAAKirB,mBAAkB,KACrBjrB,KAAK6D,UACL3H,EAAQZ,OARRY,EAAQZ,GAcZ0vB,cACE,IAAKhrB,KAAK2D,SAAU,CAClB,MAAMunB,EAAWzzB,SAAS0zB,cAAc,OACxCD,EAAST,UAAYzqB,KAAKmK,QAAQsgB,UAC9BzqB,KAAKmK,QAAQjG,YACfgnB,EAASnxB,UAAUyS,IApDH,QAuDlBxM,KAAK2D,SAAWunB,EAGlB,OAAOlrB,KAAK2D,SAGdyG,WAAW7R,GAST,OARAA,EAAS,IACJgQ,MACmB,iBAAXhQ,EAAsBA,EAAS,KAIrCmyB,YAAcvyB,EAAWI,EAAOmyB,aACvCryB,EAtES,WAsEaE,EAAQuQ,IACvBvQ,EAGTwyB,UACM/qB,KAAK8qB,cAIT9qB,KAAKmK,QAAQugB,YAAYU,OAAOprB,KAAKgrB,eAErC1qB,EAAaQ,GAAGd,KAAKgrB,cAAeJ,IAAiB,KACnD1uB,EAAQ8D,KAAKmK,QAAQwgB,kBAGvB3qB,KAAK8qB,aAAc,GAGrBjnB,UACO7D,KAAK8qB,cAIVxqB,EAAaC,IAAIP,KAAK2D,SAAUinB,IAEhC5qB,KAAK2D,SAASJ,SACdvD,KAAK8qB,aAAc,GAGrBG,kBAAkB3vB,GAChBa,EAAuBb,EAAU0E,KAAKgrB,cAAehrB,KAAKmK,QAAQjG,aClHtE,MAAMqE,GAAU,CACd8iB,YAAa,KACbC,WAAW,GAGPxiB,GAAc,CAClBuiB,YAAa,UACbC,UAAW,WAKPxnB,GAAa,gBAMbynB,GAAmB,WAEzB,MAAMC,GACJ9nB,YAAYnL,GACVyH,KAAKmK,QAAUnK,KAAKoK,WAAW7R,GAC/ByH,KAAKyrB,WAAY,EACjBzrB,KAAK0rB,qBAAuB,KAG9BC,WACE,MAAMN,YAAEA,EAAFC,UAAeA,GAActrB,KAAKmK,QAEpCnK,KAAKyrB,YAILH,GACFD,EAAYrD,QAGd1nB,EAAaC,IAAI9I,SAAUqM,IAC3BxD,EAAaQ,GAAGrJ,SA1BG,wBA0BsByH,GAASc,KAAK4rB,eAAe1sB,KACtEoB,EAAaQ,GAAGrJ,SA1BO,4BA0BsByH,GAASc,KAAK6rB,eAAe3sB,KAE1Ec,KAAKyrB,WAAY,GAGnBK,aACO9rB,KAAKyrB,YAIVzrB,KAAKyrB,WAAY,EACjBnrB,EAAaC,IAAI9I,SAAUqM,KAK7B8nB,eAAe1sB,GACb,MAAMlC,OAAEA,GAAWkC,GACbmsB,YAAEA,GAAgBrrB,KAAKmK,QAE7B,GAAInN,IAAWvF,UAAYuF,IAAWquB,GAAeA,EAAYrxB,SAASgD,GACxE,OAGF,MAAM2V,EAAWzL,EAAegB,kBAAkBmjB,GAE1B,IAApB1Y,EAASva,OACXizB,EAAYrD,QACHhoB,KAAK0rB,uBAAyBH,GACvC5Y,EAASA,EAASva,OAAS,GAAG4vB,QAE9BrV,EAAS,GAAGqV,QAIhB6D,eAAe3sB,GA3DD,QA4DRA,EAAMsD,MAIVxC,KAAK0rB,qBAAuBxsB,EAAM6sB,SAAWR,GA/DzB,WAkEtBnhB,WAAW7R,GAMT,OALAA,EAAS,IACJgQ,MACmB,iBAAXhQ,EAAsBA,EAAS,IAE5CF,EA9ES,YA8EaE,EAAQuQ,IACvBvQ,GCtEX,MAAMkD,GAAO,QAIP8qB,GAAa,SAEbhe,GAAU,CACd2iB,UAAU,EACVziB,UAAU,EACVuf,OAAO,GAGHlf,GAAc,CAClBoiB,SAAU,mBACVziB,SAAU,UACVuf,MAAO,WAKHgE,GAAgB,kBAChBC,GAAc,gBAEdC,GAAgB,kBAChBC,GAAuB,yBACvBC,GAAyB,2BAEzBC,GAA2B,6BAG3BC,GAAkB,aAElB/d,GAAkB,OAClBge,GAAoB,eAa1B,MAAMC,WAAc/oB,EAClBC,YAAY1M,EAASuB,GACnBmR,MAAM1S,GAENgJ,KAAKmK,QAAUnK,KAAKoK,WAAW7R,GAC/ByH,KAAKysB,QAAUvlB,EAAeK,QAfV,gBAemCvH,KAAK2D,UAC5D3D,KAAK0sB,UAAY1sB,KAAK2sB,sBACtB3sB,KAAK4sB,WAAa5sB,KAAK6sB,uBACvB7sB,KAAKsP,UAAW,EAChBtP,KAAK8sB,sBAAuB,EAC5B9sB,KAAK6O,kBAAmB,EACxB7O,KAAK+sB,WAAa,IAAItD,GAKblhB,qBACT,OAAOA,GAGE9M,kBACT,OAAOA,GAKT6J,OAAOxF,GACL,OAAOE,KAAKsP,SAAWtP,KAAKuP,OAASvP,KAAKwP,KAAK1P,GAGjD0P,KAAK1P,GACCE,KAAKsP,UAAYtP,KAAK6O,kBAIRvO,EAAamB,QAAQzB,KAAK2D,SAAUsoB,GAAY,CAChEnsB,cAAAA,IAGYiC,mBAId/B,KAAKsP,UAAW,EAEZtP,KAAKgtB,gBACPhtB,KAAK6O,kBAAmB,GAG1B7O,KAAK+sB,WAAWxd,OAEhB9X,SAASuD,KAAKjB,UAAUyS,IAAI8f,IAE5BtsB,KAAKitB,gBAELjtB,KAAKktB,kBACLltB,KAAKmtB,kBAEL7sB,EAAaQ,GAAGd,KAAKysB,QAASJ,IAAyB,KACrD/rB,EAAaS,IAAIf,KAAK2D,SA/EG,4BA+E8BzE,IACjDA,EAAMlC,SAAWgD,KAAK2D,WACxB3D,KAAK8sB,sBAAuB,SAKlC9sB,KAAKotB,eAAc,IAAMptB,KAAKqtB,aAAavtB,MAG7CyP,OACE,IAAKvP,KAAKsP,UAAYtP,KAAK6O,iBACzB,OAKF,GAFkBvO,EAAamB,QAAQzB,KAAK2D,SAtG5B,iBAwGF5B,iBACZ,OAGF/B,KAAKsP,UAAW,EAChB,MAAMpL,EAAalE,KAAKgtB,cAEpB9oB,IACFlE,KAAK6O,kBAAmB,GAG1B7O,KAAKktB,kBACLltB,KAAKmtB,kBAELntB,KAAK4sB,WAAWd,aAEhB9rB,KAAK2D,SAAS5J,UAAUwJ,OAAOgL,IAE/BjO,EAAaC,IAAIP,KAAK2D,SAAUwoB,IAChC7rB,EAAaC,IAAIP,KAAKysB,QAASJ,IAE/BrsB,KAAKiE,gBAAe,IAAMjE,KAAKstB,cAActtB,KAAK2D,SAAUO,GAG9DL,UACE,CAAC9I,OAAQiF,KAAKysB,SACX9zB,SAAQ40B,GAAejtB,EAAaC,IAAIgtB,EAlJ5B,eAoJfvtB,KAAK0sB,UAAU7oB,UACf7D,KAAK4sB,WAAWd,aAChBpiB,MAAM7F,UAGR2pB,eACExtB,KAAKitB,gBAKPN,sBACE,OAAO,IAAI9B,GAAS,CAClBrxB,UAAWqH,QAAQb,KAAKmK,QAAQ+gB,UAChChnB,WAAYlE,KAAKgtB,gBAIrBH,uBACE,OAAO,IAAIrB,GAAU,CACnBH,YAAarrB,KAAK2D,WAItByG,WAAW7R,GAOT,OANAA,EAAS,IACJgQ,MACA1C,EAAYI,kBAAkBjG,KAAK2D,aAChB,iBAAXpL,EAAsBA,EAAS,IAE5CF,EAAgBoD,GAAMlD,EAAQuQ,IACvBvQ,EAGT80B,aAAavtB,GACX,MAAMoE,EAAalE,KAAKgtB,cAClBS,EAAYvmB,EAAeK,QArJT,cAqJsCvH,KAAKysB,SAE9DzsB,KAAK2D,SAASlJ,YAAcuF,KAAK2D,SAASlJ,WAAWvC,WAAa2B,KAAKC,cAE1ErC,SAASuD,KAAKowB,OAAOprB,KAAK2D,UAG5B3D,KAAK2D,SAASqM,MAAMqX,QAAU,QAC9BrnB,KAAK2D,SAASqC,gBAAgB,eAC9BhG,KAAK2D,SAAS4B,aAAa,cAAc,GACzCvF,KAAK2D,SAAS4B,aAAa,OAAQ,UACnCvF,KAAK2D,SAASwW,UAAY,EAEtBsT,IACFA,EAAUtT,UAAY,GAGpBjW,GACFvJ,EAAOqF,KAAK2D,UAGd3D,KAAK2D,SAAS5J,UAAUyS,IAAI+B,IAa5BvO,KAAKiE,gBAXsB,KACrBjE,KAAKmK,QAAQ6d,OACfhoB,KAAK4sB,WAAWjB,WAGlB3rB,KAAK6O,kBAAmB,EACxBvO,EAAamB,QAAQzB,KAAK2D,SAjMX,iBAiMkC,CAC/C7D,cAAAA,MAIoCE,KAAKysB,QAASvoB,GAGxDgpB,kBACMltB,KAAKsP,SACPhP,EAAaQ,GAAGd,KAAK2D,SAAUyoB,IAAuBltB,IAChDc,KAAKmK,QAAQ1B,UAAYvJ,EAAMsD,MAAQ+jB,IACzCrnB,EAAMyD,iBACN3C,KAAKuP,QACKvP,KAAKmK,QAAQ1B,UAAYvJ,EAAMsD,MAAQ+jB,IACjDvmB,KAAK0tB,gCAITptB,EAAaC,IAAIP,KAAK2D,SAAUyoB,IAIpCe,kBACMntB,KAAKsP,SACPhP,EAAaQ,GAAG/F,OAAQmxB,IAAc,IAAMlsB,KAAKitB,kBAEjD3sB,EAAaC,IAAIxF,OAAQmxB,IAI7BoB,aACEttB,KAAK2D,SAASqM,MAAMqX,QAAU,OAC9BrnB,KAAK2D,SAAS4B,aAAa,eAAe,GAC1CvF,KAAK2D,SAASqC,gBAAgB,cAC9BhG,KAAK2D,SAASqC,gBAAgB,QAC9BhG,KAAK6O,kBAAmB,EACxB7O,KAAK0sB,UAAUnd,MAAK,KAClB9X,SAASuD,KAAKjB,UAAUwJ,OAAO+oB,IAC/BtsB,KAAK2tB,oBACL3tB,KAAK+sB,WAAW7M,QAChB5f,EAAamB,QAAQzB,KAAK2D,SAAUqoB,OAIxCoB,cAAc9xB,GACZgF,EAAaQ,GAAGd,KAAK2D,SAAUwoB,IAAqBjtB,IAC9Cc,KAAK8sB,qBACP9sB,KAAK8sB,sBAAuB,EAI1B5tB,EAAMlC,SAAWkC,EAAM0uB,iBAIG,IAA1B5tB,KAAKmK,QAAQ+gB,SACflrB,KAAKuP,OAC8B,WAA1BvP,KAAKmK,QAAQ+gB,UACtBlrB,KAAK0tB,iCAIT1tB,KAAK0sB,UAAUld,KAAKlU,GAGtB0xB,cACE,OAAOhtB,KAAK2D,SAAS5J,UAAUC,SA3PX,QA8PtB0zB,6BAEE,GADkBptB,EAAamB,QAAQzB,KAAK2D,SA3QlB,0BA4QZ5B,iBACZ,OAGF,MAAMhI,UAAEA,EAAF2hB,aAAaA,EAAb1L,MAA2BA,GAAUhQ,KAAK2D,SAC1CkqB,EAAqBnS,EAAejkB,SAAS2C,gBAAgB0c,cAG7D+W,GAA0C,WAApB7d,EAAMyK,WAA2B1gB,EAAUC,SAASuyB,MAI3EsB,IACH7d,EAAMyK,UAAY,UAGpB1gB,EAAUyS,IAAI+f,IACdvsB,KAAKiE,gBAAe,KAClBlK,EAAUwJ,OAAOgpB,IACZsB,GACH7tB,KAAKiE,gBAAe,KAClB+L,EAAMyK,UAAY,KACjBza,KAAKysB,WAETzsB,KAAKysB,SAERzsB,KAAK2D,SAASqkB,SAOhBiF,gBACE,MAAMY,EAAqB7tB,KAAK2D,SAAS+X,aAAejkB,SAAS2C,gBAAgB0c,aAC3EoT,EAAiBlqB,KAAK+sB,WAAWrD,WACjCoE,EAAoB5D,EAAiB,IAErC4D,GAAqBD,IAAuB3yB,KAAa4yB,IAAsBD,GAAsB3yB,OACzG8E,KAAK2D,SAASqM,MAAM+d,YAAe,GAAE7D,QAGlC4D,IAAsBD,IAAuB3yB,MAAc4yB,GAAqBD,GAAsB3yB,OACzG8E,KAAK2D,SAASqM,MAAMge,aAAgB,GAAE9D,OAI1CyD,oBACE3tB,KAAK2D,SAASqM,MAAM+d,YAAc,GAClC/tB,KAAK2D,SAASqM,MAAMge,aAAe,GAKf7pB,uBAAC5L,EAAQuH,GAC7B,OAAOE,KAAKiF,MAAK,WACf,MAAMC,EAAOsnB,GAAM3nB,oBAAoB7E,KAAMzH,GAE7C,GAAsB,iBAAXA,EAAX,CAIA,QAA4B,IAAjB2M,EAAK3M,GACd,MAAM,IAAIe,UAAW,oBAAmBf,MAG1C2M,EAAK3M,GAAQuH,QAWnBQ,EAAaQ,GAAGrJ,SAhVc,0BAUD,4BAsUyC,SAAUyH,GAC9E,MAAMlC,EAASrF,EAAuBqI,MAElC,CAAC,IAAK,QAAQ5I,SAAS4I,KAAK2E,UAC9BzF,EAAMyD,iBAGRrC,EAAaS,IAAI/D,EAAQivB,IAAYgC,IAC/BA,EAAUlsB,kBAKdzB,EAAaS,IAAI/D,EAAQgvB,IAAc,KACjCxyB,EAAUwG,OACZA,KAAKgoB,cAMX,MAAMkG,EAAehnB,EAAeK,QA9VhB,eA+VhB2mB,GACF1B,GAAMpoB,YAAY8pB,GAAc3e,OAGrBid,GAAM3nB,oBAAoB7H,GAElCsI,OAAOtF,SAGduE,EAAqBioB,IASrBpxB,EAAmBoxB,ICrZnB,MAAM/wB,GAAO,YAOP8M,GAAU,CACd2iB,UAAU,EACVziB,UAAU,EACV8Q,QAAQ,GAGJzQ,GAAc,CAClBoiB,SAAU,UACVziB,SAAU,UACV8Q,OAAQ,WAGJhL,GAAkB,OAElB4f,GAAgB,kBAKhBnC,GAAgB,sBAYtB,MAAMoC,WAAkB3qB,EACtBC,YAAY1M,EAASuB,GACnBmR,MAAM1S,GAENgJ,KAAKmK,QAAUnK,KAAKoK,WAAW7R,GAC/ByH,KAAKsP,UAAW,EAChBtP,KAAK0sB,UAAY1sB,KAAK2sB,sBACtB3sB,KAAK4sB,WAAa5sB,KAAK6sB,uBACvB7sB,KAAK2K,qBAKIlP,kBACT,OAAOA,GAGE8M,qBACT,OAAOA,GAKTjD,OAAOxF,GACL,OAAOE,KAAKsP,SAAWtP,KAAKuP,OAASvP,KAAKwP,KAAK1P,GAGjD0P,KAAK1P,GACCE,KAAKsP,UAIShP,EAAamB,QAAQzB,KAAK2D,SA/C5B,oBA+CkD,CAAE7D,cAAAA,IAEtDiC,mBAId/B,KAAKsP,UAAW,EAChBtP,KAAK2D,SAASqM,MAAMqe,WAAa,UAEjCruB,KAAK0sB,UAAUld,OAEVxP,KAAKmK,QAAQoP,SAChB,IAAIkQ,IAAkBla,OAGxBvP,KAAK2D,SAASqC,gBAAgB,eAC9BhG,KAAK2D,SAAS4B,aAAa,cAAc,GACzCvF,KAAK2D,SAAS4B,aAAa,OAAQ,UACnCvF,KAAK2D,SAAS5J,UAAUyS,IAAI+B,IAU5BvO,KAAKiE,gBARoB,KAClBjE,KAAKmK,QAAQoP,QAChBvZ,KAAK4sB,WAAWjB,WAGlBrrB,EAAamB,QAAQzB,KAAK2D,SAvEX,qBAuEkC,CAAE7D,cAAAA,MAGfE,KAAK2D,UAAU,IAGvD4L,OACOvP,KAAKsP,WAIQhP,EAAamB,QAAQzB,KAAK2D,SAjF5B,qBAmFF5B,mBAId/B,KAAK4sB,WAAWd,aAChB9rB,KAAK2D,SAAS2qB,OACdtuB,KAAKsP,UAAW,EAChBtP,KAAK2D,SAAS5J,UAAUwJ,OAAOgL,IAC/BvO,KAAK0sB,UAAUnd,OAefvP,KAAKiE,gBAboB,KACvBjE,KAAK2D,SAAS4B,aAAa,eAAe,GAC1CvF,KAAK2D,SAASqC,gBAAgB,cAC9BhG,KAAK2D,SAASqC,gBAAgB,QAC9BhG,KAAK2D,SAASqM,MAAMqe,WAAa,SAE5BruB,KAAKmK,QAAQoP,SAChB,IAAIkQ,IAAkBvJ,QAGxB5f,EAAamB,QAAQzB,KAAK2D,SAAUqoB,MAGAhsB,KAAK2D,UAAU,KAGvDE,UACE7D,KAAK0sB,UAAU7oB,UACf7D,KAAK4sB,WAAWd,aAChBpiB,MAAM7F,UAKRuG,WAAW7R,GAOT,OANAA,EAAS,IACJgQ,MACA1C,EAAYI,kBAAkBjG,KAAK2D,aAChB,iBAAXpL,EAAsBA,EAAS,IAE5CF,EAAgBoD,GAAMlD,EAAQuQ,IACvBvQ,EAGTo0B,sBACE,OAAO,IAAI9B,GAAS,CAClBJ,UAtIsB,qBAuItBjxB,UAAWwG,KAAKmK,QAAQ+gB,SACxBhnB,YAAY,EACZwmB,YAAa1qB,KAAK2D,SAASlJ,WAC3BkwB,cAAe,IAAM3qB,KAAKuP,SAI9Bsd,uBACE,OAAO,IAAIrB,GAAU,CACnBH,YAAarrB,KAAK2D,WAItBgH,qBACErK,EAAaQ,GAAGd,KAAK2D,SA7IM,gCA6I2BzE,IAChDc,KAAKmK,QAAQ1B,UArKJ,WAqKgBvJ,EAAMsD,KACjCxC,KAAKuP,UAOWpL,uBAAC5L,GACrB,OAAOyH,KAAKiF,MAAK,WACf,MAAMC,EAAOkpB,GAAUvpB,oBAAoB7E,KAAMzH,GAEjD,GAAsB,iBAAXA,EAAX,CAIA,QAAqB4M,IAAjBD,EAAK3M,IAAyBA,EAAOlB,WAAW,MAAmB,gBAAXkB,EAC1D,MAAM,IAAIe,UAAW,oBAAmBf,MAG1C2M,EAAK3M,GAAQyH,WAWnBM,EAAaQ,GAAGrJ,SA9Kc,8BAGD,gCA2KyC,SAAUyH,GAC9E,MAAMlC,EAASrF,EAAuBqI,MAMtC,GAJI,CAAC,IAAK,QAAQ5I,SAAS4I,KAAK2E,UAC9BzF,EAAMyD,iBAGJ/I,EAAWoG,MACb,OAGFM,EAAaS,IAAI/D,EAAQgvB,IAAc,KAEjCxyB,EAAUwG,OACZA,KAAKgoB,WAKT,MAAMkG,EAAehnB,EAAeK,QAAQ4mB,IACxCD,GAAgBA,IAAiBlxB,GACnCoxB,GAAUhqB,YAAY8pB,GAAc3e,OAGzB6e,GAAUvpB,oBAAoB7H,GACtCsI,OAAOtF,SAGdM,EAAaQ,GAAG/F,OAjOa,8BAiOgB,IAC3CmM,EAAeC,KAAKgnB,IAAex1B,SAAQ2P,GAAM8lB,GAAUvpB,oBAAoByD,GAAIkH,WAGrFjL,EAAqB6pB,IAOrBhzB,EAAmBgzB,ICtQnB,MAAMG,GAAgB,IAAI/vB,IAAI,CAC5B,aACA,OACA,OACA,WACA,WACA,SACA,MACA,eAUIgwB,GAAmB,iEAOnBC,GAAmB,qIAEnBC,GAAmB,CAACpb,EAAWqb,KACnC,MAAMC,EAAgBtb,EAAUxB,SAAS3Y,cAEzC,GAAIw1B,EAAqBv3B,SAASw3B,GAChC,OAAIL,GAAc7uB,IAAIkvB,IACb/tB,QAAQ2tB,GAAiBn1B,KAAKia,EAAUub,YAAcJ,GAAiBp1B,KAAKia,EAAUub,YAMjG,MAAMC,EAASH,EAAqBvoB,QAAO2oB,GAAkBA,aAA0B31B,SAGvF,IAAK,IAAI4F,EAAI,EAAGC,EAAM6vB,EAAO12B,OAAQ4G,EAAIC,EAAKD,IAC5C,GAAI8vB,EAAO9vB,GAAG3F,KAAKu1B,GACjB,OAAO,EAIX,OAAO,GAqCF,SAASI,GAAaC,EAAYC,EAAWC,GAClD,IAAKF,EAAW72B,OACd,OAAO62B,EAGT,GAAIE,GAAoC,mBAAfA,EACvB,OAAOA,EAAWF,GAGpB,MACMG,GADY,IAAIr0B,OAAOs0B,WACKC,gBAAgBL,EAAY,aACxDtc,EAAW,GAAGvL,UAAUgoB,EAAgBp0B,KAAKqF,iBAAiB,MAEpE,IAAK,IAAIrB,EAAI,EAAGC,EAAM0T,EAASva,OAAQ4G,EAAIC,EAAKD,IAAK,CACnD,MAAMhI,EAAU2b,EAAS3T,GACnBuwB,EAAcv4B,EAAQ8a,SAAS3Y,cAErC,IAAKV,OAAOC,KAAKw2B,GAAW93B,SAASm4B,GAAc,CACjDv4B,EAAQuM,SAER,SAGF,MAAMisB,EAAgB,GAAGpoB,UAAUpQ,EAAQkP,YACrCupB,EAAoB,GAAGroB,OAAO8nB,EAAU,MAAQ,GAAIA,EAAUK,IAAgB,IAEpFC,EAAc72B,SAAQ2a,IACfob,GAAiBpb,EAAWmc,IAC/Bz4B,EAAQgP,gBAAgBsN,EAAUxB,aAKxC,OAAOsd,EAAgBp0B,KAAK00B,UC5F9B,MAAMj0B,GAAO,UAIPk0B,GAAwB,IAAInxB,IAAI,CAAC,WAAY,YAAa,eAE1DsK,GAAc,CAClB8mB,UAAW,UACXC,SAAU,SACVC,MAAO,4BACPruB,QAAS,SACTsuB,MAAO,kBACP5U,KAAM,UACNlkB,SAAU,mBACVga,UAAW,oBACXzK,OAAQ,0BACRmJ,UAAW,2BACXmP,mBAAoB,QACpB1C,SAAU,mBACV4T,YAAa,oBACbC,SAAU,UACVd,WAAY,kBACZD,UAAW,SACX5H,aAAc,0BAGV4I,GAAgB,CACpBC,KAAM,OACNC,IAAK,MACLC,MAAOn1B,IAAU,OAAS,QAC1Bo1B,OAAQ,SACRC,KAAMr1B,IAAU,QAAU,QAGtBqN,GAAU,CACdqnB,WAAW,EACXC,SAAU,+GAIVpuB,QAAS,cACTquB,MAAO,GACPC,MAAO,EACP5U,MAAM,EACNlkB,UAAU,EACVga,UAAW,MACXzK,OAAQ,CAAC,EAAG,GACZmJ,WAAW,EACXmP,mBAAoB,CAAC,MAAO,QAAS,SAAU,QAC/C1C,SAAU,kBACV4T,YAAa,GACbC,UAAU,EACVd,WAAY,KACZD,UD5B8B,CAE9B,IAAK,CAAC,QAAS,MAAO,KAAM,OAAQ,OAzCP,kBA0C7B9Q,EAAG,CAAC,SAAU,OAAQ,QAAS,OAC/BoS,KAAM,GACNnS,EAAG,GACHoS,GAAI,GACJC,IAAK,GACLC,KAAM,GACNC,IAAK,GACLC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJpyB,EAAG,GACHqyB,IAAK,CAAC,MAAO,SAAU,MAAO,QAAS,QAAS,UAChDC,GAAI,GACJC,GAAI,GACJC,EAAG,GACHC,IAAK,GACLC,EAAG,GACHC,MAAO,GACPC,KAAM,GACNC,IAAK,GACLC,IAAK,GACLC,OAAQ,GACRC,EAAG,GACHC,GAAI,ICFJ3K,aAAc,MAGVxvB,GAAQ,CACZo6B,KAAO,kBACPC,OAAS,oBACTC,KAAO,kBACPC,MAAQ,mBACRC,SAAW,sBACXC,MAAQ,mBACRC,QAAU,qBACVC,SAAW,sBACXC,WAAa,wBACbC,WAAa,yBAGTC,GAAkB,OAElBrkB,GAAkB,OAElBskB,GAAmB,OACnBC,GAAkB,MAElBC,GAAyB,iBACzBC,GAAkB,SAElBC,GAAmB,gBAEnBC,GAAgB,QAChBC,GAAgB,QAUtB,MAAMC,WAAgB3vB,EACpBC,YAAY1M,EAASuB,GACnB,QAAsB,IAAX2vB,GACT,MAAM,IAAI5uB,UAAU,+DAGtBoQ,MAAM1S,GAGNgJ,KAAKqzB,YAAa,EAClBrzB,KAAKszB,SAAW,EAChBtzB,KAAKuzB,YAAc,GACnBvzB,KAAKwzB,eAAiB,GACtBxzB,KAAKynB,QAAU,KAGfznB,KAAKmK,QAAUnK,KAAKoK,WAAW7R,GAC/ByH,KAAKyzB,IAAM,KAEXzzB,KAAK0zB,gBAKInrB,qBACT,OAAOA,GAGE9M,kBACT,OAAOA,GAGE3D,mBACT,OAAOA,GAGEgR,yBACT,OAAOA,GAKT6qB,SACE3zB,KAAKqzB,YAAa,EAGpBO,UACE5zB,KAAKqzB,YAAa,EAGpBQ,gBACE7zB,KAAKqzB,YAAcrzB,KAAKqzB,WAG1B/tB,OAAOpG,GACL,GAAKc,KAAKqzB,WAIV,GAAIn0B,EAAO,CACT,MAAM6pB,EAAU/oB,KAAK8zB,6BAA6B50B,GAElD6pB,EAAQyK,eAAeO,OAAShL,EAAQyK,eAAeO,MAEnDhL,EAAQiL,uBACVjL,EAAQkL,OAAO,KAAMlL,GAErBA,EAAQmL,OAAO,KAAMnL,OAElB,CACL,GAAI/oB,KAAKm0B,gBAAgBp6B,UAAUC,SAASuU,IAE1C,YADAvO,KAAKk0B,OAAO,KAAMl0B,MAIpBA,KAAKi0B,OAAO,KAAMj0B,OAItB6D,UACEyI,aAAatM,KAAKszB,UAElBhzB,EAAaC,IAAIP,KAAK2D,SAASiB,QAAQouB,IAAiBC,GAAkBjzB,KAAKo0B,mBAE3Ep0B,KAAKyzB,KACPzzB,KAAKyzB,IAAIlwB,SAGXvD,KAAKq0B,iBACL3qB,MAAM7F,UAGR2L,OACE,GAAoC,SAAhCxP,KAAK2D,SAASqM,MAAMqX,QACtB,MAAM,IAAI/iB,MAAM,uCAGlB,IAAMtE,KAAKs0B,kBAAmBt0B,KAAKqzB,WACjC,OAGF,MAAMpF,EAAY3tB,EAAamB,QAAQzB,KAAK2D,SAAU3D,KAAK0D,YAAY5L,MAAMs6B,MACvEmC,EAAap6B,EAAe6F,KAAK2D,UACjC6wB,EAA4B,OAAfD,EACjBv0B,KAAK2D,SAASsO,cAAc7X,gBAAgBJ,SAASgG,KAAK2D,UAC1D4wB,EAAWv6B,SAASgG,KAAK2D,UAE3B,GAAIsqB,EAAUlsB,mBAAqByyB,EACjC,OAK4B,YAA1Bx0B,KAAK0D,YAAYjI,MAAsBuE,KAAKyzB,KAAOzzB,KAAKy0B,aAAez0B,KAAKyzB,IAAI/7B,cAAcq7B,IAAwBrD,YACxH1vB,KAAKq0B,iBACLr0B,KAAKyzB,IAAIlwB,SACTvD,KAAKyzB,IAAM,MAGb,MAAMA,EAAMzzB,KAAKm0B,gBACXO,EvE3NKC,CAAAA,IACb,GACEA,GAAUh3B,KAAKi3B,MArBH,IAqBSj3B,KAAKk3B,gBACnBp9B,SAASq9B,eAAeH,IAEjC,OAAOA,GuEsNSI,CAAO/0B,KAAK0D,YAAYjI,MAEtCg4B,EAAIluB,aAAa,KAAMmvB,GACvB10B,KAAK2D,SAAS4B,aAAa,mBAAoBmvB,GAE3C10B,KAAKmK,QAAQylB,WACf6D,EAAI15B,UAAUyS,IAAIomB,IAGpB,MAAM3hB,EAA8C,mBAA3BjR,KAAKmK,QAAQ8G,UACpCjR,KAAKmK,QAAQ8G,UAAUhY,KAAK+G,KAAMyzB,EAAKzzB,KAAK2D,UAC5C3D,KAAKmK,QAAQ8G,UAET+jB,EAAah1B,KAAKi1B,eAAehkB,GACvCjR,KAAKk1B,oBAAoBF,GAEzB,MAAMrlB,UAAEA,GAAc3P,KAAKmK,QAC3BrH,EAAKC,IAAI0wB,EAAKzzB,KAAK0D,YAAYE,SAAU5D,MAEpCA,KAAK2D,SAASsO,cAAc7X,gBAAgBJ,SAASgG,KAAKyzB,OAC7D9jB,EAAUyb,OAAOqI,GACjBnzB,EAAamB,QAAQzB,KAAK2D,SAAU3D,KAAK0D,YAAY5L,MAAMw6B,WAGzDtyB,KAAKynB,QACPznB,KAAKynB,QAAQ7N,SAEb5Z,KAAKynB,QAAUS,GAAoBloB,KAAK2D,SAAU8vB,EAAKzzB,KAAKooB,iBAAiB4M,IAG/EvB,EAAI15B,UAAUyS,IAAI+B,IAElB,MAAMyhB,EAAchwB,KAAKm1B,yBAAyBn1B,KAAKmK,QAAQ6lB,aAC3DA,GACFyD,EAAI15B,UAAUyS,OAAOwjB,EAAY14B,MAAM,MAOrC,iBAAkBG,SAAS2C,iBAC7B,GAAGgN,UAAU3P,SAASuD,KAAKwM,UAAU7O,SAAQ3B,IAC3CsJ,EAAaQ,GAAG9J,EAAS,YAAa0D,MAI1C,MAWMwJ,EAAalE,KAAKyzB,IAAI15B,UAAUC,SAAS44B,IAC/C5yB,KAAKiE,gBAZY,KACf,MAAMmxB,EAAiBp1B,KAAKuzB,YAE5BvzB,KAAKuzB,YAAc,KACnBjzB,EAAamB,QAAQzB,KAAK2D,SAAU3D,KAAK0D,YAAY5L,MAAMu6B,OAEvD+C,IAAmBtC,IACrB9yB,KAAKk0B,OAAO,KAAMl0B,QAKQA,KAAKyzB,IAAKvvB,GAG1CqL,OACE,IAAKvP,KAAKynB,QACR,OAGF,MAAMgM,EAAMzzB,KAAKm0B,gBAkBjB,GADkB7zB,EAAamB,QAAQzB,KAAK2D,SAAU3D,KAAK0D,YAAY5L,MAAMo6B,MAC/DnwB,iBACZ,OAGF0xB,EAAI15B,UAAUwJ,OAAOgL,IAIjB,iBAAkB9W,SAAS2C,iBAC7B,GAAGgN,UAAU3P,SAASuD,KAAKwM,UACxB7O,SAAQ3B,GAAWsJ,EAAaC,IAAIvJ,EAAS,YAAa0D,KAG/DsF,KAAKwzB,eAAL,OAAqC,EACrCxzB,KAAKwzB,eAAL,OAAqC,EACrCxzB,KAAKwzB,eAAL,OAAqC,EAErC,MAAMtvB,EAAalE,KAAKyzB,IAAI15B,UAAUC,SAAS44B,IAC/C5yB,KAAKiE,gBAnCY,KACXjE,KAAKg0B,yBAILh0B,KAAKuzB,cAAgBV,IACvBY,EAAIlwB,SAGNvD,KAAKq1B,iBACLr1B,KAAK2D,SAASqC,gBAAgB,oBAC9B1F,EAAamB,QAAQzB,KAAK2D,SAAU3D,KAAK0D,YAAY5L,MAAMq6B,QAE3DnyB,KAAKq0B,oBAsBuBr0B,KAAKyzB,IAAKvvB,GACxClE,KAAKuzB,YAAc,GAGrB3Z,SACuB,OAAjB5Z,KAAKynB,SACPznB,KAAKynB,QAAQ7N,SAMjB0a,gBACE,OAAOzzB,QAAQb,KAAKy0B,YAGtBN,gBACE,GAAIn0B,KAAKyzB,IACP,OAAOzzB,KAAKyzB,IAGd,MAAMz8B,EAAUS,SAAS0zB,cAAc,OACvCn0B,EAAQ04B,UAAY1vB,KAAKmK,QAAQ0lB,SAEjC,MAAM4D,EAAMz8B,EAAQwQ,SAAS,GAK7B,OAJAxH,KAAKs1B,WAAW7B,GAChBA,EAAI15B,UAAUwJ,OAAOqvB,GAAiBrkB,IAEtCvO,KAAKyzB,IAAMA,EACJzzB,KAAKyzB,IAGd6B,WAAW7B,GACTzzB,KAAKu1B,uBAAuB9B,EAAKzzB,KAAKy0B,WAAY1B,IAGpDwC,uBAAuB1F,EAAU2F,EAASv+B,GACxC,MAAMw+B,EAAkBvuB,EAAeK,QAAQtQ,EAAU44B,GAEpD2F,IAAWC,EAMhBz1B,KAAK01B,kBAAkBD,EAAiBD,GALtCC,EAAgBlyB,SAQpBmyB,kBAAkB1+B,EAASw+B,GACzB,GAAgB,OAAZx+B,EAIJ,OAAIe,EAAUy9B,IACZA,EAAUr9B,EAAWq9B,QAGjBx1B,KAAKmK,QAAQgR,KACXqa,EAAQ/6B,aAAezD,IACzBA,EAAQ04B,UAAY,GACpB14B,EAAQo0B,OAAOoK,IAGjBx+B,EAAQ2+B,YAAcH,EAAQG,mBAM9B31B,KAAKmK,QAAQgR,MACXnb,KAAKmK,QAAQ8lB,WACfuF,EAAUxG,GAAawG,EAASx1B,KAAKmK,QAAQ+kB,UAAWlvB,KAAKmK,QAAQglB,aAGvEn4B,EAAQ04B,UAAY8F,GAEpBx+B,EAAQ2+B,YAAcH,GAI1Bf,WACE,MAAM3E,EAAQ9vB,KAAK2D,SAASzM,aAAa,2BAA6B8I,KAAKmK,QAAQ2lB,MAEnF,OAAO9vB,KAAKm1B,yBAAyBrF,GAGvC8F,iBAAiBZ,GACf,MAAmB,UAAfA,EACK,MAGU,SAAfA,EACK,QAGFA,EAKTlB,6BAA6B50B,EAAO6pB,GAClC,OAAOA,GAAW/oB,KAAK0D,YAAYmB,oBAAoB3F,EAAMa,eAAgBC,KAAK61B,sBAGpFpN,aACE,MAAMjiB,OAAEA,GAAWxG,KAAKmK,QAExB,MAAsB,iBAAX3D,EACFA,EAAOlP,MAAM,KAAK8Q,KAAI3C,GAAO/I,OAAOwQ,SAASzH,EAAK,MAGrC,mBAAXe,EACFkiB,GAAcliB,EAAOkiB,EAAY1oB,KAAK2D,UAGxC6C,EAGT2uB,yBAAyBK,GACvB,MAA0B,mBAAZA,EAAyBA,EAAQv8B,KAAK+G,KAAK2D,UAAY6xB,EAGvEpN,iBAAiB4M,GACf,MAAMrM,EAAwB,CAC5B1X,UAAW+jB,EACXtR,UAAW,CACT,CACEloB,KAAM,OACNyX,QAAS,CACP6L,mBAAoB9e,KAAKmK,QAAQ2U,qBAGrC,CACEtjB,KAAM,SACNyX,QAAS,CACPzM,OAAQxG,KAAKyoB,eAGjB,CACEjtB,KAAM,kBACNyX,QAAS,CACPmJ,SAAUpc,KAAKmK,QAAQiS,WAG3B,CACE5gB,KAAM,QACNyX,QAAS,CACPjc,QAAU,IAAGgJ,KAAK0D,YAAYjI,eAGlC,CACED,KAAM,WACN+W,SAAS,EACTC,MAAO,aACP7W,GAAIuJ,GAAQlF,KAAK81B,6BAA6B5wB,KAGlDghB,cAAehhB,IACTA,EAAK+N,QAAQhC,YAAc/L,EAAK+L,WAClCjR,KAAK81B,6BAA6B5wB,KAKxC,MAAO,IACFyjB,KACsC,mBAA9B3oB,KAAKmK,QAAQmd,aAA8BtnB,KAAKmK,QAAQmd,aAAaqB,GAAyB3oB,KAAKmK,QAAQmd,cAI1H4N,oBAAoBF,GAClBh1B,KAAKm0B,gBAAgBp6B,UAAUyS,IAAK,GAAExM,KAAK+1B,0BAA0B/1B,KAAK41B,iBAAiBZ,MAG7FC,eAAehkB,GACb,OAAOif,GAAcjf,EAAU1X,eAGjCm6B,gBACmB1zB,KAAKmK,QAAQ1I,QAAQnK,MAAM,KAEnCqB,SAAQ8I,IACf,GAAgB,UAAZA,EACFnB,EAAaQ,GAAGd,KAAK2D,SAAU3D,KAAK0D,YAAY5L,MAAMy6B,MAAOvyB,KAAKmK,QAAQlT,UAAUiI,GAASc,KAAKsF,OAAOpG,UACpG,GA/ZU,WA+ZNuC,EAA4B,CACrC,MAAMu0B,EAAUv0B,IAAYyxB,GAC1BlzB,KAAK0D,YAAY5L,MAAM46B,WACvB1yB,KAAK0D,YAAY5L,MAAM06B,QACnByD,EAAWx0B,IAAYyxB,GAC3BlzB,KAAK0D,YAAY5L,MAAM66B,WACvB3yB,KAAK0D,YAAY5L,MAAM26B,SAEzBnyB,EAAaQ,GAAGd,KAAK2D,SAAUqyB,EAASh2B,KAAKmK,QAAQlT,UAAUiI,GAASc,KAAKi0B,OAAO/0B,KACpFoB,EAAaQ,GAAGd,KAAK2D,SAAUsyB,EAAUj2B,KAAKmK,QAAQlT,UAAUiI,GAASc,KAAKk0B,OAAOh1B,SAIzFc,KAAKo0B,kBAAoB,KACnBp0B,KAAK2D,UACP3D,KAAKuP,QAITjP,EAAaQ,GAAGd,KAAK2D,SAASiB,QAAQouB,IAAiBC,GAAkBjzB,KAAKo0B,mBAE1Ep0B,KAAKmK,QAAQlT,SACf+I,KAAKmK,QAAU,IACVnK,KAAKmK,QACR1I,QAAS,SACTxK,SAAU,IAGZ+I,KAAKk2B,YAITA,YACE,MAAMpG,EAAQ9vB,KAAK2D,SAASzM,aAAa,SACnCi/B,SAA2Bn2B,KAAK2D,SAASzM,aAAa,2BAExD44B,GAA+B,WAAtBqG,KACXn2B,KAAK2D,SAAS4B,aAAa,yBAA0BuqB,GAAS,KAC1DA,GAAU9vB,KAAK2D,SAASzM,aAAa,eAAkB8I,KAAK2D,SAASgyB,aACvE31B,KAAK2D,SAAS4B,aAAa,aAAcuqB,GAG3C9vB,KAAK2D,SAAS4B,aAAa,QAAS,KAIxC0uB,OAAO/0B,EAAO6pB,GACZA,EAAU/oB,KAAK8zB,6BAA6B50B,EAAO6pB,GAE/C7pB,IACF6pB,EAAQyK,eACS,YAAft0B,EAAMsB,KAAqB2yB,GAAgBD,KACzC,GAGFnK,EAAQoL,gBAAgBp6B,UAAUC,SAASuU,KAAoBwa,EAAQwK,cAAgBV,GACzF9J,EAAQwK,YAAcV,IAIxBvmB,aAAayc,EAAQuK,UAErBvK,EAAQwK,YAAcV,GAEjB9J,EAAQ5e,QAAQ4lB,OAAUhH,EAAQ5e,QAAQ4lB,MAAMvgB,KAKrDuZ,EAAQuK,SAAWp2B,YAAW,KACxB6rB,EAAQwK,cAAgBV,IAC1B9J,EAAQvZ,SAETuZ,EAAQ5e,QAAQ4lB,MAAMvgB,MARvBuZ,EAAQvZ,QAWZ0kB,OAAOh1B,EAAO6pB,GACZA,EAAU/oB,KAAK8zB,6BAA6B50B,EAAO6pB,GAE/C7pB,IACF6pB,EAAQyK,eACS,aAAft0B,EAAMsB,KAAsB2yB,GAAgBD,IAC1CnK,EAAQplB,SAAS3J,SAASkF,EAAMY,gBAGlCipB,EAAQiL,yBAIZ1nB,aAAayc,EAAQuK,UAErBvK,EAAQwK,YAAcT,GAEjB/J,EAAQ5e,QAAQ4lB,OAAUhH,EAAQ5e,QAAQ4lB,MAAMxgB,KAKrDwZ,EAAQuK,SAAWp2B,YAAW,KACxB6rB,EAAQwK,cAAgBT,IAC1B/J,EAAQxZ,SAETwZ,EAAQ5e,QAAQ4lB,MAAMxgB,MARvBwZ,EAAQxZ,QAWZykB,uBACE,IAAK,MAAMvyB,KAAWzB,KAAKwzB,eACzB,GAAIxzB,KAAKwzB,eAAe/xB,GACtB,OAAO,EAIX,OAAO,EAGT2I,WAAW7R,GACT,MAAM69B,EAAiBvwB,EAAYI,kBAAkBjG,KAAK2D,UAqC1D,OAnCAlL,OAAOC,KAAK09B,GAAgBz9B,SAAQ09B,IAC9B1G,GAAsBjwB,IAAI22B,WACrBD,EAAeC,OAI1B99B,EAAS,IACJyH,KAAK0D,YAAY6E,WACjB6tB,KACmB,iBAAX79B,GAAuBA,EAASA,EAAS,KAG/CoX,WAAiC,IAArBpX,EAAOoX,UAAsBlY,SAASuD,KAAO7C,EAAWI,EAAOoX,WAEtD,iBAAjBpX,EAAOw3B,QAChBx3B,EAAOw3B,MAAQ,CACbvgB,KAAMjX,EAAOw3B,MACbxgB,KAAMhX,EAAOw3B,QAIW,iBAAjBx3B,EAAOu3B,QAChBv3B,EAAOu3B,MAAQv3B,EAAOu3B,MAAM92B,YAGA,iBAAnBT,EAAOi9B,UAChBj9B,EAAOi9B,QAAUj9B,EAAOi9B,QAAQx8B,YAGlCX,EAAgBoD,GAAMlD,EAAQyH,KAAK0D,YAAYoF,aAE3CvQ,EAAO03B,WACT13B,EAAOs3B,SAAWb,GAAaz2B,EAAOs3B,SAAUt3B,EAAO22B,UAAW32B,EAAO42B,aAGpE52B,EAGTs9B,qBACE,MAAMt9B,EAAS,GAEf,IAAK,MAAMiK,KAAOxC,KAAKmK,QACjBnK,KAAK0D,YAAY6E,QAAQ/F,KAASxC,KAAKmK,QAAQ3H,KACjDjK,EAAOiK,GAAOxC,KAAKmK,QAAQ3H,IAO/B,OAAOjK,EAGT88B,iBACE,MAAM5B,EAAMzzB,KAAKm0B,gBACXmC,EAAwB,IAAIl9B,OAAQ,UAAS4G,KAAK+1B,6BAA8B,KAChFQ,EAAW9C,EAAIv8B,aAAa,SAASgC,MAAMo9B,GAChC,OAAbC,GAAqBA,EAASn+B,OAAS,GACzCm+B,EAASnuB,KAAIouB,GAASA,EAAMj/B,SACzBoB,SAAQ89B,GAAUhD,EAAI15B,UAAUwJ,OAAOkzB,KAI9CV,uBACE,MAvqBiB,aA0qBnBD,6BAA6BpN,GAC3B,MAAMhW,MAAEA,GAAUgW,EAEbhW,IAIL1S,KAAKyzB,IAAM/gB,EAAMC,SAAS/B,OAC1B5Q,KAAKq1B,iBACLr1B,KAAKk1B,oBAAoBl1B,KAAKi1B,eAAeviB,EAAMzB,aAGrDojB,iBACMr0B,KAAKynB,UACPznB,KAAKynB,QAAQxB,UACbjmB,KAAKynB,QAAU,MAMGtjB,uBAAC5L,GACrB,OAAOyH,KAAKiF,MAAK,WACf,MAAMC,EAAOkuB,GAAQvuB,oBAAoB7E,KAAMzH,GAE/C,GAAsB,iBAAXA,EAAqB,CAC9B,QAA4B,IAAjB2M,EAAK3M,GACd,MAAM,IAAIe,UAAW,oBAAmBf,MAG1C2M,EAAK3M,UAab6C,EAAmBg4B,ICxuBnB,MAKM7qB,GAAU,IACX6qB,GAAQ7qB,QACX0I,UAAW,QACXzK,OAAQ,CAAC,EAAG,GACZ/E,QAAS,QACT+zB,QAAS,GACT3F,SAAU,+IAON/mB,GAAc,IACfsqB,GAAQtqB,YACX0sB,QAAS,6BAGL19B,GAAQ,CACZo6B,KAAO,kBACPC,OAAS,oBACTC,KAAO,kBACPC,MAAQ,mBACRC,SAAW,sBACXC,MAAQ,mBACRC,QAAU,qBACVC,SAAW,sBACXC,WAAa,wBACbC,WAAa,yBAYf,MAAM+D,WAAgBtD,GAGT7qB,qBACT,OAAOA,GAGE9M,kBACT,MArDS,UAwDA3D,mBACT,OAAOA,GAGEgR,yBACT,OAAOA,GAKTwrB,gBACE,OAAOt0B,KAAKy0B,YAAcz0B,KAAK22B,cAGjCrB,WAAW7B,GACTzzB,KAAKu1B,uBAAuB9B,EAAKzzB,KAAKy0B,WAnCnB,mBAoCnBz0B,KAAKu1B,uBAAuB9B,EAAKzzB,KAAK22B,cAnCjB,iBAwCvBA,cACE,OAAO32B,KAAKm1B,yBAAyBn1B,KAAKmK,QAAQqrB,SAGpDO,uBACE,MA/EiB,aAoFG5xB,uBAAC5L,GACrB,OAAOyH,KAAKiF,MAAK,WACf,MAAMC,EAAOwxB,GAAQ7xB,oBAAoB7E,KAAMzH,GAE/C,GAAsB,iBAAXA,EAAqB,CAC9B,QAA4B,IAAjB2M,EAAK3M,GACd,MAAM,IAAIe,UAAW,oBAAmBf,MAG1C2M,EAAK3M,UAab6C,EAAmBs7B,ICrGnB,MAAMj7B,GAAO,YAKP8M,GAAU,CACd/B,OAAQ,GACR/B,OAAQ,OACRzH,OAAQ,IAGJ8L,GAAc,CAClBtC,OAAQ,SACR/B,OAAQ,SACRzH,OAAQ,oBAQJuM,GAAoB,SAOpBqtB,GAAuB,8CAKvBC,GAAkB,WAQxB,MAAMC,WAAkBrzB,EACtBC,YAAY1M,EAASuB,GACnBmR,MAAM1S,GACNgJ,KAAK+2B,eAA2C,SAA1B/2B,KAAK2D,SAASgB,QAAqB5J,OAASiF,KAAK2D,SACvE3D,KAAKmK,QAAUnK,KAAKoK,WAAW7R,GAC/ByH,KAAKg3B,SAAW,GAChBh3B,KAAKi3B,SAAW,GAChBj3B,KAAKk3B,cAAgB,KACrBl3B,KAAKm3B,cAAgB,EAErB72B,EAAaQ,GAAGd,KAAK+2B,eAlCH,uBAkCiC,IAAM/2B,KAAKo3B,aAE9Dp3B,KAAKq3B,UACLr3B,KAAKo3B,WAKI7uB,qBACT,OAAOA,GAGE9M,kBACT,OAAOA,GAKT47B,UACE,MAAMC,EAAat3B,KAAK+2B,iBAAmB/2B,KAAK+2B,eAAeh8B,OAtC7C,SAwChB87B,GAEIU,EAAuC,SAAxBv3B,KAAKmK,QAAQ1F,OAChC6yB,EACAt3B,KAAKmK,QAAQ1F,OAET+yB,EAAaD,IAAiBV,GAClC72B,KAAKy3B,gBACL,EAEFz3B,KAAKg3B,SAAW,GAChBh3B,KAAKi3B,SAAW,GAChBj3B,KAAKm3B,cAAgBn3B,KAAK03B,mBAEVxwB,EAAeC,KAAKyvB,GAAqB52B,KAAKmK,QAAQnN,QAE9DoL,KAAIpR,IACV,MAAM2gC,EAAiBngC,EAAuBR,GACxCgG,EAAS26B,EAAiBzwB,EAAeK,QAAQowB,GAAkB,KAEzE,GAAI36B,EAAQ,CACV,MAAM46B,EAAY56B,EAAO0J,wBACzB,GAAIkxB,EAAUlkB,OAASkkB,EAAUjkB,OAC/B,MAAO,CACL9N,EAAY0xB,GAAcv6B,GAAQ2J,IAAM6wB,EACxCG,GAKN,OAAO,QAENvxB,QAAOyxB,GAAQA,IACf1Z,MAAK,CAACC,EAAGC,IAAMD,EAAE,GAAKC,EAAE,KACxB1lB,SAAQk/B,IACP73B,KAAKg3B,SAAS/6B,KAAK47B,EAAK,IACxB73B,KAAKi3B,SAASh7B,KAAK47B,EAAK,OAI9Bh0B,UACEvD,EAAaC,IAAIP,KAAK+2B,eAhHP,iBAiHfrtB,MAAM7F,UAKRuG,WAAW7R,GAWT,OAVAA,EAAS,IACJgQ,MACA1C,EAAYI,kBAAkBjG,KAAK2D,aAChB,iBAAXpL,GAAuBA,EAASA,EAAS,KAG/CyE,OAAS7E,EAAWI,EAAOyE,SAAWvF,SAAS2C,gBAEtD/B,EAAgBoD,GAAMlD,EAAQuQ,IAEvBvQ,EAGTk/B,gBACE,OAAOz3B,KAAK+2B,iBAAmBh8B,OAC7BiF,KAAK+2B,eAAenwB,YACpB5G,KAAK+2B,eAAe5c,UAGxBud,mBACE,OAAO13B,KAAK+2B,eAAerb,cAAgB/d,KAAKC,IAC9CnG,SAASuD,KAAK0gB,aACdjkB,SAAS2C,gBAAgBshB,cAI7Boc,mBACE,OAAO93B,KAAK+2B,iBAAmBh8B,OAC7BA,OAAOg9B,YACP/3B,KAAK+2B,eAAerwB,wBAAwBiN,OAGhDyjB,WACE,MAAMjd,EAAYna,KAAKy3B,gBAAkBz3B,KAAKmK,QAAQ3D,OAChDkV,EAAe1b,KAAK03B,mBACpBM,EAAYh4B,KAAKmK,QAAQ3D,OAASkV,EAAe1b,KAAK83B,mBAM5D,GAJI93B,KAAKm3B,gBAAkBzb,GACzB1b,KAAKq3B,UAGHld,GAAa6d,EAAjB,CACE,MAAMh7B,EAASgD,KAAKi3B,SAASj3B,KAAKi3B,SAAS7+B,OAAS,GAEhD4H,KAAKk3B,gBAAkBl6B,GACzBgD,KAAKi4B,UAAUj7B,OAJnB,CAUA,GAAIgD,KAAKk3B,eAAiB/c,EAAYna,KAAKg3B,SAAS,IAAMh3B,KAAKg3B,SAAS,GAAK,EAG3E,OAFAh3B,KAAKk3B,cAAgB,UACrBl3B,KAAKk4B,SAIP,IAAK,IAAIl5B,EAAIgB,KAAKg3B,SAAS5+B,OAAQ4G,KACVgB,KAAKk3B,gBAAkBl3B,KAAKi3B,SAASj4B,IACxDmb,GAAana,KAAKg3B,SAASh4B,UACM,IAAzBgB,KAAKg3B,SAASh4B,EAAI,IAAsBmb,EAAYna,KAAKg3B,SAASh4B,EAAI,KAGhFgB,KAAKi4B,UAAUj4B,KAAKi3B,SAASj4B,KAKnCi5B,UAAUj7B,GACRgD,KAAKk3B,cAAgBl6B,EAErBgD,KAAKk4B,SAEL,MAAMC,EAAUvB,GAAoBt/B,MAAM,KACvC8Q,KAAInR,GAAa,GAAEA,qBAA4B+F,OAAY/F,WAAkB+F,QAE1Eo7B,EAAOlxB,EAAeK,QAAQ4wB,EAAQ9vB,KAAK,KAAMrI,KAAKmK,QAAQnN,QAEpEo7B,EAAKr+B,UAAUyS,IAAIjD,IACf6uB,EAAKr+B,UAAUC,SAnLU,iBAoL3BkN,EAAeK,QA1KY,mBA0KsB6wB,EAAKxzB,QA3KlC,cA4KjB7K,UAAUyS,IAAIjD,IAEjBrC,EAAeS,QAAQywB,EAnLG,qBAoLvBz/B,SAAQ0/B,IAGPnxB,EAAeW,KAAKwwB,EAAY,+BAC7B1/B,SAAQk/B,GAAQA,EAAK99B,UAAUyS,IAAIjD,MAGtCrC,EAAeW,KAAKwwB,EAzLH,aA0Ld1/B,SAAQ2/B,IACPpxB,EAAeM,SAAS8wB,EA5LX,aA6LV3/B,SAAQk/B,GAAQA,EAAK99B,UAAUyS,IAAIjD,YAKhDjJ,EAAamB,QAAQzB,KAAK+2B,eA3MN,wBA2MsC,CACxDj3B,cAAe9C,IAInBk7B,SACEhxB,EAAeC,KAAKyvB,GAAqB52B,KAAKmK,QAAQnN,QACnDoJ,QAAO4L,GAAQA,EAAKjY,UAAUC,SAASuP,MACvC5Q,SAAQqZ,GAAQA,EAAKjY,UAAUwJ,OAAOgG,MAKrBpF,uBAAC5L,GACrB,OAAOyH,KAAKiF,MAAK,WACf,MAAMC,EAAO4xB,GAAUjyB,oBAAoB7E,KAAMzH,GAEjD,GAAsB,iBAAXA,EAAX,CAIA,QAA4B,IAAjB2M,EAAK3M,GACd,MAAM,IAAIe,UAAW,oBAAmBf,MAG1C2M,EAAK3M,UAWX+H,EAAaQ,GAAG/F,OA7Oa,8BA6OgB,KAC3CmM,EAAeC,KAzOS,0BA0OrBxO,SAAQ4/B,GAAO,IAAIzB,GAAUyB,QAUlCn9B,EAAmB07B,IC7QnB,MAYMvtB,GAAoB,SACpBqpB,GAAkB,OAClBrkB,GAAkB,OAIlBiqB,GAAkB,UAClBC,GAAqB,wBAW3B,MAAMC,WAAYj1B,EAGLhI,kBACT,MAlCS,MAuCX+T,OACE,GAAKxP,KAAK2D,SAASlJ,YACjBuF,KAAK2D,SAASlJ,WAAWvC,WAAa2B,KAAKC,cAC3CkG,KAAK2D,SAAS5J,UAAUC,SAASuP,IACjC,OAGF,IAAIzB,EACJ,MAAM9K,EAASrF,EAAuBqI,KAAK2D,UACrCg1B,EAAc34B,KAAK2D,SAASiB,QA/BN,qBAiC5B,GAAI+zB,EAAa,CACf,MAAMC,EAAwC,OAAzBD,EAAY7mB,UAA8C,OAAzB6mB,EAAY7mB,SAAoB2mB,GAAqBD,GAC3G1wB,EAAWZ,EAAeC,KAAKyxB,EAAcD,GAC7C7wB,EAAWA,EAASA,EAAS1P,OAAS,GAGxC,MAAMygC,EAAY/wB,EAChBxH,EAAamB,QAAQqG,EApDP,cAoD6B,CACzChI,cAAeE,KAAK2D,WAEtB,KAMF,GAJkBrD,EAAamB,QAAQzB,KAAK2D,SAvD5B,cAuDkD,CAChE7D,cAAegI,IAGH/F,kBAAmC,OAAd82B,GAAsBA,EAAU92B,iBACjE,OAGF/B,KAAKi4B,UAAUj4B,KAAK2D,SAAUg1B,GAE9B,MAAMG,EAAW,KACfx4B,EAAamB,QAAQqG,EAnEL,gBAmE6B,CAC3ChI,cAAeE,KAAK2D,WAEtBrD,EAAamB,QAAQzB,KAAK2D,SApEX,eAoEkC,CAC/C7D,cAAegI,KAIf9K,EACFgD,KAAKi4B,UAAUj7B,EAAQA,EAAOvC,WAAYq+B,GAE1CA,IAMJb,UAAUjhC,EAAS2Y,EAAWrU,GAC5B,MAIMy9B,IAJiBppB,GAAqC,OAAvBA,EAAUmC,UAA4C,OAAvBnC,EAAUmC,SAE5E5K,EAAeM,SAASmI,EAAW6oB,IADnCtxB,EAAeC,KAAKsxB,GAAoB9oB,IAGZ,GACxBqpB,EAAkB19B,GAAay9B,GAAUA,EAAOh/B,UAAUC,SAAS44B,IAEnEkG,EAAW,IAAM94B,KAAKi5B,oBAAoBjiC,EAAS+hC,EAAQz9B,GAE7Dy9B,GAAUC,GACZD,EAAOh/B,UAAUwJ,OAAOgL,IACxBvO,KAAKiE,eAAe60B,EAAU9hC,GAAS,IAEvC8hC,IAIJG,oBAAoBjiC,EAAS+hC,EAAQz9B,GACnC,GAAIy9B,EAAQ,CACVA,EAAOh/B,UAAUwJ,OAAOgG,IAExB,MAAM2vB,EAAgBhyB,EAAeK,QA1FJ,kCA0F4CwxB,EAAOt+B,YAEhFy+B,GACFA,EAAcn/B,UAAUwJ,OAAOgG,IAGG,QAAhCwvB,EAAO7hC,aAAa,SACtB6hC,EAAOxzB,aAAa,iBAAiB,GAIzCvO,EAAQ+C,UAAUyS,IAAIjD,IACe,QAAjCvS,EAAQE,aAAa,SACvBF,EAAQuO,aAAa,iBAAiB,GAGxC5K,EAAO3D,GAEHA,EAAQ+C,UAAUC,SAAS44B,KAC7B57B,EAAQ+C,UAAUyS,IAAI+B,IAGxB,IAAID,EAAStX,EAAQyD,WAKrB,GAJI6T,GAA8B,OAApBA,EAAOwD,WACnBxD,EAASA,EAAO7T,YAGd6T,GAAUA,EAAOvU,UAAUC,SAhIF,iBAgIsC,CACjE,MAAMm/B,EAAkBniC,EAAQ4N,QA5HZ,aA8HhBu0B,GACFjyB,EAAeC,KA1HU,mBA0HqBgyB,GAC3CxgC,SAAQygC,GAAYA,EAASr/B,UAAUyS,IAAIjD,MAGhDvS,EAAQuO,aAAa,iBAAiB,GAGpCjK,GACFA,IAMkB6I,uBAAC5L,GACrB,OAAOyH,KAAKiF,MAAK,WACf,MAAMC,EAAOwzB,GAAI7zB,oBAAoB7E,MAErC,GAAsB,iBAAXzH,EAAqB,CAC9B,QAA4B,IAAjB2M,EAAK3M,GACd,MAAM,IAAIe,UAAW,oBAAmBf,MAG1C2M,EAAK3M,UAYb+H,EAAaQ,GAAGrJ,SAzKc,wBAWD,4EA8JyC,SAAUyH,GAC1E,CAAC,IAAK,QAAQ9H,SAAS4I,KAAK2E,UAC9BzF,EAAMyD,iBAGJ/I,EAAWoG,OAIF04B,GAAI7zB,oBAAoB7E,MAChCwP,UAUPpU,EAAmBs9B,ICtMnB,MAAMj9B,GAAO,QAcP49B,GAAkB,OAClB9qB,GAAkB,OAClB+qB,GAAqB,UAErBxwB,GAAc,CAClB8mB,UAAW,UACX2J,SAAU,UACVxJ,MAAO,UAGHxnB,GAAU,CACdqnB,WAAW,EACX2J,UAAU,EACVxJ,MAAO,KAST,MAAMyJ,WAAc/1B,EAClBC,YAAY1M,EAASuB,GACnBmR,MAAM1S,GAENgJ,KAAKmK,QAAUnK,KAAKoK,WAAW7R,GAC/ByH,KAAKszB,SAAW,KAChBtzB,KAAKy5B,sBAAuB,EAC5Bz5B,KAAK05B,yBAA0B,EAC/B15B,KAAK0zB,gBAKI5qB,yBACT,OAAOA,GAGEP,qBACT,OAAOA,GAGE9M,kBACT,OAAOA,GAKT+T,OACoBlP,EAAamB,QAAQzB,KAAK2D,SAtD5B,iBAwDF5B,mBAId/B,KAAK25B,gBAED35B,KAAKmK,QAAQylB,WACf5vB,KAAK2D,SAAS5J,UAAUyS,IA5DN,QAsEpBxM,KAAK2D,SAAS5J,UAAUwJ,OAAO81B,IAC/B1+B,EAAOqF,KAAK2D,UACZ3D,KAAK2D,SAAS5J,UAAUyS,IAAI+B,IAC5BvO,KAAK2D,SAAS5J,UAAUyS,IAAI8sB,IAE5Bt5B,KAAKiE,gBAZY,KACfjE,KAAK2D,SAAS5J,UAAUwJ,OAAO+1B,IAC/Bh5B,EAAamB,QAAQzB,KAAK2D,SAnEX,kBAqEf3D,KAAK45B,uBAQuB55B,KAAK2D,SAAU3D,KAAKmK,QAAQylB,YAG5DrgB,OACOvP,KAAK2D,SAAS5J,UAAUC,SAASuU,MAIpBjO,EAAamB,QAAQzB,KAAK2D,SAxF5B,iBA0FF5B,mBAWd/B,KAAK2D,SAAS5J,UAAUyS,IAAI8sB,IAC5Bt5B,KAAKiE,gBARY,KACfjE,KAAK2D,SAAS5J,UAAUyS,IAAI6sB,IAC5Br5B,KAAK2D,SAAS5J,UAAUwJ,OAAO+1B,IAC/Bt5B,KAAK2D,SAAS5J,UAAUwJ,OAAOgL,IAC/BjO,EAAamB,QAAQzB,KAAK2D,SAjGV,qBAqGY3D,KAAK2D,SAAU3D,KAAKmK,QAAQylB,aAG5D/rB,UACE7D,KAAK25B,gBAED35B,KAAK2D,SAAS5J,UAAUC,SAASuU,KACnCvO,KAAK2D,SAAS5J,UAAUwJ,OAAOgL,IAGjC7E,MAAM7F,UAKRuG,WAAW7R,GAST,OARAA,EAAS,IACJgQ,MACA1C,EAAYI,kBAAkBjG,KAAK2D,aAChB,iBAAXpL,GAAuBA,EAASA,EAAS,IAGtDF,EAAgBoD,GAAMlD,EAAQyH,KAAK0D,YAAYoF,aAExCvQ,EAGTqhC,qBACO55B,KAAKmK,QAAQovB,WAIdv5B,KAAKy5B,sBAAwBz5B,KAAK05B,0BAItC15B,KAAKszB,SAAWp2B,YAAW,KACzB8C,KAAKuP,SACJvP,KAAKmK,QAAQ4lB,SAGlB8J,eAAe36B,EAAO46B,GACpB,OAAQ56B,EAAMsB,MACZ,IAAK,YACL,IAAK,WACHR,KAAKy5B,qBAAuBK,EAC5B,MACF,IAAK,UACL,IAAK,WACH95B,KAAK05B,wBAA0BI,EAMnC,GAAIA,EAEF,YADA95B,KAAK25B,gBAIP,MAAMnsB,EAActO,EAAMY,cACtBE,KAAK2D,WAAa6J,GAAexN,KAAK2D,SAAS3J,SAASwT,IAI5DxN,KAAK45B,qBAGPlG,gBACEpzB,EAAaQ,GAAGd,KAAK2D,SA/KA,sBA+K2BzE,GAASc,KAAK65B,eAAe36B,GAAO,KACpFoB,EAAaQ,GAAGd,KAAK2D,SA/KD,qBA+K2BzE,GAASc,KAAK65B,eAAe36B,GAAO,KACnFoB,EAAaQ,GAAGd,KAAK2D,SA/KF,oBA+K2BzE,GAASc,KAAK65B,eAAe36B,GAAO,KAClFoB,EAAaQ,GAAGd,KAAK2D,SA/KD,qBA+K2BzE,GAASc,KAAK65B,eAAe36B,GAAO,KAGrFy6B,gBACErtB,aAAatM,KAAKszB,UAClBtzB,KAAKszB,SAAW,KAKInvB,uBAAC5L,GACrB,OAAOyH,KAAKiF,MAAK,WACf,MAAMC,EAAOs0B,GAAM30B,oBAAoB7E,KAAMzH,GAE7C,GAAsB,iBAAXA,EAAqB,CAC9B,QAA4B,IAAjB2M,EAAK3M,GACd,MAAM,IAAIe,UAAW,oBAAmBf,MAG1C2M,EAAK3M,GAAQyH,kBAMrBuE,EAAqBi1B,IASrBp+B,EAAmBo+B,IC3NJ,CACb10B,MAAAA,EACAO,OAAAA,EACAoE,SAAAA,GACAmF,SAAAA,GACA4Y,SAAAA,GACAgF,MAAAA,GACA4B,UAAAA,GACAsI,QAAAA,GACAI,UAAAA,GACA4B,IAAAA,GACAc,MAAAA,GACApG,QAAAA", "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.1.3): util/index.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst MAX_UID = 1000000\nconst MILLISECONDS_MULTIPLIER = 1000\nconst TRANSITION_END = 'transitionend'\n\n// Shoutout AngusCroll (https://goo.gl/pxwQGp)\nconst toType = obj => {\n  if (obj === null || obj === undefined) {\n    return `${obj}`\n  }\n\n  return {}.toString.call(obj).match(/\\s([a-z]+)/i)[1].toLowerCase()\n}\n\n/**\n * --------------------------------------------------------------------------\n * Public Util Api\n * --------------------------------------------------------------------------\n */\n\nconst getUID = prefix => {\n  do {\n    prefix += Math.floor(Math.random() * MAX_UID)\n  } while (document.getElementById(prefix))\n\n  return prefix\n}\n\nconst getSelector = element => {\n  let selector = element.getAttribute('data-bs-target')\n\n  if (!selector || selector === '#') {\n    let hrefAttr = element.getAttribute('href')\n\n    // The only valid content that could double as a selector are IDs or classes,\n    // so everything starting with `#` or `.`. If a \"real\" URL is used as the selector,\n    // `document.querySelector` will rightfully complain it is invalid.\n    // See https://github.com/twbs/bootstrap/issues/32273\n    if (!hrefAttr || (!hrefAttr.includes('#') && !hrefAttr.startsWith('.'))) {\n      return null\n    }\n\n    // Just in case some CMS puts out a full URL with the anchor appended\n    if (hrefAttr.includes('#') && !hrefAttr.startsWith('#')) {\n      hrefAttr = `#${hrefAttr.split('#')[1]}`\n    }\n\n    selector = hrefAttr && hrefAttr !== '#' ? hrefAttr.trim() : null\n  }\n\n  return selector\n}\n\nconst getSelectorFromElement = element => {\n  const selector = getSelector(element)\n\n  if (selector) {\n    return document.querySelector(selector) ? selector : null\n  }\n\n  return null\n}\n\nconst getElementFromSelector = element => {\n  const selector = getSelector(element)\n\n  return selector ? document.querySelector(selector) : null\n}\n\nconst getTransitionDurationFromElement = element => {\n  if (!element) {\n    return 0\n  }\n\n  // Get transition-duration of the element\n  let { transitionDuration, transitionDelay } = window.getComputedStyle(element)\n\n  const floatTransitionDuration = Number.parseFloat(transitionDuration)\n  const floatTransitionDelay = Number.parseFloat(transitionDelay)\n\n  // Return 0 if element or transition duration is not found\n  if (!floatTransitionDuration && !floatTransitionDelay) {\n    return 0\n  }\n\n  // If multiple durations are defined, take the first\n  transitionDuration = transitionDuration.split(',')[0]\n  transitionDelay = transitionDelay.split(',')[0]\n\n  return (Number.parseFloat(transitionDuration) + Number.parseFloat(transitionDelay)) * MILLISECONDS_MULTIPLIER\n}\n\nconst triggerTransitionEnd = element => {\n  element.dispatchEvent(new Event(TRANSITION_END))\n}\n\nconst isElement = obj => {\n  if (!obj || typeof obj !== 'object') {\n    return false\n  }\n\n  if (typeof obj.jquery !== 'undefined') {\n    obj = obj[0]\n  }\n\n  return typeof obj.nodeType !== 'undefined'\n}\n\nconst getElement = obj => {\n  if (isElement(obj)) { // it's a jQuery object or a node element\n    return obj.jquery ? obj[0] : obj\n  }\n\n  if (typeof obj === 'string' && obj.length > 0) {\n    return document.querySelector(obj)\n  }\n\n  return null\n}\n\nconst typeCheckConfig = (componentName, config, configTypes) => {\n  Object.keys(configTypes).forEach(property => {\n    const expectedTypes = configTypes[property]\n    const value = config[property]\n    const valueType = value && isElement(value) ? 'element' : toType(value)\n\n    if (!new RegExp(expectedTypes).test(valueType)) {\n      throw new TypeError(\n        `${componentName.toUpperCase()}: Option \"${property}\" provided type \"${valueType}\" but expected type \"${expectedTypes}\".`\n      )\n    }\n  })\n}\n\nconst isVisible = element => {\n  if (!isElement(element) || element.getClientRects().length === 0) {\n    return false\n  }\n\n  return getComputedStyle(element).getPropertyValue('visibility') === 'visible'\n}\n\nconst isDisabled = element => {\n  if (!element || element.nodeType !== Node.ELEMENT_NODE) {\n    return true\n  }\n\n  if (element.classList.contains('disabled')) {\n    return true\n  }\n\n  if (typeof element.disabled !== 'undefined') {\n    return element.disabled\n  }\n\n  return element.hasAttribute('disabled') && element.getAttribute('disabled') !== 'false'\n}\n\nconst findShadowRoot = element => {\n  if (!document.documentElement.attachShadow) {\n    return null\n  }\n\n  // Can find the shadow root otherwise it'll return the document\n  if (typeof element.getRootNode === 'function') {\n    const root = element.getRootNode()\n    return root instanceof ShadowRoot ? root : null\n  }\n\n  if (element instanceof ShadowRoot) {\n    return element\n  }\n\n  // when we don't find a shadow root\n  if (!element.parentNode) {\n    return null\n  }\n\n  return findShadowRoot(element.parentNode)\n}\n\nconst noop = () => {}\n\n/**\n * Trick to restart an element's animation\n *\n * @param {HTMLElement} element\n * @return void\n *\n * @see https://www.charistheo.io/blog/2021/02/restart-a-css-animation-with-javascript/#restarting-a-css-animation\n */\nconst reflow = element => {\n  // eslint-disable-next-line no-unused-expressions\n  element.offsetHeight\n}\n\nconst getjQuery = () => {\n  const { jQuery } = window\n\n  if (jQuery && !document.body.hasAttribute('data-bs-no-jquery')) {\n    return jQuery\n  }\n\n  return null\n}\n\nconst DOMContentLoadedCallbacks = []\n\nconst onDOMContentLoaded = callback => {\n  if (document.readyState === 'loading') {\n    // add listener on the first call when the document is in loading state\n    if (!DOMContentLoadedCallbacks.length) {\n      document.addEventListener('DOMContentLoaded', () => {\n        DOMContentLoadedCallbacks.forEach(callback => callback())\n      })\n    }\n\n    DOMContentLoadedCallbacks.push(callback)\n  } else {\n    callback()\n  }\n}\n\nconst isRTL = () => document.documentElement.dir === 'rtl'\n\nconst defineJQueryPlugin = plugin => {\n  onDOMContentLoaded(() => {\n    const $ = getjQuery()\n    /* istanbul ignore if */\n    if ($) {\n      const name = plugin.NAME\n      const JQUERY_NO_CONFLICT = $.fn[name]\n      $.fn[name] = plugin.jQueryInterface\n      $.fn[name].Constructor = plugin\n      $.fn[name].noConflict = () => {\n        $.fn[name] = JQUERY_NO_CONFLICT\n        return plugin.jQueryInterface\n      }\n    }\n  })\n}\n\nconst execute = callback => {\n  if (typeof callback === 'function') {\n    callback()\n  }\n}\n\nconst executeAfterTransition = (callback, transitionElement, waitForTransition = true) => {\n  if (!waitForTransition) {\n    execute(callback)\n    return\n  }\n\n  const durationPadding = 5\n  const emulatedDuration = getTransitionDurationFromElement(transitionElement) + durationPadding\n\n  let called = false\n\n  const handler = ({ target }) => {\n    if (target !== transitionElement) {\n      return\n    }\n\n    called = true\n    transitionElement.removeEventListener(TRANSITION_END, handler)\n    execute(callback)\n  }\n\n  transitionElement.addEventListener(TRANSITION_END, handler)\n  setTimeout(() => {\n    if (!called) {\n      triggerTransitionEnd(transitionElement)\n    }\n  }, emulatedDuration)\n}\n\n/**\n * Return the previous/next element of a list.\n *\n * @param {array} list    The list of elements\n * @param activeElement   The active element\n * @param shouldGetNext   Choose to get next or previous element\n * @param isCycleAllowed\n * @return {Element|elem} The proper element\n */\nconst getNextActiveElement = (list, activeElement, shouldGetNext, isCycleAllowed) => {\n  let index = list.indexOf(activeElement)\n\n  // if the element does not exist in the list return an element depending on the direction and if cycle is allowed\n  if (index === -1) {\n    return list[!shouldGetNext && isCycleAllowed ? list.length - 1 : 0]\n  }\n\n  const listLength = list.length\n\n  index += shouldGetNext ? 1 : -1\n\n  if (isCycleAllowed) {\n    index = (index + listLength) % listLength\n  }\n\n  return list[Math.max(0, Math.min(index, listLength - 1))]\n}\n\nexport {\n  getElement,\n  getUID,\n  getSelectorFromElement,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  triggerTransitionEnd,\n  isElement,\n  typeCheckConfig,\n  isVisible,\n  isDisabled,\n  findShadowRoot,\n  noop,\n  getNextActiveElement,\n  reflow,\n  getjQuery,\n  onDOMContentLoaded,\n  isRTL,\n  defineJQueryPlugin,\n  execute,\n  executeAfterTransition\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.1.3): dom/event-handler.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { getjQuery } from '../util/index'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst namespaceRegex = /[^.]*(?=\\..*)\\.|.*/\nconst stripNameRegex = /\\..*/\nconst stripUidRegex = /::\\d+$/\nconst eventRegistry = {} // Events storage\nlet uidEvent = 1\nconst customEvents = {\n  mouseenter: 'mouseover',\n  mouseleave: 'mouseout'\n}\nconst customEventsRegex = /^(mouseenter|mouseleave)/i\nconst nativeEvents = new Set([\n  'click',\n  'dblclick',\n  'mouseup',\n  'mousedown',\n  'contextmenu',\n  'mousewheel',\n  'DOMMouseScroll',\n  'mouseover',\n  'mouseout',\n  'mousemove',\n  'selectstart',\n  'selectend',\n  'keydown',\n  'keypress',\n  'keyup',\n  'orientationchange',\n  'touchstart',\n  'touchmove',\n  'touchend',\n  'touchcancel',\n  'pointerdown',\n  'pointermove',\n  'pointerup',\n  'pointerleave',\n  'pointercancel',\n  'gesturestart',\n  'gesturechange',\n  'gestureend',\n  'focus',\n  'blur',\n  'change',\n  'reset',\n  'select',\n  'submit',\n  'focusin',\n  'focusout',\n  'load',\n  'unload',\n  'beforeunload',\n  'resize',\n  'move',\n  'DOMContentLoaded',\n  'readystatechange',\n  'error',\n  'abort',\n  'scroll'\n])\n\n/**\n * ------------------------------------------------------------------------\n * Private methods\n * ------------------------------------------------------------------------\n */\n\nfunction getUidEvent(element, uid) {\n  return (uid && `${uid}::${uidEvent++}`) || element.uidEvent || uidEvent++\n}\n\nfunction getEvent(element) {\n  const uid = getUidEvent(element)\n\n  element.uidEvent = uid\n  eventRegistry[uid] = eventRegistry[uid] || {}\n\n  return eventRegistry[uid]\n}\n\nfunction bootstrapHandler(element, fn) {\n  return function handler(event) {\n    event.delegateTarget = element\n\n    if (handler.oneOff) {\n      EventHandler.off(element, event.type, fn)\n    }\n\n    return fn.apply(element, [event])\n  }\n}\n\nfunction bootstrapDelegationHandler(element, selector, fn) {\n  return function handler(event) {\n    const domElements = element.querySelectorAll(selector)\n\n    for (let { target } = event; target && target !== this; target = target.parentNode) {\n      for (let i = domElements.length; i--;) {\n        if (domElements[i] === target) {\n          event.delegateTarget = target\n\n          if (handler.oneOff) {\n            EventHandler.off(element, event.type, selector, fn)\n          }\n\n          return fn.apply(target, [event])\n        }\n      }\n    }\n\n    // To please ESLint\n    return null\n  }\n}\n\nfunction findHandler(events, handler, delegationSelector = null) {\n  const uidEventList = Object.keys(events)\n\n  for (let i = 0, len = uidEventList.length; i < len; i++) {\n    const event = events[uidEventList[i]]\n\n    if (event.originalHandler === handler && event.delegationSelector === delegationSelector) {\n      return event\n    }\n  }\n\n  return null\n}\n\nfunction normalizeParams(originalTypeEvent, handler, delegationFn) {\n  const delegation = typeof handler === 'string'\n  const originalHandler = delegation ? delegationFn : handler\n\n  let typeEvent = getTypeEvent(originalTypeEvent)\n  const isNative = nativeEvents.has(typeEvent)\n\n  if (!isNative) {\n    typeEvent = originalTypeEvent\n  }\n\n  return [delegation, originalHandler, typeEvent]\n}\n\nfunction addHandler(element, originalTypeEvent, handler, delegationFn, oneOff) {\n  if (typeof originalTypeEvent !== 'string' || !element) {\n    return\n  }\n\n  if (!handler) {\n    handler = delegationFn\n    delegationFn = null\n  }\n\n  // in case of mouseenter or mouseleave wrap the handler within a function that checks for its DOM position\n  // this prevents the handler from being dispatched the same way as mouseover or mouseout does\n  if (customEventsRegex.test(originalTypeEvent)) {\n    const wrapFn = fn => {\n      return function (event) {\n        if (!event.relatedTarget || (event.relatedTarget !== event.delegateTarget && !event.delegateTarget.contains(event.relatedTarget))) {\n          return fn.call(this, event)\n        }\n      }\n    }\n\n    if (delegationFn) {\n      delegationFn = wrapFn(delegationFn)\n    } else {\n      handler = wrapFn(handler)\n    }\n  }\n\n  const [delegation, originalHandler, typeEvent] = normalizeParams(originalTypeEvent, handler, delegationFn)\n  const events = getEvent(element)\n  const handlers = events[typeEvent] || (events[typeEvent] = {})\n  const previousFn = findHandler(handlers, originalHandler, delegation ? handler : null)\n\n  if (previousFn) {\n    previousFn.oneOff = previousFn.oneOff && oneOff\n\n    return\n  }\n\n  const uid = getUidEvent(originalHandler, originalTypeEvent.replace(namespaceRegex, ''))\n  const fn = delegation ?\n    bootstrapDelegationHandler(element, handler, delegationFn) :\n    bootstrapHandler(element, handler)\n\n  fn.delegationSelector = delegation ? handler : null\n  fn.originalHandler = originalHandler\n  fn.oneOff = oneOff\n  fn.uidEvent = uid\n  handlers[uid] = fn\n\n  element.addEventListener(typeEvent, fn, delegation)\n}\n\nfunction removeHandler(element, events, typeEvent, handler, delegationSelector) {\n  const fn = findHandler(events[typeEvent], handler, delegationSelector)\n\n  if (!fn) {\n    return\n  }\n\n  element.removeEventListener(typeEvent, fn, Boolean(delegationSelector))\n  delete events[typeEvent][fn.uidEvent]\n}\n\nfunction removeNamespacedHandlers(element, events, typeEvent, namespace) {\n  const storeElementEvent = events[typeEvent] || {}\n\n  Object.keys(storeElementEvent).forEach(handlerKey => {\n    if (handlerKey.includes(namespace)) {\n      const event = storeElementEvent[handlerKey]\n\n      removeHandler(element, events, typeEvent, event.originalHandler, event.delegationSelector)\n    }\n  })\n}\n\nfunction getTypeEvent(event) {\n  // allow to get the native events from namespaced events ('click.bs.button' --> 'click')\n  event = event.replace(stripNameRegex, '')\n  return customEvents[event] || event\n}\n\nconst EventHandler = {\n  on(element, event, handler, delegationFn) {\n    addHandler(element, event, handler, delegationFn, false)\n  },\n\n  one(element, event, handler, delegationFn) {\n    addHandler(element, event, handler, delegationFn, true)\n  },\n\n  off(element, originalTypeEvent, handler, delegationFn) {\n    if (typeof originalTypeEvent !== 'string' || !element) {\n      return\n    }\n\n    const [delegation, originalHandler, typeEvent] = normalizeParams(originalTypeEvent, handler, delegationFn)\n    const inNamespace = typeEvent !== originalTypeEvent\n    const events = getEvent(element)\n    const isNamespace = originalTypeEvent.startsWith('.')\n\n    if (typeof originalHandler !== 'undefined') {\n      // Simplest case: handler is passed, remove that listener ONLY.\n      if (!events || !events[typeEvent]) {\n        return\n      }\n\n      removeHandler(element, events, typeEvent, originalHandler, delegation ? handler : null)\n      return\n    }\n\n    if (isNamespace) {\n      Object.keys(events).forEach(elementEvent => {\n        removeNamespacedHandlers(element, events, elementEvent, originalTypeEvent.slice(1))\n      })\n    }\n\n    const storeElementEvent = events[typeEvent] || {}\n    Object.keys(storeElementEvent).forEach(keyHandlers => {\n      const handlerKey = keyHandlers.replace(stripUidRegex, '')\n\n      if (!inNamespace || originalTypeEvent.includes(handlerKey)) {\n        const event = storeElementEvent[keyHandlers]\n\n        removeHandler(element, events, typeEvent, event.originalHandler, event.delegationSelector)\n      }\n    })\n  },\n\n  trigger(element, event, args) {\n    if (typeof event !== 'string' || !element) {\n      return null\n    }\n\n    const $ = getjQuery()\n    const typeEvent = getTypeEvent(event)\n    const inNamespace = event !== typeEvent\n    const isNative = nativeEvents.has(typeEvent)\n\n    let jQueryEvent\n    let bubbles = true\n    let nativeDispatch = true\n    let defaultPrevented = false\n    let evt = null\n\n    if (inNamespace && $) {\n      jQueryEvent = $.Event(event, args)\n\n      $(element).trigger(jQueryEvent)\n      bubbles = !jQueryEvent.isPropagationStopped()\n      nativeDispatch = !jQueryEvent.isImmediatePropagationStopped()\n      defaultPrevented = jQueryEvent.isDefaultPrevented()\n    }\n\n    if (isNative) {\n      evt = document.createEvent('HTMLEvents')\n      evt.initEvent(typeEvent, bubbles, true)\n    } else {\n      evt = new CustomEvent(event, {\n        bubbles,\n        cancelable: true\n      })\n    }\n\n    // merge custom information in our event\n    if (typeof args !== 'undefined') {\n      Object.keys(args).forEach(key => {\n        Object.defineProperty(evt, key, {\n          get() {\n            return args[key]\n          }\n        })\n      })\n    }\n\n    if (defaultPrevented) {\n      evt.preventDefault()\n    }\n\n    if (nativeDispatch) {\n      element.dispatchEvent(evt)\n    }\n\n    if (evt.defaultPrevented && typeof jQueryEvent !== 'undefined') {\n      jQueryEvent.preventDefault()\n    }\n\n    return evt\n  }\n}\n\nexport default EventHandler\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.1.3): dom/data.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst elementMap = new Map()\n\nexport default {\n  set(element, key, instance) {\n    if (!elementMap.has(element)) {\n      elementMap.set(element, new Map())\n    }\n\n    const instanceMap = elementMap.get(element)\n\n    // make it clear we only want one instance per element\n    // can be removed later when multiple key/instances are fine to be used\n    if (!instanceMap.has(key) && instanceMap.size !== 0) {\n      // eslint-disable-next-line no-console\n      console.error(`Bootstrap doesn't allow more than one instance per element. Bound instance: ${Array.from(instanceMap.keys())[0]}.`)\n      return\n    }\n\n    instanceMap.set(key, instance)\n  },\n\n  get(element, key) {\n    if (elementMap.has(element)) {\n      return elementMap.get(element).get(key) || null\n    }\n\n    return null\n  },\n\n  remove(element, key) {\n    if (!elementMap.has(element)) {\n      return\n    }\n\n    const instanceMap = elementMap.get(element)\n\n    instanceMap.delete(key)\n\n    // free up element references if there are no instances left for an element\n    if (instanceMap.size === 0) {\n      elementMap.delete(element)\n    }\n  }\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.1.3): base-component.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport Data from './dom/data'\nimport {\n  executeAfterTransition,\n  getElement\n} from './util/index'\nimport EventHandler from './dom/event-handler'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst VERSION = '5.1.3'\n\nclass BaseComponent {\n  constructor(element) {\n    element = getElement(element)\n\n    if (!element) {\n      return\n    }\n\n    this._element = element\n    Data.set(this._element, this.constructor.DATA_KEY, this)\n  }\n\n  dispose() {\n    Data.remove(this._element, this.constructor.DATA_KEY)\n    EventHandler.off(this._element, this.constructor.EVENT_KEY)\n\n    Object.getOwnPropertyNames(this).forEach(propertyName => {\n      this[propertyName] = null\n    })\n  }\n\n  _queueCallback(callback, element, isAnimated = true) {\n    executeAfterTransition(callback, element, isAnimated)\n  }\n\n  /** Static */\n\n  static getInstance(element) {\n    return Data.get(getElement(element), this.DATA_KEY)\n  }\n\n  static getOrCreateInstance(element, config = {}) {\n    return this.getInstance(element) || new this(element, typeof config === 'object' ? config : null)\n  }\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get NAME() {\n    throw new Error('You have to implement the static method \"NAME\", for each component!')\n  }\n\n  static get DATA_KEY() {\n    return `bs.${this.NAME}`\n  }\n\n  static get EVENT_KEY() {\n    return `.${this.DATA_KEY}`\n  }\n}\n\nexport default BaseComponent\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.1.3): util/component-functions.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport EventHandler from '../dom/event-handler'\nimport { getElementFromSelector, isDisabled } from './index'\n\nconst enableDismissTrigger = (component, method = 'hide') => {\n  const clickEvent = `click.dismiss${component.EVENT_KEY}`\n  const name = component.NAME\n\n  EventHandler.on(document, clickEvent, `[data-bs-dismiss=\"${name}\"]`, function (event) {\n    if (['A', 'AREA'].includes(this.tagName)) {\n      event.preventDefault()\n    }\n\n    if (isDisabled(this)) {\n      return\n    }\n\n    const target = getElementFromSelector(this) || this.closest(`.${name}`)\n    const instance = component.getOrCreateInstance(target)\n\n    // Method argument is left, for Alert and only, as it doesn't implement the 'hide' method\n    instance[method]()\n  })\n}\n\nexport {\n  enableDismissTrigger\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.1.3): alert.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { defineJQueryPlugin } from './util/index'\nimport EventHandler from './dom/event-handler'\nimport BaseComponent from './base-component'\nimport { enableDismissTrigger } from './util/component-functions'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'alert'\nconst DATA_KEY = 'bs.alert'\nconst EVENT_KEY = `.${DATA_KEY}`\n\nconst EVENT_CLOSE = `close${EVENT_KEY}`\nconst EVENT_CLOSED = `closed${EVENT_KEY}`\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Alert extends BaseComponent {\n  // Getters\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n\n  close() {\n    const closeEvent = EventHandler.trigger(this._element, EVENT_CLOSE)\n\n    if (closeEvent.defaultPrevented) {\n      return\n    }\n\n    this._element.classList.remove(CLASS_NAME_SHOW)\n\n    const isAnimated = this._element.classList.contains(CLASS_NAME_FADE)\n    this._queueCallback(() => this._destroyElement(), this._element, isAnimated)\n  }\n\n  // Private\n  _destroyElement() {\n    this._element.remove()\n    EventHandler.trigger(this._element, EVENT_CLOSED)\n    this.dispose()\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Alert.getOrCreateInstance(this)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (data[config] === undefined || config.startsWith('_') || config === 'constructor') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config](this)\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nenableDismissTrigger(Alert, 'close')\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Alert to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(Alert)\n\nexport default Alert\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.1.3): button.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { defineJQueryPlugin } from './util/index'\nimport EventHandler from './dom/event-handler'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'button'\nconst DATA_KEY = 'bs.button'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst CLASS_NAME_ACTIVE = 'active'\n\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"button\"]'\n\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Button extends BaseComponent {\n  // Getters\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n\n  toggle() {\n    // Toggle class and sync the `aria-pressed` attribute with the return value of the `.toggle()` method\n    this._element.setAttribute('aria-pressed', this._element.classList.toggle(CLASS_NAME_ACTIVE))\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Button.getOrCreateInstance(this)\n\n      if (config === 'toggle') {\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, event => {\n  event.preventDefault()\n\n  const button = event.target.closest(SELECTOR_DATA_TOGGLE)\n  const data = Button.getOrCreateInstance(button)\n\n  data.toggle()\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Button to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(Button)\n\nexport default Button\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.1.3): dom/manipulator.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nfunction normalizeData(val) {\n  if (val === 'true') {\n    return true\n  }\n\n  if (val === 'false') {\n    return false\n  }\n\n  if (val === Number(val).toString()) {\n    return Number(val)\n  }\n\n  if (val === '' || val === 'null') {\n    return null\n  }\n\n  return val\n}\n\nfunction normalizeDataKey(key) {\n  return key.replace(/[A-Z]/g, chr => `-${chr.toL<PERSON>er<PERSON>ase()}`)\n}\n\nconst Manipulator = {\n  setDataAttribute(element, key, value) {\n    element.setAttribute(`data-bs-${normalizeDataKey(key)}`, value)\n  },\n\n  removeDataAttribute(element, key) {\n    element.removeAttribute(`data-bs-${normalizeDataKey(key)}`)\n  },\n\n  getDataAttributes(element) {\n    if (!element) {\n      return {}\n    }\n\n    const attributes = {}\n\n    Object.keys(element.dataset)\n      .filter(key => key.startsWith('bs'))\n      .forEach(key => {\n        let pureKey = key.replace(/^bs/, '')\n        pureKey = pureKey.charAt(0).toLowerCase() + pureKey.slice(1, pureKey.length)\n        attributes[pureKey] = normalizeData(element.dataset[key])\n      })\n\n    return attributes\n  },\n\n  getDataAttribute(element, key) {\n    return normalizeData(element.getAttribute(`data-bs-${normalizeDataKey(key)}`))\n  },\n\n  offset(element) {\n    const rect = element.getBoundingClientRect()\n\n    return {\n      top: rect.top + window.pageYOffset,\n      left: rect.left + window.pageXOffset\n    }\n  },\n\n  position(element) {\n    return {\n      top: element.offsetTop,\n      left: element.offsetLeft\n    }\n  }\n}\n\nexport default Manipulator\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.1.3): dom/selector-engine.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nimport { isDisabled, isVisible } from '../util/index'\n\nconst NODE_TEXT = 3\n\nconst SelectorEngine = {\n  find(selector, element = document.documentElement) {\n    return [].concat(...Element.prototype.querySelectorAll.call(element, selector))\n  },\n\n  findOne(selector, element = document.documentElement) {\n    return Element.prototype.querySelector.call(element, selector)\n  },\n\n  children(element, selector) {\n    return [].concat(...element.children)\n      .filter(child => child.matches(selector))\n  },\n\n  parents(element, selector) {\n    const parents = []\n\n    let ancestor = element.parentNode\n\n    while (ancestor && ancestor.nodeType === Node.ELEMENT_NODE && ancestor.nodeType !== NODE_TEXT) {\n      if (ancestor.matches(selector)) {\n        parents.push(ancestor)\n      }\n\n      ancestor = ancestor.parentNode\n    }\n\n    return parents\n  },\n\n  prev(element, selector) {\n    let previous = element.previousElementSibling\n\n    while (previous) {\n      if (previous.matches(selector)) {\n        return [previous]\n      }\n\n      previous = previous.previousElementSibling\n    }\n\n    return []\n  },\n\n  next(element, selector) {\n    let next = element.nextElementSibling\n\n    while (next) {\n      if (next.matches(selector)) {\n        return [next]\n      }\n\n      next = next.nextElementSibling\n    }\n\n    return []\n  },\n\n  focusableChildren(element) {\n    const focusables = [\n      'a',\n      'button',\n      'input',\n      'textarea',\n      'select',\n      'details',\n      '[tabindex]',\n      '[contenteditable=\"true\"]'\n    ].map(selector => `${selector}:not([tabindex^=\"-\"])`).join(', ')\n\n    return this.find(focusables, element).filter(el => !isDisabled(el) && isVisible(el))\n  }\n}\n\nexport default SelectorEngine\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.1.3): carousel.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  getElementFromSelector,\n  isRTL,\n  isVisible,\n  getNextActiveElement,\n  reflow,\n  triggerTransitionEnd,\n  typeCheckConfig\n} from './util/index'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'carousel'\nconst DATA_KEY = 'bs.carousel'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst ARROW_LEFT_KEY = 'ArrowLeft'\nconst ARROW_RIGHT_KEY = 'ArrowRight'\nconst TOUCHEVENT_COMPAT_WAIT = 500 // Time for mouse compat events to fire after touch\nconst SWIPE_THRESHOLD = 40\n\nconst Default = {\n  interval: 5000,\n  keyboard: true,\n  slide: false,\n  pause: 'hover',\n  wrap: true,\n  touch: true\n}\n\nconst DefaultType = {\n  interval: '(number|boolean)',\n  keyboard: 'boolean',\n  slide: '(boolean|string)',\n  pause: '(string|boolean)',\n  wrap: 'boolean',\n  touch: 'boolean'\n}\n\nconst ORDER_NEXT = 'next'\nconst ORDER_PREV = 'prev'\nconst DIRECTION_LEFT = 'left'\nconst DIRECTION_RIGHT = 'right'\n\nconst KEY_TO_DIRECTION = {\n  [ARROW_LEFT_KEY]: DIRECTION_RIGHT,\n  [ARROW_RIGHT_KEY]: DIRECTION_LEFT\n}\n\nconst EVENT_SLIDE = `slide${EVENT_KEY}`\nconst EVENT_SLID = `slid${EVENT_KEY}`\nconst EVENT_KEYDOWN = `keydown${EVENT_KEY}`\nconst EVENT_MOUSEENTER = `mouseenter${EVENT_KEY}`\nconst EVENT_MOUSELEAVE = `mouseleave${EVENT_KEY}`\nconst EVENT_TOUCHSTART = `touchstart${EVENT_KEY}`\nconst EVENT_TOUCHMOVE = `touchmove${EVENT_KEY}`\nconst EVENT_TOUCHEND = `touchend${EVENT_KEY}`\nconst EVENT_POINTERDOWN = `pointerdown${EVENT_KEY}`\nconst EVENT_POINTERUP = `pointerup${EVENT_KEY}`\nconst EVENT_DRAG_START = `dragstart${EVENT_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_CAROUSEL = 'carousel'\nconst CLASS_NAME_ACTIVE = 'active'\nconst CLASS_NAME_SLIDE = 'slide'\nconst CLASS_NAME_END = 'carousel-item-end'\nconst CLASS_NAME_START = 'carousel-item-start'\nconst CLASS_NAME_NEXT = 'carousel-item-next'\nconst CLASS_NAME_PREV = 'carousel-item-prev'\nconst CLASS_NAME_POINTER_EVENT = 'pointer-event'\n\nconst SELECTOR_ACTIVE = '.active'\nconst SELECTOR_ACTIVE_ITEM = '.active.carousel-item'\nconst SELECTOR_ITEM = '.carousel-item'\nconst SELECTOR_ITEM_IMG = '.carousel-item img'\nconst SELECTOR_NEXT_PREV = '.carousel-item-next, .carousel-item-prev'\nconst SELECTOR_INDICATORS = '.carousel-indicators'\nconst SELECTOR_INDICATOR = '[data-bs-target]'\nconst SELECTOR_DATA_SLIDE = '[data-bs-slide], [data-bs-slide-to]'\nconst SELECTOR_DATA_RIDE = '[data-bs-ride=\"carousel\"]'\n\nconst POINTER_TYPE_TOUCH = 'touch'\nconst POINTER_TYPE_PEN = 'pen'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\nclass Carousel extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n\n    this._items = null\n    this._interval = null\n    this._activeElement = null\n    this._isPaused = false\n    this._isSliding = false\n    this.touchTimeout = null\n    this.touchStartX = 0\n    this.touchDeltaX = 0\n\n    this._config = this._getConfig(config)\n    this._indicatorsElement = SelectorEngine.findOne(SELECTOR_INDICATORS, this._element)\n    this._touchSupported = 'ontouchstart' in document.documentElement || navigator.maxTouchPoints > 0\n    this._pointerEvent = Boolean(window.PointerEvent)\n\n    this._addEventListeners()\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n\n  next() {\n    this._slide(ORDER_NEXT)\n  }\n\n  nextWhenVisible() {\n    // Don't call next when the page isn't visible\n    // or the carousel or its parent isn't visible\n    if (!document.hidden && isVisible(this._element)) {\n      this.next()\n    }\n  }\n\n  prev() {\n    this._slide(ORDER_PREV)\n  }\n\n  pause(event) {\n    if (!event) {\n      this._isPaused = true\n    }\n\n    if (SelectorEngine.findOne(SELECTOR_NEXT_PREV, this._element)) {\n      triggerTransitionEnd(this._element)\n      this.cycle(true)\n    }\n\n    clearInterval(this._interval)\n    this._interval = null\n  }\n\n  cycle(event) {\n    if (!event) {\n      this._isPaused = false\n    }\n\n    if (this._interval) {\n      clearInterval(this._interval)\n      this._interval = null\n    }\n\n    if (this._config && this._config.interval && !this._isPaused) {\n      this._updateInterval()\n\n      this._interval = setInterval(\n        (document.visibilityState ? this.nextWhenVisible : this.next).bind(this),\n        this._config.interval\n      )\n    }\n  }\n\n  to(index) {\n    this._activeElement = SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element)\n    const activeIndex = this._getItemIndex(this._activeElement)\n\n    if (index > this._items.length - 1 || index < 0) {\n      return\n    }\n\n    if (this._isSliding) {\n      EventHandler.one(this._element, EVENT_SLID, () => this.to(index))\n      return\n    }\n\n    if (activeIndex === index) {\n      this.pause()\n      this.cycle()\n      return\n    }\n\n    const order = index > activeIndex ?\n      ORDER_NEXT :\n      ORDER_PREV\n\n    this._slide(order, this._items[index])\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(this._element),\n      ...(typeof config === 'object' ? config : {})\n    }\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _handleSwipe() {\n    const absDeltax = Math.abs(this.touchDeltaX)\n\n    if (absDeltax <= SWIPE_THRESHOLD) {\n      return\n    }\n\n    const direction = absDeltax / this.touchDeltaX\n\n    this.touchDeltaX = 0\n\n    if (!direction) {\n      return\n    }\n\n    this._slide(direction > 0 ? DIRECTION_RIGHT : DIRECTION_LEFT)\n  }\n\n  _addEventListeners() {\n    if (this._config.keyboard) {\n      EventHandler.on(this._element, EVENT_KEYDOWN, event => this._keydown(event))\n    }\n\n    if (this._config.pause === 'hover') {\n      EventHandler.on(this._element, EVENT_MOUSEENTER, event => this.pause(event))\n      EventHandler.on(this._element, EVENT_MOUSELEAVE, event => this.cycle(event))\n    }\n\n    if (this._config.touch && this._touchSupported) {\n      this._addTouchEventListeners()\n    }\n  }\n\n  _addTouchEventListeners() {\n    const hasPointerPenTouch = event => {\n      return this._pointerEvent &&\n        (event.pointerType === POINTER_TYPE_PEN || event.pointerType === POINTER_TYPE_TOUCH)\n    }\n\n    const start = event => {\n      if (hasPointerPenTouch(event)) {\n        this.touchStartX = event.clientX\n      } else if (!this._pointerEvent) {\n        this.touchStartX = event.touches[0].clientX\n      }\n    }\n\n    const move = event => {\n      // ensure swiping with one touch and not pinching\n      this.touchDeltaX = event.touches && event.touches.length > 1 ?\n        0 :\n        event.touches[0].clientX - this.touchStartX\n    }\n\n    const end = event => {\n      if (hasPointerPenTouch(event)) {\n        this.touchDeltaX = event.clientX - this.touchStartX\n      }\n\n      this._handleSwipe()\n      if (this._config.pause === 'hover') {\n        // If it's a touch-enabled device, mouseenter/leave are fired as\n        // part of the mouse compatibility events on first tap - the carousel\n        // would stop cycling until user tapped out of it;\n        // here, we listen for touchend, explicitly pause the carousel\n        // (as if it's the second time we tap on it, mouseenter compat event\n        // is NOT fired) and after a timeout (to allow for mouse compatibility\n        // events to fire) we explicitly restart cycling\n\n        this.pause()\n        if (this.touchTimeout) {\n          clearTimeout(this.touchTimeout)\n        }\n\n        this.touchTimeout = setTimeout(event => this.cycle(event), TOUCHEVENT_COMPAT_WAIT + this._config.interval)\n      }\n    }\n\n    SelectorEngine.find(SELECTOR_ITEM_IMG, this._element).forEach(itemImg => {\n      EventHandler.on(itemImg, EVENT_DRAG_START, event => event.preventDefault())\n    })\n\n    if (this._pointerEvent) {\n      EventHandler.on(this._element, EVENT_POINTERDOWN, event => start(event))\n      EventHandler.on(this._element, EVENT_POINTERUP, event => end(event))\n\n      this._element.classList.add(CLASS_NAME_POINTER_EVENT)\n    } else {\n      EventHandler.on(this._element, EVENT_TOUCHSTART, event => start(event))\n      EventHandler.on(this._element, EVENT_TOUCHMOVE, event => move(event))\n      EventHandler.on(this._element, EVENT_TOUCHEND, event => end(event))\n    }\n  }\n\n  _keydown(event) {\n    if (/input|textarea/i.test(event.target.tagName)) {\n      return\n    }\n\n    const direction = KEY_TO_DIRECTION[event.key]\n    if (direction) {\n      event.preventDefault()\n      this._slide(direction)\n    }\n  }\n\n  _getItemIndex(element) {\n    this._items = element && element.parentNode ?\n      SelectorEngine.find(SELECTOR_ITEM, element.parentNode) :\n      []\n\n    return this._items.indexOf(element)\n  }\n\n  _getItemByOrder(order, activeElement) {\n    const isNext = order === ORDER_NEXT\n    return getNextActiveElement(this._items, activeElement, isNext, this._config.wrap)\n  }\n\n  _triggerSlideEvent(relatedTarget, eventDirectionName) {\n    const targetIndex = this._getItemIndex(relatedTarget)\n    const fromIndex = this._getItemIndex(SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element))\n\n    return EventHandler.trigger(this._element, EVENT_SLIDE, {\n      relatedTarget,\n      direction: eventDirectionName,\n      from: fromIndex,\n      to: targetIndex\n    })\n  }\n\n  _setActiveIndicatorElement(element) {\n    if (this._indicatorsElement) {\n      const activeIndicator = SelectorEngine.findOne(SELECTOR_ACTIVE, this._indicatorsElement)\n\n      activeIndicator.classList.remove(CLASS_NAME_ACTIVE)\n      activeIndicator.removeAttribute('aria-current')\n\n      const indicators = SelectorEngine.find(SELECTOR_INDICATOR, this._indicatorsElement)\n\n      for (let i = 0; i < indicators.length; i++) {\n        if (Number.parseInt(indicators[i].getAttribute('data-bs-slide-to'), 10) === this._getItemIndex(element)) {\n          indicators[i].classList.add(CLASS_NAME_ACTIVE)\n          indicators[i].setAttribute('aria-current', 'true')\n          break\n        }\n      }\n    }\n  }\n\n  _updateInterval() {\n    const element = this._activeElement || SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element)\n\n    if (!element) {\n      return\n    }\n\n    const elementInterval = Number.parseInt(element.getAttribute('data-bs-interval'), 10)\n\n    if (elementInterval) {\n      this._config.defaultInterval = this._config.defaultInterval || this._config.interval\n      this._config.interval = elementInterval\n    } else {\n      this._config.interval = this._config.defaultInterval || this._config.interval\n    }\n  }\n\n  _slide(directionOrOrder, element) {\n    const order = this._directionToOrder(directionOrOrder)\n    const activeElement = SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element)\n    const activeElementIndex = this._getItemIndex(activeElement)\n    const nextElement = element || this._getItemByOrder(order, activeElement)\n\n    const nextElementIndex = this._getItemIndex(nextElement)\n    const isCycling = Boolean(this._interval)\n\n    const isNext = order === ORDER_NEXT\n    const directionalClassName = isNext ? CLASS_NAME_START : CLASS_NAME_END\n    const orderClassName = isNext ? CLASS_NAME_NEXT : CLASS_NAME_PREV\n    const eventDirectionName = this._orderToDirection(order)\n\n    if (nextElement && nextElement.classList.contains(CLASS_NAME_ACTIVE)) {\n      this._isSliding = false\n      return\n    }\n\n    if (this._isSliding) {\n      return\n    }\n\n    const slideEvent = this._triggerSlideEvent(nextElement, eventDirectionName)\n    if (slideEvent.defaultPrevented) {\n      return\n    }\n\n    if (!activeElement || !nextElement) {\n      // Some weirdness is happening, so we bail\n      return\n    }\n\n    this._isSliding = true\n\n    if (isCycling) {\n      this.pause()\n    }\n\n    this._setActiveIndicatorElement(nextElement)\n    this._activeElement = nextElement\n\n    const triggerSlidEvent = () => {\n      EventHandler.trigger(this._element, EVENT_SLID, {\n        relatedTarget: nextElement,\n        direction: eventDirectionName,\n        from: activeElementIndex,\n        to: nextElementIndex\n      })\n    }\n\n    if (this._element.classList.contains(CLASS_NAME_SLIDE)) {\n      nextElement.classList.add(orderClassName)\n\n      reflow(nextElement)\n\n      activeElement.classList.add(directionalClassName)\n      nextElement.classList.add(directionalClassName)\n\n      const completeCallBack = () => {\n        nextElement.classList.remove(directionalClassName, orderClassName)\n        nextElement.classList.add(CLASS_NAME_ACTIVE)\n\n        activeElement.classList.remove(CLASS_NAME_ACTIVE, orderClassName, directionalClassName)\n\n        this._isSliding = false\n\n        setTimeout(triggerSlidEvent, 0)\n      }\n\n      this._queueCallback(completeCallBack, activeElement, true)\n    } else {\n      activeElement.classList.remove(CLASS_NAME_ACTIVE)\n      nextElement.classList.add(CLASS_NAME_ACTIVE)\n\n      this._isSliding = false\n      triggerSlidEvent()\n    }\n\n    if (isCycling) {\n      this.cycle()\n    }\n  }\n\n  _directionToOrder(direction) {\n    if (![DIRECTION_RIGHT, DIRECTION_LEFT].includes(direction)) {\n      return direction\n    }\n\n    if (isRTL()) {\n      return direction === DIRECTION_LEFT ? ORDER_PREV : ORDER_NEXT\n    }\n\n    return direction === DIRECTION_LEFT ? ORDER_NEXT : ORDER_PREV\n  }\n\n  _orderToDirection(order) {\n    if (![ORDER_NEXT, ORDER_PREV].includes(order)) {\n      return order\n    }\n\n    if (isRTL()) {\n      return order === ORDER_PREV ? DIRECTION_LEFT : DIRECTION_RIGHT\n    }\n\n    return order === ORDER_PREV ? DIRECTION_RIGHT : DIRECTION_LEFT\n  }\n\n  // Static\n\n  static carouselInterface(element, config) {\n    const data = Carousel.getOrCreateInstance(element, config)\n\n    let { _config } = data\n    if (typeof config === 'object') {\n      _config = {\n        ..._config,\n        ...config\n      }\n    }\n\n    const action = typeof config === 'string' ? config : _config.slide\n\n    if (typeof config === 'number') {\n      data.to(config)\n    } else if (typeof action === 'string') {\n      if (typeof data[action] === 'undefined') {\n        throw new TypeError(`No method named \"${action}\"`)\n      }\n\n      data[action]()\n    } else if (_config.interval && _config.ride) {\n      data.pause()\n      data.cycle()\n    }\n  }\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      Carousel.carouselInterface(this, config)\n    })\n  }\n\n  static dataApiClickHandler(event) {\n    const target = getElementFromSelector(this)\n\n    if (!target || !target.classList.contains(CLASS_NAME_CAROUSEL)) {\n      return\n    }\n\n    const config = {\n      ...Manipulator.getDataAttributes(target),\n      ...Manipulator.getDataAttributes(this)\n    }\n    const slideIndex = this.getAttribute('data-bs-slide-to')\n\n    if (slideIndex) {\n      config.interval = false\n    }\n\n    Carousel.carouselInterface(target, config)\n\n    if (slideIndex) {\n      Carousel.getInstance(target).to(slideIndex)\n    }\n\n    event.preventDefault()\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_SLIDE, Carousel.dataApiClickHandler)\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  const carousels = SelectorEngine.find(SELECTOR_DATA_RIDE)\n\n  for (let i = 0, len = carousels.length; i < len; i++) {\n    Carousel.carouselInterface(carousels[i], Carousel.getInstance(carousels[i]))\n  }\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Carousel to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(Carousel)\n\nexport default Carousel\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.1.3): collapse.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  getElement,\n  getSelectorFromElement,\n  getElementFromSelector,\n  reflow,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'collapse'\nconst DATA_KEY = 'bs.collapse'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst Default = {\n  toggle: true,\n  parent: null\n}\n\nconst DefaultType = {\n  toggle: 'boolean',\n  parent: '(null|element)'\n}\n\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_COLLAPSE = 'collapse'\nconst CLASS_NAME_COLLAPSING = 'collapsing'\nconst CLASS_NAME_COLLAPSED = 'collapsed'\nconst CLASS_NAME_DEEPER_CHILDREN = `:scope .${CLASS_NAME_COLLAPSE} .${CLASS_NAME_COLLAPSE}`\nconst CLASS_NAME_HORIZONTAL = 'collapse-horizontal'\n\nconst WIDTH = 'width'\nconst HEIGHT = 'height'\n\nconst SELECTOR_ACTIVES = '.collapse.show, .collapse.collapsing'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"collapse\"]'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Collapse extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n\n    this._isTransitioning = false\n    this._config = this._getConfig(config)\n    this._triggerArray = []\n\n    const toggleList = SelectorEngine.find(SELECTOR_DATA_TOGGLE)\n\n    for (let i = 0, len = toggleList.length; i < len; i++) {\n      const elem = toggleList[i]\n      const selector = getSelectorFromElement(elem)\n      const filterElement = SelectorEngine.find(selector)\n        .filter(foundElem => foundElem === this._element)\n\n      if (selector !== null && filterElement.length) {\n        this._selector = selector\n        this._triggerArray.push(elem)\n      }\n    }\n\n    this._initializeChildren()\n\n    if (!this._config.parent) {\n      this._addAriaAndCollapsedClass(this._triggerArray, this._isShown())\n    }\n\n    if (this._config.toggle) {\n      this.toggle()\n    }\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n\n  toggle() {\n    if (this._isShown()) {\n      this.hide()\n    } else {\n      this.show()\n    }\n  }\n\n  show() {\n    if (this._isTransitioning || this._isShown()) {\n      return\n    }\n\n    let actives = []\n    let activesData\n\n    if (this._config.parent) {\n      const children = SelectorEngine.find(CLASS_NAME_DEEPER_CHILDREN, this._config.parent)\n      actives = SelectorEngine.find(SELECTOR_ACTIVES, this._config.parent).filter(elem => !children.includes(elem)) // remove children if greater depth\n    }\n\n    const container = SelectorEngine.findOne(this._selector)\n    if (actives.length) {\n      const tempActiveData = actives.find(elem => container !== elem)\n      activesData = tempActiveData ? Collapse.getInstance(tempActiveData) : null\n\n      if (activesData && activesData._isTransitioning) {\n        return\n      }\n    }\n\n    const startEvent = EventHandler.trigger(this._element, EVENT_SHOW)\n    if (startEvent.defaultPrevented) {\n      return\n    }\n\n    actives.forEach(elemActive => {\n      if (container !== elemActive) {\n        Collapse.getOrCreateInstance(elemActive, { toggle: false }).hide()\n      }\n\n      if (!activesData) {\n        Data.set(elemActive, DATA_KEY, null)\n      }\n    })\n\n    const dimension = this._getDimension()\n\n    this._element.classList.remove(CLASS_NAME_COLLAPSE)\n    this._element.classList.add(CLASS_NAME_COLLAPSING)\n\n    this._element.style[dimension] = 0\n\n    this._addAriaAndCollapsedClass(this._triggerArray, true)\n    this._isTransitioning = true\n\n    const complete = () => {\n      this._isTransitioning = false\n\n      this._element.classList.remove(CLASS_NAME_COLLAPSING)\n      this._element.classList.add(CLASS_NAME_COLLAPSE, CLASS_NAME_SHOW)\n\n      this._element.style[dimension] = ''\n\n      EventHandler.trigger(this._element, EVENT_SHOWN)\n    }\n\n    const capitalizedDimension = dimension[0].toUpperCase() + dimension.slice(1)\n    const scrollSize = `scroll${capitalizedDimension}`\n\n    this._queueCallback(complete, this._element, true)\n    this._element.style[dimension] = `${this._element[scrollSize]}px`\n  }\n\n  hide() {\n    if (this._isTransitioning || !this._isShown()) {\n      return\n    }\n\n    const startEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n    if (startEvent.defaultPrevented) {\n      return\n    }\n\n    const dimension = this._getDimension()\n\n    this._element.style[dimension] = `${this._element.getBoundingClientRect()[dimension]}px`\n\n    reflow(this._element)\n\n    this._element.classList.add(CLASS_NAME_COLLAPSING)\n    this._element.classList.remove(CLASS_NAME_COLLAPSE, CLASS_NAME_SHOW)\n\n    const triggerArrayLength = this._triggerArray.length\n    for (let i = 0; i < triggerArrayLength; i++) {\n      const trigger = this._triggerArray[i]\n      const elem = getElementFromSelector(trigger)\n\n      if (elem && !this._isShown(elem)) {\n        this._addAriaAndCollapsedClass([trigger], false)\n      }\n    }\n\n    this._isTransitioning = true\n\n    const complete = () => {\n      this._isTransitioning = false\n      this._element.classList.remove(CLASS_NAME_COLLAPSING)\n      this._element.classList.add(CLASS_NAME_COLLAPSE)\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._element.style[dimension] = ''\n\n    this._queueCallback(complete, this._element, true)\n  }\n\n  _isShown(element = this._element) {\n    return element.classList.contains(CLASS_NAME_SHOW)\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(this._element),\n      ...config\n    }\n    config.toggle = Boolean(config.toggle) // Coerce string values\n    config.parent = getElement(config.parent)\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _getDimension() {\n    return this._element.classList.contains(CLASS_NAME_HORIZONTAL) ? WIDTH : HEIGHT\n  }\n\n  _initializeChildren() {\n    if (!this._config.parent) {\n      return\n    }\n\n    const children = SelectorEngine.find(CLASS_NAME_DEEPER_CHILDREN, this._config.parent)\n    SelectorEngine.find(SELECTOR_DATA_TOGGLE, this._config.parent).filter(elem => !children.includes(elem))\n      .forEach(element => {\n        const selected = getElementFromSelector(element)\n\n        if (selected) {\n          this._addAriaAndCollapsedClass([element], this._isShown(selected))\n        }\n      })\n  }\n\n  _addAriaAndCollapsedClass(triggerArray, isOpen) {\n    if (!triggerArray.length) {\n      return\n    }\n\n    triggerArray.forEach(elem => {\n      if (isOpen) {\n        elem.classList.remove(CLASS_NAME_COLLAPSED)\n      } else {\n        elem.classList.add(CLASS_NAME_COLLAPSED)\n      }\n\n      elem.setAttribute('aria-expanded', isOpen)\n    })\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const _config = {}\n      if (typeof config === 'string' && /show|hide/.test(config)) {\n        _config.toggle = false\n      }\n\n      const data = Collapse.getOrCreateInstance(this, _config)\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  // preventDefault only for <a> elements (which change the URL) not inside the collapsible element\n  if (event.target.tagName === 'A' || (event.delegateTarget && event.delegateTarget.tagName === 'A')) {\n    event.preventDefault()\n  }\n\n  const selector = getSelectorFromElement(this)\n  const selectorElements = SelectorEngine.find(selector)\n\n  selectorElements.forEach(element => {\n    Collapse.getOrCreateInstance(element, { toggle: false }).toggle()\n  })\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Collapse to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(Collapse)\n\nexport default Collapse\n", "export var top = 'top';\nexport var bottom = 'bottom';\nexport var right = 'right';\nexport var left = 'left';\nexport var auto = 'auto';\nexport var basePlacements = [top, bottom, right, left];\nexport var start = 'start';\nexport var end = 'end';\nexport var clippingParents = 'clippingParents';\nexport var viewport = 'viewport';\nexport var popper = 'popper';\nexport var reference = 'reference';\nexport var variationPlacements = /*#__PURE__*/basePlacements.reduce(function (acc, placement) {\n  return acc.concat([placement + \"-\" + start, placement + \"-\" + end]);\n}, []);\nexport var placements = /*#__PURE__*/[].concat(basePlacements, [auto]).reduce(function (acc, placement) {\n  return acc.concat([placement, placement + \"-\" + start, placement + \"-\" + end]);\n}, []); // modifiers that need to read the DOM\n\nexport var beforeRead = 'beforeRead';\nexport var read = 'read';\nexport var afterRead = 'afterRead'; // pure-logic modifiers\n\nexport var beforeMain = 'beforeMain';\nexport var main = 'main';\nexport var afterMain = 'afterMain'; // modifier with the purpose to write to the DOM (or write into a framework state)\n\nexport var beforeWrite = 'beforeWrite';\nexport var write = 'write';\nexport var afterWrite = 'afterWrite';\nexport var modifierPhases = [beforeRead, read, afterRead, beforeMain, main, afterMain, beforeWrite, write, afterWrite];", "export default function getNodeName(element) {\n  return element ? (element.nodeName || '').toLowerCase() : null;\n}", "export default function getWindow(node) {\n  if (node == null) {\n    return window;\n  }\n\n  if (node.toString() !== '[object Window]') {\n    var ownerDocument = node.ownerDocument;\n    return ownerDocument ? ownerDocument.defaultView || window : window;\n  }\n\n  return node;\n}", "import getWindow from \"./getWindow.js\";\n\nfunction isElement(node) {\n  var OwnElement = getWindow(node).Element;\n  return node instanceof OwnElement || node instanceof Element;\n}\n\nfunction isHTMLElement(node) {\n  var OwnElement = getWindow(node).HTMLElement;\n  return node instanceof OwnElement || node instanceof HTMLElement;\n}\n\nfunction isShadowRoot(node) {\n  // IE 11 has no ShadowRoot\n  if (typeof ShadowRoot === 'undefined') {\n    return false;\n  }\n\n  var OwnElement = getWindow(node).ShadowRoot;\n  return node instanceof OwnElement || node instanceof ShadowRoot;\n}\n\nexport { isElement, isHTMLElement, isShadowRoot };", "import getNodeName from \"../dom-utils/getNodeName.js\";\nimport { isHTMLElement } from \"../dom-utils/instanceOf.js\"; // This modifier takes the styles prepared by the `computeStyles` modifier\n// and applies them to the HTMLElements such as popper and arrow\n\nfunction applyStyles(_ref) {\n  var state = _ref.state;\n  Object.keys(state.elements).forEach(function (name) {\n    var style = state.styles[name] || {};\n    var attributes = state.attributes[name] || {};\n    var element = state.elements[name]; // arrow is optional + virtual elements\n\n    if (!isHTMLElement(element) || !getNodeName(element)) {\n      return;\n    } // Flow doesn't support to extend this property, but it's the most\n    // effective way to apply styles to an HTMLElement\n    // $FlowFixMe[cannot-write]\n\n\n    Object.assign(element.style, style);\n    Object.keys(attributes).forEach(function (name) {\n      var value = attributes[name];\n\n      if (value === false) {\n        element.removeAttribute(name);\n      } else {\n        element.setAttribute(name, value === true ? '' : value);\n      }\n    });\n  });\n}\n\nfunction effect(_ref2) {\n  var state = _ref2.state;\n  var initialStyles = {\n    popper: {\n      position: state.options.strategy,\n      left: '0',\n      top: '0',\n      margin: '0'\n    },\n    arrow: {\n      position: 'absolute'\n    },\n    reference: {}\n  };\n  Object.assign(state.elements.popper.style, initialStyles.popper);\n  state.styles = initialStyles;\n\n  if (state.elements.arrow) {\n    Object.assign(state.elements.arrow.style, initialStyles.arrow);\n  }\n\n  return function () {\n    Object.keys(state.elements).forEach(function (name) {\n      var element = state.elements[name];\n      var attributes = state.attributes[name] || {};\n      var styleProperties = Object.keys(state.styles.hasOwnProperty(name) ? state.styles[name] : initialStyles[name]); // Set all values to an empty string to unset them\n\n      var style = styleProperties.reduce(function (style, property) {\n        style[property] = '';\n        return style;\n      }, {}); // arrow is optional + virtual elements\n\n      if (!isHTMLElement(element) || !getNodeName(element)) {\n        return;\n      }\n\n      Object.assign(element.style, style);\n      Object.keys(attributes).forEach(function (attribute) {\n        element.removeAttribute(attribute);\n      });\n    });\n  };\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'applyStyles',\n  enabled: true,\n  phase: 'write',\n  fn: applyStyles,\n  effect: effect,\n  requires: ['computeStyles']\n};", "import { auto } from \"../enums.js\";\nexport default function getBasePlacement(placement) {\n  return placement.split('-')[0];\n}", "// import { isHTMLElement } from './instanceOf';\nexport default function getBoundingClientRect(element, // eslint-disable-next-line unused-imports/no-unused-vars\nincludeScale) {\n  if (includeScale === void 0) {\n    includeScale = false;\n  }\n\n  var rect = element.getBoundingClientRect();\n  var scaleX = 1;\n  var scaleY = 1; // FIXME:\n  // `offsetWidth` returns an integer while `getBoundingClientRect`\n  // returns a float. This results in `scaleX` or `scaleY` being\n  // non-1 when it should be for elements that aren't a full pixel in\n  // width or height.\n  // if (isHTMLElement(element) && includeScale) {\n  //   const offsetHeight = element.offsetHeight;\n  //   const offsetWidth = element.offsetWidth;\n  //   // Do not attempt to divide by 0, otherwise we get `Infinity` as scale\n  //   // Fallback to 1 in case both values are `0`\n  //   if (offsetWidth > 0) {\n  //     scaleX = rect.width / offsetWidth || 1;\n  //   }\n  //   if (offsetHeight > 0) {\n  //     scaleY = rect.height / offsetHeight || 1;\n  //   }\n  // }\n\n  return {\n    width: rect.width / scaleX,\n    height: rect.height / scaleY,\n    top: rect.top / scaleY,\n    right: rect.right / scaleX,\n    bottom: rect.bottom / scaleY,\n    left: rect.left / scaleX,\n    x: rect.left / scaleX,\n    y: rect.top / scaleY\n  };\n}", "import getBoundingClientRect from \"./getBoundingClientRect.js\"; // Returns the layout rect of an element relative to its offsetParent. Layout\n// means it doesn't take into account transforms.\n\nexport default function getLayoutRect(element) {\n  var clientRect = getBoundingClientRect(element); // Use the clientRect sizes if it's not been transformed.\n  // Fixes https://github.com/popperjs/popper-core/issues/1223\n\n  var width = element.offsetWidth;\n  var height = element.offsetHeight;\n\n  if (Math.abs(clientRect.width - width) <= 1) {\n    width = clientRect.width;\n  }\n\n  if (Math.abs(clientRect.height - height) <= 1) {\n    height = clientRect.height;\n  }\n\n  return {\n    x: element.offsetLeft,\n    y: element.offsetTop,\n    width: width,\n    height: height\n  };\n}", "import { isShadowRoot } from \"./instanceOf.js\";\nexport default function contains(parent, child) {\n  var rootNode = child.getRootNode && child.getRootNode(); // First, attempt with faster native method\n\n  if (parent.contains(child)) {\n    return true;\n  } // then fallback to custom implementation with Shadow DOM support\n  else if (rootNode && isShadowRoot(rootNode)) {\n      var next = child;\n\n      do {\n        if (next && parent.isSameNode(next)) {\n          return true;\n        } // $FlowFixMe[prop-missing]: need a better way to handle this...\n\n\n        next = next.parentNode || next.host;\n      } while (next);\n    } // Give up, the result is false\n\n\n  return false;\n}", "import getWindow from \"./getWindow.js\";\nexport default function getComputedStyle(element) {\n  return getWindow(element).getComputedStyle(element);\n}", "import getNodeName from \"./getNodeName.js\";\nexport default function isTableElement(element) {\n  return ['table', 'td', 'th'].indexOf(getNodeName(element)) >= 0;\n}", "import { isElement } from \"./instanceOf.js\";\nexport default function getDocumentElement(element) {\n  // $FlowFixMe[incompatible-return]: assume body is always available\n  return ((isElement(element) ? element.ownerDocument : // $FlowFixMe[prop-missing]\n  element.document) || window.document).documentElement;\n}", "import getNodeName from \"./getNodeName.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport { isShadowRoot } from \"./instanceOf.js\";\nexport default function getParentNode(element) {\n  if (getNodeName(element) === 'html') {\n    return element;\n  }\n\n  return (// this is a quicker (but less type safe) way to save quite some bytes from the bundle\n    // $FlowFixMe[incompatible-return]\n    // $FlowFixMe[prop-missing]\n    element.assignedSlot || // step into the shadow DOM of the parent of a slotted node\n    element.parentNode || ( // DOM Element detected\n    isShadowRoot(element) ? element.host : null) || // ShadowRoot detected\n    // $FlowFixMe[incompatible-call]: HTMLElement is a Node\n    getDocumentElement(element) // fallback\n\n  );\n}", "import getWindow from \"./getWindow.js\";\nimport getNodeName from \"./getNodeName.js\";\nimport getComputedStyle from \"./getComputedStyle.js\";\nimport { isHTMLElement } from \"./instanceOf.js\";\nimport isTableElement from \"./isTableElement.js\";\nimport getParentNode from \"./getParentNode.js\";\n\nfunction getTrueOffsetParent(element) {\n  if (!isHTMLElement(element) || // https://github.com/popperjs/popper-core/issues/837\n  getComputedStyle(element).position === 'fixed') {\n    return null;\n  }\n\n  return element.offsetParent;\n} // `.offsetParent` reports `null` for fixed elements, while absolute elements\n// return the containing block\n\n\nfunction getContainingBlock(element) {\n  var isFirefox = navigator.userAgent.toLowerCase().indexOf('firefox') !== -1;\n  var isIE = navigator.userAgent.indexOf('Trident') !== -1;\n\n  if (isIE && isHTMLElement(element)) {\n    // In IE 9, 10 and 11 fixed elements containing block is always established by the viewport\n    var elementCss = getComputedStyle(element);\n\n    if (elementCss.position === 'fixed') {\n      return null;\n    }\n  }\n\n  var currentNode = getParentNode(element);\n\n  while (isHTMLElement(currentNode) && ['html', 'body'].indexOf(getNodeName(currentNode)) < 0) {\n    var css = getComputedStyle(currentNode); // This is non-exhaustive but covers the most common CSS properties that\n    // create a containing block.\n    // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\n\n    if (css.transform !== 'none' || css.perspective !== 'none' || css.contain === 'paint' || ['transform', 'perspective'].indexOf(css.willChange) !== -1 || isFirefox && css.willChange === 'filter' || isFirefox && css.filter && css.filter !== 'none') {\n      return currentNode;\n    } else {\n      currentNode = currentNode.parentNode;\n    }\n  }\n\n  return null;\n} // Gets the closest ancestor positioned element. Handles some edge cases,\n// such as table ancestors and cross browser bugs.\n\n\nexport default function getOffsetParent(element) {\n  var window = getWindow(element);\n  var offsetParent = getTrueOffsetParent(element);\n\n  while (offsetParent && isTableElement(offsetParent) && getComputedStyle(offsetParent).position === 'static') {\n    offsetParent = getTrueOffsetParent(offsetParent);\n  }\n\n  if (offsetParent && (getNodeName(offsetParent) === 'html' || getNodeName(offsetParent) === 'body' && getComputedStyle(offsetParent).position === 'static')) {\n    return window;\n  }\n\n  return offsetParent || getContainingBlock(element) || window;\n}", "export default function getMainAxisFromPlacement(placement) {\n  return ['top', 'bottom'].indexOf(placement) >= 0 ? 'x' : 'y';\n}", "export var max = Math.max;\nexport var min = Math.min;\nexport var round = Math.round;", "import { max as mathMax, min as mathMin } from \"./math.js\";\nexport default function within(min, value, max) {\n  return mathMax(min, mathMin(value, max));\n}", "import getFreshSideObject from \"./getFreshSideObject.js\";\nexport default function mergePaddingObject(paddingObject) {\n  return Object.assign({}, getFreshSideObject(), paddingObject);\n}", "export default function getFreshSideObject() {\n  return {\n    top: 0,\n    right: 0,\n    bottom: 0,\n    left: 0\n  };\n}", "export default function expandToHashMap(value, keys) {\n  return keys.reduce(function (hashMap, key) {\n    hashMap[key] = value;\n    return hashMap;\n  }, {});\n}", "import getBasePlacement from \"../utils/getBasePlacement.js\";\nimport getLayoutRect from \"../dom-utils/getLayoutRect.js\";\nimport contains from \"../dom-utils/contains.js\";\nimport getOffsetParent from \"../dom-utils/getOffsetParent.js\";\nimport getMainAxisFromPlacement from \"../utils/getMainAxisFromPlacement.js\";\nimport within from \"../utils/within.js\";\nimport mergePaddingObject from \"../utils/mergePaddingObject.js\";\nimport expandToHashMap from \"../utils/expandToHashMap.js\";\nimport { left, right, basePlacements, top, bottom } from \"../enums.js\";\nimport { isHTMLElement } from \"../dom-utils/instanceOf.js\"; // eslint-disable-next-line import/no-unused-modules\n\nvar toPaddingObject = function toPaddingObject(padding, state) {\n  padding = typeof padding === 'function' ? padding(Object.assign({}, state.rects, {\n    placement: state.placement\n  })) : padding;\n  return mergePaddingObject(typeof padding !== 'number' ? padding : expandToHashMap(padding, basePlacements));\n};\n\nfunction arrow(_ref) {\n  var _state$modifiersData$;\n\n  var state = _ref.state,\n      name = _ref.name,\n      options = _ref.options;\n  var arrowElement = state.elements.arrow;\n  var popperOffsets = state.modifiersData.popperOffsets;\n  var basePlacement = getBasePlacement(state.placement);\n  var axis = getMainAxisFromPlacement(basePlacement);\n  var isVertical = [left, right].indexOf(basePlacement) >= 0;\n  var len = isVertical ? 'height' : 'width';\n\n  if (!arrowElement || !popperOffsets) {\n    return;\n  }\n\n  var paddingObject = toPaddingObject(options.padding, state);\n  var arrowRect = getLayoutRect(arrowElement);\n  var minProp = axis === 'y' ? top : left;\n  var maxProp = axis === 'y' ? bottom : right;\n  var endDiff = state.rects.reference[len] + state.rects.reference[axis] - popperOffsets[axis] - state.rects.popper[len];\n  var startDiff = popperOffsets[axis] - state.rects.reference[axis];\n  var arrowOffsetParent = getOffsetParent(arrowElement);\n  var clientSize = arrowOffsetParent ? axis === 'y' ? arrowOffsetParent.clientHeight || 0 : arrowOffsetParent.clientWidth || 0 : 0;\n  var centerToReference = endDiff / 2 - startDiff / 2; // Make sure the arrow doesn't overflow the popper if the center point is\n  // outside of the popper bounds\n\n  var min = paddingObject[minProp];\n  var max = clientSize - arrowRect[len] - paddingObject[maxProp];\n  var center = clientSize / 2 - arrowRect[len] / 2 + centerToReference;\n  var offset = within(min, center, max); // Prevents breaking syntax highlighting...\n\n  var axisProp = axis;\n  state.modifiersData[name] = (_state$modifiersData$ = {}, _state$modifiersData$[axisProp] = offset, _state$modifiersData$.centerOffset = offset - center, _state$modifiersData$);\n}\n\nfunction effect(_ref2) {\n  var state = _ref2.state,\n      options = _ref2.options;\n  var _options$element = options.element,\n      arrowElement = _options$element === void 0 ? '[data-popper-arrow]' : _options$element;\n\n  if (arrowElement == null) {\n    return;\n  } // CSS selector\n\n\n  if (typeof arrowElement === 'string') {\n    arrowElement = state.elements.popper.querySelector(arrowElement);\n\n    if (!arrowElement) {\n      return;\n    }\n  }\n\n  if (process.env.NODE_ENV !== \"production\") {\n    if (!isHTMLElement(arrowElement)) {\n      console.error(['Popper: \"arrow\" element must be an HTMLElement (not an SVGElement).', 'To use an SVG arrow, wrap it in an HTMLElement that will be used as', 'the arrow.'].join(' '));\n    }\n  }\n\n  if (!contains(state.elements.popper, arrowElement)) {\n    if (process.env.NODE_ENV !== \"production\") {\n      console.error(['Popper: \"arrow\" modifier\\'s `element` must be a child of the popper', 'element.'].join(' '));\n    }\n\n    return;\n  }\n\n  state.elements.arrow = arrowElement;\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'arrow',\n  enabled: true,\n  phase: 'main',\n  fn: arrow,\n  effect: effect,\n  requires: ['popperOffsets'],\n  requiresIfExists: ['preventOverflow']\n};", "export default function getVariation(placement) {\n  return placement.split('-')[1];\n}", "import { top, left, right, bottom, end } from \"../enums.js\";\nimport getOffsetParent from \"../dom-utils/getOffsetParent.js\";\nimport getWindow from \"../dom-utils/getWindow.js\";\nimport getDocumentElement from \"../dom-utils/getDocumentElement.js\";\nimport getComputedStyle from \"../dom-utils/getComputedStyle.js\";\nimport getBasePlacement from \"../utils/getBasePlacement.js\";\nimport getVariation from \"../utils/getVariation.js\";\nimport { round } from \"../utils/math.js\"; // eslint-disable-next-line import/no-unused-modules\n\nvar unsetSides = {\n  top: 'auto',\n  right: 'auto',\n  bottom: 'auto',\n  left: 'auto'\n}; // Round the offsets to the nearest suitable subpixel based on the DPR.\n// Zooming can change the DPR, but it seems to report a value that will\n// cleanly divide the values into the appropriate subpixels.\n\nfunction roundOffsetsByDPR(_ref) {\n  var x = _ref.x,\n      y = _ref.y;\n  var win = window;\n  var dpr = win.devicePixelRatio || 1;\n  return {\n    x: round(round(x * dpr) / dpr) || 0,\n    y: round(round(y * dpr) / dpr) || 0\n  };\n}\n\nexport function mapToStyles(_ref2) {\n  var _Object$assign2;\n\n  var popper = _ref2.popper,\n      popperRect = _ref2.popperRect,\n      placement = _ref2.placement,\n      variation = _ref2.variation,\n      offsets = _ref2.offsets,\n      position = _ref2.position,\n      gpuAcceleration = _ref2.gpuAcceleration,\n      adaptive = _ref2.adaptive,\n      roundOffsets = _ref2.roundOffsets;\n\n  var _ref3 = roundOffsets === true ? roundOffsetsByDPR(offsets) : typeof roundOffsets === 'function' ? roundOffsets(offsets) : offsets,\n      _ref3$x = _ref3.x,\n      x = _ref3$x === void 0 ? 0 : _ref3$x,\n      _ref3$y = _ref3.y,\n      y = _ref3$y === void 0 ? 0 : _ref3$y;\n\n  var hasX = offsets.hasOwnProperty('x');\n  var hasY = offsets.hasOwnProperty('y');\n  var sideX = left;\n  var sideY = top;\n  var win = window;\n\n  if (adaptive) {\n    var offsetParent = getOffsetParent(popper);\n    var heightProp = 'clientHeight';\n    var widthProp = 'clientWidth';\n\n    if (offsetParent === getWindow(popper)) {\n      offsetParent = getDocumentElement(popper);\n\n      if (getComputedStyle(offsetParent).position !== 'static' && position === 'absolute') {\n        heightProp = 'scrollHeight';\n        widthProp = 'scrollWidth';\n      }\n    } // $FlowFixMe[incompatible-cast]: force type refinement, we compare offsetParent with window above, but Flow doesn't detect it\n\n\n    offsetParent = offsetParent;\n\n    if (placement === top || (placement === left || placement === right) && variation === end) {\n      sideY = bottom; // $FlowFixMe[prop-missing]\n\n      y -= offsetParent[heightProp] - popperRect.height;\n      y *= gpuAcceleration ? 1 : -1;\n    }\n\n    if (placement === left || (placement === top || placement === bottom) && variation === end) {\n      sideX = right; // $FlowFixMe[prop-missing]\n\n      x -= offsetParent[widthProp] - popperRect.width;\n      x *= gpuAcceleration ? 1 : -1;\n    }\n  }\n\n  var commonStyles = Object.assign({\n    position: position\n  }, adaptive && unsetSides);\n\n  if (gpuAcceleration) {\n    var _Object$assign;\n\n    return Object.assign({}, commonStyles, (_Object$assign = {}, _Object$assign[sideY] = hasY ? '0' : '', _Object$assign[sideX] = hasX ? '0' : '', _Object$assign.transform = (win.devicePixelRatio || 1) <= 1 ? \"translate(\" + x + \"px, \" + y + \"px)\" : \"translate3d(\" + x + \"px, \" + y + \"px, 0)\", _Object$assign));\n  }\n\n  return Object.assign({}, commonStyles, (_Object$assign2 = {}, _Object$assign2[sideY] = hasY ? y + \"px\" : '', _Object$assign2[sideX] = hasX ? x + \"px\" : '', _Object$assign2.transform = '', _Object$assign2));\n}\n\nfunction computeStyles(_ref4) {\n  var state = _ref4.state,\n      options = _ref4.options;\n  var _options$gpuAccelerat = options.gpuAcceleration,\n      gpuAcceleration = _options$gpuAccelerat === void 0 ? true : _options$gpuAccelerat,\n      _options$adaptive = options.adaptive,\n      adaptive = _options$adaptive === void 0 ? true : _options$adaptive,\n      _options$roundOffsets = options.roundOffsets,\n      roundOffsets = _options$roundOffsets === void 0 ? true : _options$roundOffsets;\n\n  if (process.env.NODE_ENV !== \"production\") {\n    var transitionProperty = getComputedStyle(state.elements.popper).transitionProperty || '';\n\n    if (adaptive && ['transform', 'top', 'right', 'bottom', 'left'].some(function (property) {\n      return transitionProperty.indexOf(property) >= 0;\n    })) {\n      console.warn(['Popper: Detected CSS transitions on at least one of the following', 'CSS properties: \"transform\", \"top\", \"right\", \"bottom\", \"left\".', '\\n\\n', 'Disable the \"computeStyles\" modifier\\'s `adaptive` option to allow', 'for smooth transitions, or remove these properties from the CSS', 'transition declaration on the popper element if only transitioning', 'opacity or background-color for example.', '\\n\\n', 'We recommend using the popper element as a wrapper around an inner', 'element that can have any CSS property transitioned for animations.'].join(' '));\n    }\n  }\n\n  var commonStyles = {\n    placement: getBasePlacement(state.placement),\n    variation: getVariation(state.placement),\n    popper: state.elements.popper,\n    popperRect: state.rects.popper,\n    gpuAcceleration: gpuAcceleration\n  };\n\n  if (state.modifiersData.popperOffsets != null) {\n    state.styles.popper = Object.assign({}, state.styles.popper, mapToStyles(Object.assign({}, commonStyles, {\n      offsets: state.modifiersData.popperOffsets,\n      position: state.options.strategy,\n      adaptive: adaptive,\n      roundOffsets: roundOffsets\n    })));\n  }\n\n  if (state.modifiersData.arrow != null) {\n    state.styles.arrow = Object.assign({}, state.styles.arrow, mapToStyles(Object.assign({}, commonStyles, {\n      offsets: state.modifiersData.arrow,\n      position: 'absolute',\n      adaptive: false,\n      roundOffsets: roundOffsets\n    })));\n  }\n\n  state.attributes.popper = Object.assign({}, state.attributes.popper, {\n    'data-popper-placement': state.placement\n  });\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'computeStyles',\n  enabled: true,\n  phase: 'beforeWrite',\n  fn: computeStyles,\n  data: {}\n};", "import getWindow from \"../dom-utils/getWindow.js\"; // eslint-disable-next-line import/no-unused-modules\n\nvar passive = {\n  passive: true\n};\n\nfunction effect(_ref) {\n  var state = _ref.state,\n      instance = _ref.instance,\n      options = _ref.options;\n  var _options$scroll = options.scroll,\n      scroll = _options$scroll === void 0 ? true : _options$scroll,\n      _options$resize = options.resize,\n      resize = _options$resize === void 0 ? true : _options$resize;\n  var window = getWindow(state.elements.popper);\n  var scrollParents = [].concat(state.scrollParents.reference, state.scrollParents.popper);\n\n  if (scroll) {\n    scrollParents.forEach(function (scrollParent) {\n      scrollParent.addEventListener('scroll', instance.update, passive);\n    });\n  }\n\n  if (resize) {\n    window.addEventListener('resize', instance.update, passive);\n  }\n\n  return function () {\n    if (scroll) {\n      scrollParents.forEach(function (scrollParent) {\n        scrollParent.removeEventListener('scroll', instance.update, passive);\n      });\n    }\n\n    if (resize) {\n      window.removeEventListener('resize', instance.update, passive);\n    }\n  };\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'eventListeners',\n  enabled: true,\n  phase: 'write',\n  fn: function fn() {},\n  effect: effect,\n  data: {}\n};", "var hash = {\n  left: 'right',\n  right: 'left',\n  bottom: 'top',\n  top: 'bottom'\n};\nexport default function getOppositePlacement(placement) {\n  return placement.replace(/left|right|bottom|top/g, function (matched) {\n    return hash[matched];\n  });\n}", "var hash = {\n  start: 'end',\n  end: 'start'\n};\nexport default function getOppositeVariationPlacement(placement) {\n  return placement.replace(/start|end/g, function (matched) {\n    return hash[matched];\n  });\n}", "import getWindow from \"./getWindow.js\";\nexport default function getWindowScroll(node) {\n  var win = getWindow(node);\n  var scrollLeft = win.pageXOffset;\n  var scrollTop = win.pageYOffset;\n  return {\n    scrollLeft: scrollLeft,\n    scrollTop: scrollTop\n  };\n}", "import getBoundingClientRect from \"./getBoundingClientRect.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport getWindowScroll from \"./getWindowScroll.js\";\nexport default function getWindowScrollBarX(element) {\n  // If <html> has a CSS width greater than the viewport, then this will be\n  // incorrect for RTL.\n  // Popper 1 is broken in this case and never had a bug report so let's assume\n  // it's not an issue. I don't think anyone ever specifies width on <html>\n  // anyway.\n  // Browsers where the left scrollbar doesn't cause an issue report `0` for\n  // this (e.g. Edge 2019, IE11, Safari)\n  return getBoundingClientRect(getDocumentElement(element)).left + getWindowScroll(element).scrollLeft;\n}", "import getComputedStyle from \"./getComputedStyle.js\";\nexport default function isScrollParent(element) {\n  // Firefox wants us to check `-x` and `-y` variations as well\n  var _getComputedStyle = getComputedStyle(element),\n      overflow = _getComputedStyle.overflow,\n      overflowX = _getComputedStyle.overflowX,\n      overflowY = _getComputedStyle.overflowY;\n\n  return /auto|scroll|overlay|hidden/.test(overflow + overflowY + overflowX);\n}", "import getParentNode from \"./getParentNode.js\";\nimport isScrollParent from \"./isScrollParent.js\";\nimport getNodeName from \"./getNodeName.js\";\nimport { isHTMLElement } from \"./instanceOf.js\";\nexport default function getScrollParent(node) {\n  if (['html', 'body', '#document'].indexOf(getNodeName(node)) >= 0) {\n    // $FlowFixMe[incompatible-return]: assume body is always available\n    return node.ownerDocument.body;\n  }\n\n  if (isHTMLElement(node) && isScrollParent(node)) {\n    return node;\n  }\n\n  return getScrollParent(getParentNode(node));\n}", "import getScrollParent from \"./getScrollParent.js\";\nimport getParentNode from \"./getParentNode.js\";\nimport getWindow from \"./getWindow.js\";\nimport isScrollParent from \"./isScrollParent.js\";\n/*\ngiven a DOM element, return the list of all scroll parents, up the list of ancesors\nuntil we get to the top window object. This list is what we attach scroll listeners\nto, because if any of these parent elements scroll, we'll need to re-calculate the\nreference element's position.\n*/\n\nexport default function listScrollParents(element, list) {\n  var _element$ownerDocumen;\n\n  if (list === void 0) {\n    list = [];\n  }\n\n  var scrollParent = getScrollParent(element);\n  var isBody = scrollParent === ((_element$ownerDocumen = element.ownerDocument) == null ? void 0 : _element$ownerDocumen.body);\n  var win = getWindow(scrollParent);\n  var target = isBody ? [win].concat(win.visualViewport || [], isScrollParent(scrollParent) ? scrollParent : []) : scrollParent;\n  var updatedList = list.concat(target);\n  return isBody ? updatedList : // $FlowFixMe[incompatible-call]: isBody tells us target will be an HTMLElement here\n  updatedList.concat(listScrollParents(getParentNode(target)));\n}", "export default function rectToClientRect(rect) {\n  return Object.assign({}, rect, {\n    left: rect.x,\n    top: rect.y,\n    right: rect.x + rect.width,\n    bottom: rect.y + rect.height\n  });\n}", "import { viewport } from \"../enums.js\";\nimport getViewportRect from \"./getViewportRect.js\";\nimport getDocumentRect from \"./getDocumentRect.js\";\nimport listScrollParents from \"./listScrollParents.js\";\nimport getOffsetParent from \"./getOffsetParent.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport getComputedStyle from \"./getComputedStyle.js\";\nimport { isElement, isHTMLElement } from \"./instanceOf.js\";\nimport getBoundingClientRect from \"./getBoundingClientRect.js\";\nimport getParentNode from \"./getParentNode.js\";\nimport contains from \"./contains.js\";\nimport getNodeName from \"./getNodeName.js\";\nimport rectToClientRect from \"../utils/rectToClientRect.js\";\nimport { max, min } from \"../utils/math.js\";\n\nfunction getInnerBoundingClientRect(element) {\n  var rect = getBoundingClientRect(element);\n  rect.top = rect.top + element.clientTop;\n  rect.left = rect.left + element.clientLeft;\n  rect.bottom = rect.top + element.clientHeight;\n  rect.right = rect.left + element.clientWidth;\n  rect.width = element.clientWidth;\n  rect.height = element.clientHeight;\n  rect.x = rect.left;\n  rect.y = rect.top;\n  return rect;\n}\n\nfunction getClientRectFromMixedType(element, clippingParent) {\n  return clippingParent === viewport ? rectToClientRect(getViewportRect(element)) : isHTMLElement(clippingParent) ? getInnerBoundingClientRect(clippingParent) : rectToClientRect(getDocumentRect(getDocumentElement(element)));\n} // A \"clipping parent\" is an overflowable container with the characteristic of\n// clipping (or hiding) overflowing elements with a position different from\n// `initial`\n\n\nfunction getClippingParents(element) {\n  var clippingParents = listScrollParents(getParentNode(element));\n  var canEscapeClipping = ['absolute', 'fixed'].indexOf(getComputedStyle(element).position) >= 0;\n  var clipperElement = canEscapeClipping && isHTMLElement(element) ? getOffsetParent(element) : element;\n\n  if (!isElement(clipperElement)) {\n    return [];\n  } // $FlowFixMe[incompatible-return]: https://github.com/facebook/flow/issues/1414\n\n\n  return clippingParents.filter(function (clippingParent) {\n    return isElement(clippingParent) && contains(clippingParent, clipperElement) && getNodeName(clippingParent) !== 'body';\n  });\n} // Gets the maximum area that the element is visible in due to any number of\n// clipping parents\n\n\nexport default function getClippingRect(element, boundary, rootBoundary) {\n  var mainClippingParents = boundary === 'clippingParents' ? getClippingParents(element) : [].concat(boundary);\n  var clippingParents = [].concat(mainClippingParents, [rootBoundary]);\n  var firstClippingParent = clippingParents[0];\n  var clippingRect = clippingParents.reduce(function (accRect, clippingParent) {\n    var rect = getClientRectFromMixedType(element, clippingParent);\n    accRect.top = max(rect.top, accRect.top);\n    accRect.right = min(rect.right, accRect.right);\n    accRect.bottom = min(rect.bottom, accRect.bottom);\n    accRect.left = max(rect.left, accRect.left);\n    return accRect;\n  }, getClientRectFromMixedType(element, firstClippingParent));\n  clippingRect.width = clippingRect.right - clippingRect.left;\n  clippingRect.height = clippingRect.bottom - clippingRect.top;\n  clippingRect.x = clippingRect.left;\n  clippingRect.y = clippingRect.top;\n  return clippingRect;\n}", "import getWindow from \"./getWindow.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport getWindowScrollBarX from \"./getWindowScrollBarX.js\";\nexport default function getViewportRect(element) {\n  var win = getWindow(element);\n  var html = getDocumentElement(element);\n  var visualViewport = win.visualViewport;\n  var width = html.clientWidth;\n  var height = html.clientHeight;\n  var x = 0;\n  var y = 0; // NB: This isn't supported on iOS <= 12. If the keyboard is open, the popper\n  // can be obscured underneath it.\n  // Also, `html.clientHeight` adds the bottom bar height in Safari iOS, even\n  // if it isn't open, so if this isn't available, the popper will be detected\n  // to overflow the bottom of the screen too early.\n\n  if (visualViewport) {\n    width = visualViewport.width;\n    height = visualViewport.height; // Uses Layout Viewport (like Chrome; Safari does not currently)\n    // In Chrome, it returns a value very close to 0 (+/-) but contains rounding\n    // errors due to floating point numbers, so we need to check precision.\n    // Safari returns a number <= 0, usually < -1 when pinch-zoomed\n    // Feature detection fails in mobile emulation mode in Chrome.\n    // Math.abs(win.innerWidth / visualViewport.scale - visualViewport.width) <\n    // 0.001\n    // Fallback here: \"Not Safari\" userAgent\n\n    if (!/^((?!chrome|android).)*safari/i.test(navigator.userAgent)) {\n      x = visualViewport.offsetLeft;\n      y = visualViewport.offsetTop;\n    }\n  }\n\n  return {\n    width: width,\n    height: height,\n    x: x + getWindowScrollBarX(element),\n    y: y\n  };\n}", "import getDocumentElement from \"./getDocumentElement.js\";\nimport getComputedStyle from \"./getComputedStyle.js\";\nimport getWindowScrollBarX from \"./getWindowScrollBarX.js\";\nimport getWindowScroll from \"./getWindowScroll.js\";\nimport { max } from \"../utils/math.js\"; // Gets the entire size of the scrollable document area, even extending outside\n// of the `<html>` and `<body>` rect bounds if horizontally scrollable\n\nexport default function getDocumentRect(element) {\n  var _element$ownerDocumen;\n\n  var html = getDocumentElement(element);\n  var winScroll = getWindowScroll(element);\n  var body = (_element$ownerDocumen = element.ownerDocument) == null ? void 0 : _element$ownerDocumen.body;\n  var width = max(html.scrollWidth, html.clientWidth, body ? body.scrollWidth : 0, body ? body.clientWidth : 0);\n  var height = max(html.scrollHeight, html.clientHeight, body ? body.scrollHeight : 0, body ? body.clientHeight : 0);\n  var x = -winScroll.scrollLeft + getWindowScrollBarX(element);\n  var y = -winScroll.scrollTop;\n\n  if (getComputedStyle(body || html).direction === 'rtl') {\n    x += max(html.clientWidth, body ? body.clientWidth : 0) - width;\n  }\n\n  return {\n    width: width,\n    height: height,\n    x: x,\n    y: y\n  };\n}", "import getBasePlacement from \"./getBasePlacement.js\";\nimport getVariation from \"./getVariation.js\";\nimport getMainAxisFromPlacement from \"./getMainAxisFromPlacement.js\";\nimport { top, right, bottom, left, start, end } from \"../enums.js\";\nexport default function computeOffsets(_ref) {\n  var reference = _ref.reference,\n      element = _ref.element,\n      placement = _ref.placement;\n  var basePlacement = placement ? getBasePlacement(placement) : null;\n  var variation = placement ? getVariation(placement) : null;\n  var commonX = reference.x + reference.width / 2 - element.width / 2;\n  var commonY = reference.y + reference.height / 2 - element.height / 2;\n  var offsets;\n\n  switch (basePlacement) {\n    case top:\n      offsets = {\n        x: commonX,\n        y: reference.y - element.height\n      };\n      break;\n\n    case bottom:\n      offsets = {\n        x: commonX,\n        y: reference.y + reference.height\n      };\n      break;\n\n    case right:\n      offsets = {\n        x: reference.x + reference.width,\n        y: commonY\n      };\n      break;\n\n    case left:\n      offsets = {\n        x: reference.x - element.width,\n        y: commonY\n      };\n      break;\n\n    default:\n      offsets = {\n        x: reference.x,\n        y: reference.y\n      };\n  }\n\n  var mainAxis = basePlacement ? getMainAxisFromPlacement(basePlacement) : null;\n\n  if (mainAxis != null) {\n    var len = mainAxis === 'y' ? 'height' : 'width';\n\n    switch (variation) {\n      case start:\n        offsets[mainAxis] = offsets[mainAxis] - (reference[len] / 2 - element[len] / 2);\n        break;\n\n      case end:\n        offsets[mainAxis] = offsets[mainAxis] + (reference[len] / 2 - element[len] / 2);\n        break;\n\n      default:\n    }\n  }\n\n  return offsets;\n}", "import getClippingRect from \"../dom-utils/getClippingRect.js\";\nimport getDocumentElement from \"../dom-utils/getDocumentElement.js\";\nimport getBoundingClientRect from \"../dom-utils/getBoundingClientRect.js\";\nimport computeOffsets from \"./computeOffsets.js\";\nimport rectToClientRect from \"./rectToClientRect.js\";\nimport { clippingParents, reference, popper, bottom, top, right, basePlacements, viewport } from \"../enums.js\";\nimport { isElement } from \"../dom-utils/instanceOf.js\";\nimport mergePaddingObject from \"./mergePaddingObject.js\";\nimport expandToHashMap from \"./expandToHashMap.js\"; // eslint-disable-next-line import/no-unused-modules\n\nexport default function detectOverflow(state, options) {\n  if (options === void 0) {\n    options = {};\n  }\n\n  var _options = options,\n      _options$placement = _options.placement,\n      placement = _options$placement === void 0 ? state.placement : _options$placement,\n      _options$boundary = _options.boundary,\n      boundary = _options$boundary === void 0 ? clippingParents : _options$boundary,\n      _options$rootBoundary = _options.rootBoundary,\n      rootBoundary = _options$rootBoundary === void 0 ? viewport : _options$rootBoundary,\n      _options$elementConte = _options.elementContext,\n      elementContext = _options$elementConte === void 0 ? popper : _options$elementConte,\n      _options$altBoundary = _options.altBoundary,\n      altBoundary = _options$altBoundary === void 0 ? false : _options$altBoundary,\n      _options$padding = _options.padding,\n      padding = _options$padding === void 0 ? 0 : _options$padding;\n  var paddingObject = mergePaddingObject(typeof padding !== 'number' ? padding : expandToHashMap(padding, basePlacements));\n  var altContext = elementContext === popper ? reference : popper;\n  var popperRect = state.rects.popper;\n  var element = state.elements[altBoundary ? altContext : elementContext];\n  var clippingClientRect = getClippingRect(isElement(element) ? element : element.contextElement || getDocumentElement(state.elements.popper), boundary, rootBoundary);\n  var referenceClientRect = getBoundingClientRect(state.elements.reference);\n  var popperOffsets = computeOffsets({\n    reference: referenceClientRect,\n    element: popperRect,\n    strategy: 'absolute',\n    placement: placement\n  });\n  var popperClientRect = rectToClientRect(Object.assign({}, popperRect, popperOffsets));\n  var elementClientRect = elementContext === popper ? popperClientRect : referenceClientRect; // positive = overflowing the clipping rect\n  // 0 or negative = within the clipping rect\n\n  var overflowOffsets = {\n    top: clippingClientRect.top - elementClientRect.top + paddingObject.top,\n    bottom: elementClientRect.bottom - clippingClientRect.bottom + paddingObject.bottom,\n    left: clippingClientRect.left - elementClientRect.left + paddingObject.left,\n    right: elementClientRect.right - clippingClientRect.right + paddingObject.right\n  };\n  var offsetData = state.modifiersData.offset; // Offsets can be applied only to the popper element\n\n  if (elementContext === popper && offsetData) {\n    var offset = offsetData[placement];\n    Object.keys(overflowOffsets).forEach(function (key) {\n      var multiply = [right, bottom].indexOf(key) >= 0 ? 1 : -1;\n      var axis = [top, bottom].indexOf(key) >= 0 ? 'y' : 'x';\n      overflowOffsets[key] += offset[axis] * multiply;\n    });\n  }\n\n  return overflowOffsets;\n}", "import getVariation from \"./getVariation.js\";\nimport { variationPlacements, basePlacements, placements as allPlacements } from \"../enums.js\";\nimport detectOverflow from \"./detectOverflow.js\";\nimport getBasePlacement from \"./getBasePlacement.js\";\nexport default function computeAutoPlacement(state, options) {\n  if (options === void 0) {\n    options = {};\n  }\n\n  var _options = options,\n      placement = _options.placement,\n      boundary = _options.boundary,\n      rootBoundary = _options.rootBoundary,\n      padding = _options.padding,\n      flipVariations = _options.flipVariations,\n      _options$allowedAutoP = _options.allowedAutoPlacements,\n      allowedAutoPlacements = _options$allowedAutoP === void 0 ? allPlacements : _options$allowedAutoP;\n  var variation = getVariation(placement);\n  var placements = variation ? flipVariations ? variationPlacements : variationPlacements.filter(function (placement) {\n    return getVariation(placement) === variation;\n  }) : basePlacements;\n  var allowedPlacements = placements.filter(function (placement) {\n    return allowedAutoPlacements.indexOf(placement) >= 0;\n  });\n\n  if (allowedPlacements.length === 0) {\n    allowedPlacements = placements;\n\n    if (process.env.NODE_ENV !== \"production\") {\n      console.error(['Popper: The `allowedAutoPlacements` option did not allow any', 'placements. Ensure the `placement` option matches the variation', 'of the allowed placements.', 'For example, \"auto\" cannot be used to allow \"bottom-start\".', 'Use \"auto-start\" instead.'].join(' '));\n    }\n  } // $FlowFixMe[incompatible-type]: Flow seems to have problems with two array unions...\n\n\n  var overflows = allowedPlacements.reduce(function (acc, placement) {\n    acc[placement] = detectOverflow(state, {\n      placement: placement,\n      boundary: boundary,\n      rootBoundary: rootBoundary,\n      padding: padding\n    })[getBasePlacement(placement)];\n    return acc;\n  }, {});\n  return Object.keys(overflows).sort(function (a, b) {\n    return overflows[a] - overflows[b];\n  });\n}", "import getOppositePlacement from \"../utils/getOppositePlacement.js\";\nimport getBasePlacement from \"../utils/getBasePlacement.js\";\nimport getOppositeVariationPlacement from \"../utils/getOppositeVariationPlacement.js\";\nimport detectOverflow from \"../utils/detectOverflow.js\";\nimport computeAutoPlacement from \"../utils/computeAutoPlacement.js\";\nimport { bottom, top, start, right, left, auto } from \"../enums.js\";\nimport getVariation from \"../utils/getVariation.js\"; // eslint-disable-next-line import/no-unused-modules\n\nfunction getExpandedFallbackPlacements(placement) {\n  if (getBasePlacement(placement) === auto) {\n    return [];\n  }\n\n  var oppositePlacement = getOppositePlacement(placement);\n  return [getOppositeVariationPlacement(placement), oppositePlacement, getOppositeVariationPlacement(oppositePlacement)];\n}\n\nfunction flip(_ref) {\n  var state = _ref.state,\n      options = _ref.options,\n      name = _ref.name;\n\n  if (state.modifiersData[name]._skip) {\n    return;\n  }\n\n  var _options$mainAxis = options.mainAxis,\n      checkMainAxis = _options$mainAxis === void 0 ? true : _options$mainAxis,\n      _options$altAxis = options.altAxis,\n      checkAltAxis = _options$altAxis === void 0 ? true : _options$altAxis,\n      specifiedFallbackPlacements = options.fallbackPlacements,\n      padding = options.padding,\n      boundary = options.boundary,\n      rootBoundary = options.rootBoundary,\n      altBoundary = options.altBoundary,\n      _options$flipVariatio = options.flipVariations,\n      flipVariations = _options$flipVariatio === void 0 ? true : _options$flipVariatio,\n      allowedAutoPlacements = options.allowedAutoPlacements;\n  var preferredPlacement = state.options.placement;\n  var basePlacement = getBasePlacement(preferredPlacement);\n  var isBasePlacement = basePlacement === preferredPlacement;\n  var fallbackPlacements = specifiedFallbackPlacements || (isBasePlacement || !flipVariations ? [getOppositePlacement(preferredPlacement)] : getExpandedFallbackPlacements(preferredPlacement));\n  var placements = [preferredPlacement].concat(fallbackPlacements).reduce(function (acc, placement) {\n    return acc.concat(getBasePlacement(placement) === auto ? computeAutoPlacement(state, {\n      placement: placement,\n      boundary: boundary,\n      rootBoundary: rootBoundary,\n      padding: padding,\n      flipVariations: flipVariations,\n      allowedAutoPlacements: allowedAutoPlacements\n    }) : placement);\n  }, []);\n  var referenceRect = state.rects.reference;\n  var popperRect = state.rects.popper;\n  var checksMap = new Map();\n  var makeFallbackChecks = true;\n  var firstFittingPlacement = placements[0];\n\n  for (var i = 0; i < placements.length; i++) {\n    var placement = placements[i];\n\n    var _basePlacement = getBasePlacement(placement);\n\n    var isStartVariation = getVariation(placement) === start;\n    var isVertical = [top, bottom].indexOf(_basePlacement) >= 0;\n    var len = isVertical ? 'width' : 'height';\n    var overflow = detectOverflow(state, {\n      placement: placement,\n      boundary: boundary,\n      rootBoundary: rootBoundary,\n      altBoundary: altBoundary,\n      padding: padding\n    });\n    var mainVariationSide = isVertical ? isStartVariation ? right : left : isStartVariation ? bottom : top;\n\n    if (referenceRect[len] > popperRect[len]) {\n      mainVariationSide = getOppositePlacement(mainVariationSide);\n    }\n\n    var altVariationSide = getOppositePlacement(mainVariationSide);\n    var checks = [];\n\n    if (checkMainAxis) {\n      checks.push(overflow[_basePlacement] <= 0);\n    }\n\n    if (checkAltAxis) {\n      checks.push(overflow[mainVariationSide] <= 0, overflow[altVariationSide] <= 0);\n    }\n\n    if (checks.every(function (check) {\n      return check;\n    })) {\n      firstFittingPlacement = placement;\n      makeFallbackChecks = false;\n      break;\n    }\n\n    checksMap.set(placement, checks);\n  }\n\n  if (makeFallbackChecks) {\n    // `2` may be desired in some cases – research later\n    var numberOfChecks = flipVariations ? 3 : 1;\n\n    var _loop = function _loop(_i) {\n      var fittingPlacement = placements.find(function (placement) {\n        var checks = checksMap.get(placement);\n\n        if (checks) {\n          return checks.slice(0, _i).every(function (check) {\n            return check;\n          });\n        }\n      });\n\n      if (fittingPlacement) {\n        firstFittingPlacement = fittingPlacement;\n        return \"break\";\n      }\n    };\n\n    for (var _i = numberOfChecks; _i > 0; _i--) {\n      var _ret = _loop(_i);\n\n      if (_ret === \"break\") break;\n    }\n  }\n\n  if (state.placement !== firstFittingPlacement) {\n    state.modifiersData[name]._skip = true;\n    state.placement = firstFittingPlacement;\n    state.reset = true;\n  }\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'flip',\n  enabled: true,\n  phase: 'main',\n  fn: flip,\n  requiresIfExists: ['offset'],\n  data: {\n    _skip: false\n  }\n};", "import { top, bottom, left, right } from \"../enums.js\";\nimport detectOverflow from \"../utils/detectOverflow.js\";\n\nfunction getSideOffsets(overflow, rect, preventedOffsets) {\n  if (preventedOffsets === void 0) {\n    preventedOffsets = {\n      x: 0,\n      y: 0\n    };\n  }\n\n  return {\n    top: overflow.top - rect.height - preventedOffsets.y,\n    right: overflow.right - rect.width + preventedOffsets.x,\n    bottom: overflow.bottom - rect.height + preventedOffsets.y,\n    left: overflow.left - rect.width - preventedOffsets.x\n  };\n}\n\nfunction isAnySideFullyClipped(overflow) {\n  return [top, right, bottom, left].some(function (side) {\n    return overflow[side] >= 0;\n  });\n}\n\nfunction hide(_ref) {\n  var state = _ref.state,\n      name = _ref.name;\n  var referenceRect = state.rects.reference;\n  var popperRect = state.rects.popper;\n  var preventedOffsets = state.modifiersData.preventOverflow;\n  var referenceOverflow = detectOverflow(state, {\n    elementContext: 'reference'\n  });\n  var popperAltOverflow = detectOverflow(state, {\n    altBoundary: true\n  });\n  var referenceClippingOffsets = getSideOffsets(referenceOverflow, referenceRect);\n  var popperEscapeOffsets = getSideOffsets(popperAltOverflow, popperRect, preventedOffsets);\n  var isReferenceHidden = isAnySideFullyClipped(referenceClippingOffsets);\n  var hasPopperEscaped = isAnySideFullyClipped(popperEscapeOffsets);\n  state.modifiersData[name] = {\n    referenceClippingOffsets: referenceClippingOffsets,\n    popperEscapeOffsets: popperEscapeOffsets,\n    isReferenceHidden: isReferenceHidden,\n    hasPopperEscaped: hasPopperEscaped\n  };\n  state.attributes.popper = Object.assign({}, state.attributes.popper, {\n    'data-popper-reference-hidden': isReferenceHidden,\n    'data-popper-escaped': hasPopperEscaped\n  });\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'hide',\n  enabled: true,\n  phase: 'main',\n  requiresIfExists: ['preventOverflow'],\n  fn: hide\n};", "import getBasePlacement from \"../utils/getBasePlacement.js\";\nimport { top, left, right, placements } from \"../enums.js\";\nexport function distanceAndSkiddingToXY(placement, rects, offset) {\n  var basePlacement = getBasePlacement(placement);\n  var invertDistance = [left, top].indexOf(basePlacement) >= 0 ? -1 : 1;\n\n  var _ref = typeof offset === 'function' ? offset(Object.assign({}, rects, {\n    placement: placement\n  })) : offset,\n      skidding = _ref[0],\n      distance = _ref[1];\n\n  skidding = skidding || 0;\n  distance = (distance || 0) * invertDistance;\n  return [left, right].indexOf(basePlacement) >= 0 ? {\n    x: distance,\n    y: skidding\n  } : {\n    x: skidding,\n    y: distance\n  };\n}\n\nfunction offset(_ref2) {\n  var state = _ref2.state,\n      options = _ref2.options,\n      name = _ref2.name;\n  var _options$offset = options.offset,\n      offset = _options$offset === void 0 ? [0, 0] : _options$offset;\n  var data = placements.reduce(function (acc, placement) {\n    acc[placement] = distanceAndSkiddingToXY(placement, state.rects, offset);\n    return acc;\n  }, {});\n  var _data$state$placement = data[state.placement],\n      x = _data$state$placement.x,\n      y = _data$state$placement.y;\n\n  if (state.modifiersData.popperOffsets != null) {\n    state.modifiersData.popperOffsets.x += x;\n    state.modifiersData.popperOffsets.y += y;\n  }\n\n  state.modifiersData[name] = data;\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'offset',\n  enabled: true,\n  phase: 'main',\n  requires: ['popperOffsets'],\n  fn: offset\n};", "import computeOffsets from \"../utils/computeOffsets.js\";\n\nfunction popperOffsets(_ref) {\n  var state = _ref.state,\n      name = _ref.name;\n  // Offsets are the actual position the popper needs to have to be\n  // properly positioned near its reference element\n  // This is the most basic placement, and will be adjusted by\n  // the modifiers in the next step\n  state.modifiersData[name] = computeOffsets({\n    reference: state.rects.reference,\n    element: state.rects.popper,\n    strategy: 'absolute',\n    placement: state.placement\n  });\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'popperOffsets',\n  enabled: true,\n  phase: 'read',\n  fn: popperOffsets,\n  data: {}\n};", "import { top, left, right, bottom, start } from \"../enums.js\";\nimport getBasePlacement from \"../utils/getBasePlacement.js\";\nimport getMainAxisFromPlacement from \"../utils/getMainAxisFromPlacement.js\";\nimport getAltAxis from \"../utils/getAltAxis.js\";\nimport within from \"../utils/within.js\";\nimport getLayoutRect from \"../dom-utils/getLayoutRect.js\";\nimport getOffsetParent from \"../dom-utils/getOffsetParent.js\";\nimport detectOverflow from \"../utils/detectOverflow.js\";\nimport getVariation from \"../utils/getVariation.js\";\nimport getFreshSideObject from \"../utils/getFreshSideObject.js\";\nimport { max as mathMax, min as mathMin } from \"../utils/math.js\";\n\nfunction preventOverflow(_ref) {\n  var state = _ref.state,\n      options = _ref.options,\n      name = _ref.name;\n  var _options$mainAxis = options.mainAxis,\n      checkMainAxis = _options$mainAxis === void 0 ? true : _options$mainAxis,\n      _options$altAxis = options.altAxis,\n      checkAltAxis = _options$altAxis === void 0 ? false : _options$altAxis,\n      boundary = options.boundary,\n      rootBoundary = options.rootBoundary,\n      altBoundary = options.altBoundary,\n      padding = options.padding,\n      _options$tether = options.tether,\n      tether = _options$tether === void 0 ? true : _options$tether,\n      _options$tetherOffset = options.tetherOffset,\n      tetherOffset = _options$tetherOffset === void 0 ? 0 : _options$tetherOffset;\n  var overflow = detectOverflow(state, {\n    boundary: boundary,\n    rootBoundary: rootBoundary,\n    padding: padding,\n    altBoundary: altBoundary\n  });\n  var basePlacement = getBasePlacement(state.placement);\n  var variation = getVariation(state.placement);\n  var isBasePlacement = !variation;\n  var mainAxis = getMainAxisFromPlacement(basePlacement);\n  var altAxis = getAltAxis(mainAxis);\n  var popperOffsets = state.modifiersData.popperOffsets;\n  var referenceRect = state.rects.reference;\n  var popperRect = state.rects.popper;\n  var tetherOffsetValue = typeof tetherOffset === 'function' ? tetherOffset(Object.assign({}, state.rects, {\n    placement: state.placement\n  })) : tetherOffset;\n  var data = {\n    x: 0,\n    y: 0\n  };\n\n  if (!popperOffsets) {\n    return;\n  }\n\n  if (checkMainAxis || checkAltAxis) {\n    var mainSide = mainAxis === 'y' ? top : left;\n    var altSide = mainAxis === 'y' ? bottom : right;\n    var len = mainAxis === 'y' ? 'height' : 'width';\n    var offset = popperOffsets[mainAxis];\n    var min = popperOffsets[mainAxis] + overflow[mainSide];\n    var max = popperOffsets[mainAxis] - overflow[altSide];\n    var additive = tether ? -popperRect[len] / 2 : 0;\n    var minLen = variation === start ? referenceRect[len] : popperRect[len];\n    var maxLen = variation === start ? -popperRect[len] : -referenceRect[len]; // We need to include the arrow in the calculation so the arrow doesn't go\n    // outside the reference bounds\n\n    var arrowElement = state.elements.arrow;\n    var arrowRect = tether && arrowElement ? getLayoutRect(arrowElement) : {\n      width: 0,\n      height: 0\n    };\n    var arrowPaddingObject = state.modifiersData['arrow#persistent'] ? state.modifiersData['arrow#persistent'].padding : getFreshSideObject();\n    var arrowPaddingMin = arrowPaddingObject[mainSide];\n    var arrowPaddingMax = arrowPaddingObject[altSide]; // If the reference length is smaller than the arrow length, we don't want\n    // to include its full size in the calculation. If the reference is small\n    // and near the edge of a boundary, the popper can overflow even if the\n    // reference is not overflowing as well (e.g. virtual elements with no\n    // width or height)\n\n    var arrowLen = within(0, referenceRect[len], arrowRect[len]);\n    var minOffset = isBasePlacement ? referenceRect[len] / 2 - additive - arrowLen - arrowPaddingMin - tetherOffsetValue : minLen - arrowLen - arrowPaddingMin - tetherOffsetValue;\n    var maxOffset = isBasePlacement ? -referenceRect[len] / 2 + additive + arrowLen + arrowPaddingMax + tetherOffsetValue : maxLen + arrowLen + arrowPaddingMax + tetherOffsetValue;\n    var arrowOffsetParent = state.elements.arrow && getOffsetParent(state.elements.arrow);\n    var clientOffset = arrowOffsetParent ? mainAxis === 'y' ? arrowOffsetParent.clientTop || 0 : arrowOffsetParent.clientLeft || 0 : 0;\n    var offsetModifierValue = state.modifiersData.offset ? state.modifiersData.offset[state.placement][mainAxis] : 0;\n    var tetherMin = popperOffsets[mainAxis] + minOffset - offsetModifierValue - clientOffset;\n    var tetherMax = popperOffsets[mainAxis] + maxOffset - offsetModifierValue;\n\n    if (checkMainAxis) {\n      var preventedOffset = within(tether ? mathMin(min, tetherMin) : min, offset, tether ? mathMax(max, tetherMax) : max);\n      popperOffsets[mainAxis] = preventedOffset;\n      data[mainAxis] = preventedOffset - offset;\n    }\n\n    if (checkAltAxis) {\n      var _mainSide = mainAxis === 'x' ? top : left;\n\n      var _altSide = mainAxis === 'x' ? bottom : right;\n\n      var _offset = popperOffsets[altAxis];\n\n      var _min = _offset + overflow[_mainSide];\n\n      var _max = _offset - overflow[_altSide];\n\n      var _preventedOffset = within(tether ? mathMin(_min, tetherMin) : _min, _offset, tether ? mathMax(_max, tetherMax) : _max);\n\n      popperOffsets[altAxis] = _preventedOffset;\n      data[altAxis] = _preventedOffset - _offset;\n    }\n  }\n\n  state.modifiersData[name] = data;\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'preventOverflow',\n  enabled: true,\n  phase: 'main',\n  fn: preventOverflow,\n  requiresIfExists: ['offset']\n};", "export default function getAltAxis(axis) {\n  return axis === 'x' ? 'y' : 'x';\n}", "import getBoundingClientRect from \"./getBoundingClientRect.js\";\nimport getNodeScroll from \"./getNodeScroll.js\";\nimport getNodeName from \"./getNodeName.js\";\nimport { isHTMLElement } from \"./instanceOf.js\";\nimport getWindowScrollBarX from \"./getWindowScrollBarX.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport isScrollParent from \"./isScrollParent.js\";\n\nfunction isElementScaled(element) {\n  var rect = element.getBoundingClientRect();\n  var scaleX = rect.width / element.offsetWidth || 1;\n  var scaleY = rect.height / element.offsetHeight || 1;\n  return scaleX !== 1 || scaleY !== 1;\n} // Returns the composite rect of an element relative to its offsetParent.\n// Composite means it takes into account transforms as well as layout.\n\n\nexport default function getCompositeRect(elementOrVirtualElement, offsetParent, isFixed) {\n  if (isFixed === void 0) {\n    isFixed = false;\n  }\n\n  var isOffsetParentAnElement = isHTMLElement(offsetParent);\n  var offsetParentIsScaled = isHTMLElement(offsetParent) && isElementScaled(offsetParent);\n  var documentElement = getDocumentElement(offsetParent);\n  var rect = getBoundingClientRect(elementOrVirtualElement, offsetParentIsScaled);\n  var scroll = {\n    scrollLeft: 0,\n    scrollTop: 0\n  };\n  var offsets = {\n    x: 0,\n    y: 0\n  };\n\n  if (isOffsetParentAnElement || !isOffsetParentAnElement && !isFixed) {\n    if (getNodeName(offsetParent) !== 'body' || // https://github.com/popperjs/popper-core/issues/1078\n    isScrollParent(documentElement)) {\n      scroll = getNodeScroll(offsetParent);\n    }\n\n    if (isHTMLElement(offsetParent)) {\n      offsets = getBoundingClientRect(offsetParent, true);\n      offsets.x += offsetParent.clientLeft;\n      offsets.y += offsetParent.clientTop;\n    } else if (documentElement) {\n      offsets.x = getWindowScrollBarX(documentElement);\n    }\n  }\n\n  return {\n    x: rect.left + scroll.scrollLeft - offsets.x,\n    y: rect.top + scroll.scrollTop - offsets.y,\n    width: rect.width,\n    height: rect.height\n  };\n}", "import getWindowScroll from \"./getWindowScroll.js\";\nimport getWindow from \"./getWindow.js\";\nimport { isHTMLElement } from \"./instanceOf.js\";\nimport getHTMLElementScroll from \"./getHTMLElementScroll.js\";\nexport default function getNodeScroll(node) {\n  if (node === getWindow(node) || !isHTMLElement(node)) {\n    return getWindowScroll(node);\n  } else {\n    return getHTMLElementScroll(node);\n  }\n}", "export default function getHTMLElementScroll(element) {\n  return {\n    scrollLeft: element.scrollLeft,\n    scrollTop: element.scrollTop\n  };\n}", "import { modifierPhases } from \"../enums.js\"; // source: https://stackoverflow.com/questions/49875255\n\nfunction order(modifiers) {\n  var map = new Map();\n  var visited = new Set();\n  var result = [];\n  modifiers.forEach(function (modifier) {\n    map.set(modifier.name, modifier);\n  }); // On visiting object, check for its dependencies and visit them recursively\n\n  function sort(modifier) {\n    visited.add(modifier.name);\n    var requires = [].concat(modifier.requires || [], modifier.requiresIfExists || []);\n    requires.forEach(function (dep) {\n      if (!visited.has(dep)) {\n        var depModifier = map.get(dep);\n\n        if (depModifier) {\n          sort(depModifier);\n        }\n      }\n    });\n    result.push(modifier);\n  }\n\n  modifiers.forEach(function (modifier) {\n    if (!visited.has(modifier.name)) {\n      // check for visited object\n      sort(modifier);\n    }\n  });\n  return result;\n}\n\nexport default function orderModifiers(modifiers) {\n  // order based on dependencies\n  var orderedModifiers = order(modifiers); // order based on phase\n\n  return modifierPhases.reduce(function (acc, phase) {\n    return acc.concat(orderedModifiers.filter(function (modifier) {\n      return modifier.phase === phase;\n    }));\n  }, []);\n}", "import getCompositeRect from \"./dom-utils/getCompositeRect.js\";\nimport getLayoutRect from \"./dom-utils/getLayoutRect.js\";\nimport listScrollParents from \"./dom-utils/listScrollParents.js\";\nimport getOffsetParent from \"./dom-utils/getOffsetParent.js\";\nimport getComputedStyle from \"./dom-utils/getComputedStyle.js\";\nimport orderModifiers from \"./utils/orderModifiers.js\";\nimport debounce from \"./utils/debounce.js\";\nimport validateModifiers from \"./utils/validateModifiers.js\";\nimport uniqueBy from \"./utils/uniqueBy.js\";\nimport getBasePlacement from \"./utils/getBasePlacement.js\";\nimport mergeByName from \"./utils/mergeByName.js\";\nimport detectOverflow from \"./utils/detectOverflow.js\";\nimport { isElement } from \"./dom-utils/instanceOf.js\";\nimport { auto } from \"./enums.js\";\nvar INVALID_ELEMENT_ERROR = 'Popper: Invalid reference or popper argument provided. They must be either a DOM element or virtual element.';\nvar INFINITE_LOOP_ERROR = 'Popper: An infinite loop in the modifiers cycle has been detected! The cycle has been interrupted to prevent a browser crash.';\nvar DEFAULT_OPTIONS = {\n  placement: 'bottom',\n  modifiers: [],\n  strategy: 'absolute'\n};\n\nfunction areValidElements() {\n  for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n    args[_key] = arguments[_key];\n  }\n\n  return !args.some(function (element) {\n    return !(element && typeof element.getBoundingClientRect === 'function');\n  });\n}\n\nexport function popperGenerator(generatorOptions) {\n  if (generatorOptions === void 0) {\n    generatorOptions = {};\n  }\n\n  var _generatorOptions = generatorOptions,\n      _generatorOptions$def = _generatorOptions.defaultModifiers,\n      defaultModifiers = _generatorOptions$def === void 0 ? [] : _generatorOptions$def,\n      _generatorOptions$def2 = _generatorOptions.defaultOptions,\n      defaultOptions = _generatorOptions$def2 === void 0 ? DEFAULT_OPTIONS : _generatorOptions$def2;\n  return function createPopper(reference, popper, options) {\n    if (options === void 0) {\n      options = defaultOptions;\n    }\n\n    var state = {\n      placement: 'bottom',\n      orderedModifiers: [],\n      options: Object.assign({}, DEFAULT_OPTIONS, defaultOptions),\n      modifiersData: {},\n      elements: {\n        reference: reference,\n        popper: popper\n      },\n      attributes: {},\n      styles: {}\n    };\n    var effectCleanupFns = [];\n    var isDestroyed = false;\n    var instance = {\n      state: state,\n      setOptions: function setOptions(setOptionsAction) {\n        var options = typeof setOptionsAction === 'function' ? setOptionsAction(state.options) : setOptionsAction;\n        cleanupModifierEffects();\n        state.options = Object.assign({}, defaultOptions, state.options, options);\n        state.scrollParents = {\n          reference: isElement(reference) ? listScrollParents(reference) : reference.contextElement ? listScrollParents(reference.contextElement) : [],\n          popper: listScrollParents(popper)\n        }; // Orders the modifiers based on their dependencies and `phase`\n        // properties\n\n        var orderedModifiers = orderModifiers(mergeByName([].concat(defaultModifiers, state.options.modifiers))); // Strip out disabled modifiers\n\n        state.orderedModifiers = orderedModifiers.filter(function (m) {\n          return m.enabled;\n        }); // Validate the provided modifiers so that the consumer will get warned\n        // if one of the modifiers is invalid for any reason\n\n        if (process.env.NODE_ENV !== \"production\") {\n          var modifiers = uniqueBy([].concat(orderedModifiers, state.options.modifiers), function (_ref) {\n            var name = _ref.name;\n            return name;\n          });\n          validateModifiers(modifiers);\n\n          if (getBasePlacement(state.options.placement) === auto) {\n            var flipModifier = state.orderedModifiers.find(function (_ref2) {\n              var name = _ref2.name;\n              return name === 'flip';\n            });\n\n            if (!flipModifier) {\n              console.error(['Popper: \"auto\" placements require the \"flip\" modifier be', 'present and enabled to work.'].join(' '));\n            }\n          }\n\n          var _getComputedStyle = getComputedStyle(popper),\n              marginTop = _getComputedStyle.marginTop,\n              marginRight = _getComputedStyle.marginRight,\n              marginBottom = _getComputedStyle.marginBottom,\n              marginLeft = _getComputedStyle.marginLeft; // We no longer take into account `margins` on the popper, and it can\n          // cause bugs with positioning, so we'll warn the consumer\n\n\n          if ([marginTop, marginRight, marginBottom, marginLeft].some(function (margin) {\n            return parseFloat(margin);\n          })) {\n            console.warn(['Popper: CSS \"margin\" styles cannot be used to apply padding', 'between the popper and its reference element or boundary.', 'To replicate margin, use the `offset` modifier, as well as', 'the `padding` option in the `preventOverflow` and `flip`', 'modifiers.'].join(' '));\n          }\n        }\n\n        runModifierEffects();\n        return instance.update();\n      },\n      // Sync update – it will always be executed, even if not necessary. This\n      // is useful for low frequency updates where sync behavior simplifies the\n      // logic.\n      // For high frequency updates (e.g. `resize` and `scroll` events), always\n      // prefer the async Popper#update method\n      forceUpdate: function forceUpdate() {\n        if (isDestroyed) {\n          return;\n        }\n\n        var _state$elements = state.elements,\n            reference = _state$elements.reference,\n            popper = _state$elements.popper; // Don't proceed if `reference` or `popper` are not valid elements\n        // anymore\n\n        if (!areValidElements(reference, popper)) {\n          if (process.env.NODE_ENV !== \"production\") {\n            console.error(INVALID_ELEMENT_ERROR);\n          }\n\n          return;\n        } // Store the reference and popper rects to be read by modifiers\n\n\n        state.rects = {\n          reference: getCompositeRect(reference, getOffsetParent(popper), state.options.strategy === 'fixed'),\n          popper: getLayoutRect(popper)\n        }; // Modifiers have the ability to reset the current update cycle. The\n        // most common use case for this is the `flip` modifier changing the\n        // placement, which then needs to re-run all the modifiers, because the\n        // logic was previously ran for the previous placement and is therefore\n        // stale/incorrect\n\n        state.reset = false;\n        state.placement = state.options.placement; // On each update cycle, the `modifiersData` property for each modifier\n        // is filled with the initial data specified by the modifier. This means\n        // it doesn't persist and is fresh on each update.\n        // To ensure persistent data, use `${name}#persistent`\n\n        state.orderedModifiers.forEach(function (modifier) {\n          return state.modifiersData[modifier.name] = Object.assign({}, modifier.data);\n        });\n        var __debug_loops__ = 0;\n\n        for (var index = 0; index < state.orderedModifiers.length; index++) {\n          if (process.env.NODE_ENV !== \"production\") {\n            __debug_loops__ += 1;\n\n            if (__debug_loops__ > 100) {\n              console.error(INFINITE_LOOP_ERROR);\n              break;\n            }\n          }\n\n          if (state.reset === true) {\n            state.reset = false;\n            index = -1;\n            continue;\n          }\n\n          var _state$orderedModifie = state.orderedModifiers[index],\n              fn = _state$orderedModifie.fn,\n              _state$orderedModifie2 = _state$orderedModifie.options,\n              _options = _state$orderedModifie2 === void 0 ? {} : _state$orderedModifie2,\n              name = _state$orderedModifie.name;\n\n          if (typeof fn === 'function') {\n            state = fn({\n              state: state,\n              options: _options,\n              name: name,\n              instance: instance\n            }) || state;\n          }\n        }\n      },\n      // Async and optimistically optimized update – it will not be executed if\n      // not necessary (debounced to run at most once-per-tick)\n      update: debounce(function () {\n        return new Promise(function (resolve) {\n          instance.forceUpdate();\n          resolve(state);\n        });\n      }),\n      destroy: function destroy() {\n        cleanupModifierEffects();\n        isDestroyed = true;\n      }\n    };\n\n    if (!areValidElements(reference, popper)) {\n      if (process.env.NODE_ENV !== \"production\") {\n        console.error(INVALID_ELEMENT_ERROR);\n      }\n\n      return instance;\n    }\n\n    instance.setOptions(options).then(function (state) {\n      if (!isDestroyed && options.onFirstUpdate) {\n        options.onFirstUpdate(state);\n      }\n    }); // Modifiers have the ability to execute arbitrary code before the first\n    // update cycle runs. They will be executed in the same order as the update\n    // cycle. This is useful when a modifier adds some persistent data that\n    // other modifiers need to use, but the modifier is run after the dependent\n    // one.\n\n    function runModifierEffects() {\n      state.orderedModifiers.forEach(function (_ref3) {\n        var name = _ref3.name,\n            _ref3$options = _ref3.options,\n            options = _ref3$options === void 0 ? {} : _ref3$options,\n            effect = _ref3.effect;\n\n        if (typeof effect === 'function') {\n          var cleanupFn = effect({\n            state: state,\n            name: name,\n            instance: instance,\n            options: options\n          });\n\n          var noopFn = function noopFn() {};\n\n          effectCleanupFns.push(cleanupFn || noopFn);\n        }\n      });\n    }\n\n    function cleanupModifierEffects() {\n      effectCleanupFns.forEach(function (fn) {\n        return fn();\n      });\n      effectCleanupFns = [];\n    }\n\n    return instance;\n  };\n}\nexport var createPopper = /*#__PURE__*/popperGenerator(); // eslint-disable-next-line import/no-unused-modules\n\nexport { detectOverflow };", "export default function debounce(fn) {\n  var pending;\n  return function () {\n    if (!pending) {\n      pending = new Promise(function (resolve) {\n        Promise.resolve().then(function () {\n          pending = undefined;\n          resolve(fn());\n        });\n      });\n    }\n\n    return pending;\n  };\n}", "export default function mergeByName(modifiers) {\n  var merged = modifiers.reduce(function (merged, current) {\n    var existing = merged[current.name];\n    merged[current.name] = existing ? Object.assign({}, existing, current, {\n      options: Object.assign({}, existing.options, current.options),\n      data: Object.assign({}, existing.data, current.data)\n    }) : current;\n    return merged;\n  }, {}); // IE11 does not support Object.values\n\n  return Object.keys(merged).map(function (key) {\n    return merged[key];\n  });\n}", "import { popperGenerator, detectOverflow } from \"./createPopper.js\";\nimport eventListeners from \"./modifiers/eventListeners.js\";\nimport popperOffsets from \"./modifiers/popperOffsets.js\";\nimport computeStyles from \"./modifiers/computeStyles.js\";\nimport applyStyles from \"./modifiers/applyStyles.js\";\nvar defaultModifiers = [eventListeners, popperOffsets, computeStyles, applyStyles];\nvar createPopper = /*#__PURE__*/popperGenerator({\n  defaultModifiers: defaultModifiers\n}); // eslint-disable-next-line import/no-unused-modules\n\nexport { createPopper, popperGenerator, defaultModifiers, detectOverflow };", "import { popperGenerator, detectOverflow } from \"./createPopper.js\";\nimport eventListeners from \"./modifiers/eventListeners.js\";\nimport popperOffsets from \"./modifiers/popperOffsets.js\";\nimport computeStyles from \"./modifiers/computeStyles.js\";\nimport applyStyles from \"./modifiers/applyStyles.js\";\nimport offset from \"./modifiers/offset.js\";\nimport flip from \"./modifiers/flip.js\";\nimport preventOverflow from \"./modifiers/preventOverflow.js\";\nimport arrow from \"./modifiers/arrow.js\";\nimport hide from \"./modifiers/hide.js\";\nvar defaultModifiers = [eventListeners, popperOffsets, computeStyles, applyStyles, offset, flip, preventOverflow, arrow, hide];\nvar createPopper = /*#__PURE__*/popperGenerator({\n  defaultModifiers: defaultModifiers\n}); // eslint-disable-next-line import/no-unused-modules\n\nexport { createPopper, popperGenerator, defaultModifiers, detectOverflow }; // eslint-disable-next-line import/no-unused-modules\n\nexport { createPopper as createPopperLite } from \"./popper-lite.js\"; // eslint-disable-next-line import/no-unused-modules\n\nexport * from \"./modifiers/index.js\";", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.1.3): dropdown.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport * as Popper from '@popperjs/core'\n\nimport {\n  defineJQueryPlugin,\n  getElement,\n  getElementFromSelector,\n  getNextActiveElement,\n  isDisabled,\n  isElement,\n  isRTL,\n  isVisible,\n  noop,\n  typeCheckConfig\n} from './util/index'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'dropdown'\nconst DATA_KEY = 'bs.dropdown'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst ESCAPE_KEY = 'Escape'\nconst SPACE_KEY = 'Space'\nconst TAB_KEY = 'Tab'\nconst ARROW_UP_KEY = 'ArrowUp'\nconst ARROW_DOWN_KEY = 'ArrowDown'\nconst RIGHT_MOUSE_BUTTON = 2 // MouseEvent.button value for the secondary button, usually the right button\n\nconst REGEXP_KEYDOWN = new RegExp(`${ARROW_UP_KEY}|${ARROW_DOWN_KEY}|${ESCAPE_KEY}`)\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYDOWN_DATA_API = `keydown${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYUP_DATA_API = `keyup${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_DROPUP = 'dropup'\nconst CLASS_NAME_DROPEND = 'dropend'\nconst CLASS_NAME_DROPSTART = 'dropstart'\nconst CLASS_NAME_NAVBAR = 'navbar'\n\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"dropdown\"]'\nconst SELECTOR_MENU = '.dropdown-menu'\nconst SELECTOR_NAVBAR_NAV = '.navbar-nav'\nconst SELECTOR_VISIBLE_ITEMS = '.dropdown-menu .dropdown-item:not(.disabled):not(:disabled)'\n\nconst PLACEMENT_TOP = isRTL() ? 'top-end' : 'top-start'\nconst PLACEMENT_TOPEND = isRTL() ? 'top-start' : 'top-end'\nconst PLACEMENT_BOTTOM = isRTL() ? 'bottom-end' : 'bottom-start'\nconst PLACEMENT_BOTTOMEND = isRTL() ? 'bottom-start' : 'bottom-end'\nconst PLACEMENT_RIGHT = isRTL() ? 'left-start' : 'right-start'\nconst PLACEMENT_LEFT = isRTL() ? 'right-start' : 'left-start'\n\nconst Default = {\n  offset: [0, 2],\n  boundary: 'clippingParents',\n  reference: 'toggle',\n  display: 'dynamic',\n  popperConfig: null,\n  autoClose: true\n}\n\nconst DefaultType = {\n  offset: '(array|string|function)',\n  boundary: '(string|element)',\n  reference: '(string|element|object)',\n  display: 'string',\n  popperConfig: '(null|object|function)',\n  autoClose: '(boolean|string)'\n}\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Dropdown extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n\n    this._popper = null\n    this._config = this._getConfig(config)\n    this._menu = this._getMenuElement()\n    this._inNavbar = this._detectNavbar()\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n\n  toggle() {\n    return this._isShown() ? this.hide() : this.show()\n  }\n\n  show() {\n    if (isDisabled(this._element) || this._isShown(this._menu)) {\n      return\n    }\n\n    const relatedTarget = {\n      relatedTarget: this._element\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, relatedTarget)\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    const parent = Dropdown.getParentFromElement(this._element)\n    // Totally disable Popper for Dropdowns in Navbar\n    if (this._inNavbar) {\n      Manipulator.setDataAttribute(this._menu, 'popper', 'none')\n    } else {\n      this._createPopper(parent)\n    }\n\n    // If this is a touch-enabled device we add extra\n    // empty mouseover listeners to the body's immediate children;\n    // only needed because of broken event delegation on iOS\n    // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n    if ('ontouchstart' in document.documentElement &&\n      !parent.closest(SELECTOR_NAVBAR_NAV)) {\n      [].concat(...document.body.children)\n        .forEach(elem => EventHandler.on(elem, 'mouseover', noop))\n    }\n\n    this._element.focus()\n    this._element.setAttribute('aria-expanded', true)\n\n    this._menu.classList.add(CLASS_NAME_SHOW)\n    this._element.classList.add(CLASS_NAME_SHOW)\n    EventHandler.trigger(this._element, EVENT_SHOWN, relatedTarget)\n  }\n\n  hide() {\n    if (isDisabled(this._element) || !this._isShown(this._menu)) {\n      return\n    }\n\n    const relatedTarget = {\n      relatedTarget: this._element\n    }\n\n    this._completeHide(relatedTarget)\n  }\n\n  dispose() {\n    if (this._popper) {\n      this._popper.destroy()\n    }\n\n    super.dispose()\n  }\n\n  update() {\n    this._inNavbar = this._detectNavbar()\n    if (this._popper) {\n      this._popper.update()\n    }\n  }\n\n  // Private\n\n  _completeHide(relatedTarget) {\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE, relatedTarget)\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    // If this is a touch-enabled device we remove the extra\n    // empty mouseover listeners we added for iOS support\n    if ('ontouchstart' in document.documentElement) {\n      [].concat(...document.body.children)\n        .forEach(elem => EventHandler.off(elem, 'mouseover', noop))\n    }\n\n    if (this._popper) {\n      this._popper.destroy()\n    }\n\n    this._menu.classList.remove(CLASS_NAME_SHOW)\n    this._element.classList.remove(CLASS_NAME_SHOW)\n    this._element.setAttribute('aria-expanded', 'false')\n    Manipulator.removeDataAttribute(this._menu, 'popper')\n    EventHandler.trigger(this._element, EVENT_HIDDEN, relatedTarget)\n  }\n\n  _getConfig(config) {\n    config = {\n      ...this.constructor.Default,\n      ...Manipulator.getDataAttributes(this._element),\n      ...config\n    }\n\n    typeCheckConfig(NAME, config, this.constructor.DefaultType)\n\n    if (typeof config.reference === 'object' && !isElement(config.reference) &&\n      typeof config.reference.getBoundingClientRect !== 'function'\n    ) {\n      // Popper virtual elements require a getBoundingClientRect method\n      throw new TypeError(`${NAME.toUpperCase()}: Option \"reference\" provided type \"object\" without a required \"getBoundingClientRect\" method.`)\n    }\n\n    return config\n  }\n\n  _createPopper(parent) {\n    if (typeof Popper === 'undefined') {\n      throw new TypeError('Bootstrap\\'s dropdowns require Popper (https://popper.js.org)')\n    }\n\n    let referenceElement = this._element\n\n    if (this._config.reference === 'parent') {\n      referenceElement = parent\n    } else if (isElement(this._config.reference)) {\n      referenceElement = getElement(this._config.reference)\n    } else if (typeof this._config.reference === 'object') {\n      referenceElement = this._config.reference\n    }\n\n    const popperConfig = this._getPopperConfig()\n    const isDisplayStatic = popperConfig.modifiers.find(modifier => modifier.name === 'applyStyles' && modifier.enabled === false)\n\n    this._popper = Popper.createPopper(referenceElement, this._menu, popperConfig)\n\n    if (isDisplayStatic) {\n      Manipulator.setDataAttribute(this._menu, 'popper', 'static')\n    }\n  }\n\n  _isShown(element = this._element) {\n    return element.classList.contains(CLASS_NAME_SHOW)\n  }\n\n  _getMenuElement() {\n    return SelectorEngine.next(this._element, SELECTOR_MENU)[0]\n  }\n\n  _getPlacement() {\n    const parentDropdown = this._element.parentNode\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPEND)) {\n      return PLACEMENT_RIGHT\n    }\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPSTART)) {\n      return PLACEMENT_LEFT\n    }\n\n    // We need to trim the value because custom properties can also include spaces\n    const isEnd = getComputedStyle(this._menu).getPropertyValue('--bs-position').trim() === 'end'\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPUP)) {\n      return isEnd ? PLACEMENT_TOPEND : PLACEMENT_TOP\n    }\n\n    return isEnd ? PLACEMENT_BOTTOMEND : PLACEMENT_BOTTOM\n  }\n\n  _detectNavbar() {\n    return this._element.closest(`.${CLASS_NAME_NAVBAR}`) !== null\n  }\n\n  _getOffset() {\n    const { offset } = this._config\n\n    if (typeof offset === 'string') {\n      return offset.split(',').map(val => Number.parseInt(val, 10))\n    }\n\n    if (typeof offset === 'function') {\n      return popperData => offset(popperData, this._element)\n    }\n\n    return offset\n  }\n\n  _getPopperConfig() {\n    const defaultBsPopperConfig = {\n      placement: this._getPlacement(),\n      modifiers: [{\n        name: 'preventOverflow',\n        options: {\n          boundary: this._config.boundary\n        }\n      },\n      {\n        name: 'offset',\n        options: {\n          offset: this._getOffset()\n        }\n      }]\n    }\n\n    // Disable Popper if we have a static display\n    if (this._config.display === 'static') {\n      defaultBsPopperConfig.modifiers = [{\n        name: 'applyStyles',\n        enabled: false\n      }]\n    }\n\n    return {\n      ...defaultBsPopperConfig,\n      ...(typeof this._config.popperConfig === 'function' ? this._config.popperConfig(defaultBsPopperConfig) : this._config.popperConfig)\n    }\n  }\n\n  _selectMenuItem({ key, target }) {\n    const items = SelectorEngine.find(SELECTOR_VISIBLE_ITEMS, this._menu).filter(isVisible)\n\n    if (!items.length) {\n      return\n    }\n\n    // if target isn't included in items (e.g. when expanding the dropdown)\n    // allow cycling to get the last item in case key equals ARROW_UP_KEY\n    getNextActiveElement(items, target, key === ARROW_DOWN_KEY, !items.includes(target)).focus()\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Dropdown.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    })\n  }\n\n  static clearMenus(event) {\n    if (event && (event.button === RIGHT_MOUSE_BUTTON || (event.type === 'keyup' && event.key !== TAB_KEY))) {\n      return\n    }\n\n    const toggles = SelectorEngine.find(SELECTOR_DATA_TOGGLE)\n\n    for (let i = 0, len = toggles.length; i < len; i++) {\n      const context = Dropdown.getInstance(toggles[i])\n      if (!context || context._config.autoClose === false) {\n        continue\n      }\n\n      if (!context._isShown()) {\n        continue\n      }\n\n      const relatedTarget = {\n        relatedTarget: context._element\n      }\n\n      if (event) {\n        const composedPath = event.composedPath()\n        const isMenuTarget = composedPath.includes(context._menu)\n        if (\n          composedPath.includes(context._element) ||\n          (context._config.autoClose === 'inside' && !isMenuTarget) ||\n          (context._config.autoClose === 'outside' && isMenuTarget)\n        ) {\n          continue\n        }\n\n        // Tab navigation through the dropdown menu or events from contained inputs shouldn't close the menu\n        if (context._menu.contains(event.target) && ((event.type === 'keyup' && event.key === TAB_KEY) || /input|select|option|textarea|form/i.test(event.target.tagName))) {\n          continue\n        }\n\n        if (event.type === 'click') {\n          relatedTarget.clickEvent = event\n        }\n      }\n\n      context._completeHide(relatedTarget)\n    }\n  }\n\n  static getParentFromElement(element) {\n    return getElementFromSelector(element) || element.parentNode\n  }\n\n  static dataApiKeydownHandler(event) {\n    // If not input/textarea:\n    //  - And not a key in REGEXP_KEYDOWN => not a dropdown command\n    // If input/textarea:\n    //  - If space key => not a dropdown command\n    //  - If key is other than escape\n    //    - If key is not up or down => not a dropdown command\n    //    - If trigger inside the menu => not a dropdown command\n    if (/input|textarea/i.test(event.target.tagName) ?\n      event.key === SPACE_KEY || (event.key !== ESCAPE_KEY &&\n      ((event.key !== ARROW_DOWN_KEY && event.key !== ARROW_UP_KEY) ||\n        event.target.closest(SELECTOR_MENU))) :\n      !REGEXP_KEYDOWN.test(event.key)) {\n      return\n    }\n\n    const isActive = this.classList.contains(CLASS_NAME_SHOW)\n\n    if (!isActive && event.key === ESCAPE_KEY) {\n      return\n    }\n\n    event.preventDefault()\n    event.stopPropagation()\n\n    if (isDisabled(this)) {\n      return\n    }\n\n    const getToggleButton = this.matches(SELECTOR_DATA_TOGGLE) ? this : SelectorEngine.prev(this, SELECTOR_DATA_TOGGLE)[0]\n    const instance = Dropdown.getOrCreateInstance(getToggleButton)\n\n    if (event.key === ESCAPE_KEY) {\n      instance.hide()\n      return\n    }\n\n    if (event.key === ARROW_UP_KEY || event.key === ARROW_DOWN_KEY) {\n      if (!isActive) {\n        instance.show()\n      }\n\n      instance._selectMenuItem(event)\n      return\n    }\n\n    if (!isActive || event.key === SPACE_KEY) {\n      Dropdown.clearMenus()\n    }\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_KEYDOWN_DATA_API, SELECTOR_DATA_TOGGLE, Dropdown.dataApiKeydownHandler)\nEventHandler.on(document, EVENT_KEYDOWN_DATA_API, SELECTOR_MENU, Dropdown.dataApiKeydownHandler)\nEventHandler.on(document, EVENT_CLICK_DATA_API, Dropdown.clearMenus)\nEventHandler.on(document, EVENT_KEYUP_DATA_API, Dropdown.clearMenus)\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  event.preventDefault()\n  Dropdown.getOrCreateInstance(this).toggle()\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Dropdown to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(Dropdown)\n\nexport default Dropdown\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.1.3): util/scrollBar.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport SelectorEngine from '../dom/selector-engine'\nimport Manipulator from '../dom/manipulator'\nimport { isElement } from './index'\n\nconst SELECTOR_FIXED_CONTENT = '.fixed-top, .fixed-bottom, .is-fixed, .sticky-top'\nconst SELECTOR_STICKY_CONTENT = '.sticky-top'\n\nclass ScrollBarHelper {\n  constructor() {\n    this._element = document.body\n  }\n\n  getWidth() {\n    // https://developer.mozilla.org/en-US/docs/Web/API/Window/innerWidth#usage_notes\n    const documentWidth = document.documentElement.clientWidth\n    return Math.abs(window.innerWidth - documentWidth)\n  }\n\n  hide() {\n    const width = this.getWidth()\n    this._disableOverFlow()\n    // give padding to element to balance the hidden scrollbar width\n    this._setElementAttributes(this._element, 'paddingRight', calculatedValue => calculatedValue + width)\n    // trick: We adjust positive paddingRight and negative marginRight to sticky-top elements to keep showing fullwidth\n    this._setElementAttributes(SELECTOR_FIXED_CONTENT, 'paddingRight', calculatedValue => calculatedValue + width)\n    this._setElementAttributes(SELECTOR_STICKY_CONTENT, 'marginRight', calculatedValue => calculatedValue - width)\n  }\n\n  _disableOverFlow() {\n    this._saveInitialAttribute(this._element, 'overflow')\n    this._element.style.overflow = 'hidden'\n  }\n\n  _setElementAttributes(selector, styleProp, callback) {\n    const scrollbarWidth = this.getWidth()\n    const manipulationCallBack = element => {\n      if (element !== this._element && window.innerWidth > element.clientWidth + scrollbarWidth) {\n        return\n      }\n\n      this._saveInitialAttribute(element, styleProp)\n      const calculatedValue = window.getComputedStyle(element)[styleProp]\n      element.style[styleProp] = `${callback(Number.parseFloat(calculatedValue))}px`\n    }\n\n    this._applyManipulationCallback(selector, manipulationCallBack)\n  }\n\n  reset() {\n    this._resetElementAttributes(this._element, 'overflow')\n    this._resetElementAttributes(this._element, 'paddingRight')\n    this._resetElementAttributes(SELECTOR_FIXED_CONTENT, 'paddingRight')\n    this._resetElementAttributes(SELECTOR_STICKY_CONTENT, 'marginRight')\n  }\n\n  _saveInitialAttribute(element, styleProp) {\n    const actualValue = element.style[styleProp]\n    if (actualValue) {\n      Manipulator.setDataAttribute(element, styleProp, actualValue)\n    }\n  }\n\n  _resetElementAttributes(selector, styleProp) {\n    const manipulationCallBack = element => {\n      const value = Manipulator.getDataAttribute(element, styleProp)\n      if (typeof value === 'undefined') {\n        element.style.removeProperty(styleProp)\n      } else {\n        Manipulator.removeDataAttribute(element, styleProp)\n        element.style[styleProp] = value\n      }\n    }\n\n    this._applyManipulationCallback(selector, manipulationCallBack)\n  }\n\n  _applyManipulationCallback(selector, callBack) {\n    if (isElement(selector)) {\n      callBack(selector)\n    } else {\n      SelectorEngine.find(selector, this._element).forEach(callBack)\n    }\n  }\n\n  isOverflowing() {\n    return this.getWidth() > 0\n  }\n}\n\nexport default ScrollBarHelper\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.1.3): util/backdrop.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport EventHandler from '../dom/event-handler'\nimport { execute, executeAfterTransition, getElement, reflow, typeCheckConfig } from './index'\n\nconst Default = {\n  className: 'modal-backdrop',\n  isVisible: true, // if false, we use the backdrop helper without adding any element to the dom\n  isAnimated: false,\n  rootElement: 'body', // give the choice to place backdrop under different elements\n  clickCallback: null\n}\n\nconst DefaultType = {\n  className: 'string',\n  isVisible: 'boolean',\n  isAnimated: 'boolean',\n  rootElement: '(element|string)',\n  clickCallback: '(function|null)'\n}\nconst NAME = 'backdrop'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\n\nconst EVENT_MOUSEDOWN = `mousedown.bs.${NAME}`\n\nclass Backdrop {\n  constructor(config) {\n    this._config = this._getConfig(config)\n    this._isAppended = false\n    this._element = null\n  }\n\n  show(callback) {\n    if (!this._config.isVisible) {\n      execute(callback)\n      return\n    }\n\n    this._append()\n\n    if (this._config.isAnimated) {\n      reflow(this._getElement())\n    }\n\n    this._getElement().classList.add(CLASS_NAME_SHOW)\n\n    this._emulateAnimation(() => {\n      execute(callback)\n    })\n  }\n\n  hide(callback) {\n    if (!this._config.isVisible) {\n      execute(callback)\n      return\n    }\n\n    this._getElement().classList.remove(CLASS_NAME_SHOW)\n\n    this._emulateAnimation(() => {\n      this.dispose()\n      execute(callback)\n    })\n  }\n\n  // Private\n\n  _getElement() {\n    if (!this._element) {\n      const backdrop = document.createElement('div')\n      backdrop.className = this._config.className\n      if (this._config.isAnimated) {\n        backdrop.classList.add(CLASS_NAME_FADE)\n      }\n\n      this._element = backdrop\n    }\n\n    return this._element\n  }\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...(typeof config === 'object' ? config : {})\n    }\n\n    // use getElement() with the default \"body\" to get a fresh Element on each instantiation\n    config.rootElement = getElement(config.rootElement)\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _append() {\n    if (this._isAppended) {\n      return\n    }\n\n    this._config.rootElement.append(this._getElement())\n\n    EventHandler.on(this._getElement(), EVENT_MOUSEDOWN, () => {\n      execute(this._config.clickCallback)\n    })\n\n    this._isAppended = true\n  }\n\n  dispose() {\n    if (!this._isAppended) {\n      return\n    }\n\n    EventHandler.off(this._element, EVENT_MOUSEDOWN)\n\n    this._element.remove()\n    this._isAppended = false\n  }\n\n  _emulateAnimation(callback) {\n    executeAfterTransition(callback, this._getElement(), this._config.isAnimated)\n  }\n}\n\nexport default Backdrop\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.1.3): util/focustrap.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport EventHandler from '../dom/event-handler'\nimport SelectorEngine from '../dom/selector-engine'\nimport { typeCheckConfig } from './index'\n\nconst Default = {\n  trapElement: null, // The element to trap focus inside of\n  autofocus: true\n}\n\nconst DefaultType = {\n  trapElement: 'element',\n  autofocus: 'boolean'\n}\n\nconst NAME = 'focustrap'\nconst DATA_KEY = 'bs.focustrap'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst EVENT_FOCUSIN = `focusin${EVENT_KEY}`\nconst EVENT_KEYDOWN_TAB = `keydown.tab${EVENT_KEY}`\n\nconst TAB_KEY = 'Tab'\nconst TAB_NAV_FORWARD = 'forward'\nconst TAB_NAV_BACKWARD = 'backward'\n\nclass FocusTrap {\n  constructor(config) {\n    this._config = this._getConfig(config)\n    this._isActive = false\n    this._lastTabNavDirection = null\n  }\n\n  activate() {\n    const { trapElement, autofocus } = this._config\n\n    if (this._isActive) {\n      return\n    }\n\n    if (autofocus) {\n      trapElement.focus()\n    }\n\n    EventHandler.off(document, EVENT_KEY) // guard against infinite focus loop\n    EventHandler.on(document, EVENT_FOCUSIN, event => this._handleFocusin(event))\n    EventHandler.on(document, EVENT_KEYDOWN_TAB, event => this._handleKeydown(event))\n\n    this._isActive = true\n  }\n\n  deactivate() {\n    if (!this._isActive) {\n      return\n    }\n\n    this._isActive = false\n    EventHandler.off(document, EVENT_KEY)\n  }\n\n  // Private\n\n  _handleFocusin(event) {\n    const { target } = event\n    const { trapElement } = this._config\n\n    if (target === document || target === trapElement || trapElement.contains(target)) {\n      return\n    }\n\n    const elements = SelectorEngine.focusableChildren(trapElement)\n\n    if (elements.length === 0) {\n      trapElement.focus()\n    } else if (this._lastTabNavDirection === TAB_NAV_BACKWARD) {\n      elements[elements.length - 1].focus()\n    } else {\n      elements[0].focus()\n    }\n  }\n\n  _handleKeydown(event) {\n    if (event.key !== TAB_KEY) {\n      return\n    }\n\n    this._lastTabNavDirection = event.shiftKey ? TAB_NAV_BACKWARD : TAB_NAV_FORWARD\n  }\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...(typeof config === 'object' ? config : {})\n    }\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n}\n\nexport default FocusTrap\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.1.3): modal.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  getElementFromSelector,\n  isRTL,\n  isVisible,\n  reflow,\n  typeCheckConfig\n} from './util/index'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport ScrollBarHelper from './util/scrollbar'\nimport BaseComponent from './base-component'\nimport Backdrop from './util/backdrop'\nimport FocusTrap from './util/focustrap'\nimport { enableDismissTrigger } from './util/component-functions'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'modal'\nconst DATA_KEY = 'bs.modal'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\nconst ESCAPE_KEY = 'Escape'\n\nconst Default = {\n  backdrop: true,\n  keyboard: true,\n  focus: true\n}\n\nconst DefaultType = {\n  backdrop: '(boolean|string)',\n  keyboard: 'boolean',\n  focus: 'boolean'\n}\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDE_PREVENTED = `hidePrevented${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_RESIZE = `resize${EVENT_KEY}`\nconst EVENT_CLICK_DISMISS = `click.dismiss${EVENT_KEY}`\nconst EVENT_KEYDOWN_DISMISS = `keydown.dismiss${EVENT_KEY}`\nconst EVENT_MOUSEUP_DISMISS = `mouseup.dismiss${EVENT_KEY}`\nconst EVENT_MOUSEDOWN_DISMISS = `mousedown.dismiss${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_OPEN = 'modal-open'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_STATIC = 'modal-static'\n\nconst OPEN_SELECTOR = '.modal.show'\nconst SELECTOR_DIALOG = '.modal-dialog'\nconst SELECTOR_MODAL_BODY = '.modal-body'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"modal\"]'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Modal extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n\n    this._config = this._getConfig(config)\n    this._dialog = SelectorEngine.findOne(SELECTOR_DIALOG, this._element)\n    this._backdrop = this._initializeBackDrop()\n    this._focustrap = this._initializeFocusTrap()\n    this._isShown = false\n    this._ignoreBackdropClick = false\n    this._isTransitioning = false\n    this._scrollBar = new ScrollBarHelper()\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n\n  toggle(relatedTarget) {\n    return this._isShown ? this.hide() : this.show(relatedTarget)\n  }\n\n  show(relatedTarget) {\n    if (this._isShown || this._isTransitioning) {\n      return\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, {\n      relatedTarget\n    })\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = true\n\n    if (this._isAnimated()) {\n      this._isTransitioning = true\n    }\n\n    this._scrollBar.hide()\n\n    document.body.classList.add(CLASS_NAME_OPEN)\n\n    this._adjustDialog()\n\n    this._setEscapeEvent()\n    this._setResizeEvent()\n\n    EventHandler.on(this._dialog, EVENT_MOUSEDOWN_DISMISS, () => {\n      EventHandler.one(this._element, EVENT_MOUSEUP_DISMISS, event => {\n        if (event.target === this._element) {\n          this._ignoreBackdropClick = true\n        }\n      })\n    })\n\n    this._showBackdrop(() => this._showElement(relatedTarget))\n  }\n\n  hide() {\n    if (!this._isShown || this._isTransitioning) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = false\n    const isAnimated = this._isAnimated()\n\n    if (isAnimated) {\n      this._isTransitioning = true\n    }\n\n    this._setEscapeEvent()\n    this._setResizeEvent()\n\n    this._focustrap.deactivate()\n\n    this._element.classList.remove(CLASS_NAME_SHOW)\n\n    EventHandler.off(this._element, EVENT_CLICK_DISMISS)\n    EventHandler.off(this._dialog, EVENT_MOUSEDOWN_DISMISS)\n\n    this._queueCallback(() => this._hideModal(), this._element, isAnimated)\n  }\n\n  dispose() {\n    [window, this._dialog]\n      .forEach(htmlElement => EventHandler.off(htmlElement, EVENT_KEY))\n\n    this._backdrop.dispose()\n    this._focustrap.deactivate()\n    super.dispose()\n  }\n\n  handleUpdate() {\n    this._adjustDialog()\n  }\n\n  // Private\n\n  _initializeBackDrop() {\n    return new Backdrop({\n      isVisible: Boolean(this._config.backdrop), // 'static' option will be translated to true, and booleans will keep their value\n      isAnimated: this._isAnimated()\n    })\n  }\n\n  _initializeFocusTrap() {\n    return new FocusTrap({\n      trapElement: this._element\n    })\n  }\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(this._element),\n      ...(typeof config === 'object' ? config : {})\n    }\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _showElement(relatedTarget) {\n    const isAnimated = this._isAnimated()\n    const modalBody = SelectorEngine.findOne(SELECTOR_MODAL_BODY, this._dialog)\n\n    if (!this._element.parentNode || this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {\n      // Don't move modal's DOM position\n      document.body.append(this._element)\n    }\n\n    this._element.style.display = 'block'\n    this._element.removeAttribute('aria-hidden')\n    this._element.setAttribute('aria-modal', true)\n    this._element.setAttribute('role', 'dialog')\n    this._element.scrollTop = 0\n\n    if (modalBody) {\n      modalBody.scrollTop = 0\n    }\n\n    if (isAnimated) {\n      reflow(this._element)\n    }\n\n    this._element.classList.add(CLASS_NAME_SHOW)\n\n    const transitionComplete = () => {\n      if (this._config.focus) {\n        this._focustrap.activate()\n      }\n\n      this._isTransitioning = false\n      EventHandler.trigger(this._element, EVENT_SHOWN, {\n        relatedTarget\n      })\n    }\n\n    this._queueCallback(transitionComplete, this._dialog, isAnimated)\n  }\n\n  _setEscapeEvent() {\n    if (this._isShown) {\n      EventHandler.on(this._element, EVENT_KEYDOWN_DISMISS, event => {\n        if (this._config.keyboard && event.key === ESCAPE_KEY) {\n          event.preventDefault()\n          this.hide()\n        } else if (!this._config.keyboard && event.key === ESCAPE_KEY) {\n          this._triggerBackdropTransition()\n        }\n      })\n    } else {\n      EventHandler.off(this._element, EVENT_KEYDOWN_DISMISS)\n    }\n  }\n\n  _setResizeEvent() {\n    if (this._isShown) {\n      EventHandler.on(window, EVENT_RESIZE, () => this._adjustDialog())\n    } else {\n      EventHandler.off(window, EVENT_RESIZE)\n    }\n  }\n\n  _hideModal() {\n    this._element.style.display = 'none'\n    this._element.setAttribute('aria-hidden', true)\n    this._element.removeAttribute('aria-modal')\n    this._element.removeAttribute('role')\n    this._isTransitioning = false\n    this._backdrop.hide(() => {\n      document.body.classList.remove(CLASS_NAME_OPEN)\n      this._resetAdjustments()\n      this._scrollBar.reset()\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    })\n  }\n\n  _showBackdrop(callback) {\n    EventHandler.on(this._element, EVENT_CLICK_DISMISS, event => {\n      if (this._ignoreBackdropClick) {\n        this._ignoreBackdropClick = false\n        return\n      }\n\n      if (event.target !== event.currentTarget) {\n        return\n      }\n\n      if (this._config.backdrop === true) {\n        this.hide()\n      } else if (this._config.backdrop === 'static') {\n        this._triggerBackdropTransition()\n      }\n    })\n\n    this._backdrop.show(callback)\n  }\n\n  _isAnimated() {\n    return this._element.classList.contains(CLASS_NAME_FADE)\n  }\n\n  _triggerBackdropTransition() {\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE_PREVENTED)\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    const { classList, scrollHeight, style } = this._element\n    const isModalOverflowing = scrollHeight > document.documentElement.clientHeight\n\n    // return if the following background transition hasn't yet completed\n    if ((!isModalOverflowing && style.overflowY === 'hidden') || classList.contains(CLASS_NAME_STATIC)) {\n      return\n    }\n\n    if (!isModalOverflowing) {\n      style.overflowY = 'hidden'\n    }\n\n    classList.add(CLASS_NAME_STATIC)\n    this._queueCallback(() => {\n      classList.remove(CLASS_NAME_STATIC)\n      if (!isModalOverflowing) {\n        this._queueCallback(() => {\n          style.overflowY = ''\n        }, this._dialog)\n      }\n    }, this._dialog)\n\n    this._element.focus()\n  }\n\n  // ----------------------------------------------------------------------\n  // the following methods are used to handle overflowing modals\n  // ----------------------------------------------------------------------\n\n  _adjustDialog() {\n    const isModalOverflowing = this._element.scrollHeight > document.documentElement.clientHeight\n    const scrollbarWidth = this._scrollBar.getWidth()\n    const isBodyOverflowing = scrollbarWidth > 0\n\n    if ((!isBodyOverflowing && isModalOverflowing && !isRTL()) || (isBodyOverflowing && !isModalOverflowing && isRTL())) {\n      this._element.style.paddingLeft = `${scrollbarWidth}px`\n    }\n\n    if ((isBodyOverflowing && !isModalOverflowing && !isRTL()) || (!isBodyOverflowing && isModalOverflowing && isRTL())) {\n      this._element.style.paddingRight = `${scrollbarWidth}px`\n    }\n  }\n\n  _resetAdjustments() {\n    this._element.style.paddingLeft = ''\n    this._element.style.paddingRight = ''\n  }\n\n  // Static\n\n  static jQueryInterface(config, relatedTarget) {\n    return this.each(function () {\n      const data = Modal.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config](relatedTarget)\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  const target = getElementFromSelector(this)\n\n  if (['A', 'AREA'].includes(this.tagName)) {\n    event.preventDefault()\n  }\n\n  EventHandler.one(target, EVENT_SHOW, showEvent => {\n    if (showEvent.defaultPrevented) {\n      // only register focus restorer if modal will actually get shown\n      return\n    }\n\n    EventHandler.one(target, EVENT_HIDDEN, () => {\n      if (isVisible(this)) {\n        this.focus()\n      }\n    })\n  })\n\n  // avoid conflict when clicking moddal toggler while another one is open\n  const allReadyOpen = SelectorEngine.findOne(OPEN_SELECTOR)\n  if (allReadyOpen) {\n    Modal.getInstance(allReadyOpen).hide()\n  }\n\n  const data = Modal.getOrCreateInstance(target)\n\n  data.toggle(this)\n})\n\nenableDismissTrigger(Modal)\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Modal to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(Modal)\n\nexport default Modal\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.1.3): offcanvas.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  getElementFromSelector,\n  isDisabled,\n  isVisible,\n  typeCheckConfig\n} from './util/index'\nimport ScrollBarHelper from './util/scrollbar'\nimport EventHandler from './dom/event-handler'\nimport BaseComponent from './base-component'\nimport SelectorEngine from './dom/selector-engine'\nimport Manipulator from './dom/manipulator'\nimport Backdrop from './util/backdrop'\nimport FocusTrap from './util/focustrap'\nimport { enableDismissTrigger } from './util/component-functions'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'offcanvas'\nconst DATA_KEY = 'bs.offcanvas'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\nconst ESCAPE_KEY = 'Escape'\n\nconst Default = {\n  backdrop: true,\n  keyboard: true,\n  scroll: false\n}\n\nconst DefaultType = {\n  backdrop: 'boolean',\n  keyboard: 'boolean',\n  scroll: 'boolean'\n}\n\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_BACKDROP = 'offcanvas-backdrop'\nconst OPEN_SELECTOR = '.offcanvas.show'\n\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYDOWN_DISMISS = `keydown.dismiss${EVENT_KEY}`\n\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"offcanvas\"]'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Offcanvas extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n\n    this._config = this._getConfig(config)\n    this._isShown = false\n    this._backdrop = this._initializeBackDrop()\n    this._focustrap = this._initializeFocusTrap()\n    this._addEventListeners()\n  }\n\n  // Getters\n\n  static get NAME() {\n    return NAME\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  // Public\n\n  toggle(relatedTarget) {\n    return this._isShown ? this.hide() : this.show(relatedTarget)\n  }\n\n  show(relatedTarget) {\n    if (this._isShown) {\n      return\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, { relatedTarget })\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = true\n    this._element.style.visibility = 'visible'\n\n    this._backdrop.show()\n\n    if (!this._config.scroll) {\n      new ScrollBarHelper().hide()\n    }\n\n    this._element.removeAttribute('aria-hidden')\n    this._element.setAttribute('aria-modal', true)\n    this._element.setAttribute('role', 'dialog')\n    this._element.classList.add(CLASS_NAME_SHOW)\n\n    const completeCallBack = () => {\n      if (!this._config.scroll) {\n        this._focustrap.activate()\n      }\n\n      EventHandler.trigger(this._element, EVENT_SHOWN, { relatedTarget })\n    }\n\n    this._queueCallback(completeCallBack, this._element, true)\n  }\n\n  hide() {\n    if (!this._isShown) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    this._focustrap.deactivate()\n    this._element.blur()\n    this._isShown = false\n    this._element.classList.remove(CLASS_NAME_SHOW)\n    this._backdrop.hide()\n\n    const completeCallback = () => {\n      this._element.setAttribute('aria-hidden', true)\n      this._element.removeAttribute('aria-modal')\n      this._element.removeAttribute('role')\n      this._element.style.visibility = 'hidden'\n\n      if (!this._config.scroll) {\n        new ScrollBarHelper().reset()\n      }\n\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._queueCallback(completeCallback, this._element, true)\n  }\n\n  dispose() {\n    this._backdrop.dispose()\n    this._focustrap.deactivate()\n    super.dispose()\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(this._element),\n      ...(typeof config === 'object' ? config : {})\n    }\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _initializeBackDrop() {\n    return new Backdrop({\n      className: CLASS_NAME_BACKDROP,\n      isVisible: this._config.backdrop,\n      isAnimated: true,\n      rootElement: this._element.parentNode,\n      clickCallback: () => this.hide()\n    })\n  }\n\n  _initializeFocusTrap() {\n    return new FocusTrap({\n      trapElement: this._element\n    })\n  }\n\n  _addEventListeners() {\n    EventHandler.on(this._element, EVENT_KEYDOWN_DISMISS, event => {\n      if (this._config.keyboard && event.key === ESCAPE_KEY) {\n        this.hide()\n      }\n    })\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Offcanvas.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (data[config] === undefined || config.startsWith('_') || config === 'constructor') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config](this)\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  const target = getElementFromSelector(this)\n\n  if (['A', 'AREA'].includes(this.tagName)) {\n    event.preventDefault()\n  }\n\n  if (isDisabled(this)) {\n    return\n  }\n\n  EventHandler.one(target, EVENT_HIDDEN, () => {\n    // focus on trigger when it is closed\n    if (isVisible(this)) {\n      this.focus()\n    }\n  })\n\n  // avoid conflict when clicking a toggler of an offcanvas, while another is open\n  const allReadyOpen = SelectorEngine.findOne(OPEN_SELECTOR)\n  if (allReadyOpen && allReadyOpen !== target) {\n    Offcanvas.getInstance(allReadyOpen).hide()\n  }\n\n  const data = Offcanvas.getOrCreateInstance(target)\n  data.toggle(this)\n})\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () =>\n  SelectorEngine.find(OPEN_SELECTOR).forEach(el => Offcanvas.getOrCreateInstance(el).show())\n)\n\nenableDismissTrigger(Offcanvas)\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n */\n\ndefineJQueryPlugin(Offcanvas)\n\nexport default Offcanvas\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.1.3): util/sanitizer.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst uriAttributes = new Set([\n  'background',\n  'cite',\n  'href',\n  'itemtype',\n  'longdesc',\n  'poster',\n  'src',\n  'xlink:href'\n])\n\nconst ARIA_ATTRIBUTE_PATTERN = /^aria-[\\w-]*$/i\n\n/**\n * A pattern that recognizes a commonly useful subset of URLs that are safe.\n *\n * Shoutout to Angular https://github.com/angular/angular/blob/12.2.x/packages/core/src/sanitization/url_sanitizer.ts\n */\nconst SAFE_URL_PATTERN = /^(?:(?:https?|mailto|ftp|tel|file|sms):|[^#&/:?]*(?:[#/?]|$))/i\n\n/**\n * A pattern that matches safe data URLs. Only matches image, video and audio types.\n *\n * Shoutout to Angular https://github.com/angular/angular/blob/12.2.x/packages/core/src/sanitization/url_sanitizer.ts\n */\nconst DATA_URL_PATTERN = /^data:(?:image\\/(?:bmp|gif|jpeg|jpg|png|tiff|webp)|video\\/(?:mpeg|mp4|ogg|webm)|audio\\/(?:mp3|oga|ogg|opus));base64,[\\d+/a-z]+=*$/i\n\nconst allowedAttribute = (attribute, allowedAttributeList) => {\n  const attributeName = attribute.nodeName.toLowerCase()\n\n  if (allowedAttributeList.includes(attributeName)) {\n    if (uriAttributes.has(attributeName)) {\n      return Boolean(SAFE_URL_PATTERN.test(attribute.nodeValue) || DATA_URL_PATTERN.test(attribute.nodeValue))\n    }\n\n    return true\n  }\n\n  const regExp = allowedAttributeList.filter(attributeRegex => attributeRegex instanceof RegExp)\n\n  // Check if a regular expression validates the attribute.\n  for (let i = 0, len = regExp.length; i < len; i++) {\n    if (regExp[i].test(attributeName)) {\n      return true\n    }\n  }\n\n  return false\n}\n\nexport const DefaultAllowlist = {\n  // Global attributes allowed on any supplied element below.\n  '*': ['class', 'dir', 'id', 'lang', 'role', ARIA_ATTRIBUTE_PATTERN],\n  a: ['target', 'href', 'title', 'rel'],\n  area: [],\n  b: [],\n  br: [],\n  col: [],\n  code: [],\n  div: [],\n  em: [],\n  hr: [],\n  h1: [],\n  h2: [],\n  h3: [],\n  h4: [],\n  h5: [],\n  h6: [],\n  i: [],\n  img: ['src', 'srcset', 'alt', 'title', 'width', 'height'],\n  li: [],\n  ol: [],\n  p: [],\n  pre: [],\n  s: [],\n  small: [],\n  span: [],\n  sub: [],\n  sup: [],\n  strong: [],\n  u: [],\n  ul: []\n}\n\nexport function sanitizeHtml(unsafeHtml, allowList, sanitizeFn) {\n  if (!unsafeHtml.length) {\n    return unsafeHtml\n  }\n\n  if (sanitizeFn && typeof sanitizeFn === 'function') {\n    return sanitizeFn(unsafeHtml)\n  }\n\n  const domParser = new window.DOMParser()\n  const createdDocument = domParser.parseFromString(unsafeHtml, 'text/html')\n  const elements = [].concat(...createdDocument.body.querySelectorAll('*'))\n\n  for (let i = 0, len = elements.length; i < len; i++) {\n    const element = elements[i]\n    const elementName = element.nodeName.toLowerCase()\n\n    if (!Object.keys(allowList).includes(elementName)) {\n      element.remove()\n\n      continue\n    }\n\n    const attributeList = [].concat(...element.attributes)\n    const allowedAttributes = [].concat(allowList['*'] || [], allowList[elementName] || [])\n\n    attributeList.forEach(attribute => {\n      if (!allowedAttribute(attribute, allowedAttributes)) {\n        element.removeAttribute(attribute.nodeName)\n      }\n    })\n  }\n\n  return createdDocument.body.innerHTML\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.1.3): tooltip.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport * as Popper from '@popperjs/core'\n\nimport {\n  defineJQueryPlugin,\n  findShadowRoot,\n  getElement,\n  getUID,\n  isElement,\n  isRTL,\n  noop,\n  typeCheckConfig\n} from './util/index'\nimport { DefaultAllowlist, sanitizeHtml } from './util/sanitizer'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'tooltip'\nconst DATA_KEY = 'bs.tooltip'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst CLASS_PREFIX = 'bs-tooltip'\nconst DISALLOWED_ATTRIBUTES = new Set(['sanitize', 'allowList', 'sanitizeFn'])\n\nconst DefaultType = {\n  animation: 'boolean',\n  template: 'string',\n  title: '(string|element|function)',\n  trigger: 'string',\n  delay: '(number|object)',\n  html: 'boolean',\n  selector: '(string|boolean)',\n  placement: '(string|function)',\n  offset: '(array|string|function)',\n  container: '(string|element|boolean)',\n  fallbackPlacements: 'array',\n  boundary: '(string|element)',\n  customClass: '(string|function)',\n  sanitize: 'boolean',\n  sanitizeFn: '(null|function)',\n  allowList: 'object',\n  popperConfig: '(null|object|function)'\n}\n\nconst AttachmentMap = {\n  AUTO: 'auto',\n  TOP: 'top',\n  RIGHT: isRTL() ? 'left' : 'right',\n  BOTTOM: 'bottom',\n  LEFT: isRTL() ? 'right' : 'left'\n}\n\nconst Default = {\n  animation: true,\n  template: '<div class=\"tooltip\" role=\"tooltip\">' +\n              '<div class=\"tooltip-arrow\"></div>' +\n              '<div class=\"tooltip-inner\"></div>' +\n            '</div>',\n  trigger: 'hover focus',\n  title: '',\n  delay: 0,\n  html: false,\n  selector: false,\n  placement: 'top',\n  offset: [0, 0],\n  container: false,\n  fallbackPlacements: ['top', 'right', 'bottom', 'left'],\n  boundary: 'clippingParents',\n  customClass: '',\n  sanitize: true,\n  sanitizeFn: null,\n  allowList: DefaultAllowlist,\n  popperConfig: null\n}\n\nconst Event = {\n  HIDE: `hide${EVENT_KEY}`,\n  HIDDEN: `hidden${EVENT_KEY}`,\n  SHOW: `show${EVENT_KEY}`,\n  SHOWN: `shown${EVENT_KEY}`,\n  INSERTED: `inserted${EVENT_KEY}`,\n  CLICK: `click${EVENT_KEY}`,\n  FOCUSIN: `focusin${EVENT_KEY}`,\n  FOCUSOUT: `focusout${EVENT_KEY}`,\n  MOUSEENTER: `mouseenter${EVENT_KEY}`,\n  MOUSELEAVE: `mouseleave${EVENT_KEY}`\n}\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_MODAL = 'modal'\nconst CLASS_NAME_SHOW = 'show'\n\nconst HOVER_STATE_SHOW = 'show'\nconst HOVER_STATE_OUT = 'out'\n\nconst SELECTOR_TOOLTIP_INNER = '.tooltip-inner'\nconst SELECTOR_MODAL = `.${CLASS_NAME_MODAL}`\n\nconst EVENT_MODAL_HIDE = 'hide.bs.modal'\n\nconst TRIGGER_HOVER = 'hover'\nconst TRIGGER_FOCUS = 'focus'\nconst TRIGGER_CLICK = 'click'\nconst TRIGGER_MANUAL = 'manual'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Tooltip extends BaseComponent {\n  constructor(element, config) {\n    if (typeof Popper === 'undefined') {\n      throw new TypeError('Bootstrap\\'s tooltips require Popper (https://popper.js.org)')\n    }\n\n    super(element)\n\n    // private\n    this._isEnabled = true\n    this._timeout = 0\n    this._hoverState = ''\n    this._activeTrigger = {}\n    this._popper = null\n\n    // Protected\n    this._config = this._getConfig(config)\n    this.tip = null\n\n    this._setListeners()\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  static get Event() {\n    return Event\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  // Public\n\n  enable() {\n    this._isEnabled = true\n  }\n\n  disable() {\n    this._isEnabled = false\n  }\n\n  toggleEnabled() {\n    this._isEnabled = !this._isEnabled\n  }\n\n  toggle(event) {\n    if (!this._isEnabled) {\n      return\n    }\n\n    if (event) {\n      const context = this._initializeOnDelegatedTarget(event)\n\n      context._activeTrigger.click = !context._activeTrigger.click\n\n      if (context._isWithActiveTrigger()) {\n        context._enter(null, context)\n      } else {\n        context._leave(null, context)\n      }\n    } else {\n      if (this.getTipElement().classList.contains(CLASS_NAME_SHOW)) {\n        this._leave(null, this)\n        return\n      }\n\n      this._enter(null, this)\n    }\n  }\n\n  dispose() {\n    clearTimeout(this._timeout)\n\n    EventHandler.off(this._element.closest(SELECTOR_MODAL), EVENT_MODAL_HIDE, this._hideModalHandler)\n\n    if (this.tip) {\n      this.tip.remove()\n    }\n\n    this._disposePopper()\n    super.dispose()\n  }\n\n  show() {\n    if (this._element.style.display === 'none') {\n      throw new Error('Please use show on visible elements')\n    }\n\n    if (!(this.isWithContent() && this._isEnabled)) {\n      return\n    }\n\n    const showEvent = EventHandler.trigger(this._element, this.constructor.Event.SHOW)\n    const shadowRoot = findShadowRoot(this._element)\n    const isInTheDom = shadowRoot === null ?\n      this._element.ownerDocument.documentElement.contains(this._element) :\n      shadowRoot.contains(this._element)\n\n    if (showEvent.defaultPrevented || !isInTheDom) {\n      return\n    }\n\n    // A trick to recreate a tooltip in case a new title is given by using the NOT documented `data-bs-original-title`\n    // This will be removed later in favor of a `setContent` method\n    if (this.constructor.NAME === 'tooltip' && this.tip && this.getTitle() !== this.tip.querySelector(SELECTOR_TOOLTIP_INNER).innerHTML) {\n      this._disposePopper()\n      this.tip.remove()\n      this.tip = null\n    }\n\n    const tip = this.getTipElement()\n    const tipId = getUID(this.constructor.NAME)\n\n    tip.setAttribute('id', tipId)\n    this._element.setAttribute('aria-describedby', tipId)\n\n    if (this._config.animation) {\n      tip.classList.add(CLASS_NAME_FADE)\n    }\n\n    const placement = typeof this._config.placement === 'function' ?\n      this._config.placement.call(this, tip, this._element) :\n      this._config.placement\n\n    const attachment = this._getAttachment(placement)\n    this._addAttachmentClass(attachment)\n\n    const { container } = this._config\n    Data.set(tip, this.constructor.DATA_KEY, this)\n\n    if (!this._element.ownerDocument.documentElement.contains(this.tip)) {\n      container.append(tip)\n      EventHandler.trigger(this._element, this.constructor.Event.INSERTED)\n    }\n\n    if (this._popper) {\n      this._popper.update()\n    } else {\n      this._popper = Popper.createPopper(this._element, tip, this._getPopperConfig(attachment))\n    }\n\n    tip.classList.add(CLASS_NAME_SHOW)\n\n    const customClass = this._resolvePossibleFunction(this._config.customClass)\n    if (customClass) {\n      tip.classList.add(...customClass.split(' '))\n    }\n\n    // If this is a touch-enabled device we add extra\n    // empty mouseover listeners to the body's immediate children;\n    // only needed because of broken event delegation on iOS\n    // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n    if ('ontouchstart' in document.documentElement) {\n      [].concat(...document.body.children).forEach(element => {\n        EventHandler.on(element, 'mouseover', noop)\n      })\n    }\n\n    const complete = () => {\n      const prevHoverState = this._hoverState\n\n      this._hoverState = null\n      EventHandler.trigger(this._element, this.constructor.Event.SHOWN)\n\n      if (prevHoverState === HOVER_STATE_OUT) {\n        this._leave(null, this)\n      }\n    }\n\n    const isAnimated = this.tip.classList.contains(CLASS_NAME_FADE)\n    this._queueCallback(complete, this.tip, isAnimated)\n  }\n\n  hide() {\n    if (!this._popper) {\n      return\n    }\n\n    const tip = this.getTipElement()\n    const complete = () => {\n      if (this._isWithActiveTrigger()) {\n        return\n      }\n\n      if (this._hoverState !== HOVER_STATE_SHOW) {\n        tip.remove()\n      }\n\n      this._cleanTipClass()\n      this._element.removeAttribute('aria-describedby')\n      EventHandler.trigger(this._element, this.constructor.Event.HIDDEN)\n\n      this._disposePopper()\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, this.constructor.Event.HIDE)\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    tip.classList.remove(CLASS_NAME_SHOW)\n\n    // If this is a touch-enabled device we remove the extra\n    // empty mouseover listeners we added for iOS support\n    if ('ontouchstart' in document.documentElement) {\n      [].concat(...document.body.children)\n        .forEach(element => EventHandler.off(element, 'mouseover', noop))\n    }\n\n    this._activeTrigger[TRIGGER_CLICK] = false\n    this._activeTrigger[TRIGGER_FOCUS] = false\n    this._activeTrigger[TRIGGER_HOVER] = false\n\n    const isAnimated = this.tip.classList.contains(CLASS_NAME_FADE)\n    this._queueCallback(complete, this.tip, isAnimated)\n    this._hoverState = ''\n  }\n\n  update() {\n    if (this._popper !== null) {\n      this._popper.update()\n    }\n  }\n\n  // Protected\n\n  isWithContent() {\n    return Boolean(this.getTitle())\n  }\n\n  getTipElement() {\n    if (this.tip) {\n      return this.tip\n    }\n\n    const element = document.createElement('div')\n    element.innerHTML = this._config.template\n\n    const tip = element.children[0]\n    this.setContent(tip)\n    tip.classList.remove(CLASS_NAME_FADE, CLASS_NAME_SHOW)\n\n    this.tip = tip\n    return this.tip\n  }\n\n  setContent(tip) {\n    this._sanitizeAndSetContent(tip, this.getTitle(), SELECTOR_TOOLTIP_INNER)\n  }\n\n  _sanitizeAndSetContent(template, content, selector) {\n    const templateElement = SelectorEngine.findOne(selector, template)\n\n    if (!content && templateElement) {\n      templateElement.remove()\n      return\n    }\n\n    // we use append for html objects to maintain js events\n    this.setElementContent(templateElement, content)\n  }\n\n  setElementContent(element, content) {\n    if (element === null) {\n      return\n    }\n\n    if (isElement(content)) {\n      content = getElement(content)\n\n      // content is a DOM node or a jQuery\n      if (this._config.html) {\n        if (content.parentNode !== element) {\n          element.innerHTML = ''\n          element.append(content)\n        }\n      } else {\n        element.textContent = content.textContent\n      }\n\n      return\n    }\n\n    if (this._config.html) {\n      if (this._config.sanitize) {\n        content = sanitizeHtml(content, this._config.allowList, this._config.sanitizeFn)\n      }\n\n      element.innerHTML = content\n    } else {\n      element.textContent = content\n    }\n  }\n\n  getTitle() {\n    const title = this._element.getAttribute('data-bs-original-title') || this._config.title\n\n    return this._resolvePossibleFunction(title)\n  }\n\n  updateAttachment(attachment) {\n    if (attachment === 'right') {\n      return 'end'\n    }\n\n    if (attachment === 'left') {\n      return 'start'\n    }\n\n    return attachment\n  }\n\n  // Private\n\n  _initializeOnDelegatedTarget(event, context) {\n    return context || this.constructor.getOrCreateInstance(event.delegateTarget, this._getDelegateConfig())\n  }\n\n  _getOffset() {\n    const { offset } = this._config\n\n    if (typeof offset === 'string') {\n      return offset.split(',').map(val => Number.parseInt(val, 10))\n    }\n\n    if (typeof offset === 'function') {\n      return popperData => offset(popperData, this._element)\n    }\n\n    return offset\n  }\n\n  _resolvePossibleFunction(content) {\n    return typeof content === 'function' ? content.call(this._element) : content\n  }\n\n  _getPopperConfig(attachment) {\n    const defaultBsPopperConfig = {\n      placement: attachment,\n      modifiers: [\n        {\n          name: 'flip',\n          options: {\n            fallbackPlacements: this._config.fallbackPlacements\n          }\n        },\n        {\n          name: 'offset',\n          options: {\n            offset: this._getOffset()\n          }\n        },\n        {\n          name: 'preventOverflow',\n          options: {\n            boundary: this._config.boundary\n          }\n        },\n        {\n          name: 'arrow',\n          options: {\n            element: `.${this.constructor.NAME}-arrow`\n          }\n        },\n        {\n          name: 'onChange',\n          enabled: true,\n          phase: 'afterWrite',\n          fn: data => this._handlePopperPlacementChange(data)\n        }\n      ],\n      onFirstUpdate: data => {\n        if (data.options.placement !== data.placement) {\n          this._handlePopperPlacementChange(data)\n        }\n      }\n    }\n\n    return {\n      ...defaultBsPopperConfig,\n      ...(typeof this._config.popperConfig === 'function' ? this._config.popperConfig(defaultBsPopperConfig) : this._config.popperConfig)\n    }\n  }\n\n  _addAttachmentClass(attachment) {\n    this.getTipElement().classList.add(`${this._getBasicClassPrefix()}-${this.updateAttachment(attachment)}`)\n  }\n\n  _getAttachment(placement) {\n    return AttachmentMap[placement.toUpperCase()]\n  }\n\n  _setListeners() {\n    const triggers = this._config.trigger.split(' ')\n\n    triggers.forEach(trigger => {\n      if (trigger === 'click') {\n        EventHandler.on(this._element, this.constructor.Event.CLICK, this._config.selector, event => this.toggle(event))\n      } else if (trigger !== TRIGGER_MANUAL) {\n        const eventIn = trigger === TRIGGER_HOVER ?\n          this.constructor.Event.MOUSEENTER :\n          this.constructor.Event.FOCUSIN\n        const eventOut = trigger === TRIGGER_HOVER ?\n          this.constructor.Event.MOUSELEAVE :\n          this.constructor.Event.FOCUSOUT\n\n        EventHandler.on(this._element, eventIn, this._config.selector, event => this._enter(event))\n        EventHandler.on(this._element, eventOut, this._config.selector, event => this._leave(event))\n      }\n    })\n\n    this._hideModalHandler = () => {\n      if (this._element) {\n        this.hide()\n      }\n    }\n\n    EventHandler.on(this._element.closest(SELECTOR_MODAL), EVENT_MODAL_HIDE, this._hideModalHandler)\n\n    if (this._config.selector) {\n      this._config = {\n        ...this._config,\n        trigger: 'manual',\n        selector: ''\n      }\n    } else {\n      this._fixTitle()\n    }\n  }\n\n  _fixTitle() {\n    const title = this._element.getAttribute('title')\n    const originalTitleType = typeof this._element.getAttribute('data-bs-original-title')\n\n    if (title || originalTitleType !== 'string') {\n      this._element.setAttribute('data-bs-original-title', title || '')\n      if (title && !this._element.getAttribute('aria-label') && !this._element.textContent) {\n        this._element.setAttribute('aria-label', title)\n      }\n\n      this._element.setAttribute('title', '')\n    }\n  }\n\n  _enter(event, context) {\n    context = this._initializeOnDelegatedTarget(event, context)\n\n    if (event) {\n      context._activeTrigger[\n        event.type === 'focusin' ? TRIGGER_FOCUS : TRIGGER_HOVER\n      ] = true\n    }\n\n    if (context.getTipElement().classList.contains(CLASS_NAME_SHOW) || context._hoverState === HOVER_STATE_SHOW) {\n      context._hoverState = HOVER_STATE_SHOW\n      return\n    }\n\n    clearTimeout(context._timeout)\n\n    context._hoverState = HOVER_STATE_SHOW\n\n    if (!context._config.delay || !context._config.delay.show) {\n      context.show()\n      return\n    }\n\n    context._timeout = setTimeout(() => {\n      if (context._hoverState === HOVER_STATE_SHOW) {\n        context.show()\n      }\n    }, context._config.delay.show)\n  }\n\n  _leave(event, context) {\n    context = this._initializeOnDelegatedTarget(event, context)\n\n    if (event) {\n      context._activeTrigger[\n        event.type === 'focusout' ? TRIGGER_FOCUS : TRIGGER_HOVER\n      ] = context._element.contains(event.relatedTarget)\n    }\n\n    if (context._isWithActiveTrigger()) {\n      return\n    }\n\n    clearTimeout(context._timeout)\n\n    context._hoverState = HOVER_STATE_OUT\n\n    if (!context._config.delay || !context._config.delay.hide) {\n      context.hide()\n      return\n    }\n\n    context._timeout = setTimeout(() => {\n      if (context._hoverState === HOVER_STATE_OUT) {\n        context.hide()\n      }\n    }, context._config.delay.hide)\n  }\n\n  _isWithActiveTrigger() {\n    for (const trigger in this._activeTrigger) {\n      if (this._activeTrigger[trigger]) {\n        return true\n      }\n    }\n\n    return false\n  }\n\n  _getConfig(config) {\n    const dataAttributes = Manipulator.getDataAttributes(this._element)\n\n    Object.keys(dataAttributes).forEach(dataAttr => {\n      if (DISALLOWED_ATTRIBUTES.has(dataAttr)) {\n        delete dataAttributes[dataAttr]\n      }\n    })\n\n    config = {\n      ...this.constructor.Default,\n      ...dataAttributes,\n      ...(typeof config === 'object' && config ? config : {})\n    }\n\n    config.container = config.container === false ? document.body : getElement(config.container)\n\n    if (typeof config.delay === 'number') {\n      config.delay = {\n        show: config.delay,\n        hide: config.delay\n      }\n    }\n\n    if (typeof config.title === 'number') {\n      config.title = config.title.toString()\n    }\n\n    if (typeof config.content === 'number') {\n      config.content = config.content.toString()\n    }\n\n    typeCheckConfig(NAME, config, this.constructor.DefaultType)\n\n    if (config.sanitize) {\n      config.template = sanitizeHtml(config.template, config.allowList, config.sanitizeFn)\n    }\n\n    return config\n  }\n\n  _getDelegateConfig() {\n    const config = {}\n\n    for (const key in this._config) {\n      if (this.constructor.Default[key] !== this._config[key]) {\n        config[key] = this._config[key]\n      }\n    }\n\n    // In the future can be replaced with:\n    // const keysWithDifferentValues = Object.entries(this._config).filter(entry => this.constructor.Default[entry[0]] !== this._config[entry[0]])\n    // `Object.fromEntries(keysWithDifferentValues)`\n    return config\n  }\n\n  _cleanTipClass() {\n    const tip = this.getTipElement()\n    const basicClassPrefixRegex = new RegExp(`(^|\\\\s)${this._getBasicClassPrefix()}\\\\S+`, 'g')\n    const tabClass = tip.getAttribute('class').match(basicClassPrefixRegex)\n    if (tabClass !== null && tabClass.length > 0) {\n      tabClass.map(token => token.trim())\n        .forEach(tClass => tip.classList.remove(tClass))\n    }\n  }\n\n  _getBasicClassPrefix() {\n    return CLASS_PREFIX\n  }\n\n  _handlePopperPlacementChange(popperData) {\n    const { state } = popperData\n\n    if (!state) {\n      return\n    }\n\n    this.tip = state.elements.popper\n    this._cleanTipClass()\n    this._addAttachmentClass(this._getAttachment(state.placement))\n  }\n\n  _disposePopper() {\n    if (this._popper) {\n      this._popper.destroy()\n      this._popper = null\n    }\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Tooltip.getOrCreateInstance(this, config)\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Tooltip to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(Tooltip)\n\nexport default Tooltip\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.1.3): popover.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { defineJQueryPlugin } from './util/index'\nimport Tooltip from './tooltip'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'popover'\nconst DATA_KEY = 'bs.popover'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst CLASS_PREFIX = 'bs-popover'\n\nconst Default = {\n  ...Tooltip.Default,\n  placement: 'right',\n  offset: [0, 8],\n  trigger: 'click',\n  content: '',\n  template: '<div class=\"popover\" role=\"tooltip\">' +\n              '<div class=\"popover-arrow\"></div>' +\n              '<h3 class=\"popover-header\"></h3>' +\n              '<div class=\"popover-body\"></div>' +\n            '</div>'\n}\n\nconst DefaultType = {\n  ...Tooltip.DefaultType,\n  content: '(string|element|function)'\n}\n\nconst Event = {\n  HIDE: `hide${EVENT_KEY}`,\n  HIDDEN: `hidden${EVENT_KEY}`,\n  SHOW: `show${EVENT_KEY}`,\n  SHOWN: `shown${EVENT_KEY}`,\n  INSERTED: `inserted${EVENT_KEY}`,\n  CLICK: `click${EVENT_KEY}`,\n  FOCUSIN: `focusin${EVENT_KEY}`,\n  FOCUSOUT: `focusout${EVENT_KEY}`,\n  MOUSEENTER: `mouseenter${EVENT_KEY}`,\n  MOUSELEAVE: `mouseleave${EVENT_KEY}`\n}\n\nconst SELECTOR_TITLE = '.popover-header'\nconst SELECTOR_CONTENT = '.popover-body'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Popover extends Tooltip {\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  static get Event() {\n    return Event\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  // Overrides\n\n  isWithContent() {\n    return this.getTitle() || this._getContent()\n  }\n\n  setContent(tip) {\n    this._sanitizeAndSetContent(tip, this.getTitle(), SELECTOR_TITLE)\n    this._sanitizeAndSetContent(tip, this._getContent(), SELECTOR_CONTENT)\n  }\n\n  // Private\n\n  _getContent() {\n    return this._resolvePossibleFunction(this._config.content)\n  }\n\n  _getBasicClassPrefix() {\n    return CLASS_PREFIX\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Popover.getOrCreateInstance(this, config)\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Popover to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(Popover)\n\nexport default Popover\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.1.3): scrollspy.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  getElement,\n  getSelectorFromElement,\n  typeCheckConfig\n} from './util/index'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'scrollspy'\nconst DATA_KEY = 'bs.scrollspy'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst Default = {\n  offset: 10,\n  method: 'auto',\n  target: ''\n}\n\nconst DefaultType = {\n  offset: 'number',\n  method: 'string',\n  target: '(string|element)'\n}\n\nconst EVENT_ACTIVATE = `activate${EVENT_KEY}`\nconst EVENT_SCROLL = `scroll${EVENT_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_DROPDOWN_ITEM = 'dropdown-item'\nconst CLASS_NAME_ACTIVE = 'active'\n\nconst SELECTOR_DATA_SPY = '[data-bs-spy=\"scroll\"]'\nconst SELECTOR_NAV_LIST_GROUP = '.nav, .list-group'\nconst SELECTOR_NAV_LINKS = '.nav-link'\nconst SELECTOR_NAV_ITEMS = '.nav-item'\nconst SELECTOR_LIST_ITEMS = '.list-group-item'\nconst SELECTOR_LINK_ITEMS = `${SELECTOR_NAV_LINKS}, ${SELECTOR_LIST_ITEMS}, .${CLASS_NAME_DROPDOWN_ITEM}`\nconst SELECTOR_DROPDOWN = '.dropdown'\nconst SELECTOR_DROPDOWN_TOGGLE = '.dropdown-toggle'\n\nconst METHOD_OFFSET = 'offset'\nconst METHOD_POSITION = 'position'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass ScrollSpy extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n    this._scrollElement = this._element.tagName === 'BODY' ? window : this._element\n    this._config = this._getConfig(config)\n    this._offsets = []\n    this._targets = []\n    this._activeTarget = null\n    this._scrollHeight = 0\n\n    EventHandler.on(this._scrollElement, EVENT_SCROLL, () => this._process())\n\n    this.refresh()\n    this._process()\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n\n  refresh() {\n    const autoMethod = this._scrollElement === this._scrollElement.window ?\n      METHOD_OFFSET :\n      METHOD_POSITION\n\n    const offsetMethod = this._config.method === 'auto' ?\n      autoMethod :\n      this._config.method\n\n    const offsetBase = offsetMethod === METHOD_POSITION ?\n      this._getScrollTop() :\n      0\n\n    this._offsets = []\n    this._targets = []\n    this._scrollHeight = this._getScrollHeight()\n\n    const targets = SelectorEngine.find(SELECTOR_LINK_ITEMS, this._config.target)\n\n    targets.map(element => {\n      const targetSelector = getSelectorFromElement(element)\n      const target = targetSelector ? SelectorEngine.findOne(targetSelector) : null\n\n      if (target) {\n        const targetBCR = target.getBoundingClientRect()\n        if (targetBCR.width || targetBCR.height) {\n          return [\n            Manipulator[offsetMethod](target).top + offsetBase,\n            targetSelector\n          ]\n        }\n      }\n\n      return null\n    })\n      .filter(item => item)\n      .sort((a, b) => a[0] - b[0])\n      .forEach(item => {\n        this._offsets.push(item[0])\n        this._targets.push(item[1])\n      })\n  }\n\n  dispose() {\n    EventHandler.off(this._scrollElement, EVENT_KEY)\n    super.dispose()\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(this._element),\n      ...(typeof config === 'object' && config ? config : {})\n    }\n\n    config.target = getElement(config.target) || document.documentElement\n\n    typeCheckConfig(NAME, config, DefaultType)\n\n    return config\n  }\n\n  _getScrollTop() {\n    return this._scrollElement === window ?\n      this._scrollElement.pageYOffset :\n      this._scrollElement.scrollTop\n  }\n\n  _getScrollHeight() {\n    return this._scrollElement.scrollHeight || Math.max(\n      document.body.scrollHeight,\n      document.documentElement.scrollHeight\n    )\n  }\n\n  _getOffsetHeight() {\n    return this._scrollElement === window ?\n      window.innerHeight :\n      this._scrollElement.getBoundingClientRect().height\n  }\n\n  _process() {\n    const scrollTop = this._getScrollTop() + this._config.offset\n    const scrollHeight = this._getScrollHeight()\n    const maxScroll = this._config.offset + scrollHeight - this._getOffsetHeight()\n\n    if (this._scrollHeight !== scrollHeight) {\n      this.refresh()\n    }\n\n    if (scrollTop >= maxScroll) {\n      const target = this._targets[this._targets.length - 1]\n\n      if (this._activeTarget !== target) {\n        this._activate(target)\n      }\n\n      return\n    }\n\n    if (this._activeTarget && scrollTop < this._offsets[0] && this._offsets[0] > 0) {\n      this._activeTarget = null\n      this._clear()\n      return\n    }\n\n    for (let i = this._offsets.length; i--;) {\n      const isActiveTarget = this._activeTarget !== this._targets[i] &&\n          scrollTop >= this._offsets[i] &&\n          (typeof this._offsets[i + 1] === 'undefined' || scrollTop < this._offsets[i + 1])\n\n      if (isActiveTarget) {\n        this._activate(this._targets[i])\n      }\n    }\n  }\n\n  _activate(target) {\n    this._activeTarget = target\n\n    this._clear()\n\n    const queries = SELECTOR_LINK_ITEMS.split(',')\n      .map(selector => `${selector}[data-bs-target=\"${target}\"],${selector}[href=\"${target}\"]`)\n\n    const link = SelectorEngine.findOne(queries.join(','), this._config.target)\n\n    link.classList.add(CLASS_NAME_ACTIVE)\n    if (link.classList.contains(CLASS_NAME_DROPDOWN_ITEM)) {\n      SelectorEngine.findOne(SELECTOR_DROPDOWN_TOGGLE, link.closest(SELECTOR_DROPDOWN))\n        .classList.add(CLASS_NAME_ACTIVE)\n    } else {\n      SelectorEngine.parents(link, SELECTOR_NAV_LIST_GROUP)\n        .forEach(listGroup => {\n          // Set triggered links parents as active\n          // With both <ul> and <nav> markup a parent is the previous sibling of any nav ancestor\n          SelectorEngine.prev(listGroup, `${SELECTOR_NAV_LINKS}, ${SELECTOR_LIST_ITEMS}`)\n            .forEach(item => item.classList.add(CLASS_NAME_ACTIVE))\n\n          // Handle special case when .nav-link is inside .nav-item\n          SelectorEngine.prev(listGroup, SELECTOR_NAV_ITEMS)\n            .forEach(navItem => {\n              SelectorEngine.children(navItem, SELECTOR_NAV_LINKS)\n                .forEach(item => item.classList.add(CLASS_NAME_ACTIVE))\n            })\n        })\n    }\n\n    EventHandler.trigger(this._scrollElement, EVENT_ACTIVATE, {\n      relatedTarget: target\n    })\n  }\n\n  _clear() {\n    SelectorEngine.find(SELECTOR_LINK_ITEMS, this._config.target)\n      .filter(node => node.classList.contains(CLASS_NAME_ACTIVE))\n      .forEach(node => node.classList.remove(CLASS_NAME_ACTIVE))\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = ScrollSpy.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  SelectorEngine.find(SELECTOR_DATA_SPY)\n    .forEach(spy => new ScrollSpy(spy))\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .ScrollSpy to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(ScrollSpy)\n\nexport default ScrollSpy\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.1.3): tab.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  getElementFromSelector,\n  isDisabled,\n  reflow\n} from './util/index'\nimport EventHandler from './dom/event-handler'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'tab'\nconst DATA_KEY = 'bs.tab'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_DROPDOWN_MENU = 'dropdown-menu'\nconst CLASS_NAME_ACTIVE = 'active'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\n\nconst SELECTOR_DROPDOWN = '.dropdown'\nconst SELECTOR_NAV_LIST_GROUP = '.nav, .list-group'\nconst SELECTOR_ACTIVE = '.active'\nconst SELECTOR_ACTIVE_UL = ':scope > li > .active'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"tab\"], [data-bs-toggle=\"pill\"], [data-bs-toggle=\"list\"]'\nconst SELECTOR_DROPDOWN_TOGGLE = '.dropdown-toggle'\nconst SELECTOR_DROPDOWN_ACTIVE_CHILD = ':scope > .dropdown-menu .active'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Tab extends BaseComponent {\n  // Getters\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n\n  show() {\n    if ((this._element.parentNode &&\n      this._element.parentNode.nodeType === Node.ELEMENT_NODE &&\n      this._element.classList.contains(CLASS_NAME_ACTIVE))) {\n      return\n    }\n\n    let previous\n    const target = getElementFromSelector(this._element)\n    const listElement = this._element.closest(SELECTOR_NAV_LIST_GROUP)\n\n    if (listElement) {\n      const itemSelector = listElement.nodeName === 'UL' || listElement.nodeName === 'OL' ? SELECTOR_ACTIVE_UL : SELECTOR_ACTIVE\n      previous = SelectorEngine.find(itemSelector, listElement)\n      previous = previous[previous.length - 1]\n    }\n\n    const hideEvent = previous ?\n      EventHandler.trigger(previous, EVENT_HIDE, {\n        relatedTarget: this._element\n      }) :\n      null\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, {\n      relatedTarget: previous\n    })\n\n    if (showEvent.defaultPrevented || (hideEvent !== null && hideEvent.defaultPrevented)) {\n      return\n    }\n\n    this._activate(this._element, listElement)\n\n    const complete = () => {\n      EventHandler.trigger(previous, EVENT_HIDDEN, {\n        relatedTarget: this._element\n      })\n      EventHandler.trigger(this._element, EVENT_SHOWN, {\n        relatedTarget: previous\n      })\n    }\n\n    if (target) {\n      this._activate(target, target.parentNode, complete)\n    } else {\n      complete()\n    }\n  }\n\n  // Private\n\n  _activate(element, container, callback) {\n    const activeElements = container && (container.nodeName === 'UL' || container.nodeName === 'OL') ?\n      SelectorEngine.find(SELECTOR_ACTIVE_UL, container) :\n      SelectorEngine.children(container, SELECTOR_ACTIVE)\n\n    const active = activeElements[0]\n    const isTransitioning = callback && (active && active.classList.contains(CLASS_NAME_FADE))\n\n    const complete = () => this._transitionComplete(element, active, callback)\n\n    if (active && isTransitioning) {\n      active.classList.remove(CLASS_NAME_SHOW)\n      this._queueCallback(complete, element, true)\n    } else {\n      complete()\n    }\n  }\n\n  _transitionComplete(element, active, callback) {\n    if (active) {\n      active.classList.remove(CLASS_NAME_ACTIVE)\n\n      const dropdownChild = SelectorEngine.findOne(SELECTOR_DROPDOWN_ACTIVE_CHILD, active.parentNode)\n\n      if (dropdownChild) {\n        dropdownChild.classList.remove(CLASS_NAME_ACTIVE)\n      }\n\n      if (active.getAttribute('role') === 'tab') {\n        active.setAttribute('aria-selected', false)\n      }\n    }\n\n    element.classList.add(CLASS_NAME_ACTIVE)\n    if (element.getAttribute('role') === 'tab') {\n      element.setAttribute('aria-selected', true)\n    }\n\n    reflow(element)\n\n    if (element.classList.contains(CLASS_NAME_FADE)) {\n      element.classList.add(CLASS_NAME_SHOW)\n    }\n\n    let parent = element.parentNode\n    if (parent && parent.nodeName === 'LI') {\n      parent = parent.parentNode\n    }\n\n    if (parent && parent.classList.contains(CLASS_NAME_DROPDOWN_MENU)) {\n      const dropdownElement = element.closest(SELECTOR_DROPDOWN)\n\n      if (dropdownElement) {\n        SelectorEngine.find(SELECTOR_DROPDOWN_TOGGLE, dropdownElement)\n          .forEach(dropdown => dropdown.classList.add(CLASS_NAME_ACTIVE))\n      }\n\n      element.setAttribute('aria-expanded', true)\n    }\n\n    if (callback) {\n      callback()\n    }\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Tab.getOrCreateInstance(this)\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  if (['A', 'AREA'].includes(this.tagName)) {\n    event.preventDefault()\n  }\n\n  if (isDisabled(this)) {\n    return\n  }\n\n  const data = Tab.getOrCreateInstance(this)\n  data.show()\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Tab to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(Tab)\n\nexport default Tab\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.1.3): toast.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  reflow,\n  typeCheckConfig\n} from './util/index'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport BaseComponent from './base-component'\nimport { enableDismissTrigger } from './util/component-functions'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'toast'\nconst DATA_KEY = 'bs.toast'\nconst EVENT_KEY = `.${DATA_KEY}`\n\nconst EVENT_MOUSEOVER = `mouseover${EVENT_KEY}`\nconst EVENT_MOUSEOUT = `mouseout${EVENT_KEY}`\nconst EVENT_FOCUSIN = `focusin${EVENT_KEY}`\nconst EVENT_FOCUSOUT = `focusout${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_HIDE = 'hide' // @deprecated - kept here only for backwards compatibility\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_SHOWING = 'showing'\n\nconst DefaultType = {\n  animation: 'boolean',\n  autohide: 'boolean',\n  delay: 'number'\n}\n\nconst Default = {\n  animation: true,\n  autohide: true,\n  delay: 5000\n}\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Toast extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n\n    this._config = this._getConfig(config)\n    this._timeout = null\n    this._hasMouseInteraction = false\n    this._hasKeyboardInteraction = false\n    this._setListeners()\n  }\n\n  // Getters\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n\n  show() {\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW)\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    this._clearTimeout()\n\n    if (this._config.animation) {\n      this._element.classList.add(CLASS_NAME_FADE)\n    }\n\n    const complete = () => {\n      this._element.classList.remove(CLASS_NAME_SHOWING)\n      EventHandler.trigger(this._element, EVENT_SHOWN)\n\n      this._maybeScheduleHide()\n    }\n\n    this._element.classList.remove(CLASS_NAME_HIDE) // @deprecated\n    reflow(this._element)\n    this._element.classList.add(CLASS_NAME_SHOW)\n    this._element.classList.add(CLASS_NAME_SHOWING)\n\n    this._queueCallback(complete, this._element, this._config.animation)\n  }\n\n  hide() {\n    if (!this._element.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    const complete = () => {\n      this._element.classList.add(CLASS_NAME_HIDE) // @deprecated\n      this._element.classList.remove(CLASS_NAME_SHOWING)\n      this._element.classList.remove(CLASS_NAME_SHOW)\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._element.classList.add(CLASS_NAME_SHOWING)\n    this._queueCallback(complete, this._element, this._config.animation)\n  }\n\n  dispose() {\n    this._clearTimeout()\n\n    if (this._element.classList.contains(CLASS_NAME_SHOW)) {\n      this._element.classList.remove(CLASS_NAME_SHOW)\n    }\n\n    super.dispose()\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(this._element),\n      ...(typeof config === 'object' && config ? config : {})\n    }\n\n    typeCheckConfig(NAME, config, this.constructor.DefaultType)\n\n    return config\n  }\n\n  _maybeScheduleHide() {\n    if (!this._config.autohide) {\n      return\n    }\n\n    if (this._hasMouseInteraction || this._hasKeyboardInteraction) {\n      return\n    }\n\n    this._timeout = setTimeout(() => {\n      this.hide()\n    }, this._config.delay)\n  }\n\n  _onInteraction(event, isInteracting) {\n    switch (event.type) {\n      case 'mouseover':\n      case 'mouseout':\n        this._hasMouseInteraction = isInteracting\n        break\n      case 'focusin':\n      case 'focusout':\n        this._hasKeyboardInteraction = isInteracting\n        break\n      default:\n        break\n    }\n\n    if (isInteracting) {\n      this._clearTimeout()\n      return\n    }\n\n    const nextElement = event.relatedTarget\n    if (this._element === nextElement || this._element.contains(nextElement)) {\n      return\n    }\n\n    this._maybeScheduleHide()\n  }\n\n  _setListeners() {\n    EventHandler.on(this._element, EVENT_MOUSEOVER, event => this._onInteraction(event, true))\n    EventHandler.on(this._element, EVENT_MOUSEOUT, event => this._onInteraction(event, false))\n    EventHandler.on(this._element, EVENT_FOCUSIN, event => this._onInteraction(event, true))\n    EventHandler.on(this._element, EVENT_FOCUSOUT, event => this._onInteraction(event, false))\n  }\n\n  _clearTimeout() {\n    clearTimeout(this._timeout)\n    this._timeout = null\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Toast.getOrCreateInstance(this, config)\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config](this)\n      }\n    })\n  }\n}\n\nenableDismissTrigger(Toast)\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Toast to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(Toast)\n\nexport default Toast\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.1.3): index.umd.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport <PERSON><PERSON> from './src/alert'\nimport But<PERSON> from './src/button'\nimport Carousel from './src/carousel'\nimport Collapse from './src/collapse'\nimport Dropdown from './src/dropdown'\nimport Modal from './src/modal'\nimport Offcanvas from './src/offcanvas'\nimport Popover from './src/popover'\nimport ScrollSpy from './src/scrollspy'\nimport Tab from './src/tab'\nimport Toast from './src/toast'\nimport Tooltip from './src/tooltip'\n\nexport default {\n  Alert,\n  Button,\n  Carousel,\n  Collapse,\n  Dropdown,\n  Modal,\n  Offcanvas,\n  Popover,\n  ScrollSpy,\n  Tab,\n  Toast,\n  Tooltip\n}\n"]}