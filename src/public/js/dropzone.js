class DinampTreeEditor {
    mainNode;     //selector
    uuid;
    contextMenu;  //jQuery node
    selectedItem; //jQuery node
    options;      //{}

    //TODO add max capacity and tags, stackabel=bool
    constructor(mainNode) {

        this.uuid = this.newuuid();
        if (!$(mainNode).hasClass("jsTree"))
            $(mainNode).addClass("jsTree");//make sure styles are accurate

        $(mainNode).attr("ui-uuid", this.uuid);
        this.mainNode = mainNode + "[ui-uuid='" + this.uuid + "']";

        //TODO translte contextmenu items
        this.contextMenu = $("<div class='jsTreeContextMenu' ui-uuid='" + this.uuid + "'><p>Delete</p></div>");//<p>Move up</p><p>Move down</p>
        this.contextMenu.insertAfter($(this.mainNode));

        //one-off listeners:

        let jsTree = this;
        $(document).on("mousedown", function (e) {

            if (!$(e.target).hasClass("afterIcon") && !$(e.target).hasClass("jsTreeContextMenu") && !($(e.target).parents(".jsTreeContextMenu").length > 0)) {
                jsTree.contextMenu.hide();
            }
        });

        //initial options:
        this.options = {
            checkboxes: false,
            radios: false,
            editable: true
        };
        //this.rebindListeners();
    }

    setData(data) {
        $(this.mainNode).empty();
        data.forEach(element => this.addElement(element, $(this.mainNode)));
        //TODO Optimize this line here
        this.rebindListeners();
        return this;
    }

    set(opts) {
        let jsTree = this;
        jsTree.options = opts;
        if (opts.extended === false) {
            $(this.mainNode + " .afterIcon").each(function () {
                if ($(this).hasClass("arrowDown")) $(this).addClass("arrowRotate");
            });
            $(this.mainNode + " .childGroup").hide();
        }
        if (opts.checkboxes === true) {
            $(this.mainNode + " .afterIcon").each(function () {
                $(this).removeClass("arrowDown");
                $(this).addClass("checkboxIcon");
            });
            jsTree.options.radios = false;
        } else if (opts.radios === true) {
            $(this.mainNode + " .afterIcon").each(function () {
                if (!$(this).hasClass("arrowDown")) {
                    $(this).addClass("radiobtnIcon");
                }
            });
            jsTree.options.checkboxes = false;
        } else {
            jsTree.options.radios = false;
            jsTree.options.checkboxes = false;
        }

        if (opts.editable === false) {
            $(this.mainNode + " p").removeAttr("contenteditable");
            $(this.mainNode + " .afterIcon").hide();
        } else {
            jsTree.options.editable = true;

        }

        this.rebindListeners();
        return this;
    }

    getData() {
        let jsTree = this;
        var retVal = [];
        $(this.mainNode).subs().each(function () {
            jsTree.pushData(retVal, jsTree, $(this));
        });
        return retVal;
    }

    pushData(parentData, jsTree, subject) {
        if (subject.is("ul")) return;
        if (subject.is(".itemParent")) {
            let currentItem = {
                title: subject.find("p").text()
            };
            if (subject.find(".afterIcon").hasClass("checked")) currentItem.checked = true;
            if (subject.next().is("ul")) {
                currentItem.subs = [];
                $(subject.next()).subs().each(function () {
                    jsTree.pushData(currentItem.subs, jsTree, $(this).find(".itemParent").eq(0));
                });
            }

            parentData.push(currentItem);
        }
    }

    addElement(el, parentNode = null, id = null) {
        var $newNode;
        $(".childGroup").hide();
        if (parentNode.is("ul"))
            $newNode = $(`<li class='item' id='${el.id}'><div class='itemParent' id='${el.id}'><i class="fa-regular fa-folder preIcon" style="color: #5B5B5B"></i>  </i><div class="contenteditable"><p contenteditable='false' style="font-family: Poppins, sans-serif; color: #5B5B5B"  >${el.title}</p></div></span><span class='afterIcon' style="color: #5B5B5B"></span></div></li>`);
        else
            $newNode = $(`<div class='itemParent' id='${el.id}'> <i class="fa-regular fa-folder preIcon" style="color: #5B5B5B"></i> </span><div class="contenteditable"><p contenteditable='false' style="font-family: Poppins, sans-serif; color: #5B5B5B">${el.title} </p></div></span> <span class='afterIcon' style="color: #5B5B5B"></span></div>`);
        //if(parentNode == null) parentNode = $(this.mainNode);
        parentNode.append($newNode);
        if (el.checked === true || el.checked === "true") $newNode.find(".afterIcon").addClass("checked");
        if (el.subs !== undefined) {
            $newNode.find(".afterIcon").addClass("arrowDown arrowRotate");
            var $chContainer = $("<ul class='childGroup'></ul>");
            if (parentNode.is("ul"))
                $newNode.append($chContainer);
            else
                $(this.mainNode).append($chContainer);
            el.subs.forEach(element => this.addElement(element, $chContainer));
        }
    }


//EVENT LISTENERS BEGIN HERE
    unbindListenders() {
        $(this.mainNode + " p").off();
        $(this.mainNode + " .preIcon").off();
        $(this.mainNode + " .afterIcon").off();
        $(".jsTreeContextMenu[ui-uuid='" + this.uuid + "'] p").off();
    }

    rebindListeners(jsTree = this) {
        jsTree.unbindListenders();
        $(this.mainNode + " p").keydown(function (e) {
            if (e.keyCode == 13) {//code here is duplicate from below
                jsTree.selectedItem = $(":focus").closest(".itemParent");
                if (jsTree.selectedItem.parent().is("li")) {

                } else if (jsTree.selectedItem.next().length > 0 && jsTree.selectedItem.next().is(".childGroup")) {
                    $newNode = $("<div class='itemParent'><span class='preIcon'></span><div><p contenteditable='true'>New item</p></div><span class='afterIconEdit'></span><span class='afterIcon'></span></div>");
                    $newNode.insertAfter(jsTree.selectedItem.next());
                } else {
                    $newNode = $("<div class='itemParent'><span class='preIcon'></span><div><p contenteditable='true'>New item</p></div><span class='afterIconEdit'></span><span class='afterIcon'></span></div>");
                    $newNode.insertAfter(jsTree.selectedItem);
                }
                jsTree.rerender(jsTree);
                return false;
            }
        });

        $(this.mainNode + " p").on('blur', function () {
            jsTree.options.onchange(jsTree);
        });

        $(this.mainNode + " .afterIcon").on('click', function () {
            if ($(this).hasClass("arrowDown") && !$(this).hasClass("arrowRotate")) { //subs are expanded must retract
                if ($(this).parent().parent().is("li"))
                    $(this).parent().parent().find(".childGroup").eq(0).animate({height: "toggle"}, 400);
                else
                    $(this).parent().next().animate({height: "toggle"}, 400);
                $(this).addClass("arrowRotate");
            } else if ($(this).hasClass("arrowDown") && $(this).hasClass("arrowRotate")) { //subs are retracted
                if ($(this).parent().parent().is("li"))
                    $(this).parent().parent().find(".childGroup").eq(0).animate({height: "toggle"}, 400);
                else
                    $(this).parent().next().animate({height: "toggle"}, 400);
                $(this).removeClass("arrowRotate");
            } else if ($(this).hasClass("checkboxIcon")) {
                if ($(this).hasClass("checked")) $(this).removeClass("checked");
                else $(this).addClass("checked");
            } else if ($(this).hasClass("radiobtnIcon")) {
                $(jsTree.mainNode + " .afterIcon").removeClass("checked");
                $(this).addClass("checked");
            }

            if ($(this).hasClass("checkboxIcon") || $(this).hasClass("radiobtnIcon")) {
                if (jsTree.options.oncheck !== undefined) {
                    let pathToDis = [];
                    var curItem = $(this).parent();
                    while (curItem.parent().is("li") || curItem.parent().is(jsTree.mainNode)) {
                        pathToDis.unshift(curItem.find("p").text());
                        curItem = curItem.parent().parent().prevAll().eq(0);
                    }
                    jsTree.options.oncheck($(this).hasClass("checked"), $(this).parent().find("p").text(), pathToDis);
                }
                if (jsTree.options.onchange !== undefined) {
                    jsTree.options.onchange(jsTree);
                }
            }
        });


        var value
        $(this.mainNode + " .preIcon").on('click', function () {
            value = $(this).parents('.itemParent').attr('id')
            $(`#${value}`).toggleClass('color');
        });

    }

    rerender(jsTree = this) {
        if (jsTree.options.checkboxes === true) {
            $(jsTree.mainNode + " .afterIcon").each(function () {
                if (!$(this).hasClass("arrowDown")) {
                    $(this).addClass("checkboxIcon");
                }
            });
            jsTree.options.radios = false;
        } else if (jsTree.options.radios === true) {
            $(jsTree.mainNode + " .afterIcon").each(function () {
                if (!$(this).hasClass("arrowDown")) {
                    $(this).addClass("radiobtnIcon");
                }
            });
        } else {
            $(jsTree.mainNode + " .itemParent").each(function () {//TODO optimize, when delete delay required, otherwise not
                if ($(this).next().is("ul")) {
                    if ($(this).next().subs().length > 0) {
                        $(this).find(".afterIcon").eq(0).addClass("arrowDown");
                        if ($(this).next().is(":visible")) {
                            $(this).find(".afterIcon").eq(0).removeClass("arrowRotate");
                        } else
                            $(this).find(".afterIcon").eq(0).addClass("arrowRotate");
                    } else
                        $(this).find(".afterIcon").eq(0).removeClass("arrowDown");
                } else
                    $(this).find(".afterIcon").eq(0).removeClass("arrowDown");
            });
        }
        if (jsTree.options.onchange !== undefined) {
            jsTree.options.onchange(jsTree);
        }

        jsTree.rebindListeners(jsTree);
    }

    newuuid() {
        return ([1e7] + -1e11).replace(/[018]/g, c =>
            (c ^ crypto.getRandomValues(new Uint8Array(1))[0] & 15 >> c / 4).toString(16)
        );
    }
}
let data = @json($folders_list);
$(".firstTree").each(function( index ) {
    const divIdAttribute=$('.firstTree')[index];
    const divId=$(divIdAttribute).attr('id');
    new DinampTreeEditor(`#${divId}`).setData(data);
});

$('.contenteditable').on('click',function () {
    const folderId=$(this).parents('.itemParent').attr('id');
    $(this).parents('.itemParent').toggleClass('selectedFolder')
    if($(this).parents('.itemParent').hasClass('selectedFolder')){
        console.log('folderId',folderId);
        $('#folderId').val(folderId);
        console.log("value",$('#folder_id').val())
    }
})
