<?php


namespace Apimio\Gallery\Classes;


class ImageQualityCount{

    public $file; //save scoring of images
    public function __construct() {

    }

    /*
    * return count of approved, warning, error images
    * */
    public function countImageQuality($files) {
        $this->file = array();
        $this->file['approve'] = 0;
        $this->file['warning'] = 0;
        $this->file['error'] = 0;

        foreach ($files as $file) {
            if ($file->imageQualityScoreByImage($file)['approve'] == 100) {
                $this->file['approve'] += 1;
            } elseif ($file->imageQualityScoreByImage($file)['warning'] == 100) {
                $this->file['warning'] += 1;
            } else {
                $this->file['error'] += 1;
            }
        }
        return $this->file;
    }

}
