<?php

namespace Apimio\MappingConnectorPackage\Http\Controllers;

use Apimio\MappingConnectorPackage\models\Vlookup;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class VLookupController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $vlookup = new Vlookup();
        $message = "";
        $submit_type = $request->submit_type;
        if ($submit_type == 'add'){
            $message = "VLookup created successfully.";
        }
        elseif($submit_type == 'edit'){
            $message = "VLookup updated successfully.";

        }
        return $vlookup->set_data($request->all())->store(function ($error) {
            return response()->json([
                'status' => 'error',
                'data' => $error,
                'message' => "Please fill all the fields",
            ],500);
        }, function ($success) use ($message) {
            return response()->json([
                'status' => 'success',
                'data' => $success,
                'message' => $message,
            ],200);
        });
    }

    /**
     * Display the specified resource.
     *
     * @param Vlookup $vlookup
     * @return \Illuminate\Http\Response
     */
    public function show(Vlookup $vlookup)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param Vlookup $vlookup
     * @return \Illuminate\Http\Response
     */
    public function edit(Vlookup $vlookup)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param Vlookup $vlookup
     */
    public function update(Request $request, Vlookup $vlookup)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     * @param Vlookup $vlookup
     */
    public function destroy(Vlookup $vlookup)
    {
        //
    }


    /**
     * fetch the specified id values.
     *
     */
    public function fetch(Request $request)
    {
       
        $result = [];
        
        // Check if 'id' is present in the request
        if ($request->has('id')) {
            $vlookup = Vlookup::find($request->id);
            
            if (!$vlookup) {
                return response()->json([
                    'status' => 'error',
                    'message' => "VLookup not found for the specified id",
                ], 404);
            }
    
            $vlookup_values = json_decode($vlookup->payload, true);
    
            $values = '';
            foreach ($vlookup_values as $lookup_key => $lookup_value) {
                $values .= $lookup_key . "," . $lookup_value . "\n";
            }
    
            $result[] = [
                'id' => $vlookup->id,
                'name' => $vlookup->name,
                'values' => $values,
            ];
        } else {
            // If 'id' is not present, fetch all Vlookups
            $vlookups = Vlookup::all();
    
            foreach ($vlookups as $vlookup) {
                $vlookup_values = json_decode($vlookup->payload, true);
    
                $values = '';
                foreach ($vlookup_values as $lookup_key => $lookup_value) {
                    $values .= $lookup_key . "," . $lookup_value . "\n";
                }
    
                $result[] = [
                    'id' => $vlookup->id,
                    'name' => $vlookup->name,
                    'values' => $values,
                ];
            }
        }
    
        return response()->json([
            'status' => 'success',
            'data' => $result,
            'message' => "Vlookups fetched",
        ], 200);
    }
    
}
