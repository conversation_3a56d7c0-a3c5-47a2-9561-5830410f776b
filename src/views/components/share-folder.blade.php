<!-- The Modal -->
<div class="modal fade" id="{{$id}}" tabindex="-1" aria-labelledby="example-{{$id}}" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title Poppins semibold" id="ModalLabel" style="font-size: 20px">
                    Share Link
                </h5>
                <button type="button" class="btn-sm border-0 bg-white fs-24 px-0 pt-0" data-bs-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form  id="share-form" class="share_input">
                    <div class="form-group input_parent mb-5">
                        <label>Folder Link</label>
                        <input name="folder_link"
                               type="text"
                               class="form-control share_link "
                               id="share-{{$id}}"
                               value="{{$shareLink}}"
                               placeholder="{{$shareLink}}">

                        <span class="text-danger" role="alert">
                            <small></small>
                        </span>
                    </div>
                    <div class="form-group btn_div">
                        <div class="input-group-append ">
                            <button type="button"
                                    class="btn btn-dark ripplelink shadow-0 copy-btn"
                                    style="z-index: 0"
                                    id="btn-{{$id}}"
                                   >
                                COPY LINK
                            </button>
                    </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

{{--// TODO: Fahad we have to change button and show copied here--}}
@push('footer_scripts')

    <script>
            $(`.copy-btn`).on('click', function () {
                let value = $(this).closest('.share_input').find('.input_parent input').val();
                $(this).closest('.share_input').find('.input_parent input').select();
                // copyText.setSelectionRange(0, 99999); /*For mobile devices*/
                navigator.clipboard.writeText(value);
                var copyButton = $(this).closest('.share_input').find('.btn_div button')
                copyButton.html("COPIED!");
                setTimeout(function() {
                    copyButton.html("COPY LINK");
                }, 2000); // Change back to "COPY LINK" after 2 seconds (2000 milliseconds)
            } )




        // function outFunc() {
        //     var tooltip = document.getElementById("myTooltip");
        // }
    </script>
@endpush
