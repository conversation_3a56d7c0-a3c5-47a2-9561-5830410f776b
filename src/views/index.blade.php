@extends('mapping::layouts.app')
@section('titles','Mapping Fields')
@section('content')

    <x-products.page-title name="Mapping Products" description="{{trans('Mapping convertion of your all products')}}" links="false"
                           button="false" buttonname="null"/>

    <form action="{{ $mapping_type == 'import' ? route('mappingfields.import') : route('mappingfields.export') }}" method="POST">
        <input type="hidden" name="template" value=""  class="import_input_template">
        @csrf
        <div>
            <div class="d-flex justify-content-between">
                <div class="d-flex align-items-sm-center">
                    <h3 class="mb-0">
                        Templates
                    </h3>
                </div>
                <div class="position-relative me-2">
                    <button role="button" id="create_template" name="create_template" value="create" class="btn btn-primary">
                        {{trans('products_export_step2.create_new_template')}}
                    </button>


                </div>
            </div>
            <hr style="width: 100%;border-top: 1px solid rgba(0,0,0,.1);margin-top:4px">


            <div class="mt-4">
                <p>Would you like to use one of your previously saved templates
                    for {{$templates->first()->type}}?</p>


                <div id="no_template_found" style="display: none">
                    <div class="d-flex flex-column ">
                        <div class="p-2 mt-5 mx-auto">
                            <img src="{{URL::asset('/mapping-fields/icons/<EMAIL>')}}" class="img-fluid"
                                 width="150px" alt="empty page">
                        </div>
                        <div class="p-2 mx-auto">
                            <p class="Roboto">
                                You have not created any template yet.
                            </p>
                        </div>
                    </div>
                </div>


                <table id="mapping_templates_table" class="hover row-border" style="width:100%">
                    <thead>
                    <tr>
                        <th>Name</th>
                        <th>Modified</th>

                        @if($templates->first()->type == "export")
                            <th>Export Type</th>
                        @endif
                        <th class="text-end">Action</th>

                    </tr>
                    </thead>
                    <tbody>
                    @foreach($templates as $key => $template)
                        <tr class="template-row"
                            data-temp-id="{{isset($template->id) ? $template->id : Str::slug($template->name)}}"
                            data-temp-name="{{$template->name ?? null}}">

                            <td>
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-folder-open temp-folder-icon me-3"></i>
                                    <span class="font-weight-bold">{{$template->name}}</span>
                                </div>
                            </td>

                            <td>{{isset($template->updated_at) ? $template->updated_at->diffForHumans() : null}}</td>
                            @if($templates->first()->type == "export")
                                <td>{{$template->export_type}}</td>
                            @endif
                            <td class="text-right">
                                <div class="d-flex align-items-center justify-content-end">
                                    <div>
                                        <a href="javascript:void(0)" type="button" class="edit_mapping_template text-primary text-decoration-none">Apply</a>
                                        @if(isset($template->id))
                                            <a href="javascript:void(0)" type="button" class="delete_mapping_template text-decoration-none ms-1"><i class="fa-regular fa-trash-can fs-20 text-danger" ></i></a>
                                        @endif
                                    </div>

                                </div>
                            </td>
                        </tr>
                    @endforeach
                    </tbody>

                </table>
            </div>
        </div>



        {{--        <div class="row mapping_footer">--}}

        {{--            <div class="col-lg-8 text-right ml-auto">--}}
        {{--                <div class="row">--}}
        {{--                    <div class="col-lg-12 ">--}}
        {{--                        <div class="d-flex flex-row-reverse mt-4">--}}
        {{--                            <div class="p-2" style="width: 159px">--}}
        {{--                                <button type="submit" id="template_submit" name="template_submit"--}}
        {{--                                        class="form-control btn btn-primary ripplelink">--}}
        {{--                                    Next--}}
        {{--                                </button>--}}
        {{--                            </div>--}}
        {{--                            <div class="p-2" style="width: 159px">--}}
        {{--                                <a href="{{route('products.index')}}" id="cancel-btn"--}}
        {{--                                   class="form-control btn btn-outline-primary hovereffect ripplelink">--}}
        {{--                                    Cancel--}}

        {{--                                </a>--}}
        {{--                            </div>--}}
        {{--                        </div>--}}

        {{--                    </div>--}}

        {{--                </div>--}}

        {{--            </div>--}}
        {{--        </div>--}}
    </form>


    <!--VLookup Modal -->
    <x-component-template-delete/>
@endsection
@push('footer_scripts')
    <script>

        $(document).ready(function () {


            var template_row = "";

            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                }
            });

            // Default Configuration
            $(document).ready(function() {
                toastr.options = {
                    'closeButton': true,
                    'debug': false,
                    'newestOnTop': false,
                    'progressBar': true,
                    'positionClass': 'toast-top-right',
                    'preventDuplicates': false,
                    'showDuration': '1000',
                    'hideDuration': '1000',
                    'timeOut': '4000',
                    'extendedTimeOut': '1000',
                    'showEasing': 'swing',
                    'hideEasing': 'linear',
                    'showMethod': 'fadeIn',
                    'hideMethod': 'fadeOut',
                }
            });


            const mapping_templates_table = $('#mapping_templates_table').DataTable({
                responsive: true,
                order: [[ 1, 'desc' ]],
                // "pageLength": 10
            });

            $(document).on('click', ".edit_mapping_template",function (event) {
                event.preventDefault();
                const template_input_field = $('input[name=template]');
                template_input_field.val($(this).parents('.template-row').data('temp-id'));
                template_input_field.closest("form").submit();
            });

             $(document).on('click', ".delete_mapping_template" ,function (event) {
                event.preventDefault();
                template_row = $(this).parents('.template-row');
                $('input[name=template_delete]').val(template_row.data('temp-id'));
                $('#template_delete_name').html(template_row.data('temp-name'));
                $("#template_delete_alert").modal('show');
            });

            $('#template_delete_btn').on('click',function (event) {
                event.preventDefault();
                var self = this;
                var previous_value = $(self).html();
                var form = $(this).closest('form');
                $.ajax({
                    url: '{{route('mapping_delete_template')}}',
                    type: "POST",
                    data: $(form).serialize(),
                    beforeSend: function () {
                        $(self).addClass('disabled');
                        $(self).html('<span class="spinner-border spinner-border-sm ml-2" role="status" aria-hidden="true"></span>');
                    },
                    error: function (response) {
                        $('#template_delete_alert').modal('hide');
                        $(self).removeClass('disabled');
                        $(self).html(previous_value);
                        toastr.error(response.responseJSON.msg);
                    },
                    success: function (response) {
                        $('#template_delete_alert').modal('hide');
                        $(self).removeClass('disabled');
                        $(self).html(previous_value);
                        toastr.success(response.msg);
                        template_row.addClass("bg-danger");
                        var templates_count = $('#mapping_templates_table').find('.template-row').length;
                        if(templates_count <= 1){
                            $('#mapping_templates_table_wrapper').hide();
                            $('#no_template_found').show();
                        }
                        template_row.hide(1000, function(){
                            this.remove();
                        });


                    }
                });
            });

            // $(".apply_mapping_template").click(function () {
            //     $(this).parents('tr').find('td:first').trigger('click');
            // });

            {{--$('#mapping_templates_table tbody tr td').not(":last-child").click(function() {--}}
            {{--    var result = confirm('Do you want to use this template to perform your action?');--}}
            {{--    if(!result){--}}
            {{--        return false;--}}
            {{--    }--}}
            {{--    let template_id = $(this).parents('.template-row').data('temp-id');--}}
            {{--    var url = '{{ route("apply_mapping_template", ":id") }}';--}}
            {{--    url = url.replace(':id', template_id);--}}
            {{--    location.href= url;--}}

            {{--});--}}

        });



    </script>
@endpush







