<?php

namespace Apimio\MappingConnectorPackage\components;

use Illuminate\View\Component;

class MappingPopup extends Component
{
    public $template_attributes,$data_required;
    /**
     * Create a new component instance.
     *
     * @return void
     */
    public function __construct($templateAttributes = null,$dataRequired)
    {
        $this->template_attributes = $templateAttributes;
        $this->data_required = $dataRequired;
    }

    /**
     * Get the view / contents that represent the component.
     *
     * @return \Illuminate\View\View|string
     */
    public function render()
    {
        $export_types = [
                'shopify' => 'Shopify',
                'magento' => 'Magento',
                'Custom' => 'Custom',
        ];

        $text_initials = [];
        if ($this->data_required){
            $text_initials = $this->popup_text_initial($this->data_required['template_method_type'] ?? '');
        }
        return view('mapping::components.mapping-popup' , compact('export_types', 'text_initials'));
    }


    public function popup_text_initial($template_method_type){
        $text_initials = array();
        $initials = [
            'import' => [
                'heading_name' => 'Import Configuration',
                'btn_name' => 'Start Importing'
            ],
            'export' => [
                'heading_name' => 'Export Configuration',
                'btn_name' => 'Start Exporting'
            ],
            'shopify' => [
                'heading_name' => 'Shopify Configuration',
                'btn_name' => 'Start Shopify'
            ],
        ];

        if (in_array($template_method_type , array_keys($initials))){
            $text_initials = $initials[$template_method_type];
        }

        return $text_initials;
    }
}
