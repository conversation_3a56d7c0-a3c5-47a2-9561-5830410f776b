<?php

namespace Apimio\Gallery\components;

use Apimio\Gallery\Models\File;
use Apimio\Gallery\Models\Folder;
use Illuminate\View\Component;

class UploadFromGallery extends Component
{

    public $files;
    public $folders_list;
    public $product;

    /**
     * Create a new component instance.
     *
     * @return void
     */
    public function __construct($product)
    {
        $this->files = File::with('folders')->whereDoesntHave('products',function ($query) use ($product) {
            $query->where('product_id',$product->id);
        })->where('type','img')
        // ->get();
        ->paginate(20);
        $this->folders_list = (new Folder())->format_folders();
        $this->product = $product;
    }

    /**
     * Get the view / contents that represent the component.
     *
     * @return \Illuminate\Contracts\View\View|\Closure|string
     */
    public function render()
    {
        return view('gallery::components.upload-from-gallery');
    }
}
