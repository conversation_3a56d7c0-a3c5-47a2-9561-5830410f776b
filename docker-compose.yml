services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    image: laravel-app
    container_name: laravel-app
    restart: unless-stopped
    ports:
      - "8000:80"
      - "5173:5173" # If using Vite's default port
    volumes:
      - ./:/var/task
      - node_modules:/var/task/node_modules # Named volume for node_modules
    depends_on:
      - db
    networks:
      - laravel-network

  db:
    image: mysql:8.0
    container_name: mysql-db
    restart: unless-stopped
    env_file:
      - docker.env
    ports:
      - "3306:3306"
    volumes:
      - dbdata:/var/lib/mysql
    networks:
      - laravel-network

networks:
  laravel-network:

volumes:
  dbdata:
  node_modules:
