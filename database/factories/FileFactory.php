<?php

/** @var \Illuminate\Database\Eloquent\Factory $factory */

use App\File;
use Faker\Generator as Faker;

$factory->define(\App\Models\Organization\File::class, function (Faker $faker) {
    $width = rand(10, 2000);
    $height = rand(10, 2000);
    $link = "http://lorempixel.com/" . $width . "/" . $height . "/food/";
    $type = ["img", "video", "file"];
    return [
        "organization_id" => \App\Models\Organization\Organization::all()->random()->id,
        "link" => $link,
        "width" => $width,
        "height" => $height,
        "size" => rand(10, 2000),
        "ext" => "jpg",
        "type" => $type[rand(0,2)]
    ];
});
