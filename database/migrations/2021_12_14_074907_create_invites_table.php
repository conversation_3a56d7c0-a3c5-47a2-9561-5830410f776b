<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateInvitesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('invites', function (Blueprint $table) {
            $table->id();
            $table->string('email', 76);
            $table->foreignId('organization_id_sender')->comment("User sending invite.")->constrained('organizations')->onDelete('cascade');
            $table->foreignId('organization_id_receiver')->comment("User receiving invite.")->nullable()->constrained('organizations')->onDelete('cascade');
            $table->string("token", 35)->unique();
            $table->boolean("is_accepted");
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('invites');
    }
}
