<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\Channel\ChannelProduct;
use App\Models\Channel\ChannelProductStatus;

class CreateChannelProductStatusesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('channel_product_statuses', function (Blueprint $table) {
            $table->id();
            $table->foreignId('organization_id')->constrained('organizations')->onDelete('cascade');
            $table->foreignId('channel_product_id')->constrained('channel_product')->onDelete('cascade');
            $table->string('type')->nullable()->comment('type=>shopify,clone');
            $table->longText("response")->nullable()->comment('sync_id');
            $table->timestamps();
        });

        if(env('APP_ENV') == 'production'){
            $channel_product = ChannelProduct::with('channel')->get();
            foreach ($channel_product as $p)
            {
                $status = new ChannelProductStatus();
                $status->organization_id     = $p->channel?$p->channel->organization_id:1 ;
                $status->channel_product_id  = $p->id;
                $status->type                = 'shopify';
                $status->response            = $p->channel_sync_id?json_encode(['sync_id'=>$p->channel_sync_id]):null;
                $status->created_at          = $p->created_at??now();
                $status->updated_at          = $p->updated_at??now();
                $status->save();
            }
        }


    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('channel_product_statuses');
    }
}

