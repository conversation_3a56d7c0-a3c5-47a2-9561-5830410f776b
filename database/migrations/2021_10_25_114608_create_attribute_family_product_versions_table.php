<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateAttributeFamilyProductVersionsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('attribute_family_product_versions', function (Blueprint $table) {
            $table->id();
            $table->text('value')->nullable();
            $table->foreignId('attribute_family_id')->constrained('attribute_family')->onDelete('cascade')->nullable();
            $table->foreignId('product_version_id')->constrained('product_version')->onDelete('cascade')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('attribute_family_product_versions');
    }
}
