<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('attribute_family_product_versions', function (Blueprint $table) {
            $table->foreignId('product_id')->after("id")->nullable()->constrained('products')->onDelete('cascade');
            $table->foreignId('version_id')->after("id")->nullable()->constrained('versions')->onDelete('cascade');
            $table->foreignId('family_id')->after("id")->nullable()->constrained('families')->onDelete('cascade');
            $table->foreignId('attribute_id')->after("id")->nullable()->constrained('attributes')->onDelete('cascade');
        });


    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
};
