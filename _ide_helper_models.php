<?php

// @formatter:off
/**
 * A helper file for your Eloquent Models
 * Copy the phpDocs from this file to the correct Model,
 * And remove them from this file, to prevent double declarations.
 *
 * <AUTHOR> vd. Heuvel <<EMAIL>>
 */


namespace App\Models\Admin{
/**
 * App\Models\Admin\Admin
 *
 * @method static \Illuminate\Database\Eloquent\Builder|Admin newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Admin newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Admin query()
 */
	class Admin extends \Eloquent {}
}

namespace App\Models{
/**
 * App\Models\App
 *
 * @property int $id
 * @property string|null $name
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|App newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|App newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|App query()
 * @method static \Illuminate\Database\Eloquent\Builder|App whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|App whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|App whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|App whereUpdatedAt($value)
 */
	class App extends \Eloquent {}
}

namespace App\Models{
/**
 * App\Models\BatchProgress
 *
 * @property int $id
 * @property int $user_id
 * @property int $organization_id
 * @property string $batch_id
 * @property string|null $type
 * @property int $status 0=>not complete, 1=>complete
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|BatchProgress newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|BatchProgress newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|BatchProgress query()
 * @method static \Illuminate\Database\Eloquent\Builder|BatchProgress whereBatchId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BatchProgress whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BatchProgress whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BatchProgress whereOrganizationId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BatchProgress whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BatchProgress whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BatchProgress whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BatchProgress whereUserId($value)
 */
	class BatchProgress extends \Eloquent {}
}

namespace App\Models\Billing{
/**
 * App\Models\Billing\Billing
 *
 * @method static \Illuminate\Database\Eloquent\Builder|Billing newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Billing newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Billing query()
 */
	class Billing extends \Eloquent {}
}

namespace App\Models\Billing{
/**
 * App\Models\Billing\ShopifySubscription
 *
 * @property int $id
 * @property string $name
 * @property int $shopify_channel_id
 * @property string $recurring_application_charge_id
 * @property string $price
 * @property string $status
 * @property string $billing_on when client is billed
 * @property int $test whether it is a test or not.
 * @property string|null $activated_on
 * @property string|null $cancelled_on
 * @property int $trial_days
 * @property string $capped_amount
 * @property string|null $trial_ends_on
 * @property string $balance_used
 * @property string $balance_remaining
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property int|null $organization_id
 * @property int|null $plan_id
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifySubscription newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifySubscription newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifySubscription query()
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifySubscription whereActivatedOn($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifySubscription whereBalanceRemaining($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifySubscription whereBalanceUsed($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifySubscription whereBillingOn($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifySubscription whereCancelledOn($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifySubscription whereCappedAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifySubscription whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifySubscription whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifySubscription whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifySubscription whereOrganizationId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifySubscription wherePlanId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifySubscription wherePrice($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifySubscription whereRecurringApplicationChargeId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifySubscription whereShopifyChannelId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifySubscription whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifySubscription whereTest($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifySubscription whereTrialDays($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifySubscription whereTrialEndsOn($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifySubscription whereUpdatedAt($value)
 */
	class ShopifySubscription extends \Eloquent {}
}

namespace App\Models\Channel{
/**
 * App\Models\Channel\Channel
 *
 * @property int $id
 * @property int|null $organization_id
 * @property string $name
 * @property string|null $product_update
 * @property int|null $inventory
 * @property string|null $export_status
 * @property int $category_empty yes=>1, no=>0
 * @property string $type
 * @property string $syncing_method
 * @property int $is_create_product_webhook_enabled 1=>enable, 0=>disable
 * @property int $is_product_update_webhook_enabled 1=>enable, 0=>disable
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Channel\ChannelVersion> $channel_versions
 * @property-read int|null $channel_versions_count
 * @property-read \App\Models\Organization\Organization|null $organization
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Product\Product> $products
 * @property-read int|null $products_count
 * @property-read \App\Models\Channel\ShopifyChannel|null $shopify_channel
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Channel\ShopifyChannel> $shopify_channels
 * @property-read int|null $shopify_channels_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Product\Version> $versions
 * @property-read int|null $versions_count
 * @method static \Illuminate\Database\Eloquent\Builder|Channel allOrganizations()
 * @method static \Illuminate\Database\Eloquent\Builder|Channel cloneAction($channel_id, $product)
 * @method static \Illuminate\Database\Eloquent\Builder|Channel isShopifyLinked($channel_id)
 * @method static \Illuminate\Database\Eloquent\Builder|Channel newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Channel newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Channel query()
 * @method static \Illuminate\Database\Eloquent\Builder|Channel syncAction($channel_id, $product)
 * @method static \Illuminate\Database\Eloquent\Builder|Channel whereCategoryEmpty($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Channel whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Channel whereExportStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Channel whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Channel whereInventory($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Channel whereIsCreateProductWebhookEnabled($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Channel whereIsProductUpdateWebhookEnabled($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Channel whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Channel whereOrganizationId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Channel whereProductUpdate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Channel whereSyncingMethod($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Channel whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Channel whereUpdatedAt($value)
 */
	class Channel extends \Eloquent {}
}

namespace App\Models\Channel{
/**
 * App\Models\Channel\ChannelProduct
 *
 * @property int $id
 * @property int $channel_id
 * @property int $product_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Channel\Channel|null $channel
 * @property-read \App\Models\Channel\ChannelProductStatus|null $shopify_status
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Channel\ChannelProductStatus> $status
 * @property-read int|null $status_count
 * @method static \Illuminate\Database\Eloquent\Builder|ChannelProduct newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ChannelProduct newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ChannelProduct query()
 * @method static \Illuminate\Database\Eloquent\Builder|ChannelProduct whereChannelId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChannelProduct whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChannelProduct whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChannelProduct whereProductId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChannelProduct whereUpdatedAt($value)
 */
	class ChannelProduct extends \Eloquent {}
}

namespace App\Models\Channel{
/**
 * App\Models\Channel\ChannelProductStatus
 *
 * @property int $id
 * @property int $organization_id
 * @property int $channel_product_id
 * @property string|null $type type=>shopify,clone
 * @property int $status 0=>not synced, 1=>synced
 * @property string|null $response sync_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Channel\ChannelProduct $channel_product
 * @method static \Illuminate\Database\Eloquent\Builder|ChannelProductStatus newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ChannelProductStatus newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ChannelProductStatus query()
 * @method static \Illuminate\Database\Eloquent\Builder|ChannelProductStatus whereChannelProductId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChannelProductStatus whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChannelProductStatus whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChannelProductStatus whereOrganizationId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChannelProductStatus whereResponse($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChannelProductStatus whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChannelProductStatus whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChannelProductStatus whereUpdatedAt($value)
 */
	class ChannelProductStatus extends \Eloquent {}
}

namespace App\Models\Channel{
/**
 * App\Models\Channel\ChannelVersion
 *
 * @property int $id
 * @property int $channel_id
 * @property int $version_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|ChannelVersion newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ChannelVersion newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ChannelVersion query()
 * @method static \Illuminate\Database\Eloquent\Builder|ChannelVersion whereChannelId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChannelVersion whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChannelVersion whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChannelVersion whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChannelVersion whereVersionId($value)
 */
	class ChannelVersion extends \Eloquent {}
}

namespace App\Models\Channel{
/**
 * App\Models\Channel\ShopifyChannel
 *
 * @property int $id
 * @property int $channel_id
 * @property string $access_token
 * @property string $shop
 * @property string $shop_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Channel\Channel $channel
 * @property-read \App\Models\Billing\ShopifySubscription|null $shopify_subscription
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifyChannel newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifyChannel newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifyChannel query()
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifyChannel whereAccessToken($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifyChannel whereChannelId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifyChannel whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifyChannel whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifyChannel whereShop($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifyChannel whereShopId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifyChannel whereUpdatedAt($value)
 */
	class ShopifyChannel extends \Eloquent {}
}

namespace App\Models{
/**
 * App\Models\EventStatusHandler
 *
 * @property int $id
 * @property int $organization_id
 * @property string|null $listener_name
 * @property int $status 0=>delete, 1=> active function
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|EventStatusHandler newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|EventStatusHandler newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|EventStatusHandler query()
 * @method static \Illuminate\Database\Eloquent\Builder|EventStatusHandler whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EventStatusHandler whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EventStatusHandler whereListenerName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EventStatusHandler whereOrganizationId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EventStatusHandler whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EventStatusHandler whereUpdatedAt($value)
 */
	class EventStatusHandler extends \Eloquent {}
}

namespace App\Models\Formula{
/**
 * App\Models\Formula\Vlookup
 *
 * @property int $id
 * @property int $organization_id
 * @property string $name
 * @property string $payload
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|Vlookup newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Vlookup newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Vlookup query()
 * @method static \Illuminate\Database\Eloquent\Builder|Vlookup whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Vlookup whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Vlookup whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Vlookup whereOrganizationId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Vlookup wherePayload($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Vlookup whereUpdatedAt($value)
 */
	class Vlookup extends \Eloquent {}
}

namespace App\Models\Invite{
/**
 * App\Models\Invite\ChannelInvite
 *
 * @property int $id
 * @property int $channel_id
 * @property int $invite_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|ChannelInvite newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ChannelInvite newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ChannelInvite query()
 * @method static \Illuminate\Database\Eloquent\Builder|ChannelInvite whereChannelId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChannelInvite whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChannelInvite whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChannelInvite whereInviteId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChannelInvite whereUpdatedAt($value)
 */
	class ChannelInvite extends \Eloquent {}
}

namespace App\Models\Invite{
/**
 * App\Models\Invite\Invite
 *
 * @property int $id
 * @property string|null $email
 * @property int $organization_id_sender User sending invite.
 * @property int|null $organization_id_receiver User receiving invite.
 * @property string|null $token
 * @property string $fname
 * @property string|null $lname
 * @property string|null $designation
 * @property string|null $phone
 * @property int|null $is_accepted
 * @property int $is_declined 0 => not declined , 1 => declined
 * @property string $type
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Channel\Channel> $channels
 * @property-read int|null $channels_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Channel\Channel> $channels_without_scope
 * @property-read int|null $channels_without_scope_count
 * @property-read \App\Models\Organization\Organization $organization_sender
 * @property-read \App\Models\Organization\Organization $organization_sender_without_scope
 * @property-read \App\User|null $user_sender
 * @property-read \App\User|null $user_sender_without_scope
 * @method static \Illuminate\Database\Eloquent\Builder|Invite newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Invite newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Invite query()
 * @method static \Illuminate\Database\Eloquent\Builder|Invite retailer()
 * @method static \Illuminate\Database\Eloquent\Builder|Invite vendor()
 * @method static \Illuminate\Database\Eloquent\Builder|Invite whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Invite whereDesignation($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Invite whereEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Invite whereFname($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Invite whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Invite whereIsAccepted($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Invite whereIsDeclined($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Invite whereLname($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Invite whereOrganizationIdReceiver($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Invite whereOrganizationIdSender($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Invite wherePhone($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Invite whereToken($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Invite whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Invite whereUpdatedAt($value)
 */
	class Invite extends \Eloquent {}
}

namespace App\Models\Notification{
/**
 * App\Models\Notification\ErrorLog
 *
 * @property int $id
 * @property int $organization_id
 * @property string|null $description
 * @property string|null $type
 * @property string|null $link
 * @property string|null $link_text
 * @property string|null $status warning,success,error,info
 * @property int $is_solved 1=>solved,0=un-solve
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Organization\Organization|null $organization
 * @method static \Illuminate\Database\Eloquent\Builder|ErrorLog newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ErrorLog newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ErrorLog query()
 * @method static \Illuminate\Database\Eloquent\Builder|ErrorLog whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ErrorLog whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ErrorLog whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ErrorLog whereIsSolved($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ErrorLog whereLink($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ErrorLog whereLinkText($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ErrorLog whereOrganizationId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ErrorLog whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ErrorLog whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ErrorLog whereUpdatedAt($value)
 */
	class ErrorLog extends \Eloquent {}
}

namespace App\Models\Notification{
/**
 * App\Models\Notification\Notification
 *
 * @property string $id
 * @property int|null $organization_id
 * @property int|null $user_id
 * @property string $type
 * @property string $notifiable_type
 * @property int $notifiable_id
 * @property array $data
 * @property string|null $read_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|Notification newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Notification newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Notification query()
 * @method static \Illuminate\Database\Eloquent\Builder|Notification whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Notification whereData($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Notification whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Notification whereNotifiableId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Notification whereNotifiableType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Notification whereOrganizationId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Notification whereReadAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Notification whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Notification whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Notification whereUserId($value)
 */
	class Notification extends \Eloquent {}
}

namespace App\Models\Organization{
/**
 * App\Models\Organization\File
 *
 * @property int $id
 * @property string|null $name
 * @property int $organization_id
 * @property string|null $link
 * @property string|null $width
 * @property string|null $height
 * @property string|null $size
 * @property string|null $ext
 * @property string|null $type
 * @property int $should_sync
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Product\Product> $products
 * @property-read int|null $products_count
 * @method static \Illuminate\Database\Eloquent\Builder|File newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|File newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|File query()
 * @method static \Illuminate\Database\Eloquent\Builder|File whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|File whereExt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|File whereHeight($value)
 * @method static \Illuminate\Database\Eloquent\Builder|File whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|File whereLink($value)
 * @method static \Illuminate\Database\Eloquent\Builder|File whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|File whereOrganizationId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|File whereShouldSync($value)
 * @method static \Illuminate\Database\Eloquent\Builder|File whereSize($value)
 * @method static \Illuminate\Database\Eloquent\Builder|File whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|File whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|File whereWidth($value)
 */
	class File extends \Eloquent {}
}

namespace App\Models\Organization{
/**
 * App\Models\Organization\Organization
 *
 * @property int $id
 * @property string $name
 * @property string|null $region
 * @property string|null $units
 * @property string|null $currency
 * @property int $block_status 0 => Unblock, 1 => block
 * @property string|null $stripe_id
 * @property string|null $card_brand
 * @property string|null $card_last_four
 * @property \Illuminate\Support\Carbon|null $trial_ends_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Laravel\Cashier\Subscription> $subscriptions
 * @property-read int|null $subscriptions_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\User> $users
 * @property-read int|null $users_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\User> $users_without_scope
 * @property-read int|null $users_without_scope_count
 * @method static \Illuminate\Database\Eloquent\Builder|Organization newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Organization newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Organization query()
 * @method static \Illuminate\Database\Eloquent\Builder|Organization whereBlockStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Organization whereCardBrand($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Organization whereCardLastFour($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Organization whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Organization whereCurrency($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Organization whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Organization whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Organization whereRegion($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Organization whereStripeId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Organization whereTrialEndsAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Organization whereUnits($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Organization whereUpdatedAt($value)
 */
	class Organization extends \Eloquent {}
}

namespace App\Models\Organization{
/**
 * App\Models\Organization\OrganizationUser
 *
 * @property int $id
 * @property int $user_id
 * @property int $organization_id
 * @property int $bill_by
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|OrganizationUser newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|OrganizationUser newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|OrganizationUser query()
 * @method static \Illuminate\Database\Eloquent\Builder|OrganizationUser whereBillBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrganizationUser whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrganizationUser whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrganizationUser whereOrganizationId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrganizationUser whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrganizationUser whereUserId($value)
 */
	class OrganizationUser extends \Eloquent {}
}

namespace App\Models\Organization{
/**
 * App\Models\Organization\OrganizationUserPermission
 *
 * @property int $id
 * @property int|null $organization_user_id
 * @property int|null $team_invite_id
 * @property int $permission_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|OrganizationUserPermission newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|OrganizationUserPermission newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|OrganizationUserPermission query()
 * @method static \Illuminate\Database\Eloquent\Builder|OrganizationUserPermission whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrganizationUserPermission whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrganizationUserPermission whereOrganizationUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrganizationUserPermission wherePermissionId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrganizationUserPermission whereTeamInviteId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrganizationUserPermission whereUpdatedAt($value)
 */
	class OrganizationUserPermission extends \Eloquent {}
}

namespace App\Models\Organization{
/**
 * App\Models\Organization\Permission
 *
 * @property int $id
 * @property string $name
 * @property string $handle
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|Permission newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Permission newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Permission query()
 * @method static \Illuminate\Database\Eloquent\Builder|Permission whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Permission whereHandle($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Permission whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Permission whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Permission whereUpdatedAt($value)
 */
	class Permission extends \Eloquent {}
}

namespace App\Models\Organization{
/**
 * App\Models\Organization\Plan
 *
 * @property int $id
 * @property string $stripe_monthly_id
 * @property string $stripe_yearly_id
 * @property string $name
 * @property string $handle
 * @property string $icon_link
 * @property string $price_per_month
 * @property string $price_per_year
 * @property int $next_price
 * @property int $no_of_products
 * @property int $storage in GB's
 * @property int $no_of_catalogue 0 for unlimited
 * @property int $no_of_languages 0 for unlimited
 * @property int $no_of_currencies 0 for unlimited
 * @property int $no_of_retailers 0 for unlimited
 * @property int $no_of_vendors 0 for unlimited
 * @property int $no_of_team_members 0 for unlimited
 * @property int $no_of_channels 0 for unlimited
 * @property string $no_of_templates
 * @property int $is_brand_portal 0 for unlimited
 * @property int $is_vaf if variants, attributes, families are available in this package
 * @property int $is_oks if online knowledge support are available in this package
 * @property int $is_trial_available if trial available in this package
 * @property int $trial_period
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|Plan newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Plan newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Plan query()
 * @method static \Illuminate\Database\Eloquent\Builder|Plan whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Plan whereHandle($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Plan whereIconLink($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Plan whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Plan whereIsBrandPortal($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Plan whereIsOks($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Plan whereIsTrialAvailable($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Plan whereIsVaf($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Plan whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Plan whereNextPrice($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Plan whereNoOfCatalogue($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Plan whereNoOfChannels($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Plan whereNoOfCurrencies($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Plan whereNoOfLanguages($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Plan whereNoOfProducts($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Plan whereNoOfRetailers($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Plan whereNoOfTeamMembers($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Plan whereNoOfTemplates($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Plan whereNoOfVendors($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Plan wherePricePerMonth($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Plan wherePricePerYear($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Plan whereStorage($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Plan whereStripeMonthlyId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Plan whereStripeYearlyId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Plan whereTrialPeriod($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Plan whereUpdatedAt($value)
 */
	class Plan extends \Eloquent {}
}

namespace App\Models\Organization{
/**
 * App\Models\Organization\TeamInvite
 *
 * @property int $id
 * @property string $first_name
 * @property string $last_name
 * @property string $email
 * @property int $organization_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Organization\Permission> $permissions
 * @property-read int|null $permissions_count
 * @method static \Illuminate\Database\Eloquent\Builder|TeamInvite newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|TeamInvite newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|TeamInvite query()
 * @method static \Illuminate\Database\Eloquent\Builder|TeamInvite whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TeamInvite whereEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TeamInvite whereFirstName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TeamInvite whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TeamInvite whereLastName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TeamInvite whereOrganizationId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TeamInvite whereUpdatedAt($value)
 */
	class TeamInvite extends \Eloquent {}
}

namespace App\Models\Product{
/**
 * App\Models\Product\Attribute
 *
 * @property int $id
 * @property int $organization_id
 * @property int $attribute_type_id
 * @property string $name
 * @property string|null $description
 * @property string|null $handle
 * @property string|null $rules validation and units array
 * @property int $is_default
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Product\AttributeOption> $attribute_options
 * @property-read int|null $attribute_options_count
 * @property-read \App\Models\Product\AttributeType $attribute_type
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Product\Family> $families
 * @property-read int|null $families_count
 * @method static \Illuminate\Database\Eloquent\Builder|Attribute newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Attribute newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Attribute query()
 * @method static \Illuminate\Database\Eloquent\Builder|Attribute whereAttributeTypeId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Attribute whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Attribute whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Attribute whereHandle($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Attribute whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Attribute whereIsDefault($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Attribute whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Attribute whereOrganizationId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Attribute whereRules($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Attribute whereUpdatedAt($value)
 */
	class Attribute extends \Eloquent {}
}

namespace App\Models\Product{
/**
 * App\Models\Product\AttributeFamily
 *
 * @property int $id
 * @property int $family_id
 * @property int $attribute_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Product\Attribute $attributes
 * @property-read \App\Models\Product\Family $families
 * @method static \Illuminate\Database\Eloquent\Builder|AttributeFamily newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|AttributeFamily newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|AttributeFamily query()
 * @method static \Illuminate\Database\Eloquent\Builder|AttributeFamily whereAttributeId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AttributeFamily whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AttributeFamily whereFamilyId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AttributeFamily whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AttributeFamily whereUpdatedAt($value)
 */
	class AttributeFamily extends \Eloquent {}
}

namespace App\Models\Product{
/**
 * App\Models\Product\AttributeFamilyProductVersion
 *
 * @property int $id
 * @property int|null $attribute_id
 * @property int|null $family_id
 * @property int|null $version_id
 * @property int|null $product_id
 * @property string|null $value
 * @property int $attribute_family_id
 * @property int $product_version_id
 * @property string|null $unit unit will come only when user selects measurement attribute
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Product\Attribute|null $attribute
 * @property-read \App\Models\Product\AttributeFamily|null $attributeFamilies
 * @property-read \App\Models\Product\FamilyProductVersion|null $familyProductVersions
 * @method static \Illuminate\Database\Eloquent\Builder|AttributeFamilyProductVersion newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|AttributeFamilyProductVersion newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|AttributeFamilyProductVersion query()
 * @method static \Illuminate\Database\Eloquent\Builder|AttributeFamilyProductVersion whereAttributeFamilyId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AttributeFamilyProductVersion whereAttributeId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AttributeFamilyProductVersion whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AttributeFamilyProductVersion whereFamilyId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AttributeFamilyProductVersion whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AttributeFamilyProductVersion whereProductId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AttributeFamilyProductVersion whereProductVersionId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AttributeFamilyProductVersion whereUnit($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AttributeFamilyProductVersion whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AttributeFamilyProductVersion whereValue($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AttributeFamilyProductVersion whereVersionId($value)
 */
	class AttributeFamilyProductVersion extends \Eloquent {}
}

namespace App\Models\Product{
/**
 * App\Models\Product\AttributeOption
 *
 * @property int $id
 * @property int $attribute_id
 * @property string $name
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|AttributeOption newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|AttributeOption newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|AttributeOption query()
 * @method static \Illuminate\Database\Eloquent\Builder|AttributeOption whereAttributeId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AttributeOption whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AttributeOption whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AttributeOption whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AttributeOption whereUpdatedAt($value)
 */
	class AttributeOption extends \Eloquent {}
}

namespace App\Models\Product{
/**
 * App\Models\Product\AttributeType
 *
 * @property int $id
 * @property string $name
 * @property string|null $icon_path
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|AttributeType newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|AttributeType newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|AttributeType query()
 * @method static \Illuminate\Database\Eloquent\Builder|AttributeType whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AttributeType whereIconPath($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AttributeType whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AttributeType whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AttributeType whereUpdatedAt($value)
 */
	class AttributeType extends \Eloquent {}
}

namespace App\Models\Product{
/**
 * App\Models\Product\Brand
 *
 * @property int $id
 * @property string $name
 * @property int $is_default 0 => false, 1 => true
 * @property int $organization_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Product\Product> $products
 * @property-read int|null $products_count
 * @method static \Illuminate\Database\Eloquent\Builder|Brand newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Brand newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Brand query()
 * @method static \Illuminate\Database\Eloquent\Builder|Brand whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Brand whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Brand whereIsDefault($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Brand whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Brand whereOrganizationId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Brand whereUpdatedAt($value)
 */
	class Brand extends \Eloquent {}
}

namespace App\Models\Product{
/**
 * App\Models\Product\BrandProduct
 *
 * @property int $id
 * @property int $brand_id
 * @property int $product_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|BrandProduct newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|BrandProduct newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|BrandProduct query()
 * @method static \Illuminate\Database\Eloquent\Builder|BrandProduct whereBrandId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BrandProduct whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BrandProduct whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BrandProduct whereProductId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BrandProduct whereUpdatedAt($value)
 */
	class BrandProduct extends \Eloquent {}
}

namespace App\Models\Product{
/**
 * App\Models\Product\Category
 *
 * @property int $id
 * @property string $name
 * @property string|null $description
 * @property int $is_default 0 => false, 1 => true
 * @property int $status 0 => false, 1 => true
 * @property int $organization_id
 * @property int|null $category_id
 * @property string|null $response json responses
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Product\Product> $products
 * @property-read int|null $products_count
 * @method static \Illuminate\Database\Eloquent\Builder|Category newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Category newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Category query()
 * @method static \Illuminate\Database\Eloquent\Builder|Category whereCategoryId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Category whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Category whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Category whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Category whereIsDefault($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Category whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Category whereOrganizationId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Category whereResponse($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Category whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Category whereUpdatedAt($value)
 */
	class Category extends \Eloquent {}
}

namespace App\Models\Product{
/**
 * App\Models\Product\CategoryProduct
 *
 * @property int $id
 * @property int $category_id
 * @property int $product_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|CategoryProduct newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|CategoryProduct newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|CategoryProduct query()
 * @method static \Illuminate\Database\Eloquent\Builder|CategoryProduct whereCategoryId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CategoryProduct whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CategoryProduct whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CategoryProduct whereProductId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CategoryProduct whereUpdatedAt($value)
 */
	class CategoryProduct extends \Eloquent {}
}

namespace App\Models\Product{
/**
 * App\Models\Product\Family
 *
 * @property int $id
 * @property string $name
 * @property int $is_default 0=>not default , 1=> default family
 * @property int $organization_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Product\Attribute> $attributes
 * @property-read int|null $attributes_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Product\AttributeFamilyProductVersion> $values
 * @property-read int|null $values_count
 * @method static \Illuminate\Database\Eloquent\Builder|Family newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Family newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Family notDefault()
 * @method static \Illuminate\Database\Eloquent\Builder|Family query()
 * @method static \Illuminate\Database\Eloquent\Builder|Family whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Family whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Family whereIsDefault($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Family whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Family whereOrganizationId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Family whereUpdatedAt($value)
 */
	class Family extends \Eloquent {}
}

namespace App\Models\Product{
/**
 * App\Models\Product\FamilyProductVersion
 *
 * @method static \Illuminate\Database\Eloquent\Builder|FamilyProductVersion newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|FamilyProductVersion newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|FamilyProductVersion query()
 */
	class FamilyProductVersion extends \Eloquent {}
}

namespace App\Models\Product{
/**
 * App\Models\Product\FileProduct
 *
 * @property int $id
 * @property int $file_id
 * @property int $product_id
 * @property string $uploaded_for
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|FileProduct newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|FileProduct newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|FileProduct query()
 * @method static \Illuminate\Database\Eloquent\Builder|FileProduct whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FileProduct whereFileId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FileProduct whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FileProduct whereProductId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FileProduct whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FileProduct whereUploadedFor($value)
 */
	class FileProduct extends \Eloquent {}
}

namespace App\Models\Product{
/**
 * App\Models\Product\Product
 *
 * @property int $id
 * @property int $organization_id
 * @property string $sku
 * @property int $status
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Product\ProductVersion> $Product_version
 * @property-read int|null $product_version_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Product\Brand> $brands
 * @property-read int|null $brands_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Product\Category> $categories
 * @property-read int|null $categories_count
 * @property-read \App\Models\Channel\ChannelProduct|null $channel
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Channel\Channel> $channels
 * @property-read int|null $channels_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Product\FamilyProductVersion> $familyProductVersion
 * @property-read int|null $family_product_version_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Apimio\Gallery\Models\File> $files
 * @property-read int|null $files_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Invite\Invite> $inviteVendor
 * @property-read int|null $invite_vendor_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Invite\Invite> $invites
 * @property-read int|null $invites_count
 * @property-read \App\Models\Organization\Organization $organization
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Channel\ChannelProductStatus> $product_status
 * @property-read int|null $product_status_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Product\AttributeFamilyProductVersion> $values
 * @property-read int|null $values_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Product\Variant> $variants
 * @property-read int|null $variants_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Product\Vendor> $vendors
 * @property-read int|null $vendors_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Product\Version> $versions
 * @property-read int|null $versions_count
 * @method static \Illuminate\Database\Eloquent\Builder|Product allOrganizations()
 * @method static \Illuminate\Database\Eloquent\Builder|Product newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Product newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Product query()
 * @method static \Illuminate\Database\Eloquent\Builder|Product synced($channel_id)
 * @method static \Illuminate\Database\Eloquent\Builder|Product updateAvailable($channel_id)
 * @method static \Illuminate\Database\Eloquent\Builder|Product whereChannelFirst($channel_id)
 * @method static \Illuminate\Database\Eloquent\Builder|Product whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Product whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Product whereOrganizationId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Product whereSku($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Product whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Product whereUpdatedAt($value)
 */
	class Product extends \Eloquent {}
}

namespace App\Models\Product{
/**
 * App\Models\Product\ProductShopifyMapping
 *
 * @property int $id
 * @property int|null $attribute_family_id
 * @property int|null $organization_id
 * @property string|null $shopify_fields
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|ProductShopifyMapping newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ProductShopifyMapping newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ProductShopifyMapping query()
 * @method static \Illuminate\Database\Eloquent\Builder|ProductShopifyMapping whereAttributeFamilyId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductShopifyMapping whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductShopifyMapping whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductShopifyMapping whereOrganizationId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductShopifyMapping whereShopifyFields($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductShopifyMapping whereUpdatedAt($value)
 */
	class ProductShopifyMapping extends \Eloquent {}
}

namespace App\Models\Product{
/**
 * App\Models\Product\ProductVendor
 *
 * @property int $id
 * @property int $vendor_id
 * @property int $product_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|ProductVendor newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ProductVendor newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ProductVendor query()
 * @method static \Illuminate\Database\Eloquent\Builder|ProductVendor whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductVendor whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductVendor whereProductId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductVendor whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductVendor whereVendorId($value)
 */
	class ProductVendor extends \Eloquent {}
}

namespace App\Models\Product{
/**
 * App\Models\Product\ProductVersion
 *
 * @property int $id
 * @property int $version_id
 * @property int $product_id
 * @property int $score
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|ProductVersion newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ProductVersion newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ProductVersion query()
 * @method static \Illuminate\Database\Eloquent\Builder|ProductVersion whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductVersion whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductVersion whereProductId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductVersion whereScore($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductVersion whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductVersion whereVersionId($value)
 */
	class ProductVersion extends \Eloquent {}
}

namespace App\Models\Product{
/**
 * App\Models\Product\Template
 *
 * @property int $id
 * @property int $organization_id
 * @property int $version_id
 * @property int $channel_id
 * @property string $name
 * @property string $payload
 * @property string $type
 * @property string|null $export_type
 * @property int $product_status
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Channel\Channel|null $channels
 * @property-read \App\Models\Organization\Organization|null $organizations
 * @property-read \App\Models\Product\Version|null $versions
 * @method static \Illuminate\Database\Eloquent\Builder|Template newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Template newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Template query()
 * @method static \Illuminate\Database\Eloquent\Builder|Template whereChannelId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Template whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Template whereExportType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Template whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Template whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Template whereOrganizationId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Template wherePayload($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Template whereProductStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Template whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Template whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Template whereVersionId($value)
 */
	class Template extends \Eloquent {}
}

namespace App\Models\Product{
/**
 * App\Models\Product\Variant
 *
 * @property int $id
 * @property int $product_id
 * @property int|null $file_id
 * @property string $option
 * @property string|null $name
 * @property string|null $sku
 * @property string|null $price
 * @property string|null $cost_price
 * @property string|null $quantity
 * @property string|null $barcode
 * @property string|null $weight
 * @property string|null $weight_unit
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string|null $response
 * @property-read \Apimio\Gallery\Models\File|null $file
 * @method static \Illuminate\Database\Eloquent\Builder|Variant newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Variant newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Variant query()
 * @method static \Illuminate\Database\Eloquent\Builder|Variant whereBarcode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Variant whereCostPrice($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Variant whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Variant whereFileId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Variant whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Variant whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Variant whereOption($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Variant wherePrice($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Variant whereProductId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Variant whereQuantity($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Variant whereResponse($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Variant whereSku($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Variant whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Variant whereWeight($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Variant whereWeightUnit($value)
 */
	class Variant extends \Eloquent {}
}

namespace App\Models\Product{
/**
 * App\Models\Product\Vendor
 *
 * @property int $id
 * @property string|null $name
 * @property int $organization_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Product\Product> $products
 * @property-read int|null $products_count
 * @method static \Illuminate\Database\Eloquent\Builder|Vendor newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Vendor newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Vendor query()
 * @method static \Illuminate\Database\Eloquent\Builder|Vendor whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Vendor whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Vendor whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Vendor whereOrganizationId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Vendor whereUpdatedAt($value)
 */
	class Vendor extends \Eloquent {}
}

namespace App\Models\Product{
/**
 * App\Models\Product\Version
 *
 * @property int $id
 * @property string $name
 * @property int $is_default
 * @property int $organization_id
 * @property string $currency
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Channel\ChannelVersion|null $channel
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Product\AttributeFamilyProductVersion> $families
 * @property-read int|null $families_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Product\Product> $products
 * @property-read int|null $products_count
 * @method static \Illuminate\Database\Eloquent\Builder|Version newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Version newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Version query()
 * @method static \Illuminate\Database\Eloquent\Builder|Version whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Version whereCurrency($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Version whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Version whereIsDefault($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Version whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Version whereOrganizationId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Version whereUpdatedAt($value)
 */
	class Version extends \Eloquent {}
}

namespace App{
/**
 * App\User
 *
 * @property int $id
 * @property string|null $google_id
 * @property string|null $shopify_shop_id
 * @property string|null $freshworks_id
 * @property string $fname
 * @property string|null $lname
 * @property string $email
 * @property string|null $ip
 * @property \Illuminate\Support\Carbon|null $email_verified_at
 * @property string|null $password
 * @property string|null $phone
 * @property string|null $last_login
 * @property string|null $remember_token
 * @property int $block_status
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read mixed $organization_id
 * @property-read \App\Models\Invite\Invite|null $invites
 * @property-read \Illuminate\Notifications\DatabaseNotificationCollection<int, \Illuminate\Notifications\DatabaseNotification> $notifications
 * @property-read int|null $notifications_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Organization\Organization> $organizations
 * @property-read int|null $organizations_count
 * @method static \Illuminate\Database\Eloquent\Builder|User newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|User newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|User query()
 * @method static \Illuminate\Database\Eloquent\Builder|User whereBlockStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereEmailVerifiedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereFname($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereFreshworksId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereGoogleId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereIp($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereLastLogin($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereLname($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User wherePassword($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User wherePhone($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereRememberToken($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereShopifyShopId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereUpdatedAt($value)
 */
	class User extends \Eloquent implements \Illuminate\Contracts\Auth\MustVerifyEmail {}
}

