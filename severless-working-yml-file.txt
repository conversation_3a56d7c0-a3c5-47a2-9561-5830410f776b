service: laravel3

provider:
    name: aws
    # The AWS region in which to deploy (us-east-1 is the default)
    region: us-east-2
    iam:
      role:
        name: laravel-us-east-2-lambdaRole-new8
    # Environment variables
    environment:
        APP_URL: http://localhost
        APP_NAME: Laravel
        APP_ENV: lambda # Or use ${sls:stage} if you want the environment to match the stage
        APP_VERSION: 10.0
        DB_CONNECTION: mysql
        DB_HOST: apimio.clsqeeg2qimy.us-east-2.rds.amazonaws.com
        DB_PORT: 3306
        DB_DATABASE: vapor
        DB_USERNAME: vapor
        DB_PASSWORD: dda5mUvyVk0WPHmSthSNAFSZQlaxLGYZUWj3f8dj
        BROADCAST_DRIVER: pusher
        CACHE_DRIVER: database
        QUEUE_CONNECTION: sqs
        SESSION_DRIVER: database
        SESSION_LIFETIME: 120
        MAIL_DRIVER: smtp
        MAIL_HOST: smtp.sendgrid.net
        MAIL_PORT: 587
        MAIL_USERNAME: apikey
        MAIL_PASSWORD: *********************************************************************
        MAIL_ENCRYPTION: tls
        MAIL_FROM_ADDRESS: <EMAIL>
        MAIL_FROM_NAME: "${APP_NAME}"
        AWS_BUCKET: apimio-staging
        AWS_MAIN_URL: https://apimio-staging.s3.amazonaws.com/
        SQS_PREFIX: https://sqs.us-east-2.amazonaws.com/574295680953/
        PUSHER_APP_ID: *******
        PUSHER_APP_KEY: e55f61295ab31a355055
        PUSHER_APP_SECRET: 353aab4192d2b3edff6d
        PUSHER_APP_CLUSTER: us2
        MIX_PUSHER_APP_KEY: "${PUSHER_APP_KEY}"
        MIX_PUSHER_APP_CLUSTER: "${PUSHER_APP_CLUSTER}"
        LOG_SLACK_WEBHOOK_URL: *******************************************************************************
        LOG_SLACK_WEBHOOK_URL_STAGING: *******************************************************************************
        STRIPE_KEY: pk_test_aXkxuuqQz8k881VU3C1Jvehg
        STRIPE_SECRET: sk_test_bW7sPjB0LmgvOZkXaxFftHSm
        TEST_MONTHLY_ID: price_1NL1krJKtHOhwx4oRXBin9di
        TEST_YEARLY_ID: price_1NL1krJKtHOhwx4oRXBin9di
        STARTUP_MONTHLY_PRICE_ID: price_1MBcqGJKtHOhwx4od4nzmP0S
        STARTUP_YEARLY_PRICE_ID: price_1MBcqGJKtHOhwx4oPlvfHYNu
        PLUS_MONTHLY_PRICE_ID: price_1M6rZXJKtHOhwx4oXsFJwlYs
        PLUS_YEARLY_PRICE_ID: price_1M6rZXJKtHOhwx4ol6J3P0LO
        PRO_MONTHLY_PRICE_ID: price_1M6rbWJKtHOhwx4oq4Ddh4X2
        PRO_YEARLY_PRICE_ID: price_1M6rbWJKtHOhwx4oLa58CfkQ


package:
    # Files and directories to exclude from deployment
    patterns:
        - '!node_modules/**'
        - '!public/storage'
        - '!resources/assets/**'
        - '!storage/**'
        - '!tests/**'
        - '!vendor/bin/**'
#        - '!vendor/aws/**'

functions:

    # This function runs the Laravel website/API
#    web:
#        handler: public/index.php
#        runtime: php-81-fpm
#        timeout: 28 # in seconds (API Gateway has a timeout of 29 seconds)
#        events:
#            - httpApi: '*'

    # This function lets us run artisan commands in Lambda
    artisan:
        handler: artisan
        runtime: php-82-console
        timeout: 720 # in seconds
#         Uncomment to also run the scheduler every minute
        #events:
        #    - schedule:
        #          rate: rate(1 minute)
        #          input: '"schedule:run"'
    # This function lets us run queue workers in Lambda
    queueWorker:
      handler: public/index.php
      runtime: php-82-console
      timeout: 720 # in seconds
      environment:
        SQS_QUEUE: "UserQueue-*"
        LOG_CHANNEL: "stderr"
#      events:
#        - sqs:
#            arn:
#              Fn::GetAtt: [ MyQueue, Arn ]
#            batchSize: 1

resources:
  Resources:
    SetLambdaPermissions:
      Type: "AWS::Lambda::Function"
      Properties:
        FunctionName: !Ref QueueWorkerLambdaFunction
        Handler: index.handler
        Runtime: provided.al2
        Environment:
          Variables:
            SCRIPT: |
              #!/bin/bash
              chmod -R 775 /var/task/bootstrap/cache
        Code:
          ZipFile: |
            #!/bin/bash
            echo "Setting permissions"
        Timeout: 300
        MemorySize: 128
        Role: arn:aws:iam::574295680953:role/laravel-us-east-2-lambdaRole-new8
#    MyQueue:
#      Type: "AWS::SQS::Queue"
#      Properties:
#        QueueName: UserQueue5
#        VisibilityTimeout: 800

plugins:
    # We need to include the Bref plugin
    - ./vendor/bref/bref
