<?php
// Set up MySQL connection details
$host = 'apimio1.clsqeeg2qimy.us-east-2.rds.amazonaws.com';
$username = 'admin';
$password = 'Apimiodevs321!';
$dbname = 'apimio_test';
$databaseHost = getenv('DB_HOST');


// Create connection
$conn = new mysqli($host, $username, $password, $dbname);

// Check connection
if ($conn->connect_error) {
    echo "Connection failed: " . $conn->connect_error . "\n";
} else {
    echo "Successfully connected to the database\n";
}

$conn->close();
