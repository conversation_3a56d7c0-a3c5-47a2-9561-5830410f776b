<?php

namespace Tests\Unit;

use App\Classes\Template\GenerateNode;
use PHPUnit\Framework\TestCase;

class TemplateGenerateNodeTest extends TestCase
{
    private $example_input_payload;

    public function __construct(?string $name = null, array $data = [], $dataName = '')
    {
        parent::__construct($name, $data, $dataName);

        $this->example_input_payload = [
            [
                "from" => [
                    "sku"
                ],
                "with_formula" => "assign",
                "to" => [
                    "sku"
                ]
            ],
            [
                "from" => [
                    "Product Name"
                ],
                "with_formula" => "assign",
                "to" => [
                    "product_name"
                ]
            ],
            [
                "from" => [
                    "quality"
                ],
                "with_formula" => "assign",
                "to" => [
                    "quality",
                ]
            ],
            [
                "from" => [
                    "size"
                ],
                "with_formula" => "split",
                "with" => "-",
                "to" => [
                    "width",
                    "height"
                ]
            ],
            [
                "from" => [
                    "price"
                ],
                "with_formula" => "replace",
                "replace" => "$",
                "with" => "",
                "to" => [
                    "cost_price"
                ]
            ],
            [
                "from" => [
                    "category",
                    "subcategory"
                ],
                "with_formula" => "merge",
                "with" => "-",
                "to" => [
                    "category"
                ]
            ],
            [
                "from" => [
                    "product Title"
                ],
                "with_formula" => "slug",
                "to" => [
                    "seo_title"
                ]
            ]
        ];
    }

    public function get_example_input_payload() : string {
        $generate_node = new GenerateNode();

        return $generate_node->generateObjectNode($this->example_input_payload);
    }

    /**
     * A basic unit test for generate node.
     *
     * @return void
     */
    public function testExample()
    {
        $generate_node = new GenerateNode();

        $this->assertEquals($generate_node->generateObjectNode($this->example_input_payload), json_encode($this->example_input_payload));
    }
}
