<?php

use Illuminate\Http\Request;
use App\Models\Product\Brand;
use App\Classes\Plan\PlanClass;
use App\Models\Channel\Channel;
use App\Models\Product\Product;
use Apimio\Gallery\Models\Folder;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Auth;
use App\Models\Notification\ErrorLog;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Redirect;
use App\Models\Notification\Notification;
use App\Models\Organization\Organization;
use App\Classes\Billing\SubscriptionClass;
use App\Http\Controllers\Api\BrandsPortal\BrandsPortalController;
use Apimio\MappingConnectorPackage\Http\Controllers\MappingFieldController;
use App\Http\Controllers\Product\LocationController;
use App\Http\Controllers\Auth\RegisterController;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\Organization\OrganizationController;
use App\Classes\Shopify\MetaFieldDefinition;
use App\Jobs\Shopify\FetchMetaFields;
use App\Jobs\Shopify\FetchCollections;
use App\Models\Channel\ShopifyChannel;
use App\Jobs\Shopify\SaveFetchMatefields;
use App\Notifications\ApimioNotification;
use App\Http\Controllers\SettingController;
use App\Jobs\Shopify\FetchMetaFieldDefinitions;
use App\Classes\Shopify\CreateMetaFieldDefinition;
use App\Jobs\PreviousData\MultiStoreOrganizationMetafields;
use App\Jobs\PreviousData\SingleStoreOrganizationMetafields;
use App\Models\Product\Attribute;
use App\Classes\Shopify\Metafield;
use App\Classes\Export\ExportToCsv;
use App\Http\Controllers\Api\AuthController;
use Inertia\Inertia;

/*
|--------------------------------------------------------------------------
| Test Routes
|--------------------------------------------------------------------------
|
| Do not edit the below route it is used by devs and management for
| accessing test user accounts. It will only run on local and staging
| environments
|
*/

//add extra routes here
require_once __DIR__ . '/web_routes/auth_routes.php';
require_once __DIR__ . '/web_routes/mapping_testing_route.php';
require_once __DIR__ . '/web_routes/shopify_testing_routes.php';
require_once __DIR__ . '/web_routes/integrations.php';
require_once __DIR__ . '/web_routes/web_testing.php';

require_once __DIR__ . '/web_routes/import_export.php';
require_once __DIR__ . '/web_routes/bulk.php';
require_once __DIR__ . '/web_routes/deprecated_routes.php';

if (App::environment(["local", "staging"])) {
    Route::get('/test-users', 'Auth\LoginController@test_users')->name('test-users');
}

/*Route::get('previous-images',function(){
    \App\Jobs\PreviousData\ImagesData::dispatch();
});
Route::get('previous-single-attributes',function(){
    dispatch(new SingleStoreOrganizationMetafields());
});
Route::get('previous-multi-attributes',function(){
    MultiStoreOrganizationMetafields::dispatch();
});*/

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/
Auth::routes(['verify' => true]);

Route::middleware(['guest'])->group(function () {
    // TODO: Integrate CAPTCHA
    Route::get('/register', [AuthController::class, 'registerForm'])->name('register');
    Route::get('/login', [AuthController::class, 'loginForm'])->name('login');
});

Route::get('brandportal/create', [BrandsPortalController::class, 'create'])->name('brandportal.create');
Route::get('brandportal/show/{hash}', [BrandsPortalController::class, 'show'])->name('brandportal.show')->withoutMiddleware('auth:sanctum', 'verified');

Route::get('/', function () {
    if (App::environment('local')) {
        return redirect(route('register'));
    } else {
        return redirect('https://apimio.com/demo/');
    }
});

//Accept invite
Route::get('invite/{token}', [RegisterController::class, 'invite'])->name("invite");

//OnBoarding questions
// Route::get('on-boarding', [ HomeController::class , 'on_boarding'])->name("auth.on-boarding");

//Redirection to dashboard or organzation
Route::get('/home', [HomeController::class, 'index'])->name('home')->middleware(['auth', 'verified']);

Route::get('/event/call', 'HomeController@shopifyEventCall')->name('shopify.event.call');

//Select organizaiton
Route::get('organziation/checkpoint', [OrganizationController::class, 'checkpoint'])->name("organization.checkpoint");
Route::get('organization/active/{id}', [OrganizationController::class, 'set_active_organization'])->name("organization.active");

//Organization crud
Route::resource('organization', OrganizationController::class)->only([
    'index',
    'create',
    'store',
    'edit',
    'update'
]);


/*
            |--------------------------------------------------------------------------
            | shopify requests
            |--------------------------------------------------------------------------
            |
 */

Route::prefix('channel')->namespace('Channel')->group(function () {
    Route::get('shopify/sync', 'ShopifyController@syncProduct')->name('shopify.product.sync');
    Route::get('shopify/product-fetch-confirmation', 'ShopifyController@productFetchConfirmation')->name('shopify.product.fetch.confirmation');
    Route::get('shopify/product-fetch/{id}', 'ShopifyController@productFetch')->name('shopify.product.fetch');
    Route::get('shopify/install', 'ChannelController@install_shopify')->name('channel.shopify.install');
    Route::get('shopify/redirect', 'ChannelController@redirect_shopify')->name('channel.shopify.redirect');
    Route::get('shopify/bill/store', 'ChannelController@store_shopify_bill')->name('channel.shopify.bill.store');
    Route::get('shopify/bill/redirect', 'ChannelController@redirect_shopify_bill')->name('channel.shopify.bill.redirect');
    Route::get('shopify/bill/{shopify_channels_id}', 'ChannelController@shopify_bill')->name('channel.shopify.bill');
    Route::get('shopify/disconnect/{shopify_channel_id}', 'ShopifyController@disconnectShopify')->name('channel.shopify.disconnect');
    // Route::get('shopify/disconnect-webhook-register', 'ShopifyController@disconnectWebhookRegister');
    Route::get('shopify/fetch-all-products/{channel_id}', 'ChannelController@shopify_fetch_all_products')->name('channel.shopify.fetch_all_products');
    Route::resource('shopify', 'ShopifyController');

    Route::get('create-template/{id}', 'TemplateController@create')->name('shopify.template.create');
    Route::post('template-redirect', 'TemplateController@redirection')->name('shopify.template.redirect');
    Route::get('template-shopify-array', 'TemplateController@getShopifyArray')->name('shopify.mapping.get');
    Route::match(['get', 'post'], '/bulk-sync', 'TemplateController@bulkSync')->name('shopify.bulk.sync');
});

Route::middleware(['check_session'])->group(function () {
    /*
            |--------------------------------------------------------------------------
            |  // channel request
            |--------------------------------------------------------------------------
            |
            */
    Route::resource('channel', 'Channel\ChannelController')->only([
        'index',
        'create',
        'store',
        'edit',
        'update',
    ]);

    Route::post('channel/connect', 'Channel\ChannelController@register_webhook')->name('channel.connect.webhook');
    Route::get('channel/disconnect/{webhook_id}/{channel_id}', 'Channel\ChannelController@unInstallWebhooks')->name('channel.disconnect.webhook');


    /*
    |--------------------------------------------------------------------------
    | FOR NOTIFICATION
    |--------------------------------------------------------------------------
    |
    */

    Route::prefix('notification')->name('notifications.')->group(function () {
        Route::get('error-logs', 'NotificationController@getErrorLogs')->name('error.logs');
    });


    // ------------------------------------------- checking billing starts here -------------------------------------------
    Route::middleware('auth.stripe')->group(function () {

        //files
        Route::post('/upload', 'Organization\FileController@upload');
        Route::get('product/{product_id}/detach-file/{id}', [\Apimio\Gallery\Http\Controllers\FileController::class, 'detach'])->name('delete.file');

        /*
            |--------------------------------------------------------------------------
            | for team invitation routing organization
            |--------------------------------------------------------------------------
            |
            */
        Route::namespace('Organization')->prefix('')->name('organization.')->group(function () {
            Route::prefix('invite_team')->name('invite_team.')->group(function () {
                Route::get('index', 'InviteTeamController@index')->name('index');
                Route::get('create', 'InviteTeamController@create')->name('create');
                Route::get('edit/{id}', 'InviteTeamController@edit')->name('edit');
                Route::delete('delete/{id}', 'InviteTeamController@destroy')->name('delete');
                Route::post('store', 'InviteTeamController@store')->name('store');
            });
        });

        //get all the products from shopify
        Route::get('shopify/channel/', 'Channel\ChannelController@syncProductAlert')->name('shopify.fetch.products');

        //
        /*
            |--------------------------------------------------------------------------
            | location routes
            |--------------------------------------------------------------------------
            |
            */
        Route::prefix('products/locations')->namespace('Product')->group(function () {
            Route::resource('/', LocationController::class)->names([
                'index' => 'locations.index',
                'create' => 'locations.create',
                'store' => 'locations.store',
                'edit' => 'locations.edit',
                'update' => 'locations.update',
                'destroy' => 'locations.destroy',
            ]);
        });
    });

    /*
            |--------------------------------------------------------------------------
            | VariantController Routes
            |--------------------------------------------------------------------------
            |
            */

    Route::prefix('products')->namespace('Product')->group(function () {
        Route::get('variants/step-2', 'VariantController@stepTwo')->name('variants.step.two');
        Route::get('variants/step-3', 'VariantController@stepThree')->name('variants.step.three');

        // Route::get('variants/delete/{id}', 'VariantController@file_delete')->name('variants.image.delete');
        Route::get('variants/delete/all/{productId}/{version_id}', 'VariantController@all_variant_delete')->name('variants.all.delete');
        Route::get('variants/image/delete/{id}', 'VariantController@file_delete')->name('variants.image.delete');
        Route::get('variants/delete/{id}', 'VariantController@destroy')->name('variants.delete');
        //TODO:: Ask Ghazni: Remove this resource or not as we are not using this approch
        Route::resources([
            'brands' => 'BrandsController',
            'vendors' => 'VendorController',
            'categories' => 'CategoriesController',
            'attributes' => 'AttributeController',
            'versions' => 'VersionController',
            'family' => 'FamilyController',
            'variants' => 'VariantController',
            'tasks' => 'TaskController'
        ]);
        // Route::get('/family-attribute', 'ProductController@familyAttributes');
        Route::post('attribute-rules', 'AttributeController@attributeSaveRules')->name('update.attributes.rules');
        Route::get('unassign/family/{id}/{version_id}/{product_id}', 'FamilyController@unassign_family')->name('unassign.family');
        Route::get('{id}/variants/{version_id}/step-1', 'VariantController@index')->name('variants.step.one');
        Route::get('variant-attribute', 'AttributeController@variantAttributeindex')->name('variant_attribute.index');
        Route::get('variant-attribute/edit/{id}', 'AttributeController@variantAttributeEdit')->name('variant_attribute.edit');
        Route::get('shopify-attribute', 'AttributeController@syncShopifyAttribute')->name('attribute.shopify');
    });

    // for update settings table toggles/attributes
    Route::post('/update-settings-toggle', 'SettingController@updateSettingsToggle')->name('update.settings.toggle');

    Route::resource('products', 'Product\ProductController')->except('index');
    Route::get('products/{id}/edit/{version_id?}', 'Product\ProductController@edit')->name('products.edit');
    Route::delete('products/delete/{id}', 'Product\ProductController@destroyJson')->name('products.delete.json');
    Route::get('products/{id}/seo/{version_id?}', 'Product\ProductController@seo_edit')->name('seo.products.edit');
    Route::get('products/{id}/attribute-set/{version_id?}', 'Product\ProductController@attribute_set')->name('products.attribute_set');
    Route::get('products/{id}/media/{version_id?}', 'Product\ProductController@media')->name('products.media');
    Route::get('products/{id}/score/{version_id?}', 'Product\ProductController@score')->name('products.score');
    Route::get('products/{id}/catalog/{version_id?}', 'Product\ProductController@catalog')->name('products.catalog');
    Route::get('products/{product}/inventory/{version_id?}', 'Product\ProductController@inventory')->name('products.inventory');
    Route::post('products/{product}/inventory/{version_id?}', 'Product\ProductController@updateInventory')->name('products.updateInventory');
    Route::post('products/inventory/update', 'Product\ProductController@newUpdateInventory')->name('products.newUpdateInventory');
    Route::delete('products/bulk/delete', 'Product\ProductController@bulk_delete_all_products')->name('products.bulk.delete');
    Route::get('products/bulk/all', 'Product\ProductController@bulk_select_all_products')->name('products.bulk.select.all');

    Route::resource('retailer', 'Invite\RetailerController')->names([
        'index' => 'retailer.index',
        'create' => 'retailer.create',
        'show' => 'retailer.show',
        'edit' => 'retailer.edit',
        'update' => 'retailer.update',
    ]);
    Route::get('invite/{id}/accept', 'Invite\RetailerController@accept')->name('invite.accept');
    Route::get('invite/{id}/decline', 'Invite\RetailerController@decline')->name('invite.decline');
    Route::get('invite/{id}/disconnect', 'Invite\RetailerController@disconnect')->name('invite.disconnect');
    Route::get('invite/{id}/resend_email', 'Invite\RetailerController@resend_email')->name('invite.resend');

    Route::resource('vendors', 'Invite\VendorController')->names([
        'index' => 'vendor.index',
        'create' => 'vendor.create',
        'store' => 'vendor.store',
        'show' => 'vendor.show',
        'edit' => 'vendor.edit',
        'update' => 'vendor.update',
        'destroy' => 'vendor.destroy',
    ]);
    Route::get('vendor/{invite_id}/product/{product_id}/clone', 'Invite\VendorController@clone')->name('vendor.product.clone');
    Route::post('invite/AcceptVendor', 'Invite\VendorController@AcceptVendor')->name('invite.AcceptVendor');


    // Route::get('dashboard/{onBoarding?}', 'Pages\PageController@dashboard')->name('dashboard')->withoutMiddleware('check_billing'); //TODO:: Change this after demo
    Route::get('billing/plan/success', 'Pages\PageController@dashboardSuccess')->name('dashboard.success')->withoutMiddleware('check_billing'); //TODO:: Change this after demo

    Route::get('update/subscription/{plan_id}/{plan_type}', 'Billing\BillingController@update_plan')->name('update.subscription');
    Route::get('syncing/plans', 'Billing\BillingController@syncing_plans')->name('syncing.plans');


    /*
         * TODO: Test routes should only be created below this line. 👇 👇 👇 👇 👇
         * */

    Route::post('product/family/save', "Product\ProductFamilyController@save")->name('f.s');
    Route::get('product/delete', "Product\ProductFamilyController@delete")->name('f.delete');

    //ERROR PAGE ROUTE
    Route::get('error/error500', function () {
        return view('errors.error500');
    });
    Route::get('error/error404', function () {
        return view('errors.error404');
    });
    Route::get('error/noresults', function () {
        return view('errors.no-results-found');
    });

    Route::get('library/view', function () {
        return view('products.library.view');
    })->name("library.view");


    //NOTIFICATION
    Route::get('notification/send', 'HomeController@sendNotification')->name('notification');
    Route::get('pages/settings', 'HomeController@settings')->name('pages.settings');
    Route::post('pages/settings/update', 'HomeController@settings_update')->name('settings.update');

    Route::get('notification', function () {
        if (Gate::denies('SubscriptionAccess', 'notification')) {
            return redirect(route('billing'))->withErrors(['main' => "upgrade your plan to access all modules"]);
        }
        //first read all the unread message
        $unread_notification = Auth::user()->notifications()->where('organization_id', Auth::user()->organization_id)->whereNull('read_at')->get();
        $unread_notification->markAsRead();

        $logs = ErrorLog::query()->orderByDesc('id')->get();

        $notifications = Notification::orderBy('updated_at', 'DESC')->get();
        return view('notification.index', compact('notifications', 'logs'));
    })->name("notification.index");


    // notification read message route
    Route::get('notification/message/{id}', function ($id) {
        $notification = Auth::user()->notifications()->where('id', $id)->first();
        $notification->markAsRead();

        return Redirect::to($notification->data[0]['actionURL']);
    })->name("notification.message");

    //RETAILER-LISTING
    Route::get('retailers/information', function () {
        return view('invite.retailer.preview');
    })->name("retailer.information");

    Route::get('retailers/products', function () {
        return view('invite.retailer.catalog');
    })->name("retailer.products");
});

// ------------------------------------------- checking billing ends here -------------------------------------------


//billing routes
Route::withoutMiddleware('check_billing')->group(function () {
    Route::get('billing/portal', 'Billing\BillingController@billingPortal')->name('billing.portal');
    Route::get('billing/plans', 'Billing\BillingController@viewPlans')->name('billing.plans.view');
    Route::get('billing', 'Billing\BillingController@index')->name('billing');
    Route::get('update/billing', 'Billing\BillingController@update_billing')->name('update.billing');
    Route::get('billing/checkout', 'Billing\BillingController@checkout')->name('billing.checkout');
    Route::get('subscription/info', 'Billing\BillingController@viewSubscription')->name('subscription.info');
    Route::post('subscribe/post', 'Billing\BillingController@Subscription')->name('subscribe.post');
    Route::get('subscribe/post', 'Billing\BillingController@Subscription')->name('subscribe.post');
    Route::post('billing/checkout/add_card', 'Billing\BillingController@add_card_details')->name('billing.checkout.add_card');
    Route::get('payment/coupon/retrieve', 'Billing\BillingController@coupon_retrieve')->name('payment.coupon.retrieve');
});

Route::get('logs', [\Rap2hpoutre\LaravelLogViewer\LogViewerController::class, 'index'])->middleware("admin");
// ------------------------------------ check_session ends here -------------------------------------------


Route::put('custom/fields/{key}/{value}', 'HomeController@saveCustomFields')->name('custom.fields');

//webhooks
Route::prefix('webhook/shopify/')->namespace('Webhooks')->group(function () {
    Route::post('create-product', 'ShopifyWebhookController@create_product')->name('shopify.create.product.webhook');
    Route::post('update-product', 'ShopifyWebhookController@updateProduct')->name('shopify.update.product.webhook');
    Route::post('delete-product', 'ShopifyWebhookController@delete_product')->name('shopify.delete.product.webhook');
    Route::post('uninstall-app', 'ShopifyWebhookController@uninstall_app')->name('shopify.uninstall.app.webhook');
    Route::post('create-collection', 'ShopifyWebhookController@createCollection')->name('shopify.create.collection.webhook');
    Route::post('update-collection', 'ShopifyWebhookController@updateCollection')->name('shopify.update.collection.webhook');
    Route::post('delete-collection', 'ShopifyWebhookController@deleteCollection')->name('shopify.delete.collection.webhook');

    Route::get('get-single-product', 'ShopifyWebhookController@getProduct');
    Route::prefix('location/')->group(function () {
        Route::post('create', 'ShopifyLocationController@createLocation')->name('shopify.create.location.webhook');
        Route::post('update', 'ShopifyLocationController@updateLocation')->name('shopify.update.location.webhook');
        Route::post('delete', 'ShopifyLocationController@deleteLocation')->name('shopify.delete.location.webhook');
    });
    Route::prefix('inventory/')->group(function () {
        Route::post('create', 'ShopifyInventoryController@createInventory')->name('shopify.create.inventory.webhook');
        Route::post('update', 'ShopifyInventoryController@updateInventory')->name('shopify.update.inventory.webhook');
        Route::post('delete', 'ShopifyInventoryController@deleteInventory')->name('shopify.delete.inventory.webhook');
        Route::post('levels', 'ShopifyInventoryController@createInventoryLevel')->name('shopify.delete.inventory.levels.webhook');
    });
});

// -------------------------- Auth end here ----------------------------


//test metafields
Route::get('/mappingreact', function () {

    $mapping = new MappingFieldController();

    return $mapping->mappingReact();
});

Route::get('extend-trial', function () {
    $users = \App\User::all(); //fetching all users.
    foreach ($users as $user) { //looping through all the users.
        $invite = new \App\Models\Invite\Invite();
        if (!$invite->IsInvited($user->email)) {
            $org_ids = \App\Models\Organization\OrganizationUser::where('user_id', $user->id)->pluck('organization_id')->toArray(); //getting organization_ids of all users.
            foreach ($org_ids as $org_id) {
                $org = \App\Models\Organization\Organization::where('id', $org_id)->whereNull('stripe_id')->whereNull('trial_ends_at')->first();
                if ($org) { //getting organization object
                    $org->createAsStripeCustomer(); //making user stripe customer
                    $org->trial_ends_at = \Carbon\Carbon::now()->addMonths(3); //extending trial of existing users upto 3 months
                    $org->save();
                }
            }
        }
    }
});


Route::get("read_image", function () {
    // Or open as a string
    $img = 'https://cdn.shopify.com/s/files/1/2185/2813/products/W4405R_04061_b1_s1_a1_1_m18.jpg?v=1640298484';

    $size_info1 = getimagesize($img) ?? '';


    $data = get_headers($img, true);
    $size = isset($data['Content - Length']) ? (int)$data['Content - Length'] : 0;
    $sizeInKb = round($size / 1024, 2);

    dd("sdsdsd", $size_info1, $size, $sizeInKb, $data);
});


//adding default folders for existing users
Route::get('default/folders', function () {
    $orgs = \App\Models\Organization\Organization::withoutGlobalScope('user_id')->select('id', 'name')->get();
    foreach ($orgs as $org) {
        $folder = new \Apimio\Gallery\Models\Folder();
        $folder->name = $org->name;
        $folder->organization_id = $org->id;
        $folder->is_default = 1;
        $folder->save();

        $enc_id = encrypt($folder->id);
        $link = "gallery/" . $enc_id;
        $folder = Folder::withoutGlobalScope('organization_id')->where('id', $folder->id)->first();
        $folder->link = $link;
        $folder->updated_at = now();
        $folder->save();
    }
});

Route::get('update/table', function () {
    if (App::environment('local')) {
        \App\Jobs\MigrationsJob::dispatch();
    } else {
        abort(404);
    }
});


Route::controller(\App\Http\Controllers\Organization\ProgressBarController::class)
    ->prefix('/progress-bar')
    ->name('progressbar.')
    ->group(function () {
        Route::get('/{batch_id}', 'index')->name('index');
    });
Route::get('/listen-event1', [\App\Http\Controllers\Organization\ProgressBarController::class, 'listenEvent']);

// remove this route once testing is done TODO
Route::get('/testing-vite', function () {
    return view("testing_vite");
});

Route::get('/subscription/plans', function () {
    return view('billing.buttons');
})->name('billing.buttons');

Route::get('/subscription-checkout', function (Request $request) {
    // $organizations = Organization::whereHas("subscriptions",function($query){
    //     $query->whereHas("items",function($query){
    //         $query->whereIn("stripe_product",PlanClass::$items->pluck("stripe_product_id"));
    //     });
    // })->get();
    // dd($organizations);
    $plan = PlanClass::$plans->where("handle", "community_plan")->first();
    $organization = Organization::where("id", Auth::user()->organization_id)->first();

    return (new SubscriptionClass)->set_user(Auth::user())
        ->set_organization($organization->id)
        ->setMonthlyPlan($plan->handle)
        ->subscription(
            function ($error) {
                return redirect(route('dashboard'))->withErrors(["main" => $error]);
            },
            function ($success) {
                if ($success) {
                    if (isset($success["url"])) {
                        return $success["url"];
                    }
                    return redirect(route('dashboard'))->withSuccess($success["msg"]);
                }
            }
        );
})->name("subscription.checkout");

Route::get('/subscription-schedular', function (Request $request) {
    try {
        Stripe::setApiKey(env('STRIPE_SECRET'));

        // Retrieve organizations with relevant subscriptions
        $organizations = Organization::with("subscriptions.items")->whereHas("subscriptions", function ($query) {
            $query->whereHas("items", function ($query1) {
                $query1->whereIn("stripe_product", PlanClass::$items->pluck("stripe_product_id"));
            });
        })->get();

        // Loop through each organization and update quantities
        foreach ($organizations as $org) {
            $subscriptions = $org->subscriptions->first();
            if ($subscriptions) {
                foreach ($subscriptions->items as $item) {
                    $planItem = PlanClass::$items->where("stripe_price_id", $item->stripe_price)->first();
                    if ($planItem) {

                        switch ($planItem->handle) {
                            case 'sku':
                                $quantity = max(Product::count(), 1);
                            case 'channel':
                                $quantity = max(Channel::count(), 1);
                            case 'brand':
                                $quantity = max(Brand::count(), 1);
                            default:
                                $quantity = 1;
                        }
                        $quantity = $this->getQuantity($planItem->handle);
                        $org->subscription('default')->updateQuantity($quantity, $item->stripe_price);
                    }
                }
            }
        }

        $this->info('Subscription items quantities have been updated successfully.');
    } catch (\Exception $e) {
        $this->error('An error occurred: ' . $e->getMessage());
    }
});


Route::get('/products', "Product\ProductController@index")->name('products.index');
