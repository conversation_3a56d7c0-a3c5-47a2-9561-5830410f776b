{"private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "development": "cross-env NODE_ENV=development node_modules/webpack/bin/webpack.js --progress --config=node_modules/laravel-mix/setup/webpack.config.js", "watch": "npm run development -- --watch", "watch-poll": "npm run watch -- --watch-poll", "hot": "cross-env NODE_ENV=development node_modules/webpack-dev-server/bin/webpack-dev-server.js --inline --hot --disable-host-check --config=node_modules/laravel-mix/setup/webpack.config.js", "prod": "npm run production", "production": "cross-env NODE_ENV=production node_modules/webpack/bin/webpack.js --no-progress --config=node_modules/laravel-mix/setup/webpack.config.js"}, "devDependencies": {"@babel/preset-env": "^7.22.20", "@babel/preset-react": "^7.22.15", "@popperjs/core": "^2.10.2", "@vitejs/plugin-react": "^4.2.1", "axios": "^0.21.4", "bootstrap": "^4.0.0", "cross-env": "^7.0", "jquery": "^3.7.1", "laravel-vite-plugin": "^1.2.0", "lodash": "^4.17.19", "popper.js": "^1.12", "react": "^18.3.1", "react-dom": "^18.3.1", "resolve-url-loader": "^3.1.5", "sass": "^1.67.0", "sass-loader": "^8.0.2", "tailwindcss": "^4.0.1", "vite": "^6.0.11", "vue-template-compiler": "^2.7.14", "webpack-cli": "^5.1.4"}, "dependencies": {"@ant-design/icons": "^5.5.1", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@inertiajs/inertia": "^0.11.1", "@inertiajs/inertia-react": "^0.8.1", "@inertiajs/progress": "^0.2.7", "@inertiajs/react": "^1.3.0", "@reduxjs/toolkit": "^2.5.1", "@tailwindcss/vite": "^4.0.0", "@tinymce/tinymce-react": "^5.1.1", "antd": "^5.22", "chart.js": "^4.4.8", "jodit-react": "^5.2.15", "lucide-react": "^0.503.0", "moment": "^2.30.1", "quill-html-edit-button": "^3.0.0", "react-chartjs-2": "^5.3.0", "react-phone-input-2": "^2.15.1", "react-quill": "^2.0.0", "react-infinite-scroll-component": "^6.1.0", "react-redux": "^9.2.0", "react-router-dom": "^6.16.0", "react-scripts": "^5.0.1", "react-select": "^5.8.0", "react-tagsinput": "^3.20.3", "react-toastify": "^9.0.3", "react-widgets": "^5.8.4", "react-window": "^1.8.11", "recharts": "^2.15.0", "styled-components": "^6.1.13", "ziggy-js": "^2.5.0"}}