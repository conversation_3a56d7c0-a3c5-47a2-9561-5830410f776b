# Check our guides at https://confluence.atlassian.com/x/e8YWN for more examples.
# Only use spaces to indent your .yml configuration.
# -----
# You can specify a custom docker image from Docker Hub as your build environment.
image: php:7.1.29

pipelines:
  branches:
    master:
      - step:
          name: Deploy to production
          deployment: production
          script:
            - pipe: atlassian/sftp-deploy:0.5.6
              variables:
                USER: $MASTER_USER
                SERVER: "**************"
                REMOTE_PATH: "/public_html/"
                PASSWORD: $MASTER_PASSWORD # Optional.
            - pipe: atlassian/ssh-run:0.4.0
              variables:
                SSH_USER: $SSH_USER
                SERVER: "**************"
                MODE: "command"
                COMMAND: "cd applications/zqvfmfrksb/public_html && composer install && npm run build" # path to a script in your repository
    staging:
      - step:
          name: Deploy to staging
          deployment: staging
          script:
            - pipe: atlassian/sftp-deploy:0.5.6
              variables:
                USER: $STAGING_USER
                SERVER: "**************"
                REMOTE_PATH: "/public_html/"
                PASSWORD: $STAGING_PASSWORD # Optional.
            - pipe: atlassian/ssh-run:0.4.0
              variables:
                SSH_USER: $SSH_USER
                SERVER: "**************"
                MODE: "command"
                COMMAND: "cd applications/zvgmyhcgah/public_html && composer update && npm update && npm run build && php artisan migrate:fresh --seed" # path to a script in your repository
            - pipe: atlassian/slack-notify:2.0.0
              variables:
                WEBHOOK_URL: $STAGING_SLACK_WEBHOOK_URL
                PRETEXT: "New Deployment on Staging"
                MESSAGE: "Updated code has been pushed to staging server and database is refreshed."
    development:
      - step:
          name: Deploy to Development
          deployment: test
          script:
            - pipe: atlassian/sftp-deploy:0.5.8
              variables:
                USER: $DEVELOPMENT_USER
                SERVER: "**************"
                REMOTE_PATH: "/public_html/"
                PASSWORD: $DEVELOPMENT_PASSWORD # Optional.
            - pipe: atlassian/ssh-run:0.4.0
              variables:
                SSH_USER: $DEVELOPMENT_SSH_USER
                SERVER: "**************"
                MODE: "command"
                COMMAND: "cd applications/gtcrqgksnb/public_html && composer install && npm install && npm run build && php artisan migrate:fresh --seed" # path to a script in your repository
