(function(f,u){typeof exports=="object"&&typeof module<"u"?module.exports=u():typeof define=="function"&&define.amd?define(u):(f=typeof globalThis<"u"?globalThis:f||self,f.Treeselect=u())})(this,function(){var I,y,H,x,pe,Wt,G,oe,me,Rt,fe,Ut,M,re,P,j,Ce,zt,be,Yt,ge,Kt,we,Xt,ke,Jt,Ee,Zt,ve,Qt,Le,es,ye,ts,xe,ss,Se,is,_e,ls,Ae,ns,Te,as,Ne,os,Oe,rs,z,Lt,F,B,_,Y,Ie,cs,Pe,ds,Be,hs,Ve,us,De,ps,He,ms,K,yt,Ge,fs,Me,Cs,Fe,bs,X,xt,je,gs,qe,ws,$e,ks,<PERSON>,<PERSON><PERSON>,<PERSON>,vs,Ue,Ls,ze,ys,Ye,xs,Ke,Ss,Xe,_s,Je,As,J,St,Z,_t,Ze,Ts,h,p,q,Q,$,A,T,S,V,ee,At,te,Tt,Qe,Ns,et,Os,tt,Is,st,Ps,it,Bs,lt,Vs,se,Nt,nt,Ds,at,Hs,ot,Gs,rt,Ms,ie,Ot,ct,Fs,W,bt,le,It,R,gt,dt,js,ne,Pt,ht,qs,ut,$s,pt,Ws,mt,Rs;"use strict";var Ii=Object.defineProperty;var Pi=(f,u,m)=>u in f?Ii(f,u,{enumerable:!0,configurable:!0,writable:!0,value:m}):f[u]=m;var c=(f,u,m)=>(Pi(f,typeof u!="symbol"?u+"":u,m),m),vt=(f,u,m)=>{if(!u.has(f))throw TypeError("Cannot "+m)};var n=(f,u,m)=>(vt(f,u,"read from private field"),m?m.call(f):u.get(f)),r=(f,u,m)=>{if(u.has(f))throw TypeError("Cannot add the same private member more than once");u instanceof WeakSet?u.add(f):u.set(f,m)},b=(f,u,m,U)=>(vt(f,u,"write to private field"),U?U.call(f,m):u.set(f,m),m);var o=(f,u,m)=>(vt(f,u,"access private method"),m);const f="",u={arrowUp:'<svg xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 25 25" fill="none" stroke="#000000" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M18 15l-6-6-6 6"/></svg>',arrowDown:'<svg xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 25 25" fill="none" stroke="#000000" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M6 9l6 6 6-6"/></svg>',arrowRight:'<svg xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 25 25" fill="none" stroke="#000000" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M9 18l6-6-6-6"/></svg>',attention:'<svg xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 25 25" fill="none" stroke="#000000" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path><line x1="12" y1="9" x2="12" y2="13"></line><line x1="12" y1="17" x2="12.01" y2="17"></line></svg>',clear:'<svg xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 25 25" fill="none" stroke="#000000" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><line x1="15" y1="9" x2="9" y2="15"></line><line x1="9" y1="9" x2="15" y2="15"></line></svg>',cross:'<svg xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 25 25" fill="none" stroke="#000000" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></svg>',check:'<svg xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 25 25" fill="none" stroke="#000000" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="20 6 9 17 4 12"></polyline></svg>',partialCheck:'<svg xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 25 25" fill="none" stroke="#000000" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="5" y1="12" x2="19" y2="12"></line></svg>'},m=(l,e)=>{if(e.innerHTML="",typeof l=="string")e.innerHTML=l;else{const t=l.cloneNode(!0);e.appendChild(t)}},U=l=>{const e=l?{...l}:{};return Object.keys(u).forEach(t=>{e[t]||(e[t]=u[t])}),e},Us=l=>l.reduce((e,{name:t},s)=>(e+=t,s<l.length-1&&(e+=", "),e),"");class zs{constructor({value:e,showTags:t,tagsCountText:s,clearable:i,isAlwaysOpened:a,searchable:d,placeholder:C,disabled:g,isSingleSelect:w,id:k,iconElements:E,inputCallback:v,searchCallback:L,openCallback:N,closeCallback:D,keydownCallback:ae,focusCallback:ft,blurCallback:Et,nameChangeCallback:Ct}){r(this,pe);r(this,G);r(this,me);r(this,fe);r(this,M);r(this,P);r(this,Ce);r(this,be);r(this,ge);r(this,we);r(this,ke);r(this,Ee);r(this,ve);r(this,Le);r(this,ye);r(this,xe);r(this,Se);r(this,_e);r(this,Ae);r(this,Te);r(this,Ne);r(this,Oe);r(this,z);c(this,"value");c(this,"showTags");c(this,"tagsCountText");c(this,"clearable");c(this,"isAlwaysOpened");c(this,"searchable");c(this,"placeholder");c(this,"disabled");c(this,"isSingleSelect");c(this,"id");c(this,"iconElements");c(this,"isOpened");c(this,"searchText");c(this,"srcElement");r(this,I,void 0);r(this,y,void 0);r(this,H,void 0);r(this,x,void 0);c(this,"inputCallback");c(this,"searchCallback");c(this,"openCallback");c(this,"closeCallback");c(this,"keydownCallback");c(this,"focusCallback");c(this,"blurCallback");c(this,"nameChangeCallback");this.value=e,this.showTags=t,this.tagsCountText=s,this.searchable=d,this.placeholder=C,this.clearable=i,this.isAlwaysOpened=a,this.disabled=g,this.isSingleSelect=w,this.id=k,this.iconElements=E,this.isOpened=!1,this.searchText="",b(this,I,o(this,ge,Kt).call(this)),b(this,y,o(this,ye,ts).call(this)),b(this,H,o(this,_e,ls).call(this)),b(this,x,null),this.inputCallback=v,this.searchCallback=L,this.openCallback=N,this.closeCallback=D,this.keydownCallback=ae,this.focusCallback=ft,this.blurCallback=Et,this.nameChangeCallback=Ct,this.srcElement=o(this,Ce,zt).call(this,n(this,I),n(this,y),n(this,H)),o(this,pe,Wt).call(this)}focus(){setTimeout(()=>n(this,y).focus(),0)}blur(){this.isOpened&&o(this,P,j).call(this),this.clearSearch(),n(this,y).blur()}updateValue(e){this.value=e,o(this,G,oe).call(this),o(this,M,re).call(this)}removeItem(e){this.value=this.value.filter(t=>t.id!==e),o(this,z,Lt).call(this),o(this,G,oe).call(this),o(this,M,re).call(this)}clear(){this.value=[],o(this,z,Lt).call(this),o(this,G,oe).call(this),this.clearSearch()}openClose(){o(this,P,j).call(this)}clearSearch(){this.searchText="",this.searchCallback(""),o(this,M,re).call(this)}}I=new WeakMap,y=new WeakMap,H=new WeakMap,x=new WeakMap,pe=new WeakSet,Wt=function(){o(this,G,oe).call(this),o(this,M,re).call(this),o(this,me,Rt).call(this)},G=new WeakSet,oe=function(){if(n(this,I).innerHTML="",this.showTags){n(this,I).append(...o(this,we,Xt).call(this));const e=Us(this.value);this.nameChangeCallback(e)}else{const e=o(this,Le,es).call(this);n(this,I).appendChild(e),this.nameChangeCallback(e.innerText)}n(this,I).appendChild(n(this,y))},me=new WeakSet,Rt=function(){const e=[];n(this,H).innerHTML="",this.clearable&&e.push(o(this,Ae,ns).call(this)),this.isAlwaysOpened||e.push(o(this,Ne,os).call(this,this.isOpened)),e.length&&n(this,H).append(...e)},fe=new WeakSet,Ut=function(){if(!this.isAlwaysOpened&&n(this,x)){const e=this.isOpened?this.iconElements.arrowUp:this.iconElements.arrowDown;m(e,n(this,x))}},M=new WeakSet,re=function(){var e;(e=this.value)!=null&&e.length?(n(this,y).removeAttribute("placeholder"),this.srcElement.classList.remove("treeselect-input--value-not-selected")):(n(this,y).setAttribute("placeholder",this.placeholder),this.srcElement.classList.add("treeselect-input--value-not-selected")),this.searchable?this.srcElement.classList.remove("treeselect-input--unsearchable"):this.srcElement.classList.add("treeselect-input--unsearchable"),this.isSingleSelect?this.srcElement.classList.add("treeselect-input--is-single-select"):this.srcElement.classList.remove("treeselect-input--is-single-select"),n(this,y).value=this.searchText},P=new WeakSet,j=function(){this.isOpened=!this.isOpened,o(this,fe,Ut).call(this),this.isOpened?this.openCallback():this.closeCallback()},Ce=new WeakSet,zt=function(e,t,s){const i=document.createElement("div");return i.classList.add("treeselect-input"),i.setAttribute("tabindex","-1"),i.addEventListener("mousedown",a=>o(this,be,Yt).call(this,a)),i.addEventListener("focus",()=>this.focusCallback(),!0),i.addEventListener("blur",()=>this.blurCallback(),!0),e.appendChild(t),i.append(e,s),i},be=new WeakSet,Yt=function(e){e.stopPropagation(),this.isOpened||o(this,P,j).call(this),this.focus()},ge=new WeakSet,Kt=function(){const e=document.createElement("div");return e.classList.add("treeselect-input__tags"),e},we=new WeakSet,Xt=function(){return this.value.map(e=>{const t=document.createElement("div");t.classList.add("treeselect-input__tags-element"),t.setAttribute("tabindex","-1"),t.setAttribute("tag-id",e.id.toString()),t.setAttribute("title",e.name);const s=o(this,Ee,Zt).call(this,e.name),i=o(this,ve,Qt).call(this);return t.addEventListener("mousedown",a=>o(this,ke,Jt).call(this,a,e.id)),t.append(s,i),t})},ke=new WeakSet,Jt=function(e,t){e.preventDefault(),e.stopPropagation(),this.removeItem(t),this.focus()},Ee=new WeakSet,Zt=function(e){const t=document.createElement("span");return t.classList.add("treeselect-input__tags-name"),t.textContent=e,t},ve=new WeakSet,Qt=function(){const e=document.createElement("span");return e.classList.add("treeselect-input__tags-cross"),m(this.iconElements.cross,e),e},Le=new WeakSet,es=function(){const e=document.createElement("span");if(e.classList.add("treeselect-input__tags-count"),!this.value.length)return e.textContent="",e.setAttribute("title",""),e;const t=this.value.length===1?this.value[0].name:`${this.value.length} ${this.tagsCountText}`;return e.textContent=t,e.setAttribute("title",t),e},ye=new WeakSet,ts=function(){const e=document.createElement("input");return e.classList.add("treeselect-input__edit"),this.id&&e.setAttribute("id",this.id),(!this.searchable||this.disabled)&&e.setAttribute("readonly","readonly"),this.disabled&&e.setAttribute("tabindex","-1"),e.addEventListener("keydown",t=>o(this,xe,ss).call(this,t)),e.addEventListener("input",t=>o(this,Se,is).call(this,t,e)),e},xe=new WeakSet,ss=function(e){e.stopPropagation();const t=e.key;t==="Backspace"&&!this.searchText.length&&this.value.length&&!this.showTags&&this.clear(),t==="Backspace"&&!this.searchText.length&&this.value.length&&this.removeItem(this.value[this.value.length-1].id),e.code==="Space"&&(!this.searchText||!this.searchable)&&o(this,P,j).call(this),(t==="Enter"||t==="ArrowDown"||t==="ArrowUp")&&e.preventDefault(),this.keydownCallback(e),t!=="Tab"&&this.focus()},Se=new WeakSet,is=function(e,t){e.stopPropagation();const s=this.searchText,i=t.value.trim();if(s.length===0&&i.length===0){t.value="";return}if(this.searchable){const a=e.target.value;this.searchCallback(a),this.isOpened||o(this,P,j).call(this)}else t.value="";this.searchText=t.value},_e=new WeakSet,ls=function(){const e=document.createElement("div");return e.classList.add("treeselect-input__operators"),e},Ae=new WeakSet,ns=function(){const e=document.createElement("span");return e.classList.add("treeselect-input__clear"),e.setAttribute("tabindex","-1"),m(this.iconElements.clear,e),e.addEventListener("mousedown",t=>o(this,Te,as).call(this,t)),e},Te=new WeakSet,as=function(e){e.preventDefault(),e.stopPropagation(),(this.searchText.length||this.value.length)&&this.clear(),this.focus()},Ne=new WeakSet,os=function(e){b(this,x,document.createElement("span")),n(this,x).classList.add("treeselect-input__arrow");const t=e?this.iconElements.arrowUp:this.iconElements.arrowDown;return m(t,n(this,x)),n(this,x).addEventListener("mousedown",s=>o(this,Oe,rs).call(this,s)),n(this,x)},Oe=new WeakSet,rs=function(e){e.stopPropagation(),e.preventDefault(),this.focus(),o(this,P,j).call(this)},z=new WeakSet,Lt=function(){this.inputCallback(this.value)};const Bt=(l,e,t,s)=>{Js(e);const i=e.filter(a=>!a.disabled&&l.some(d=>d===a.id));if(t&&i.length){i[0].checked=!0;return}i.forEach(a=>{a.checked=!0;const d=wt(a,e,s);a.checked=d})},wt=({id:l,checked:e},t,s)=>{const i=t.find(d=>d.id===l);if(!i)return!1;if(s)return i.checked=i.disabled?!1:!!e,i.checked;const a=Vt(!!e,i,t);return Dt(i,t),a},Vt=(l,e,t)=>{if(!e.isGroup)return e.checked=e.disabled?!1:!!l,e.isPartialChecked=!1,e.checked;const s=t.filter(C=>C.childOf===e.id);return!l||e.disabled||e.isPartialChecked?(e.checked=!1,e.isPartialChecked=!1,kt(e,s,t),e.checked):Ht(s,t)?Gt(s)?(e.checked=!1,e.isPartialChecked=!1,e.disabled=!0,e.checked):(e.checked=!1,e.isPartialChecked=!0,s.forEach(C=>{Vt(l,C,t)}),e.checked):(e.checked=!0,e.isPartialChecked=!1,kt(e,s,t),e.checked)},Dt=(l,e)=>{const t=e.find(s=>s.id===l.childOf);t&&(Ys(t,e),Dt(t,e))},Ys=(l,e)=>{const t=ce(l,e);if(Gt(t)){l.checked=!1,l.isPartialChecked=!1,l.disabled=!0;return}if(Ks(t)){l.checked=!0,l.isPartialChecked=!1;return}if(Xs(t)){l.checked=!1,l.isPartialChecked=!0;return}l.checked=!1,l.isPartialChecked=!1},kt=({checked:l,disabled:e},t,s)=>{t.forEach(i=>{i.disabled=!!e||!!i.disabled,i.checked=!!l&&!i.disabled,i.isPartialChecked=!1;const a=ce(i,s);kt({checked:l,disabled:e},a,s)})},Ht=(l,e)=>l.some(i=>i.disabled)?!0:l.some(i=>{if(i.isGroup){const a=ce(i,e);return Ht(a,e)}return!1}),Gt=l=>l.every(e=>!!e.disabled),Ks=l=>l.every(e=>!!e.checked),Xs=l=>l.some(e=>!!e.checked||!!e.isPartialChecked),Js=l=>{l.forEach(e=>{e.checked=!1,e.isPartialChecked=!1})},Zs=(l,e,t)=>{const s={level:0,groupId:""},i=Mt(l,e,s.groupId,s.level);return ei(i,t)},Mt=(l,e,t,s)=>l.reduce((i,a)=>{var w;const d=!!((w=a.children)!=null&&w.length),C=s>=e&&d,g=s>e;if(i.push({id:a.value,name:a.name,childOf:t,isGroup:d,checked:!1,isPartialChecked:!1,level:s,isClosed:C,hidden:g,disabled:a.disabled??!1}),d){const k=Mt(a.children,e,a.value,s+1);i.push(...k)}return i},[]),ce=({id:l},e)=>e.filter(t=>t.childOf===l),Qs=l=>{const{ungroupedNodes:e,allGroupedNodes:t,allNodes:s}=l.reduce((a,d)=>(d.checked&&(a.allNodes.push(d),d.isGroup?a.allGroupedNodes.push(d):a.ungroupedNodes.push(d)),a),{ungroupedNodes:[],allGroupedNodes:[],allNodes:[]}),i=s.filter(a=>!t.some(({id:d})=>d===a.childOf));return{ungroupedNodes:e,groupedNodes:i,allNodes:s}},ei=(l,e)=>(l.filter(s=>!!s.disabled).forEach(({id:s})=>wt({id:s,checked:!1},l,e)),l),de=(l,{id:e,isClosed:t})=>{ce({id:e},l).forEach(i=>{i.hidden=t??!1,i.isGroup&&!i.isClosed&&de(l,{id:i.id,isClosed:t})})},ti=l=>{l.filter(e=>e.isGroup&&!e.disabled&&(e.checked||e.isPartialChecked)).forEach(e=>{e.isClosed=!1,de(l,e)})},si=(l,e)=>{const t=ii(l,e);l.forEach(s=>{t.some(({id:a})=>a===s.id)?(s.isGroup&&(s.isClosed=!1,de(l,s)),s.hidden=!1):s.hidden=!0})},ii=(l,e)=>l.reduce((t,s)=>{if(s.name.toLowerCase().includes(e.toLowerCase())){if(t.push(s),s.isGroup){const a=Ft(s.id,l);t.push(...a)}if(s.childOf){const a=jt(s.childOf,l);t.push(...a)}}return t},[]),Ft=(l,e)=>e.reduce((t,s)=>(s.childOf===l&&(t.push(s),s.isGroup&&t.push(...Ft(s.id,e))),t),[]),jt=(l,e)=>e.reduce((t,s)=>(s.id===l&&(t.push(s),s.childOf&&t.push(...jt(s.childOf,e))),t),[]),li=l=>{const{duplications:e}=l.reduce((t,s)=>(t.allItems.some(i=>i.toString()===s.id.toString())&&t.duplications.push(s.id),t.allItems.push(s.id),t),{duplications:[],allItems:[]});e.length&&console.error(`Validation: You have duplicated values: ${e.join(", ")}! You should use unique values.`)},ni=(l,e,t,s,i,a,d,C,g)=>{Bt(l,e,i,g),C&&d&&ti(e),he(e,t,s,a)},he=(l,e,t,s)=>{l.forEach(i=>{const a=e.querySelector(`[input-id="${i.id}"]`),d=O(a);a.checked=i.checked,ai(i,d,s),oi(i,d),ri(i,d),ci(i,d,t),di(i,d),ui(i,d,l),hi(i,a,t)}),pi(l,e)},ai=(l,e,t)=>{l.checked?e.classList.add("treeselect-list__item--checked"):e.classList.remove("treeselect-list__item--checked"),Array.isArray(t)&&t[0]===l.id&&!l.disabled?e.classList.add("treeselect-list__item--single-selected"):e.classList.remove("treeselect-list__item--single-selected")},oi=(l,e)=>{l.isPartialChecked?e.classList.add("treeselect-list__item--partial-checked"):e.classList.remove("treeselect-list__item--partial-checked")},ri=(l,e)=>{l.disabled?e.classList.add("treeselect-list__item--disabled"):e.classList.remove("treeselect-list__item--disabled")},ci=(l,e,t)=>{if(l.isGroup){const s=e.querySelector(".treeselect-list__item-icon"),i=l.isClosed?t.arrowRight:t.arrowDown;m(i,s),l.isClosed?e.classList.add("treeselect-list__item--closed"):e.classList.remove("treeselect-list__item--closed")}},di=(l,e)=>{l.hidden?e.classList.add("treeselect-list__item--hidden"):e.classList.remove("treeselect-list__item--hidden")},hi=(l,e,t)=>{const i=e.parentNode.querySelector(".treeselect-list__item-checkbox-icon");l.checked?m(t.check,i):l.isPartialChecked?m(t.partialCheck,i):i.innerHTML=""},ui=(l,e,t)=>{const s=l.level===0,i=20,a=5;if(s){const d=t.some(g=>g.isGroup&&g.level===l.level),C=!l.isGroup&&d?`${i}px`:`${a}px`;e.style.paddingLeft=l.isGroup?"0":C}else e.style.paddingLeft=l.isGroup?`${l.level*i}px`:`${l.level*i+i}px`;e.setAttribute("level",l.level.toString()),e.setAttribute("group",l.isGroup.toString())},pi=(l,e)=>{const t=l.some(i=>!i.hidden),s=e.querySelector(".treeselect-list__empty");t?s.classList.add("treeselect-list__empty--hidden"):s.classList.remove("treeselect-list__empty--hidden")},O=l=>l.parentNode.parentNode,qt=(l,e)=>e.find(t=>t.id.toString()===l),mi=l=>O(l).querySelector(".treeselect-list__item-icon"),fi=(l,e)=>{e&&Object.keys(e).forEach(t=>{const s=e[t];typeof s=="string"&&l.setAttribute(t,s)})};class Ci{constructor({options:e,value:t,openLevel:s,listSlotHtmlComponent:i,emptyText:a,isSingleSelect:d,iconElements:C,showCount:g,disabledBranchNode:w,expandSelected:k,isIndependentNodes:E,inputCallback:v,arrowClickCallback:L,mouseupCallback:N}){r(this,Ie);r(this,Pe);r(this,Be);r(this,Ve);r(this,De);r(this,He);r(this,K);r(this,Ge);r(this,Me);r(this,Fe);r(this,X);r(this,je);r(this,qe);r(this,$e);r(this,We);r(this,Re);r(this,Ue);r(this,ze);r(this,Ye);r(this,Ke);r(this,Xe);r(this,Je);r(this,J);r(this,Z);r(this,Ze);c(this,"options");c(this,"value");c(this,"openLevel");c(this,"listSlotHtmlComponent");c(this,"emptyText");c(this,"isSingleSelect");c(this,"showCount");c(this,"disabledBranchNode");c(this,"expandSelected");c(this,"isIndependentNodes");c(this,"iconElements");c(this,"searchText");c(this,"flattedOptions");c(this,"flattedOptionsBeforeSearch");c(this,"selectedNodes");c(this,"srcElement");c(this,"inputCallback");c(this,"arrowClickCallback");c(this,"mouseupCallback");r(this,F,null);r(this,B,!0);r(this,_,[]);r(this,Y,!0);this.options=e,this.value=t,this.openLevel=s??0,this.listSlotHtmlComponent=i??null,this.emptyText=a??"No results found...",this.isSingleSelect=d??!1,this.showCount=g??!1,this.disabledBranchNode=w??!1,this.expandSelected=k??!1,this.isIndependentNodes=E??!1,this.iconElements=C,this.searchText="",this.flattedOptions=Zs(this.options,this.openLevel,this.isIndependentNodes),this.flattedOptionsBeforeSearch=this.flattedOptions,this.selectedNodes={nodes:[],groupedNodes:[],allNodes:[]},this.srcElement=o(this,Be,hs).call(this),this.inputCallback=v,this.arrowClickCallback=L,this.mouseupCallback=N,li(this.flattedOptions)}updateValue(e){this.value=e,b(this,_,this.isSingleSelect?this.value:[]),ni(e,this.flattedOptions,this.srcElement,this.iconElements,this.isSingleSelect,n(this,_),this.expandSelected,n(this,Y),this.isIndependentNodes),b(this,Y,!1),o(this,Z,_t).call(this)}updateSearchValue(e){if(e===this.searchText)return;const t=this.searchText===""&&e!=="";this.searchText=e,t&&(this.flattedOptionsBeforeSearch=JSON.parse(JSON.stringify(this.flattedOptions))),this.searchText===""&&(this.flattedOptions=this.flattedOptionsBeforeSearch.map(s=>{const i=this.flattedOptions.find(a=>a.id===s.id);return i.isClosed=s.isClosed,i.hidden=s.hidden,i}),this.flattedOptionsBeforeSearch=[]),this.searchText&&si(this.flattedOptions,e),he(this.flattedOptions,this.srcElement,this.iconElements,n(this,_)),this.focusFirstListElement()}callKeyAction(e){b(this,B,!1);const t=this.srcElement.querySelector(".treeselect-list__item--focused");if(t==null?void 0:t.classList.contains("treeselect-list__item--hidden"))return;const i=e.key;i==="Enter"&&t&&t.dispatchEvent(new Event("mousedown")),(i==="ArrowLeft"||i==="ArrowRight")&&o(this,Ie,cs).call(this,t,e),(i==="ArrowDown"||i==="ArrowUp")&&o(this,Pe,ds).call(this,t,i)}focusFirstListElement(){const e="treeselect-list__item--focused",t=this.srcElement.querySelector(`.${e}`),s=Array.from(this.srcElement.querySelectorAll(".treeselect-list__item-checkbox")).filter(a=>window.getComputedStyle(O(a)).display!=="none");if(!s.length)return;t&&t.classList.remove(e),O(s[0]).classList.add(e)}isLastFocusedElementExist(){return!!n(this,F)}}F=new WeakMap,B=new WeakMap,_=new WeakMap,Y=new WeakMap,Ie=new WeakSet,cs=function(e,t){if(!e)return;const s=t.key,a=e.querySelector(".treeselect-list__item-checkbox").getAttribute("input-id"),d=qt(a,this.flattedOptions),C=e.querySelector(".treeselect-list__item-icon");s==="ArrowLeft"&&!d.isClosed&&d.isGroup&&(C.dispatchEvent(new Event("mousedown")),t.preventDefault()),s==="ArrowRight"&&d.isClosed&&d.isGroup&&(C.dispatchEvent(new Event("mousedown")),t.preventDefault())},Pe=new WeakSet,ds=function(e,t){var i;const s=Array.from(this.srcElement.querySelectorAll(".treeselect-list__item-checkbox")).filter(a=>window.getComputedStyle(O(a)).display!=="none");if(s.length)if(!e)O(s[0]).classList.add("treeselect-list__item--focused");else{const a=s.findIndex(D=>O(D).classList.contains("treeselect-list__item--focused"));O(s[a]).classList.remove("treeselect-list__item--focused");const C=t==="ArrowDown"?a+1:a-1,g=t==="ArrowDown"?0:s.length-1,w=s[C]??s[g],k=!s[C],E=O(w);E.classList.add("treeselect-list__item--focused");const v=this.srcElement.getBoundingClientRect(),L=E.getBoundingClientRect();if(k&&t==="ArrowDown"){this.srcElement.scroll(0,0);return}if(k&&t==="ArrowUp"){this.srcElement.scroll(0,this.srcElement.scrollHeight);return}const N=((i=this.listSlotHtmlComponent)==null?void 0:i.clientHeight)??0;if(v.y+v.height<L.y+L.height+N){this.srcElement.scroll(0,this.srcElement.scrollTop+L.height);return}if(v.y>L.y){this.srcElement.scroll(0,this.srcElement.scrollTop-L.height);return}}},Be=new WeakSet,hs=function(){const e=o(this,Ve,us).call(this),t=o(this,K,yt).call(this,this.options);e.append(...t);const s=o(this,Me,Cs).call(this);e.append(s);const i=o(this,Ge,fs).call(this);return i&&e.append(i),e},Ve=new WeakSet,us=function(){const e=document.createElement("div");return e.classList.add("treeselect-list"),this.isSingleSelect&&e.classList.add("treeselect-list--single-select"),this.disabledBranchNode&&e.classList.add("treeselect-list--disabled-branch-node"),e.addEventListener("mouseout",t=>o(this,De,ps).call(this,t)),e.addEventListener("mousemove",()=>o(this,He,ms).call(this)),e.addEventListener("mouseup",()=>this.mouseupCallback(),!0),e},De=new WeakSet,ps=function(e){e.stopPropagation(),n(this,F)&&n(this,B)&&n(this,F).classList.add("treeselect-list__item--focused")},He=new WeakSet,ms=function(){b(this,B,!0)},K=new WeakSet,yt=function(e){return e.reduce((t,s)=>{var a;if((a=s.children)!=null&&a.length){const d=o(this,Fe,bs).call(this,s),C=o(this,K,yt).call(this,s.children);return d.append(...C),t.push(d),t}const i=o(this,X,xt).call(this,s,!1);return t.push(i),t},[])},Ge=new WeakSet,fs=function(){if(!this.listSlotHtmlComponent)return null;const e=document.createElement("div");return e.classList.add("treeselect-list__slot"),e.appendChild(this.listSlotHtmlComponent),e},Me=new WeakSet,Cs=function(){const e=document.createElement("div");e.classList.add("treeselect-list__empty"),e.setAttribute("title",this.emptyText);const t=document.createElement("span");t.classList.add("treeselect-list__empty-icon"),m(this.iconElements.attention,t);const s=document.createElement("span");return s.classList.add("treeselect-list__empty-text"),s.textContent=this.emptyText,e.append(t,s),e},Fe=new WeakSet,bs=function(e){const t=document.createElement("div");t.setAttribute("group-container-id",e.value.toString()),t.classList.add("treeselect-list__group-container");const s=o(this,X,xt).call(this,e,!0);return t.appendChild(s),t},X=new WeakSet,xt=function(e,t){const s=o(this,je,gs).call(this,e);if(t){const d=o(this,Re,vs).call(this);s.appendChild(d),s.classList.add("treeselect-list__item--group")}const i=o(this,ze,ys).call(this,e),a=o(this,Ye,xs).call(this,e,t);return s.append(i,a),s},je=new WeakSet,gs=function(e){const t=document.createElement("div");return fi(t,e.htmlAttr),t.setAttribute("tabindex","-1"),t.setAttribute("title",e.name),t.classList.add("treeselect-list__item"),t.addEventListener("mouseover",()=>o(this,qe,ws).call(this,t),!0),t.addEventListener("mouseout",()=>o(this,$e,ks).call(this,t),!0),t.addEventListener("mousedown",s=>o(this,We,Es).call(this,s,e)),t},qe=new WeakSet,ws=function(e){n(this,B)&&o(this,J,St).call(this,!0,e)},$e=new WeakSet,ks=function(e){n(this,B)&&(o(this,J,St).call(this,!1,e),b(this,F,e))},We=new WeakSet,Es=function(e,t){var a;if(e.preventDefault(),e.stopPropagation(),(a=this.flattedOptions.find(d=>d.id===t.value))==null?void 0:a.disabled)return;const i=e.target.querySelector(".treeselect-list__item-checkbox");i.checked=!i.checked,o(this,Xe,_s).call(this,i,t)},Re=new WeakSet,vs=function(){const e=document.createElement("span");return e.setAttribute("tabindex","-1"),e.classList.add("treeselect-list__item-icon"),m(this.iconElements.arrowDown,e),e.addEventListener("mousedown",t=>o(this,Ue,Ls).call(this,t)),e},Ue=new WeakSet,Ls=function(e){e.preventDefault(),e.stopPropagation(),o(this,Je,As).call(this,e)},ze=new WeakSet,ys=function(e){const t=document.createElement("div");t.classList.add("treeselect-list__item-checkbox-container");const s=document.createElement("span");s.classList.add("treeselect-list__item-checkbox-icon"),s.innerHTML="";const i=document.createElement("input");return i.setAttribute("tabindex","-1"),i.setAttribute("type","checkbox"),i.setAttribute("input-id",e.value.toString()),i.classList.add("treeselect-list__item-checkbox"),t.append(s,i),t},Ye=new WeakSet,xs=function(e,t){const s=document.createElement("label");if(s.textContent=e.name,s.classList.add("treeselect-list__item-label"),t&&this.showCount){const i=o(this,Ke,Ss).call(this,e);s.appendChild(i)}return s},Ke=new WeakSet,Ss=function(e){const t=document.createElement("span"),s=this.flattedOptions.filter(i=>i.childOf===e.value);return t.textContent=`(${s.length})`,t.classList.add("treeselect-list__item-label-counter"),t},Xe=new WeakSet,_s=function(e,t){const s=this.flattedOptions.find(i=>i.id===t.value);if(s){if(s!=null&&s.isGroup&&this.disabledBranchNode){const i=mi(e);i==null||i.dispatchEvent(new Event("mousedown"));return}if(this.isSingleSelect){const[i]=n(this,_);if(s.id===i)return;b(this,_,[s.id]),Bt([s.id],this.flattedOptions,this.isSingleSelect,this.isIndependentNodes)}else{s.checked=e.checked;const i=wt(s,this.flattedOptions,this.isIndependentNodes);e.checked=i}he(this.flattedOptions,this.srcElement,this.iconElements,n(this,_)),o(this,Ze,Ts).call(this)}},Je=new WeakSet,As=function(e){var a,d;const t=(d=(a=e.target)==null?void 0:a.parentNode)==null?void 0:d.querySelector("[input-id]"),s=(t==null?void 0:t.getAttribute("input-id"))??null,i=qt(s,this.flattedOptions);i&&(i.isClosed=!i.isClosed,de(this.flattedOptions,i),he(this.flattedOptions,this.srcElement,this.iconElements,n(this,_)),this.arrowClickCallback())},J=new WeakSet,St=function(e,t){const s="treeselect-list__item--focused";if(e){const i=Array.from(this.srcElement.querySelectorAll(`.${s}`));i.length&&i.forEach(a=>a.classList.remove(s)),t.classList.add(s)}else t.classList.remove(s)},Z=new WeakSet,_t=function(){const{ungroupedNodes:e,groupedNodes:t,allNodes:s}=Qs(this.flattedOptions);this.selectedNodes={nodes:e,groupedNodes:t,allNodes:s}},Ze=new WeakSet,Ts=function(){o(this,Z,_t).call(this),this.inputCallback(this.selectedNodes),this.value=this.selectedNodes.nodes.map(e=>e.id)};const $t=({parentHtmlContainer:l,staticList:e,appendToBody:t,isSingleSelect:s,value:i,direction:a})=>{l||console.error("Validation: parentHtmlContainer prop is required!"),e&&t&&console.error("Validation: You should set staticList to false if you use appendToBody!"),s&&Array.isArray(i)&&console.error("Validation: if you use isSingleSelect prop, you should pass a single value!"),!s&&!Array.isArray(i)&&console.error("Validation: you should pass an array as a value!"),a&&a!=="auto"&&a!=="bottom"&&a!=="top"&&console.error("Validation: you should pass (auto | top | bottom | undefined) as a value for the direction prop!")},ue=l=>l.map(e=>e.id),bi=l=>l?Array.isArray(l)?l:[l]:[],gi=(l,e)=>{if(e){const[t]=l;return t??null}return l};class wi{constructor({parentHtmlContainer:e,value:t,options:s,openLevel:i,appendToBody:a,alwaysOpen:d,showTags:C,tagsCountText:g,clearable:w,searchable:k,placeholder:E,grouped:v,isGroupedValue:L,listSlotHtmlComponent:N,disabled:D,emptyText:ae,staticList:ft,id:Et,isSingleSelect:Ct,showCount:ki,disabledBranchNode:Ei,direction:vi,expandSelected:Li,saveScrollPosition:yi,isIndependentNodes:xi,iconElements:Si,inputCallback:_i,openCallback:Ai,closeCallback:Ti,nameChangeCallback:Ni,searchCallback:Oi}){r(this,ee);r(this,te);r(this,Qe);r(this,et);r(this,tt);r(this,st);r(this,it);r(this,lt);r(this,se);r(this,nt);r(this,at);r(this,ot);r(this,rt);r(this,ie);r(this,ct);r(this,W);r(this,le);r(this,R);r(this,dt);r(this,ne);r(this,ht);r(this,ut);r(this,pt);r(this,mt);c(this,"parentHtmlContainer");c(this,"value");c(this,"options");c(this,"openLevel");c(this,"appendToBody");c(this,"alwaysOpen");c(this,"showTags");c(this,"tagsCountText");c(this,"clearable");c(this,"searchable");c(this,"placeholder");c(this,"grouped");c(this,"isGroupedValue");c(this,"listSlotHtmlComponent");c(this,"disabled");c(this,"emptyText");c(this,"staticList");c(this,"id");c(this,"isSingleSelect");c(this,"showCount");c(this,"disabledBranchNode");c(this,"direction");c(this,"expandSelected");c(this,"saveScrollPosition");c(this,"isIndependentNodes");c(this,"iconElements");c(this,"inputCallback");c(this,"openCallback");c(this,"closeCallback");c(this,"nameChangeCallback");c(this,"searchCallback");c(this,"ungroupedValue");c(this,"groupedValue");c(this,"allValue");c(this,"isListOpened");c(this,"selectedName");c(this,"srcElement");r(this,h,null);r(this,p,null);r(this,q,null);r(this,Q,0);r(this,$,0);r(this,A,null);r(this,T,null);r(this,S,null);r(this,V,null);$t({parentHtmlContainer:e,value:t,staticList:ft,appendToBody:a,isSingleSelect:Ct}),this.parentHtmlContainer=e,this.value=[],this.options=s??[],this.openLevel=i??0,this.appendToBody=a??!1,this.alwaysOpen=!!(d&&!D),this.showTags=C??!0,this.tagsCountText=g??"elements selected",this.clearable=w??!0,this.searchable=k??!0,this.placeholder=E??"Search...",this.grouped=v??!0,this.isGroupedValue=L??!1,this.listSlotHtmlComponent=N??null,this.disabled=D??!1,this.emptyText=ae??"No results found...",this.staticList=!!(ft&&!this.appendToBody),this.id=Et??"",this.isSingleSelect=Ct??!1,this.showCount=ki??!1,this.disabledBranchNode=Ei??!1,this.direction=vi??"auto",this.expandSelected=Li??!1,this.saveScrollPosition=yi??!0,this.isIndependentNodes=xi??!1,this.iconElements=U(Si),this.inputCallback=_i,this.openCallback=Ai,this.closeCallback=Ti,this.nameChangeCallback=Ni,this.searchCallback=Oi,this.ungroupedValue=[],this.groupedValue=[],this.allValue=[],this.isListOpened=!1,this.selectedName="",this.srcElement=null,o(this,ee,At).call(this,t)}mount(){$t({parentHtmlContainer:this.parentHtmlContainer,value:this.value,staticList:this.staticList,appendToBody:this.appendToBody,isSingleSelect:this.isSingleSelect}),this.iconElements=U(this.iconElements),o(this,ee,At).call(this,this.value)}updateValue(e){const t=bi(e),s=n(this,h);s&&(s.updateValue(t),o(this,se,Nt).call(this,s==null?void 0:s.selectedNodes))}destroy(){this.srcElement&&(o(this,ie,Ot).call(this),this.srcElement.innerHTML="",this.srcElement=null,o(this,R,gt).call(this,!0))}focus(){n(this,p)&&n(this,p).focus()}toggleOpenClose(){n(this,p)&&(n(this,p).openClose(),n(this,p).focus())}scrollWindowHandler(){this.updateListPosition()}focusWindowHandler(e){var s,i,a;((s=this.srcElement)==null?void 0:s.contains(e.target))||((i=n(this,h))==null?void 0:i.srcElement.contains(e.target))||((a=n(this,p))==null||a.blur(),o(this,R,gt).call(this,!1),o(this,W,bt).call(this,!1))}blurWindowHandler(){var e;(e=n(this,p))==null||e.blur(),o(this,R,gt).call(this,!1),o(this,W,bt).call(this,!1)}updateListPosition(){var N;const e=this.srcElement,t=(N=n(this,h))==null?void 0:N.srcElement;if(!e||!t)return;const{height:s}=t.getBoundingClientRect(),{x:i,y:a,height:d,width:C}=e.getBoundingClientRect(),g=window.innerHeight,w=a,k=g-a-d;let E=w>k&&w>=s&&k<s;if(this.direction!=="auto"&&(E=this.direction==="top"),this.appendToBody){(t.style.top!=="0px"||t.style.left!=="0px")&&(t.style.top="0px",t.style.left="0px");const D=i+window.scrollX,ae=E?a+window.scrollY-s:a+window.scrollY+d;t.style.transform=`translate(${D}px,${ae}px)`,t.style.width=`${C}px`}const v=E?"top":"bottom";t.getAttribute("direction")!==v&&(t.setAttribute("direction",v),o(this,ct,Fs).call(this,E,this.appendToBody))}}return h=new WeakMap,p=new WeakMap,q=new WeakMap,Q=new WeakMap,$=new WeakMap,A=new WeakMap,T=new WeakMap,S=new WeakMap,V=new WeakMap,ee=new WeakSet,At=function(e){var a;this.destroy();const{container:t,list:s,input:i}=o(this,Qe,Ns).call(this);this.srcElement=t,b(this,h,s),b(this,p,i),b(this,A,this.scrollWindowHandler.bind(this)),b(this,T,this.scrollWindowHandler.bind(this)),b(this,S,this.focusWindowHandler.bind(this)),b(this,V,this.blurWindowHandler.bind(this)),this.alwaysOpen&&((a=n(this,p))==null||a.openClose()),this.disabled?this.srcElement.classList.add("treeselect--disabled"):this.srcElement.classList.remove("treeselect--disabled"),this.updateValue(e??this.value)},te=new WeakSet,Tt=function({groupedNodes:e,nodes:t,allNodes:s}){this.ungroupedValue=t?ue(t):[],this.groupedValue=e?ue(e):[],this.allValue=s?ue(s):[];let i=[];this.isIndependentNodes||this.isSingleSelect?i=this.allValue:this.isGroupedValue?i=this.groupedValue:i=this.ungroupedValue,this.value=gi(i,this.isSingleSelect)},Qe=new WeakSet,Ns=function(){const e=this.parentHtmlContainer;e.classList.add("treeselect");const t=new Ci({value:[],options:this.options,openLevel:this.openLevel,listSlotHtmlComponent:this.listSlotHtmlComponent,emptyText:this.emptyText,isSingleSelect:this.isSingleSelect,showCount:this.showCount,disabledBranchNode:this.disabledBranchNode,expandSelected:this.expandSelected,isIndependentNodes:this.isIndependentNodes,iconElements:this.iconElements,inputCallback:i=>o(this,nt,Ds).call(this,i),arrowClickCallback:()=>o(this,at,Hs).call(this),mouseupCallback:()=>{var i;return(i=n(this,p))==null?void 0:i.focus()}}),s=new zs({value:[],showTags:this.showTags,tagsCountText:this.tagsCountText,clearable:this.clearable,isAlwaysOpened:this.alwaysOpen,searchable:this.searchable,placeholder:this.placeholder,disabled:this.disabled,isSingleSelect:this.isSingleSelect,id:this.id,iconElements:this.iconElements,inputCallback:i=>o(this,et,Os).call(this,i),searchCallback:i=>o(this,st,Ps).call(this,i),openCallback:()=>o(this,rt,Ms).call(this),closeCallback:()=>o(this,ie,Ot).call(this),keydownCallback:i=>o(this,tt,Is).call(this,i),focusCallback:()=>o(this,it,Bs).call(this),blurCallback:()=>o(this,lt,Vs).call(this),nameChangeCallback:i=>o(this,ot,Gs).call(this,i)});return this.appendToBody&&b(this,q,new ResizeObserver(()=>this.updateListPosition())),e.append(s.srcElement),{container:e,list:t,input:s}},et=new WeakSet,Os=function(e){var i,a;const t=ue(e);(i=n(this,h))==null||i.updateValue(t);const s=((a=n(this,h))==null?void 0:a.selectedNodes)??{};o(this,te,Tt).call(this,s),o(this,ne,Pt).call(this)},tt=new WeakSet,Is=function(e){var t;this.isListOpened&&((t=n(this,h))==null||t.callKeyAction(e))},st=new WeakSet,Ps=function(e){n(this,$)&&clearTimeout(n(this,$)),b(this,$,window.setTimeout(()=>{var t;(t=n(this,h))==null||t.updateSearchValue(e),this.updateListPosition()},350)),o(this,mt,Rs).call(this,e)},it=new WeakSet,Bs=function(){o(this,W,bt).call(this,!0),n(this,S)&&n(this,S)&&n(this,V)&&(document.addEventListener("mousedown",n(this,S),!0),document.addEventListener("focus",n(this,S),!0),window.addEventListener("blur",n(this,V)))},lt=new WeakSet,Vs=function(){setTimeout(()=>{var s,i;const e=(s=n(this,p))==null?void 0:s.srcElement.contains(document.activeElement),t=(i=n(this,h))==null?void 0:i.srcElement.contains(document.activeElement);!e&&!t&&this.blurWindowHandler()},1)},se=new WeakSet,Nt=function(e){var s;if(!e)return;let t=[];this.isIndependentNodes||this.isSingleSelect?t=e.allNodes:this.grouped?t=e.groupedNodes:t=e.nodes,(s=n(this,p))==null||s.updateValue(t),o(this,te,Tt).call(this,e)},nt=new WeakSet,Ds=function(e){var t,s,i;o(this,se,Nt).call(this,e),this.isSingleSelect&&!this.alwaysOpen&&((t=n(this,p))==null||t.openClose(),(s=n(this,p))==null||s.clearSearch()),(i=n(this,p))==null||i.focus(),o(this,ne,Pt).call(this)},at=new WeakSet,Hs=function(){var e;(e=n(this,p))==null||e.focus(),this.updateListPosition()},ot=new WeakSet,Gs=function(e){this.selectedName!==e&&(this.selectedName=e,o(this,ht,qs).call(this))},rt=new WeakSet,Ms=function(){var e;this.isListOpened=!0,n(this,A)&&n(this,T)&&(window.addEventListener("scroll",n(this,A),!0),window.addEventListener("resize",n(this,T))),!(!n(this,h)||!this.srcElement)&&(this.appendToBody?(document.body.appendChild(n(this,h).srcElement),(e=n(this,q))==null||e.observe(this.srcElement)):this.srcElement.appendChild(n(this,h).srcElement),this.updateListPosition(),o(this,le,It).call(this,!0),o(this,dt,js).call(this),o(this,ut,$s).call(this))},ie=new WeakSet,Ot=function(){var t;this.alwaysOpen||(this.isListOpened=!1,n(this,A)&&n(this,T)&&(window.removeEventListener("scroll",n(this,A),!0),window.removeEventListener("resize",n(this,T))),!n(this,h)||!this.srcElement)||!(this.appendToBody?document.body.contains(n(this,h).srcElement):this.srcElement.contains(n(this,h).srcElement))||(b(this,Q,n(this,h).srcElement.scrollTop),this.appendToBody?(document.body.removeChild(n(this,h).srcElement),(t=n(this,q))==null||t.disconnect()):this.srcElement.removeChild(n(this,h).srcElement),o(this,le,It).call(this,!1),o(this,pt,Ws).call(this))},ct=new WeakSet,Fs=function(e,t){if(!n(this,h)||!n(this,p))return;const s=t?"treeselect-list--top-to-body":"treeselect-list--top",i=t?"treeselect-list--bottom-to-body":"treeselect-list--bottom";e?(n(this,h).srcElement.classList.add(s),n(this,h).srcElement.classList.remove(i),n(this,p).srcElement.classList.add("treeselect-input--top"),n(this,p).srcElement.classList.remove("treeselect-input--bottom")):(n(this,h).srcElement.classList.remove(s),n(this,h).srcElement.classList.add(i),n(this,p).srcElement.classList.remove("treeselect-input--top"),n(this,p).srcElement.classList.add("treeselect-input--bottom"))},W=new WeakSet,bt=function(e){!n(this,p)||!n(this,h)||(e?(n(this,p).srcElement.classList.add("treeselect-input--focused"),n(this,h).srcElement.classList.add("treeselect-list--focused")):(n(this,p).srcElement.classList.remove("treeselect-input--focused"),n(this,h).srcElement.classList.remove("treeselect-list--focused")))},le=new WeakSet,It=function(e){var t,s,i,a;e?(t=n(this,p))==null||t.srcElement.classList.add("treeselect-input--opened"):(s=n(this,p))==null||s.srcElement.classList.remove("treeselect-input--opened"),this.staticList?(i=n(this,h))==null||i.srcElement.classList.add("treeselect-list--static"):(a=n(this,h))==null||a.srcElement.classList.remove("treeselect-list--static")},R=new WeakSet,gt=function(e){!n(this,A)||!n(this,T)||!n(this,S)||!n(this,V)||((!this.alwaysOpen||e)&&(window.removeEventListener("scroll",n(this,A),!0),window.removeEventListener("resize",n(this,T))),document.removeEventListener("mousedown",n(this,S),!0),document.removeEventListener("focus",n(this,S),!0),window.removeEventListener("blur",n(this,V)))},dt=new WeakSet,js=function(){var t,s,i;const e=(t=n(this,h))==null?void 0:t.isLastFocusedElementExist();this.saveScrollPosition&&e?(s=n(this,h))==null||s.srcElement.scroll(0,n(this,Q)):(i=n(this,h))==null||i.focusFirstListElement()},ne=new WeakSet,Pt=function(){var e;(e=this.srcElement)==null||e.dispatchEvent(new CustomEvent("input",{detail:this.value})),this.inputCallback&&this.inputCallback(this.value)},ht=new WeakSet,qs=function(){var e;(e=this.srcElement)==null||e.dispatchEvent(new CustomEvent("name-change",{detail:this.selectedName})),this.nameChangeCallback&&this.nameChangeCallback(this.selectedName)},ut=new WeakSet,$s=function(){var e;this.alwaysOpen||((e=this.srcElement)==null||e.dispatchEvent(new CustomEvent("open",{detail:this.value})),this.openCallback&&this.openCallback(this.value))},pt=new WeakSet,Ws=function(){var e;this.alwaysOpen||((e=this.srcElement)==null||e.dispatchEvent(new CustomEvent("close",{detail:this.value})),this.closeCallback&&this.closeCallback(this.value))},mt=new WeakSet,Rs=function(e){var s;const t=(e==null?void 0:e.trim())??"";(s=this.srcElement)==null||s.dispatchEvent(new CustomEvent("search",{detail:t})),this.searchCallback&&this.searchCallback(t)},wi});
