const limit = 300;
let count = document.querySelectorAll(".dynamic_fields_js").length + 1;
function addRow() {
    window.addEventListener(
        "keydown",
        function (e) {
            if (e.keyIdentifier == "U+000A" || e.keyIdentifier == "Enter" || e.keyCode == 13) {
                if (e.target.nodeName === "INPUT" && e.target.type !== "textarea") {
                    e.preventDefault();
                    return false;
                }
            }
        },
        true
    );
    const dynamic_filed = document.querySelector(".dynamic-field");
    const uniqueId = "new_" + Date.now();
    const input_html = `
                     <div class="d-flex justify-content-between dynamic_fields_js mt-3">
                     <input type="hidden" name="attribute_options[${uniqueId}][id]" value="">
                     <input type="text" name="attribute_options[${uniqueId}][name]" class="form-control form-control-sm duplicate-validation variant-option">  <button class="border-0 bg-white ms-2" onclick="deleteRow(this)">
                           <i class="fa-regular fa-trash-can fs-20 text-danger"></i>
                        </button>
                   </div>`;
    if (count > limit) return false;
    count += 1;
    dynamic_filed.insertAdjacentHTML("beforeend", input_html);
}
function deleteRow(obj) {
    obj.closest(".dynamic_fields_js").remove();
    count -= 1;
    $(".duplicate-validation").each(function () {
        $(this).trigger("keyup");
        return false;
    });
    addbutton.disabled = false;
    $("#add-btn").removeClass("btn-disabled");
}
