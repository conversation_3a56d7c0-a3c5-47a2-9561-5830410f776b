@import "variables";
// sumoselect css
.SumoSelect {
    > .CaptionCont {
        > label {
            > i {
                background-image: url("../images/down_black.svg") !important;
                background-position: center center;
                width: 16px;
                height: 16px;
                display: block;
                position: absolute;
                top: 2px !important;
                left: -2px !important;
                right: 0;
                bottom: 0;
                margin: auto;
                background-repeat: no-repeat;
                opacity: 1 !important;
            }
        }
    }
}

.SumoSelect {
    > .CaptionCont {
        color: $black !important;
        font-weight: 400;
        font-size: 0.875rem;
        height: 2.25rem !important;
        line-height: 1rem;
        &:hover {
            border: 1px solid $blue !important;
        }
        &:focus {
            border: 1px solid $blue !important;
        }
    }
}
.SumoSelect.open > .optWrapper {
    top: 36px !important;
    display: block;
}
#AttributeSet {
    overflow: hidden;
}
#Category {
    overflow: hidden;
}

// tagger css
.tox-statusbar {
    display: none !important;
}
.tagger + .tagger {
    margin-top: 10px;
}
.custom-border-css {
    border: 1px solid $gains-boro !important;
    background: $white-smoke;
    margin-right: 1px;
    border-radius: 0px;
}
.tox {
    .tox-statusbar {
        align-items: center;
        background-color: #fff;
        border-top: 1px solid #e3e3e3;
        color: rgba(34, 47, 62, 0.7);
        display: flex;
        flex: 0 0 auto;
        font-size: 14px;
        font-weight: 400;
        height: 25px;
        overflow: hidden;
        padding: 2px 8px;
        position: relative;
        display: block !important;
        text-transform: none;
    }
    .tox-edit-area__iframe {
        background-color: #f8f8f8 !important;
        border: 0;
        box-sizing: border-box;
        flex: 1;
        height: 100%;
        position: absolute;
        width: 100%;
    }
    .tox-notification--in {
        opacity: 0 !important;
        height: 0px !important;
        width: 0px !important;
        display: none;
    }
}
.tox-statusbar__branding {
    display: none !important;
}
.tox-notification--warning {
    background-color: #fff5cc;
    border-color: #fff0b3;
    height: 0px !important;
    width: 0px !important;
    color: #222f3e;
}
.tox-notifications-container {
    position: absolute;
    left: 1198px !important;
    top: 433px !important;
    max-height: 383px;
}

// date picker
.active2 {
    border: 2px solid #2c4bff !important;
    opacity: 1;
}

.gj-textbox-md {
    background: #f8f8f8;
    border: 1px solid #e5e5e5;
    display: block;
    font-family: Roboto, sans-serif;
    font-size: 0.875rem;
    line-height: 0;
    padding: 4px 12px;
    width: 100%;
    text-align: left;
    color: #000000;
}

.gj-datepicker-md [role="right-icon"] {
    top: 7px;
}

.comboTreeArrowBtn {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background-image: url("../images/down_black.svg");
    background-repeat: no-repeat;
    background-position: right 8px center !important;
    background-color: #fff !important;
    background-size: 20px;
    right: 1px !important;
    bottom: 1px !important;
    top: 1px !important;
    border: 0 !important;
}
.comboTreeArrowBtn:hover {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background-image: url("../images/down_black.svg") !important;
    background-repeat: no-repeat !important;
    background-color: #fff !important;
    background-position: right 8px center !important;
    background-size: 20px;
    border: 0 !important;
}
.comboTreeArrowBtn:active {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background-image: url("../images/down_black.svg") !important;
    background-repeat: no-repeat !important;
    background-color: #fff !important;
    background-position: right 8px center !important;
    background-size: 20px;
    border: 0 !important;
}
.mdi:before,
.mdi-set {
    display: inline-block;
    font: normal normal normal 26px/1 "Material Design Icons" !important;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}
.mdi-chevron-down-circle-outline::before {
    content: "\F0B27";
    font-size: 20px !important;
}
.mdi-chevron-right-circle-outline::before {
    content: "\F0B2B";
    font-size: 20px !important;
}

// js tree category
.jsTree {
    width: 100%;
}
.jsTree .itemParent {
    /* div. more down under */
    transition: all 0.3s ease-in;
    padding: 6px 0px;
    display: flex;
}
.jsTree .itemParent:hover {
    background-color: #d1d1d1;
}
.jsTree .itemParent .contenteditable {
    margin: 0px;
    flex-grow: 1;
}
.jsTree .itemParent p {
    margin: 0px 6px;
    max-width: 300px;
    padding: 2px 0px;
}
#color-change-delete {
    fill: red;
}

.jsTree .itemParent .afterIcon {
    display: inline-block;
    flex-shrink: 0;
    width: 19px;
    height: 19px;
    margin: 0px 4px;
    background: url("../../../img/tree-images/delete.svg");
    background-size: 12px 12px;
    background-repeat: no-repeat;
    background-position: center center;
    cursor: pointer;
    transition: opacity 0.3s ease-out;
    background-size: 18px 16px;
    opacity: 1;
}
.jsTree .itemParent:hover .afterIcon {
    opacity: 1;
}
.jsTree .itemParent .afterIconEdit {
    display: inline-block;
    flex-shrink: 0;
    width: 19px;
    height: 19px;
    margin: 0px 4px;
    background: url("../../../img/tree-images/edit.svg");
    background-size: 12px 12px;
    background-repeat: no-repeat;
    background-position: center center;
    cursor: pointer;
    transition: opacity 0.3s ease-out;
    background-size: 18px 25px;
    opacity: 1;
}
.jsTree .childGroup {
    /* ul */
    padding: 0px 0px 0px 12px;
    margin: 0;
}
.jsTree .item {
    /* li */
    list-style: none;
    padding: 0;
    margin: 0;
    transition: all 0.3s ease-in;
}
.jsTree .itemParent .preIcon {
    display: inline-block;
    flex-shrink: 0;
    width: 19px;
    height: 19px;
    margin: 0px 4px;
    background-size: 14px 14px !important;
    background-repeat: no-repeat !important;
    background-position: center center !important;
}
.jsTree .itemParent .preIcon.arrowDown {
    cursor: pointer;
    background: url("../../../img/tree-images/arrowdown-black.svg");
    transition: transform 0.3s ease-out;
    margin-top: 4px;
}
.jsTree .itemParent .preIcon.arrowDown.arrowRotate {
    transform: rotate(-90deg);
}
.jsTreeContextMenu {
    width: max-content;
    display: none;
    position: fixed;
    border-radius: 1px;
    overflow: hidden;
    background: white;
    border: 1px solid #106fab;
    box-sizing: border-box;
}
.jsTreeContextMenu p {
    margin: 0;
    padding: 4px 8px;
    transition: all 0.3s ease-in;
    background: white;
}
.jsTreeContextMenu p:hover {
    background: #eee;
}
.firstTree {
    padding: 0px 0px 0px 18px;
}

// selectize input element css
.selectize-input {
    height: 36px !important;
    border: 1px solid $gains-boro !important;
    padding: 0px 12px !important;
    font-size: 0.875rem !important;
    color: $black;
    font-weight: 400;
    line-height: 37px !important;
    background: $white-smoke !important;
}

.selectize-control.single {
    .selectize-input {
        &:after {
            content: " ";
            display: block;
            position: absolute;
            top: 50%;
            right: 12px !important;
            margin-top: -3px;
            width: 0;
            height: 0;
            border-style: solid;
            border-width: 6px 6px 0 6px !important;
            border-color: $black transparent transparent transparent !important;
        }
    }
}

$color_1: #6c757d;
$color_2: #0d6efd;
$color_3: #fff;
$color_4: #0a58ca;
$font-family_1: "Roboto", sans-serif;
$background-color_1: #fff;
$background-color_2: #0d6efd;
$background-color_3: #e9ecef;
$border-color_1: #dee2e6;
$border-color_2: #0d6efd;

.pagination-container {
    display: flex;
    padding-left: 0;
    button {
        &:disabled {
            color: $color_1;
            pointer-events: none;
            background-color: $background-color_1;
            border-color: $border-color_1;
            border-top-left-radius: 0.25rem;
            border-bottom-left-radius: 0.25rem;
            border: 1px solid #dee2e6;
        }
        position: relative;
        color: $color_2;
        text-decoration: none;
        background-color: $background-color_1;
        border: 1px solid #dee2e6;
        transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out,
            box-shadow 0.15s ease-in-out;
        font-family: $font-family_1;
        padding: 0.375rem 0.75rem;
        display: block;
        &:hover {
            z-index: 2;
            color: $color_4;
            background-color: $background-color_3;
            border-color: $border-color_1;
        }
    }
    span.numbered-page {
        position: relative;
        color: $color_2;
        text-decoration: none;
        background-color: $background-color_1;
        border: 1px solid #dee2e6;
        transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out,
            box-shadow 0.15s ease-in-out;
        font-family: $font-family_1;
        padding: 0.375rem 0.75rem;
        display: block;
        cursor: pointer;
        &:hover {
            z-index: 2;
            color: $color_4;
            background-color: $background-color_3;
            border-color: $border-color_1;
        }
    }
    span.numbered-page.active {
        z-index: 3;
        color: $color_3;
        background-color: $background-color_2;
        border-color: $border-color_2;
    }
}
.list-group {
    .list-group-item {
        &[aria-disabled="true"] {
            cursor: not-allowed;
            background: #e6e6e6;
            color: #6c757d;
            border: 1px solid #e6e6e6;
            &:active {
                pointer-events: none;
            }
            &:focus {
                pointer-events: none;
            }
        }
    }
}
.update-store-btn.disabled {
    cursor: not-allowed;
    #Update-btn[aria-disabled="true"] {
        background-color: #e6e6e6;
        border: 1px solid #e6e6e6;
        pointer-events: none;
        color: #6c757d;
    }
}
.import_tabs {
    border-bottom: 1px solid #dee2e6 !important;
    .tab-list {
        display: flex;
        list-style: none;
        font-size: 18px;
        padding: 0;
        height: 100%;
        margin: 0;
    }
}
.tabs {
    width: auto;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    padding: 0.5rem 1.3rem;
    font-weight: 400;
    font-size: 0.875rem;
    cursor: pointer;
}
.tabs.active-tabs {
    color: #2c4bff;
    font-weight: 600;
}
.active-tabs::before {
    content: "";
    display: block;
    position: absolute;
    bottom: -4px;
    left: 50%;
    transform: translateX(-50%);
    width: calc(100% + 2px);
    height: 4px;
    background: #2c4bff;
}

.content {
    display: none;
    padding: 10px;
}

.active-content {
    display: flex;
}
.succesfully-mapped {
    border: 1px solid #ddd;
    border-left: 8px solid #28a745;
}
span.treeselect-input__clear {
    position: absolute;
    bottom: -35px;
    display: none;
    svg {
        stroke: #c5c7cb;
        width: 17px;
        min-width: 17px;
        height: 20px;
        fill: #000 !important;
    }
}
.treeselect-input__arrow svg {
    stroke: #7f7f7f;
    width: 25px;
    min-width: 25px;
    height: 25px;
    stroke-width: 0;
    fill: #000 !important;
}
.selected-item-container {
    display: block;
    margin: 10px 0 20px;
    .treeselect-input__tags-element {
        padding: 5px;
        position: relative;
        background: #e3e3e3;
        border: 1px solid #e3e3e3;
        font-size: 12px;
        color: #252525;
        font-weight: 400;
        align-items: center;
        width: auto;
        display: inline-flex;
        margin-right: 5px;
        margin-bottom: 5px;
        border-radius: 10px;
        &:hover {
            background: #e3e3e3;
            border: 1px solid #2c4bff;
        }
    }
}

.SumoSelect {
    select.disabled + .CaptionCont {
        background: #e6e6e6;
        color: #6c757d;
        border: 1px solid #e6e6e6;
        cursor: not-allowed;
        span {
            cursor: not-allowed;
            color: #6c757d;
        }
    }
}
.accordion-button {
    &:after {
        flex-shrink: 0;
        width: 1.25rem;
        height: 1.25rem;
        margin-left: auto;
        content: "+";
        background-image: none;
        background-repeat: no-repeat;
        background-size: 1.25rem;
        transition: transform 0.2s ease-in-out;
    }
    padding: 0.2rem 1.25rem;
}
.accordion-button:not(.collapsed) {
    &:after {
        background-image: none;
        transform: rotate(0deg);
        content: "-";
    }
}
.accordion-body {
    .table {
        tbody {
            tr {
                &:hover {
                    background-color: transparent !important;
                }
                td {
                    .inventory-quantity {
                        height: 1.5rem;
                    }
                }
            }
        }
    }
}

.bulkedit {
    font-family: $font-family_1;
    position: relative;
    margin-right: auto;
    margin-left: auto;
    .bulkedit-content {
        height: calc(100vh - 56px);
        overflow: auto;
    }
    .tox-tinymce {
        height: 230px !important;
        width: 200% !important;
    }
    .apimio-react-select__menu {
        z-index: 99999;
    }
    .apimio-react-select__value-container {
        height: 36px;
        padding: 2px 8px;
        align-items: flex-start;
        overflow: auto;
    }
    .apimio-react-select__control {
        height: 36px;
        padding: 0px 8px;
        align-items: flex-start;
    }

    table {
        width: 100%;
        padding: 1em;
        margin: 20px 0;
        border-collapse: collapse;
        box-shadow: none;

        thead {
            font-size: 16px;
            border: none;
            tr {
                th {
                    text-align: left;
                    padding: 8px;
                    color: #252525;
                    border-bottom: 0px solid #e6e6e6;
                    background-color: #fff;
                    &:first-child {
                        position: sticky;
                        left: 0px;
                        color: #252525;
                        z-index: 999;
                        background-color: #fff;
                    }
                    &:nth-child(2) {
                        position: sticky;
                        left: 60px;
                        color: #252525;
                        z-index: 999;
                        background-color: #fff;
                    }
                    &:nth-child(4) {
                        position: sticky;
                        left: 120px;
                        color: #252525;
                        z-index: 999;
                        background-color: #fff;
                    }
                }
            }
        }

        tr {
            height: 50px;
            border: none;
            border-bottom: 0px solid #e6e6e6 !important;
            background: #fff;
            td {
                text-align: left;
                padding: 0px 8px;
                border-bottom: 0px solid #e6e6e6;
                @media (max-width: 768px) {
                    padding: 0px 3px; // Adjust padding for mobile view
                }
                &:first-child {
                    position: sticky;
                    left: 0px;
                    background-color: #fff;
                    color: #252525;
                    z-index: 999;
                    background-color: #fff;
                }
                &:nth-child(2) {
                    position: sticky;
                    left: 60px;
                    color: #252525;
                    z-index: 999;
                    background-color: #fff;
                }
                &:nth-child(4) {
                    position: sticky;
                    left: 120px;
                    color: #252525;
                    z-index: 999;
                    background-color: #fff;
                }
                .inputgroup {
                    input {
                        border: 1px solid #e6e6e6;
                        background-color: #f2f2f2;
                        color: #252525;
                        line-height: 1.25rem;
                        height: 2.25rem;
                        padding: 0.5rem 0.75rem;
                        border-radius: 4px;
                        font-size: 0.875rem;
                        font-weight: 400;
                    }
                }
                input[type="text"],
                input[type="number"] {
                    border: 1px solid #e6e6e6;
                    background-color: #f2f2f2;
                    color: #252525;
                    line-height: 1.25rem;
                    height: 2.25rem;
                    padding: 0.5rem 0.75rem;
                    border-radius: 4px;
                    font-size: 0.875rem;
                    font-weight: 400;
                }
                .react-tagsinput-input {
                    width: 100px;
                }
                .css-3w2yfm-ValueContainer {
                    max-height: 48px;
                    overflow: auto;
                }
                .values-container {
                    .inline-block {
                        max-height: 48px;
                        overflow: auto;
                    }
                }
                .inputgroup {
                    display: flex;
                    justify-content: center;
                }
                textarea {
                    border: 1px solid #e6e6e6;
                    background-color: #f8f8f8;
                    color: #252525;
                    line-height: 1.25rem;

                    padding: 0.5rem 0.75rem;
                    border-radius: 4px;
                    font-size: 0.875rem;
                    font-weight: 400;
                }
                select {
                    border: 1px solid #e6e6e6;
                    background-color: #f8f8f8;
                    color: #252525;
                    line-height: 1.25rem;
                    height: 2.25rem;
                    padding: 0.5rem 0.75rem;
                    border-radius: 4px;
                    font-size: 0.875rem;
                    font-weight: 400;
                }
            }
        }
        tr.active {
            border: 1px solid #e6e6e6 !important;
            td {
                border-bottom: 0px !important;
            }
        }
        tr.variant-row {
            td:first-child {
                position: relative;
                left: 0px;
            }
            td:nth-child(2) {
                position: relative;
                left: 0;
            }
            td {
                left: 0px;
                padding: 0px;
                .variant-item {
                    border: 1px solid #e6e6e6;
                }
                input[type="text"],
                input[type="number"] {
                    background-color: #f8f8f8;
                    border: 1px solid #e6e6e6;
                    background-clip: padding-box;
                    -webkit-appearance: none;
                    -moz-appearance: none;
                    appearance: none;
                    border-radius: 0.25rem;
                    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
                    font-weight: 500;
                    font-size: 1rem;
                    font-weight: 400;
                    line-height: 1.5;
                }
                input[type="text"].weight-input,
                input[type="number"].weight-input {
                    border-top-left-radius: 0px;
                    border-bottom-left-radius: 0px;
                }
            }
            tr:nth-child(odd) {
                border: none;
                td {
                    text-align: left;
                    padding: 8px;
                    border-bottom: 0px solid #e6e6e6;

                    input[type="text"],
                    input[type="number"] {
                        background-color: #f8f8f8;
                        border: 1px solid #e6e6e6;
                        color: #252525;
                        line-height: 1.25rem;
                        height: 2.25rem;
                        padding: 0.5rem 0.75rem;
                        border-radius: 4px;
                        font-size: 0.875rem;
                        font-weight: 400;
                    }
                    .css-3w2yfm-ValueContainer {
                        max-height: 48px;
                        overflow: auto;
                    }
                    .values-container {
                        .inline-block {
                            max-height: 48px;
                            overflow: auto;
                        }
                    }
                    .inputgroup {
                        display: flex;
                        justify-content: center;
                    }
                    textarea {
                        border: 1px solid #e6e6e6;
                        background-color: #f8f8f8;
                        color: #252525;
                        line-height: 1.25rem;

                        padding: 0.5rem 0.75rem;
                        border-radius: 4px;
                        font-size: 0.875rem;
                        font-weight: 400;
                    }
                    select {
                        border: 1px solid #e6e6e6;
                        background-color: #f8f8f8;
                        color: #252525;
                        line-height: 1.25rem;
                        height: 2.25rem;
                        padding: 0.5rem 0.75rem;
                        border-radius: 4px;
                        font-size: 0.875rem;
                        font-weight: 400;
                    }
                }
            }
        }
    }
}
.variant-table {
    table {
        thead {
            border-bottom: 0px solid #ccc;
            th {
                border-bottom: 0px solid #ccc;
            }
        }
    }
}
.bulkedit table {
    .apimio-react-select__multi-value {
        background-color: #dce4f9;
    }
    .apimio-react-select__indicators {
        .apimio-react-select__indicator {
            padding: 0px 8px;
        }
        .apimio-react-select__clear-indicator {
            display: none;
        }
        .apimio-react-select__indicator-separator {
            display: none;
        }
    }
    tr.variant-row {
        table {
            margin-top: 0;
            thead {
                border-bottom: 0px solid #ccc !important;
                th {
                    background: #fff;

                    &:first-child {
                        position: relative;
                        left: 0px;
                        color: #252525;
                        z-index: 999;
                        background-color: #fff;
                    }
                    &:nth-child(2) {
                        position: relative;
                        left: 0px;
                        color: #252525;
                        z-index: 999;
                        background-color: #fff;
                    }
                    &:nth-child(3) {
                        position: relative;
                        left: 0px;
                        color: #252525;
                        z-index: 999;
                        background-color: #fff;
                    }
                    &:nth-child(4) {
                        position: relative;
                        left: 0px;
                        color: #252525;
                        z-index: 999;
                        background-color: #fff;
                    }
                }
            }
            tbody {
                tr:nth-child(odd) {
                    border: none;
                    background-color: #fff;
                    td {
                        text-align: left;
                        padding: 0 8px;
                        border-bottom: 0px solid #ccc;
                        &:first-child {
                            position: relative;
                            left: 0px;
                            background: #fff;
                            color: #6c757d;
                            z-index: 999;
                        }
                        &:nth-child(2) {
                            background-color: #fff;
                            color: #6c757d;
                            position: relative;
                            left: 0px;
                            z-index: 999;
                        }
                        &:nth-child(3) {
                            background-color: #fff;
                            color: #6c757d;
                            position: relative;
                            left: 0px;
                            z-index: 999;
                        }
                        &:nth-child(4) {
                            background-color: #fff;
                            color: #6c757d;
                            position: relative;
                            left: 0px;
                            z-index: 999;
                        }
                        input[type="text"],
                        input[type="number"] {
                            background-color: #f8f8f8;
                            border: 1px solid #e6e6e6;
                            color: #252525;
                            line-height: 1.25rem;
                            height: 2.25rem;
                            padding: 0.5rem 0.75rem;
                            border-radius: 4px;
                            font-size: 0.875rem;
                            font-weight: 400;
                        }
                    }
                }
                tr:nth-child(even) {
                    border: none;
                    background-color: #fff;
                    td {
                        text-align: left;
                        padding: 8px;
                        border-bottom: 0px solid #ccc;
                        &:first-child {
                            position: relative;
                            left: 0px;
                            background: #fff;
                            color: #222;
                            z-index: 999;
                        }
                        &:nth-child(2) {
                            background-color: #fff;
                            color: #222;
                            position: relative;
                            left: 0px;
                            z-index: 999;
                        }
                        &:nth-child(4) {
                            background-color: #fff;
                            color: #222;
                            position: relative;
                            left: 0px;
                            z-index: 999;
                        }
                        input[type="text"],
                        input[type="number"] {
                            background-color: #f8f8f8;
                            border: 1px solid #e6e6e6;
                            color: #222;
                            line-height: 1.25rem;
                            height: 2.25rem;
                            padding: 0.5rem 0.75rem;
                            border-radius: 4px;
                            font-size: 0.875rem;
                            font-weight: 400;
                        }
                    }
                }
            }
        }
    }
}
button {
    outline: 0 !important;
}
.loader {
    z-index: 9999;
}
.confirmation-modal {
    z-index: 9999;
}
select {
    optgroup {
        background: #ddddddee;
    }
    option {
        background: #fff;
    }
}
.multiplevalues {
    position: relative;

    .value-inner {
        display: none;
        position: absolute;
        top: 40px;
        left: 0;
        padding: 10px;
        border-radius: 5px;
        border: 1px solid #ccc;
        background: #fff;
        z-index: 999;
        input {
            margin-bottom: 10px;
        }
        .inputgroup {
            margin-bottom: 10px;
        }
    }
}
.multiplevalues.active {
    .value-inner {
        display: block;
    }
}

input[readonly] {
    color: #6c757d;
    background-color: #e9ecef;
    &:focus {
        border: none;
    }
}
.tox .tox-notification--warn,
.tox .tox-notification--warning {
    display: none;
}

.product-header.fixed-header {
    position: fixed;
    z-index: 5;
    background: white;
    top: 0;
    right: 0;
    padding: 0 20px;
    background-color: #fff;
    z-index: 999;
}

.tooltip-custom {
    .tooltip-content {
        display: none;
    }
    &:hover {
        .tooltip-content {
            display: block;
        }
    }
}
@keyframes fadeOutLeft {
    0% {
        width: 100%;
        opacity: 1;
    }
    50% {
        width: 50%;
        opacity: 0.5;
    }
    100% {
        width: 0%;
        opacity: 0;
    }
}
.mapping-item-isremoving {
    animation-duration: 0.3s;
    animation-fill-mode: both;
    animation-name: fadeOutLeft;
    z-index: 99;
    background-color: rgba(255, 5, 5, 0.2);
}
.merge-advance {
    .dropdown-menu {
        max-height: 300px;
        overflow: auto;
        border: 1px solid #ccc;
        border-top: none;
        transform: none !important;
        top: 36px !important;
    }
}
.close_row {
    transition: all 0.3s ease-in;
}

.inventory {
    .nav-tabs {
        .nav-link.active {
            background-color: #2c4bff;
            color: #fff;
        }
        .nav-link {
            color: #000;
        }
    }

    .store-variants {
        border: 1px solid #ced4da;
        border-radius: 4px;
    }
}
.tagger {
    .tagger {
        display: none;
    }
}

.SumoUnder {
    opacity: 0;
    position: absolute;
    z-index: -1;
}
.react-tagsinput {
    height: 36px;
    overflow-y: auto !important;
}
.react-tagsinput-remove {
    text-decoration: none;
}
.react-tagsinput-tag {
    background-color: #dce4f9 !important;
    border-radius: 2px;
    border: 1px solid #dce4f9 !important;
    color: #222 !important;
    display: inline-block;
    font-family: sans-serif;
    font-size: 13px;
    font-weight: 400;
    margin-bottom: 5px;
    margin-right: 5px;
    padding: 5px;
}

.background-image-css {
    cursor: pointer; /* Change cursor to pointer to indicate it's clickable */
}

.overlay {
    display: none; /* Hidden by default */
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6); /* Semi-transparent background */
    justify-content: center;
    align-items: center;
    z-index: 1000; /* Ensure it's above other content */
    padding-top: 2%;
    overflow: hidden;
}

.overlay-content {
    max-width: 60%;
    max-height: 90vh;
    position: relative;
    margin: 0 auto;
    border-radius: 10px;

    img {
        width: 100%;
        max-width: 100%;
        max-height: 90vh;
        object-fit: cover;
    }
    .close-btn {
        z-index: 999;
        position: absolute;
        top: 5px;
        right: 5px;
        background: rgba(0, 0, 0, 0.6);
        font-size: 30px;
        cursor: pointer;
        border: 1px solid rgba(74, 78, 77, 0.1);
        border-radius: 50%;
        padding: 5px;
        font-weight: 400;
        color: rgba(239, 240, 245, 1);
    }
}

.custom-list-grid {
    list-style: none;
    padding-left: 0;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px; /* Adjust the gap as needed */
}

.custom-list-grid li {
    margin-bottom: 10px; /* Adjust as needed */
}
.dropdown-menu {
    background: #ffffff;
    box-shadow: 0px 1px 20px rgba(0, 0, 0, 0.07);
    border: 0;
    transform: none !important;
    inset: unset !important;
}

.mapping-item {
    transition: all 0.3s ease-out;
    left: 0%;
}

.delete-row {
    left: 100%;
    transition: all 0.3s ease-out;
}

.loader {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.spinner {
    border: 4px solid rgba(0, 0, 0, 0.1);
    width: 36px;
    height: 36px;
    border-radius: 50%;
    border-left-color: #09f;
    animation: spin 1s ease infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}
.list-group.editing {
    overflow: hidden !important;
}

input[name="temp_name"]:disabled {
    cursor: not-allowed;
    background: #c7c7c7;
    color: #7a7a7a;
}
.bottom-fullimpo {
    bottom: 100% !important;
}
