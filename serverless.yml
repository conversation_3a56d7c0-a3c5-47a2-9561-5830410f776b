service: laravel-apimio

provider:
  name: aws
  region: us-east-2
  iam:
    #    role: arn:aws:iam::574295680953:role/laravel-us-east-2-lambdaRole-new8-new
    role:
      statements:
        - Effect: Allow
          Resource:
            - !GetAtt CacheTable.Arn
            - arn:aws:sqs:us-east-2:574295680953:UserQueue-*
            - "*"
          Action:
            - cloudwatch:GetMetricStatistics
            - ec2:CreateNetworkInterface
            - ec2:DeleteNetworkInterface
            - ec2:DescribeNetworkInterfaces
            - logs:CreateLogGroup
            - logs:CreateLogStream
            - logs:FilterLogEvents
            - logs:PutLogEvents
            - kms:Decrypt
            - secretsmanager:GetSecretValue
            - ssm:GetParameters
            - ssm:GetParameter
            - lambda:invokeFunction
            - s3:*
            - ses:*
            - sqs:*
            - dynamodb:*

      managedPolicies:
        - arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole


  environment:
    EXCEL_TEMP_PATH : /tmp
    APP_URL: https://phpstack-1322452-4837507.cloudwaysapps.com
    APP_NAME: Apimio
    APP_ENV: lambda
    APP_VERSION: 11.0
    DB_CONNECTION: mysql
    DB_HOST: apimio1.clsqeeg2qimy.us-east-2.rds.amazonaws.com
    DB_PORT: 3306
    DB_DATABASE: apimio_test
    DB_USERNAME: admin
    DB_PASSWORD: Apimiodevs321!
    BROADCAST_DRIVER: pusher
    CACHE_DRIVER: dynamodb
    QUEUE_CONNECTION: sqs
    SESSION_DRIVER: database
    SESSION_LIFETIME: 120
    MAIL_DRIVER: smtp
    MAIL_HOST: smtp.sendgrid.net
    MAIL_PORT: 587
    MAIL_USERNAME: apikey
    MAIL_PASSWORD: *********************************************************************
    MAIL_ENCRYPTION: tls
    MAIL_FROM_ADDRESS: <EMAIL>
    MAIL_FROM_NAME: "${APP_NAME}"
    AWS_BUCKET: apimio-staging
    AWS_MAIN_URL: https://apimio-staging.s3.amazonaws.com/
    SQS_PREFIX: https://sqs.us-east-2.amazonaws.com/574295680953/
    STRIPE_KEY: pk_test_aXkxuuqQz8k881VU3C1Jvehg
    STRIPE_SECRET: sk_test_bW7sPjB0LmgvOZkXaxFftHSm
    DYNAMODB_CACHE_TABLE: !Ref CacheTable
    DQL_URL: DLQ-fail-jobs.fifo
    LAMBDA_FUNCTION_NAME: laravel-apimio-dev-queueWorker
    CUSTOM_QUEUE_WORKER: lambda

package:
  patterns:
    - '!node_modules/**'
    - '!public/storage'
    - '!resources/assets/**'
    - '!storage/**'
    - '!tests/**'
    - '!vendor/bin/**'
    - 'bootstrap/cache/**'
    - 'app/Classes/**'

functions:
  queueWorker:
    handler: App\MyHandler
    runtime: php-82
    timeout: 840
    environment:
      SQS_QUEUE: "UserQueue-*"
      LOG_CHANNEL: "stderr"


resources:
  Resources:
    CacheTable:
      Type: AWS::DynamoDB::Table
      Properties:
        AttributeDefinitions: # only keys are defined here, other attributes are dynamic
          -   AttributeName: id # adds a mandatory id field
              AttributeType: S # the type of id is a string
        BillingMode: PAY_PER_REQUEST # billed for each request instead of paying for a constant capacity
        TimeToLiveSpecification: # deletes cache keys automatically based on a ttl field which contains a timestamp
          AttributeName: ttl
          Enabled: true
        KeySchema:
          -   AttributeName: id
              KeyType: HASH

plugins:
  - ./vendor/bref/bref
