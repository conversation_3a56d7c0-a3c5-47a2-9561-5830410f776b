<?php ?>
@extends('layouts.app_new')
@section('titles','Notification')
@section('content')
    {{--/****************************** new code start *********************/--}}
    <x-products.page-title name="{{trans('notification.page_title')}}" description="" links="false" button="false"></x-products.page-title>
    {{--product tabs start--}}
    <ul class="nav nav-pills border-bottom" id="myTab" role="tablist">
        <li class="nav-item" role="presentation">
            <button class="nav-link active" id="notifications-tab" data-bs-toggle="tab" data-bs-target="#notifications"
                    type="button" role="tab" aria-controls="notifications" aria-selected="true">Notifications
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="Activity_log-tab" data-bs-toggle="tab" data-bs-target="#Activity_log"
                    type="button" role="tab" aria-controls="profile" aria-selected="false">Activity log
            </button>
        </li>
    </ul>
    <div class="tab-content mt-2" id="myTabContent">
        {{-- notification--}}
        <div class="tab-pane fade show active" id="notifications" role="tabpanel" aria-labelledby="home-tab">
            {{--  new table view start   --}}
            <div class="row">
                @if($notifications->isNotEmpty())
                    <div class="col-12 col-xl-7">
                        <ul class="notification-css">
                            @foreach($notifications as $notification)
                                <li>
                                    <h3>{!! $notification->data['subject'] !!}</h3>
                                    <p class="mb-2">{!! $notification->data['body'] !!}</p>
                                    @if(isset($notification->data['batch']['batch_progress']))
                                        <div class="notification-queue" data-batchId="{{$notification->data['batch']['id']}}">
                                            <div>
                                                @if($notification->data['batch']['batch_progress'] != 100)
                                                    <a href="{{ url()->full() }}" class="text-decoration-none me-2 reload-page-js d-none" title="Reload the page to update response">
                                                        <i class="fa-sharp fa-solid fa-rotate-right fs-14"></i>
                                                    </a>
                                                @endif
                                                <span class="batch-status-js status {{ $notification->data['batch']['batch_progress'] != 100 ? 'status-publish' : 'status-success' }} notification-status-css">
                                                    {{ $notification->data['batch']['batch_progress'] != 100 ? 'Processing' : 'Processed' }}
                                                </span>

                                            </div>
                                            @if($notification->data['batch']['batch_progress'] != 100)
                                                <div class="main-progress-js d-flex align-items-center mt-3">
                                                    <div class="progress" style="height: 1rem;width: 60%;">
                                                        <div class="batch-progress-js progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: {{$notification->data['batch']['batch_progress']}}%"></div>
                                                    </div>
                                                    <div class="ms-3">
                                                        <span class="status-publish p-1 px-2 rounded fs-12 batch-progress-number-js">{{$notification->data['batch']['batch_progress']}}%</span>
                                                    </div>
                                                </div>
                                            @endif
                                        </div>
                                    @else
                                        <span class="status status-success notification-status-css">{{ $notification->data['batch']['id'] ?? 'info' }}</span>
                                    @endif
                                    <p class="clr-grey mt-3 fs-14">{{\Carbon\Carbon::parse($notification->created_at)->diffForHumans()}}</p>
                                    <hr>
                                </li>
                            @endforeach
                        </ul>
                    </div>
                @else
                    <x-general.empty-page description="{{trans('notification.page_empty')}}"/>
                @endif
            </div>
            {{--  new table view end   --}}
        </div>
        {{--  activity log--}}
        <div class="tab-pane fade" id="Activity_log" role="tabpanel" aria-labelledby="profile-tab">
            <div class="row">
                <div class="col-12 col-lg-12">
                
                    {{--  new table view start   --}}
                    @if(count($logs) > 0)
                        <table class="table">
                            <thead>
                            <tr>
                                <th scope="col">S#</th>
                                <th scope="col" style="width: 60%;padding: 0px 10px">Message</th>
                                <th scope="col">Type</th>
                                <th scope="col">Status</th>
                                <th scope="col">Date & Time</th>
                                <th scope="col" class="text-end">Action</th>
                            </tr>
                            </thead>
                            <tbody>
                            {{--                    <h3 class="text-capitalize clr-grey">today</h3>--}}
                            @foreach($logs as $log)
                                <tr>
                                    <td class="align-top pt-4">{{$loop->iteration}}</td>
                                    <td class="pt-1">{!! $log->description !!}</td>
                                    <td class="align-top pt-4">{{$log->type}}</td>
                                    <td class="align-top pt-4">
                                        @if($log->status == 'warning')
                                            <span class="status status-warning">{{$log->status}}</span>
                                        @elseif($log->status == 'success')
                                            <span class="status status-success">{{$log->status}}</span>
                                        @elseif($log->status == 'error')
                                            <span class="status status-danger">{{$log->status}}</span>
                                        @else
                                            <span class="status status-publish">{{$log->status}}</span>
                                        @endif
                                    </td>
                                    <td class="align-top pt-4" style="width: 15%;">{{$log->created_at->format('d M Y H:i a')}}</td>
                                    <td class="align-top pt-4 text-end" style="width: 25%;"><a class="btn-sm btn-primary me-1" href="{{$log->link}}">{{$log->link_text}}</a></td>
                                </tr>
                            @endforeach
                            </tbody>
                        </table>
                        {{-- {{ $logs->links() }} --}}
                    @else
                        <x-general.empty-page description="{{trans('notification.logs_page_empty')}}"/>
                    @endif
                </div>
            </div>
        </div>
    </div>

    </div>

    {{--product tabs end--}}
    {{--/****************************** new code end *********************/--}}
@endsection
@push('footer_scripts')
@endpush
