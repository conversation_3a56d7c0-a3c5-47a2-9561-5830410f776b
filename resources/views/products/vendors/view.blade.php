@extends('layouts.app_new')

@section('titles','Vendors')
@section('content')

    <div>
        <x-products.page-title name="{{trans('products_vendors.page_title')}}" description="{{trans('products_vendors.page_description')}}"
                               links="true" button="true">
            <x-slot name="addbutton">
                <a href="{{route('vendors.create')}}" style="width: 194px" id="add-vendors"
                   class="btn btn-primary ripplelink
                               float-lg-right float-md-right ">{{trans('products_vendors.add_vendor_btn')}}</a>
            </x-slot>
        </x-products.page-title>
        <div class="row">
            <div class="col-12 col-md-12 col-lg-12 col-xl-3">
                @if(count($data["vendor"]) > 0)
                    <x-general.search-bar placeholder="{{trans('products_vendors.search_placeholder')}}"/>
                @endif
            </div>
        </div>
        <div >
            <div >
                <div class="row">
                    <div>
                        @if(count($data['vendor']) > 0)
                            <div class="table-responsive">
                                <table class="table table-borderless">
                                    <caption style="visibility: hidden"></caption>
                                    <thead class="thead-light" style="height: 50px;">
                                    <tr>
                                        <th scope="col" class="Roboto bold text-dark w-25 border-radius-right border-radius-left">
                                            {{ __('Name') }}
                                        </th>
                                        <th class="Roboto bold text-dark disabled-sorting text-end w-25 border-radius-right border-radius-left">{{ __('Actions') }}
                                        </th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    @foreach($data['vendor'] as $vendor)
                                        <tr>
                                            <td style="padding: 0.75rem!important;"
                                                class="Roboto regular mt-3 text-left">
                                                {{__(substr($vendor->fname,0,52))}}
                                            </td>

                                            <td class="Roboto mt-3 text-end">
                                                <a href="{{route('vendors.edit', $vendor->id)}}"
                                                   class="pro_ven_edit mr-3">
                                                   <i class="fa-regular fa-pen-to-square fs-20" aria-hidden="true"></i>

                                                </a>

                                                <a href="#" data-id="{{$vendor->id}}" data-retailer-name=""
                                                   data-bs-toggle="modal" data-bs-target="#delete-modal" class="btn-delete">
                                                    <i class="fa-regular fa-trash-can fs-20 text-danger" aria-hidden="true"></i>

                                                </a>
                                            </td>
                                        </tr>
                                    @endforeach

                                    </tbody>
                                </table>
                                {!! $data['vendor']->appends($request->all())->links() !!}
                            </div>
                        @else
                            <x-general.empty-page description="{{trans('products_vendors.page_empty')}}"/>
                        @endif
                    </div>

                </div>
            </div>
        </div>
    </div>

    <!-- Modal DELETE-->
    <div class="modal fade" id="delete-modal" tabindex="-1" aria-labelledby="deleteModalLabel"
         aria-hidden="true" >
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title Poppins semibold" id="exampleModalLabel">{{trans('products_vendors.modal_title')}}</h5>
                    <button type="button" class="close" data-bs-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <p class="Roboto regular">{{trans('products_vendors.modal_description')}}
{{--                        <strong><span id="name">{{__('name')}}</span></strong>--}}
                    </p>

                    <div class="modal-footer p-0">
                        <button type="button" data-bs-dismiss="modal" id="delete-cancel-btn"
                                class="btn btn-dark-tertiary float-left shadow"
                                style="width: 120px;">
                            {{trans('products_vendors.cancel_btn')}}
                        </button>
                        <form action="#" id="delete-vendor" method="post">
                            @csrf
                            @method('DELETE')
                            <button id="pro_ven_del_btn" class="btn btn-danger ripplelink shadow"
                                    onclick="del()">{{trans('products_vendors.delete_btn')}}</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
@push('footer_scripts')
    <script type="text/javascript">
        let attrid;
        let attrname;

        $(".btn-delete").click(function () {
            attrid = $(this).attr('data-id');
            attrname = $(this).attr('data-retailer-name');
            document.getElementById('name').innerHTML = attrname;
        });

        function del() {
            var form = document.getElementById('delete-vendor');
            form.setAttribute('action', 'vendors/' + attrid);
            form.submit();
        }
    </script>

    <script type="text/javascript">
        @error('name')
        $('#add-modal').modal('show')
        @enderror
    </script>
@endpush
