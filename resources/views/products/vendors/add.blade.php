<?php ?>
@extends('layouts.app_new')

@if(Request::segment(4) == 'edit')
    @section('titles','Edit Vendor')
@else
    @section('titles','Add Vendors')
@endif
@section('content')

    <div>
        <x-products.add-page-title name="{{trans('products_vendors.page_title')}}"  type="false" :routes="route('vendors.index')" />


       
        <div class="row">
            <div class="col-12 col-md-9 col-lg-9 col-xl-6">
                <form id="pro_ven_create_form" class="formStyle" action="{{isset($vendor) ? route('vendors.update',$vendor->id) : route('vendors.store')}}"

                      method="POST">
                    @csrf
                    @if(isset($vendor))
                        @method('PUT')
                        <input type="hidden" name="id" value="{{$vendor->id}}">
                    @endif
                    <div class="form-group mt-4">
                        <label for="fname">{{trans('products_vendors_create.vendor_name')}}&nbsp;<span class="text-danger-light">*</span></label>
                        <input type="text" class="form-control  @error('fname') is-invalid @enderror" id="fname"
                               name="fname" value="{{isset($vendor) ? $vendor->fname : old('fname')}}" autofocus>
                        @error('fname')
                        <span class="text-danger">
                        <small>{{$message}}</small>
                        </span>
                        @enderror
                    </div>
                    <div class="form-group mb-4 mt-40">

                        <div class="d-flex justify-content-end">
                                <a href="{{route('vendors.index')}}" class="btn btn-outline-danger">
                                    {{trans('products_vendors_create.cancel_btn')}}
                                </a>

                                <button type="submit" id="pro_ven_create_btn"
                                        class=" btn btn-primary ms-2">
                                    {{trans('products_vendors_create.save_btn')}}
                                </button>
                               
                            
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
@endsection
