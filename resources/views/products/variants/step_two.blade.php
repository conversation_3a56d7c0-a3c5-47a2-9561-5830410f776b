<?php ?>
@extends('layouts.app_new')
@if(request()->has('id'))
    @section('titles','Edit Products')
@else
    @section('titles','Update Variants')
@endif
@section('content')
    <x-products.edit-product-header :product="$product" :buttons="false" :version="$current_version"/>
    <x-products.edit-product-header-navs :product="$product" :version="$current_version"/>
{{--        <div>--}}
{{--            <div class="card border-radius shadow-none mb-4">--}}
{{--                <!--products navbar-->--}}


{{--                <!-- ////====== start step 3 of variants=======////-->--}}
{{--                <div>--}}
{{--                    <div id="next-2-body">--}}
{{--                        <form id="var_step2_form" method="post" action="{{route('variants.store')}}">--}}
{{--                            @csrf--}}

{{--                            <input name="product_id" value="{{$id}}" type="hidden">--}}
{{--                            <input type="hidden" value ="{{$current_version->id}}" name="version_id">--}}

{{--                            <div class="row mt-5">--}}
{{--                                <div class="col-12">--}}
{{--                                    <p class="m-0 Roboto bold text-dark">{{trans('products_variants_step2.step2_title')}}</p>--}}
{{--                                </div>--}}
{{--                                <div class="col-12 mt-5">--}}
{{--                                    <div class="accordion" id="accordionaddattr">--}}

{{--                                        @forelse($attributes as $key=>$attribute)--}}

{{--                                            <div class="card border-radius">--}}
{{--                                                <div class="card-header collapse-btn" id="headingcolor">--}}
{{--                                                    <h2 class="m-0">--}}
{{--                                                        <button class="btn btn-link btn-block text-left collapse-btn Roboto regular"--}}
{{--                                                                type="button" data-toggle="collapse" data-target="#collapsecolor" aria-expanded="true"--}}
{{--                                                                aria-controls="collapsecolor">--}}
{{--                                                            {{$attribute->name}}--}}
{{--                                                            <input type="hidden" name="attributes[{{$key}}][id]" value="{{$attribute->id}}" >--}}
{{--                                                            <input type="hidden" name="attributes[{{$key}}][name]" value="{{$attribute->name}}" >--}}
{{--                                                        </button>--}}
{{--                                                    </h2>--}}
{{--                                                </div>--}}

{{--                                                <div id="collapsecolor" class="collapse show" aria-labelledby="headingcolor" data-parent="#accordionaddattr">--}}
{{--                                                    <div class="card-body">--}}
{{--                                                        <div class="d-flex flex-row-reverse mb-3">--}}
{{--                                                            <div class="p-0">--}}
{{--                                                                <button type="button" class="py-0 px-1 m-0 btn blue-text-btn Roboto regular" style="height: 20px">{{__("Select All")}}</button>--}}
{{--                                                                <span class="black">{{__("|")}}</span>--}}
{{--                                                                <button type="button" class="py-0 px-1 m-0 btn blue-text-btn Roboto regular" style="height: 20px">{{__("Deselect All")}}</button>--}}
{{--                                                            </div>--}}
{{--                                                        </div>--}}
{{--                                                        <div class="table-responsive">--}}
{{--                                                            <table class="table table-borderless ">--}}
{{--                                                                <tbody>--}}
{{--                                                                <tr class="py-0">--}}
{{--                                                                    @foreach($attribute->attribute_options as $option)--}}
{{--                                                                        <td class="py-0 pl-4">--}}
{{--                                                                            <div class="form-check form-check-inline">--}}
{{--                                                                                <input class="form-check-input" type="checkbox" id="defaultCheck_{{$key}}_{{$loop->iteration}}" value="{{$option->name}}" name="attributes[{{$key}}][options][]" {{isset($selected_options[$option->attribute_id]) ? (in_array($option->name,$selected_options[$option->attribute_id]) ? 'checked' : '') : ''}}>--}}
{{--                                                                                <label class="form-check-label regular pl-1 text-lowercase" for="defaultCheck_{{$key}}_{{$loop->iteration}}">{{$option->name}} </label>--}}
{{--                                                                            </div>--}}
{{--                                                                        </td>--}}
{{--                                                                    @endforeach--}}
{{--                                                                </tr>--}}


{{--                                                                </tbody>--}}
{{--                                                            </table>--}}
{{--                                                        </div>--}}
{{--                                                    </div>--}}
{{--                                                </div>--}}
{{--                                            </div>--}}
{{--                                        @empty--}}
{{--                                            no result--}}
{{--                                        @endforelse--}}

{{--                                    </div>--}}
{{--                                </div>--}}
{{--                                <div class="col-12 text-right">--}}
{{--                                    <a href="{{route('variants.step.one', ["id" => $product->id, "version_id" => $current_version->id])}}"--}}
{{--                                       class="px-4 btn btn-outline-primary ripplelink hovereffect mr-3">--}}
{{--                                        {{trans('products_variants_step2.cancel_btn')}}--}}
{{--                                    </a>--}}
{{--                                    <button id="var_step2_btn" type="submit" class="btn btn-primary px-5 ripplelink">--}}
{{--                                        {{trans('products_variants_step1.next_btn')}}--}}
{{--                                    </button>--}}
{{--                                </div>--}}
{{--                            </div>--}}
{{--                        </form>--}}
{{--                    </div>--}}
{{--                </div>--}}

{{--                <!-- ////==================================== end step 3 of variants ======================================////-->--}}


{{--            </div>--}}
{{--        </div>--}}

    {{--  step 2 new code start  --}}
    <div class="row">
        <div class="col-12">
            <h3 class="mb-0">{{trans('products_variants_step2.step2_title')}}</h3>
            <p class="mb-0">{{trans('products_variants_step2.step2_description')}}</p>
            {{--  table start   --}}
            <form id="var_step2_form" method="post" action="{{route('variants.store')}}">
                @csrf
                <input name="product_id" value="{{$id}}" type="hidden">
                <input type="hidden" value ="{{$current_version->id}}" name="version_id">
                @forelse($attributes as $key=>$attribute)
                <table class="table my-3">
                    <thead>
                        <tr>
                            <th scope="col" colspan="5" class="text-white bg-black table-head-border">&nbsp; {{$attribute->name}}</th>
                            <input type="hidden" name="attributes[{{$key}}][id]" value="{{$attribute->id}}">
                            <input type="hidden" name="attributes[{{$key}}][name]" value="{{$attribute->name}}">
                        </tr>
                    </thead>
                    <tbody>
                        @php $optionCount = 0; @endphp
                        @foreach($attribute->attribute_options as $option)
                        @if ($optionCount % 5 == 0)
                        @if ($optionCount != 0)
                        </tr>
                        @endif
                        <tr class="mb-2">
                            @endif
                            <td class="text-capitalize col-3">
                                <div style="display: flex; align-items: center; overflow: hidden;">
                                    <input type="checkbox" id="defaultCheck_{{$key}}_{{$loop->iteration}}" value="{{$option->name}}"
                                        name="attributes[{{$key}}][options][]" 
                                        {{isset($selected_options[$option->attribute_id]) ? (in_array($option->name, $selected_options[$option->attribute_id]) ? 'checked' : '') : ''}}
                                        style="{{isset($selected_options[$option->attribute_id]) ? (in_array($option->name, $selected_options[$option->attribute_id]) ? 'pointer-events: none; opacity:0.7' : '') : ''}}"
                                        class="form-check-input mt-0">
                                    <span style="white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">&nbsp; {{$option->name}}</span>
                                </div>
                            </td>

                            @php $optionCount++; @endphp
                            @endforeach
                            @if ($optionCount % 4 != 0)
                        </tr>
                        @endif
                    </tbody>
                </table>

                @empty
                    no result
                @endforelse
                {{-- table end   --}}
                <div class="col-12 d-flex justify-content-end">
                    <a href="{{route('variants.step.one', ["id" => $product->id, "version_id" => $current_version->id])}}"
                       class="btn btn-outline-primary mr-3">
                        {{trans('products_variants_step2.cancel_btn')}}
                    </a>
                    @can('create-product', \App\Models\Product\Variant::query())
                    <button id="var_step2_btn" type="submit" class="btn btn-primary ms-3">
                        {{trans('products_variants_step1.next_btn')}}
                    </button>
                        @else
                        <a href="javascript:void(0)"
                            id="disabled-button"
                            class="btn btn-primary ms-3 float-lg-right float-md-right "
                            data-bs-toggle="tooltip"
                            data-bs-placement="top"
                            disabled
                            title="Please upgrade to access this feature">
                            {{trans('products_variants_step1.next_btn')}}
                        </a>
                        @endcan
                </div>
            </form>
        </div>
    </div>
    {{--  step 2 new code end  --}}


@endsection
@push('footer_scripts')


@endpush
