@if(count($data["attribute"]) > 0)
<div class="row">
    <div class="col-12 col-md-6 col-xl-3">
        <x-general.search-bar placeholder="{{trans('products_attributes.search_placeholder')}}"/>
    </div>
</div>
<div class="row mt-2">
    <div class="col-12">
        <table class="table table-responsive" >
            <thead>
            <tr>
                <th scope="col">{{ __('Name') }}</th>
                <th scope="col">{{ __('Attribute Type') }}</th>
                <th scope="col">{{ __('Attribute Set') }}</th>
                <th scope="col">{{ __('Is Required') }}</th>
                <th class="text-end">{{ __('Actions') }}</th>
            </tr>
            </thead>
            <tbody>
            @foreach($data["attribute"] as $attribute)
            @if(!($attribute->handle === 'quantity'  && $attribute->is_default === 1))
                <tr>
                    <td>
                        {{__(substr($attribute->name,0,50))}}
                    </td>
                    <td>{{__($attribute->attribute_type->id == 4 ? 'List' : $attribute->attribute_type->name)}}</td>
                    <td>
                        @foreach($attribute->families as $family)
                            {{$family->name}},
                            @if($loop->iteration == 5)
                                @break
                            @endif
                        @endforeach
                    </td>
{{--                                <td>{{ isset(json_decode($attribute->rules,true)['required']) ? "Yes" : "No" }}</td>--}}
                    <td>
                        <div class="form-check form-switch">
                        @if($attribute->handle === 'product_name')
                            <input type="checkbox"
                                   class="form-check-input save-attribute-rules attribute-rule-checkbox"
                                   disabled
                                   data-attribute-id="{{ $attribute->id }}"
                                   name="attribute_rules[{{ $attribute->id }}][required]"
                                {{ (isset(json_decode($attribute->rules, true)['required']) ?  (json_decode($attribute->rules, true)['required'] == 1 ? 'checked' : '' ) : "")}}>
{{--                                        <label class="form-check-label">{{ __('Required') }}</label>--}}
                        @else
                        <input type="checkbox"
                                   class="form-check-input save-attribute-rules attribute-rule-checkbox"

                                   data-attribute-id="{{ $attribute->id }}"
                                   name="attribute_rules[{{ $attribute->id }}][required]"
                                {{ (isset(json_decode($attribute->rules, true)['required']) ?  (json_decode($attribute->rules, true)['required'] == 1 ? 'checked' : '' ) : "")}}>
{{--                                        <label class="form-check-label">{{ __('Required') }}</label>--}}
                        @endif
                        </div>
                    </td>
                    <td class="text-end">
                        @if(!$attribute->is_default)
                            <a href="{{route('attributes.edit',$attribute->id)}}"
                               class="pro_attr_edit mr-3 edit-btn text-decoration-none">
                                <i class="fa-regular fa-pen-to-square fs-20"></i>
                            </a>

                            <a href="#" id="modal" data-id="{{$attribute->id}}"
                               data-retailer-name=""
                               data-bs-toggle="modal" data-bs-target="#delete-modal-{{$attribute->id}}" class="btn-delete text-decoration-none">
                                <i class="fa-regular fa-trash-can fs-20 text-danger"></i>
                            </a>
                        @endif
                    </td>
                    <x-assets.delete-modal id="{{$attribute->id}}" text="Are you sure you want to delete this attribute?" button="Delete Attribute" title="Delete Attribute" url="{{route('attributes.destroy',$attribute->id)}}" type="attribute"/>
                </tr>
                @endif
            @endforeach
            </tbody>
        </table>


        {!! $data["attribute"]->appends($request->all())->links() !!}
    </div>
</div>
@else
<x-general.empty-page description="{{trans('products.empty_table_description')}}"/>
@endif
</div>
