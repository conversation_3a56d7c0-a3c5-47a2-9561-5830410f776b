<?php ?>
@extends('layouts.app_new')
@section('titles','Attributes')
@section('content')
    <div>
        <x-products.page-title name="{{trans('products_attributes.page_title')}}" description="{{trans('products_attributes.page_description')}}"
                               links="true" button="true">
            <x-slot name="addbutton">
                <a href="{{route('attributes.create')}}"
                   style="width: 194px" id="add-attribute"
                   class="btn btn-primary
                          float-lg-right float-md-right" id="add-attribute">
                    {{trans('products_attributes.add_attribute_btn')}}
                </a>
                @if($channel)
                @endif
            </x-slot>
        </x-products.page-title>

                 <!-- Tabs -->
            <ul class="nav nav-tabs">
                <li class="nav-item">
                    <a class="nav-link active" data-bs-toggle="tab" href="#attributes">Attributes</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" data-bs-toggle="tab" href="#variantAttributes">Variant Attributes</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" data-bs-toggle="tab" href="#organizationAttributes">Product Organization Attributes</a>
                </li>
            </ul>

            <!-- Tab Content -->
            <div class="tab-content">
                <div id="attributes" class=" tab-pane active">
                    <!-- Attributes Table -->
                    @include('products.attributes.partials.attributes_table', ['attributes' => $data['attribute']])

                <div id="variantAttributes" class=" tab-pane fade">
                    <!-- Variant Attributes Table -->
                    @include('products.attributes.partials.variant_attributes_table', ['variantSettings' => $variantSettings])
                </div>
                <div id="organizationAttributes" class=" tab-pane fade">
                    <!-- Variant Attributes Table -->
                    @include('products.attributes.partials.organization_attributes_table', ['specialAttributeSettings' => $specialAttributeSettings])
                </div>
            </div>

        </div>
@endsection
@push('footer_scripts')

    <script>
        $(document).ready(function () {
            $('#AssignCategory').SumoSelect({
                placeholder: 'Select Category',
                csvDispCount: 4,
                captionFormat: '{0} Selected',
                captionFormatAllSelected: 'All Options Selected',
                okCancelInMulti: true,
                isClickAwayOk: true,
                selectAll: true,
                search: true,
                searchText: 'Search here',
                noMatch: 'No matches for {0}',

            });
        });
    </script>
    <script>
                $(document).ready(function () {
                    $('.save-attribute-rules').on('change', function () {
                        // alert('ok');
                        const attributeRules = [];
                            const attributeId = $(this).data('attribute-id');
                            const isChecked = $(this).prop('checked');
                            if (isChecked) {
                                attributeRules.push({ id: attributeId, required: 1 });
                            }else {
                                attributeRules.push({ id: attributeId, required: 0 });
                            }

                        $.ajax({
                            url: '{{ route('update.attributes.rules') }}',
                            method: 'post',
                            data: {
                                'attribute_rules': attributeRules,
                            },
                            success: function (data) {
                                console.log(data); console.log('data');
                            },
                            error: function (error) {
                                console.error(error);
                            }
                        });
                    });
                });
            </script>
        <script>
            $(document).ready(function () {
                $('.setting-toggle').on('change', function () {
                const settingKey = $(this).data('key');
                const isChecked = $(this).is(':checked') ? 1 : 0;

                $.ajax({
                    url: '{{ route('update.settings.toggle') }}',
                    type: 'POST',
                    data: {
                        '_token': '{{ csrf_token() }}',
                        'variant_settings': [{ key: settingKey, value: isChecked }]
                    },
                    success: function (response) {
                        console.log('Success:', response);
                    },
                    error: function (error) {
                        console.error('Error:', error);
                    }
                });
            });

            });
        </script>

@endpush
<!DOCTYPE html>

<head>
    <!-- Styles -->

    <style>
        .table-custom-spacing th.col-name,
        .table-custom-spacing td.col-name {
            width: 50%;
        }

        .table-custom-spacing th.col-is-required,
        .table-custom-spacing td.col-is-required {
            width: 30%;
        }

        .table-custom-spacing th.col-actions,
        .table-custom-spacing td.col-actions {
            width: 20%;
        }
    </style>
</head>

</html>
