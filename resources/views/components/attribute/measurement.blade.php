@php
use App\Classes\Unit;
@endphp

<div>
    {{--Weight options--}}
    @if($type == 'weight')
    @foreach (Unit::$WEIGHT_UNITS as $key => $unit_value)
        <option value="{{$key}}" {{isset($unit) ? ($unit == $key ? 'selected' : '') : ''}}>{{ Unit::formatUnitKey($key)}}</option>
    @endforeach

        {{--Volume options--}}
    @elseif($type == 'volume')
    @foreach (Unit::$VOLUME_UNITS as $key => $unit_value)
        <option value="{{$key}}" {{isset($unit) ? ($unit == $key ? 'selected' : '') : ''}}>{{ Unit::formatUnitKey($key)}}</option>
    @endforeach
        {{--Dimension options--}}
    @elseif($type == 'dimension')
        @foreach (Unit::$DIMENSION_UNITS as $key => $unit_value)
        <option value="{{$key}}" {{isset($unit) ? ($unit == $key ? 'selected' : '') : ''}}>{{ Unit::formatUnitKey($key)}}</option>
        @endforeach
    @endif
</div>
