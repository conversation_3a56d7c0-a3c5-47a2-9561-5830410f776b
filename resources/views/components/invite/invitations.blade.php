<div class="card border-radius shadow-none">
    <div class="col mt-4 mt-lg-0 ">
        <div class="card mb-0 border shadow-none border-radius" style="height: 225px">
            <div class="card-body">
                <div style="float: right; padding: 40px 50px 0 0">
                    <img src="{{asset('media/invite/invite-noti4834.png')}}" alt="mailbox">
                </div>
                <h4 class="Poppins mt-0 semibold text-red-50 text-uppercase">{{ trans_choice('apimio_dashboard.invites',$invites_list->count()) }}
                    <span class="bg-danger rounded-circle"
                          style="color: white; border-radius: 50%; padding: 0 7px; ">
                          {{$invites_list->count()??0}}
                          </span>
                </h4>
                {{--                List of Invite requests--}}
                @foreach($organization_list->take(2) as $invite_list)
                    <li class="Roboto regular p-1" style="text-align: left">
                        <span class="semibold">{{ trans('apimio_dashboard.organization') }}</span>
                        <span class="text-primary cursor-pointer"
                              onclick="location.href ='{{ route("retailer.show", $invite_list->id) }}'">
                            {{$invite_list->organization_sender_without_scope->name}}</span>
                    </li>
                @endforeach
                {{--                Show dots for longer list--}}
                @if($organization_list->count()>2)
                    <span class="p-4 bold ">{{trans('apimio_dashboard.seemore')}}</span>
                @endif
            </div>
            <div class="card-footer">
                <div class="col-12 p-0">
                    <a href="{{ route('retailer.index') }}"
                       class="btn  btn-outline-primary hovereffect ripplelink ">
                        {{trans('apimio_dashboard.vendors')}}
                        <span class="bg-danger "
                              style=" width: 180px; color: white; font-size:12px; border-radius: 50%; padding: 3px 6px">
                                             {{$invites_list->where("type","vendor")->count()}}
                                         </span>
                    </a>&nbsp
                    <a href="{{ route('vendor.index') }}"
                       class="btn  btn-outline-primary  hovereffect ripplelink ">
                        {{trans('apimio_dashboard.retailers')}}
                        <span class="bg-danger "
                              style="color: white; width: 180px; font-size:12px; border-radius: 50%; padding: 3px 6px">
                                               {{$invites_list->where("type","retailer")->count()}}
                                                 </span>

                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
