{{--
    @deprecated This file is deprecated and will be removed in the future.
--}}
<div class="col-6 col-md-2 col-lg-2">
    <div class="form-group">

        <label for="name_in_import">{{__("Apimio column")}}</label>
        <select name="nodes[data][{{$row_count}}][from][]" style="width: 182px"
                class="form-control apimio-column">
            <option value="" class="Poppins regular text-color">Select Column</option>
            <optgroup label="Default">
                @foreach($apimio_attributes as $ap_attr_key => $ap_attr)
                    @if(isset($row_node['from'][0]))
                        <option value="{{$ap_attr_key}}" {{($row_node['from'][0] == $ap_attr_key || $row_node['from'][0] == $ap_attr) ? 'selected' : null}} >{{$ap_attr}}</option>
                    @else
                        @if($row_count == 0)
                            <option value="{{$ap_attr_key}}" {{("sku" == $ap_attr_key) ? 'selected' : null}}>{{$ap_attr}}</option>
                        @else
                            <option value="{{$ap_attr_key}}">{{$ap_attr}}</option>
                        @endif
                    @endif
                @endforeach
            </optgroup>

            @foreach($heading_attributes as $family)

                <optgroup label="{{$family->name}}">
                    @foreach($family->attributes as $h_attribute)
                        @if(isset($row_node['from']))
                            <option value="{{$h_attribute->pivotId}}" {{($row_node['from'][0] == $h_attribute->pivotId || $row_node['from'][0] == $h_attribute->handle) ? 'selected' : null}} >{{$h_attribute->name}}</option>
                        @else
                            <option value="{{$h_attribute->pivotId}}">{{$h_attribute->name}}</option>
                        @endif
                    @endforeach
                </optgroup>

            @endforeach

        </select>
    </div>
</div>
