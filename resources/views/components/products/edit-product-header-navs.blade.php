<ul class="nav nav-pills mb-3" id="pills-tab" role="tablist">
   {{-- <li class="nav-item dropdown">
        <a class="nav-link dropdown-toggle Roboto bold imagescorecard {{(Request::segment(3) == 'edit')?'active':''}}" data-toggle="dropdown"
           href="#"
           role="button" aria-haspopup="true"
           aria-expanded="false">{{trans('products_edit.default_families')}}</a>
        <div class="dropdown-menu">
            @foreach($default_families as $family)
                <a id="{{ strtolower($family->name) }}-btn" class="Roboto bold dropdown-item general product_data imagescorecard"
                   href="{{ route("products.edit", ["id" => $product->id, "version_id" => $version->id]) }}#{{ strtolower($family->name) }}">
                    {{$family->name}}
                </a>
            @endforeach
        </div>
    </li>--}}
    <li class="nav-item" role="presentation">
        <a class="nav-link Roboto bold product_data imagescorecard {{(Request::segment(3) == 'edit')?'active':''}}"
           href="{{ route("products.edit", ["id" => $product->id, "version_id" => $version->id]) }}">{{trans('products_edit.general')}}</a>
    </li>
    <li class="nav-item" role="presentation">
        <a class="nav-link Roboto bold product_data imagescorecard {{(Request::segment(3) == 'seo')?'active':''}}"
           href="{{ route("seo.products.edit", ["id" => $product->id, "version_id" => $version->id]) }}">{{trans('products_edit.seo')}}</a>
    </li>
    @can('SubscriptionAccess', "inventory")
    <li class="nav-item" role="presentation">
        <a href="{{ route("products.inventory", ["product" => $product->id, "version_id" => $version->id]) }}"
           class="nav-link Roboto bold product_data {{(Request::segment(3) == 'inventory')?'active':''}}" >
            {{trans('products_edit.inventory')}}
        </a>
    </li>
    @endcan
    <li class="nav-item" role="presentation">
        <a class="nav-link Roboto bold product_data imagescorecard {{(Request::segment(3) == 'attribute-set')?'active':''}}"
           href="{{ route("products.attribute_set", ["id" => $product->id, "version_id" => $version->id]) }}">{{trans('products_edit.attribute_set')}}</a>
    </li>
    <li class="nav-item">
        <a  href="{{ route("products.media", ["id" => $product->id, "version_id" => $version->id]) }}"
            class="nav-link Roboto bold media-dropdown {{--product_data--}} {{(Request::segment(3) == 'media')?'active':''}}">
            {{trans('products_edit.media')}}
        </a>
    </li>
    <li class="nav-item">
        <a  href="{{ route("variants.step.three", ["id" => $product->id, "version_id" => $version->id]) }}"
            class="nav-link Roboto bold media-dropdown  {{ in_array(Route::currentRouteName(), ['variants.step.two', 'variants.step.three', 'variants.step.one'])?'active':''}}">
            {{trans('products_edit.variants')}}
        </a>
    </li>

    <li class="nav-item" role="presentation">
        <a href="{{ route("products.catalog", ["id" => $product->id, "version_id" => $version->id]) }}"
            class="nav-link Roboto bold product_data {{(Request::segment(3) == 'catalog')?'active':''}}" >
            {{trans('products_edit.catalog')}}
        </a>
    </li>
    <li class="nav-item" role="presentation">
        <a class="nav-link Roboto bold media-dropdown {{(Request::segment(3) == 'score')?'active':''}}" href="{{ route("products.score", ["id" => $product->id, "version_id" => $version->id]) }}">
            {{trans('products_edit.completeness_score')}} &nbsp;
        </a>
    </li>
    <li class="nav-item d-block d-xl-none" role="presentation">
        {{--dropdown with search--}}
        <div class="dropdown float-md-right">
            <button class="btn btn-outline dropdown-toggle" type="button"
                    id="dropdownMenuButton" data-toggle="dropdown" aria-expanded="false">
                {{ $version->name }}
            </button>
            <div class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                @foreach($versions as $version)
                    <a class="dropdown-item"
                       href="{{ route("products.edit", ["id" => $product->id, "version_id" => $version->id]) }}">{{ $version->name }}
                        @if( in_array($version->id, $product->versions()->pluck("versions.id")->toArray()))
                            <span class="text-success"><i class="fas fa-check"></i></span>
                        @endif
                    </a>
                @endforeach
            </div>
        </div>
    </li>
</ul>

@push("footer_scripts")
    <script>
        function switchtabs(hashId) {
            if(hashId === '#seo') {
                $("#general-tab").hide()
                $("#seo-tab").tab('show')
                $("#seo-tab").show()
            }
            else {
                $("#seo-tab").hide()
               // $("#general-tab").tab('show')
                $("#general-tab").show()
            }
        }

        let hashId = window.location.hash;
        switchtabs(hashId);

        $("#seo-btn").click(function () {
            switchtabs("#seo");
        });

        $("#general-btn").click(function () {
            switchtabs("#general");
        });

    </script>
@endpush
