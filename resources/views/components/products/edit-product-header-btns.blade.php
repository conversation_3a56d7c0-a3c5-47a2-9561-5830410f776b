<div class="d-flex justify-content-md-end align-items-center">

    @if($buttons)
    <a class="btn btn-outline-danger" id="delete_product_btn" data-bs-toggle="modal" data-bs-target="#delete-modal-{{$product->id}}"
        data-id="{{$product->id}}">
        {{trans('products_edit.delete_btn')}}
    </a>
    <button type="submit" id="pro_edit_pub_btn" class="btn btn-primary ms-2">
        {{trans('products_edit.save_btn')}}</button>
    @endif
    @if(isset($data['next_product_id']))
    <a href="{{route('products.edit', $data['next_product_id'])}}" class="btn btn-outline-primary pt-2 ms-2 arrow-btn">
        <i class="icon-arrow-left fs-20 "></i>
    </a>
    @endif
    @if(isset($data['last_product_id']))
    <a href="{{route('products.edit', $data['last_product_id'])}}" class="btn btn-outline-primary pt-2 ms-2 arrow-btn">
        <i class="icon-arrow-right fs-20"></i>
    </a>
    @endif
</div>
