<div class="col-12 col-xl-4">
    <div class="border rounded p-4">
        <h3 class="text-center">Media Scoring</h3>
        @php 
          $zero_images = $image_score['approve'] == 0 && $image_score['warning'] == 0 && $image_score['error'] == 0 ? true : false ; 
        @endphp
        <div class="{{ !$zero_images ? 'media-score-chart' : '' }} d-flex justify-content-center w-100">
            @if(!$zero_images)
                <canvas id="imageQualityScore" width="100" height="100"></canvas>
            @endif
        </div>
        <div class="d-flex flex-column flex-md-row justify-content-between mt-4">
            <div class="d-flex flex-column align-items-start align-items-md-center  justify-content-around">
                <span class="score-label score-label-success mt-4 mt-md-0"></span>
                <span class="score-label score-label-warning mt-4 mt-md-0"></span>
                <span class="score-label score-label-danger mt-4 mt-md-0"></span>
            </div>
            <div class="d-flex flex-column justify-content-sm-start justify-content-md-center mt-4 mt-md-0 ms-4">
                <h4 class="mb-0 fw-700">Good Images</h4>
                <p class="mb-0">These images match the required standard.</p>
                <h4 class="mb-0 fw-700 mt-1">Fair Images</h4>
                <p class="mb-0">These Images can be improved.</p>
                <h4 class=" mb-0 fw-700 mt-1">Bad Images</h4>
                <p class="mb-0">These images need immediate attention.</p>
            </div>
            <div class="d-flex flex-column align-items-start align-items-md-center justify-content-around mt-1 mt-md-0">
                <span class="status status-success mt-4 mt-md-0" >{{$image_score['approve']}}</span>
                <span class="status status-warning mt-4 mt-md-0" >{{$image_score['warning']}}</span>
                <span class="status status-danger mt-4 mt-md-0" >{{$image_score['error']}}</span>
            </div>
        </div>
    </div>
</div>

@push('footer_scripts')
{{-- chart js--}}
<script>
    const imageQualityScore = document.getElementById('imageQualityScore');
    //  image quality score
    new Chart(imageQualityScore, {
        type: 'doughnut',
        data: {
            labels: [
                'Good Images',
                'Fair Images',
                'Bad Images',
            ],
            datasets: [{
                data: [
                    @if($image_score['approve'] == 0 && $image_score['warning'] == 0 && $image_score['error'] == 0)
                        0,0,100
                    @else
                    {{$image_score['approve']}},
                    {{$image_score['warning']}},
                    {{$image_score['error'] }}
                    @endif
                ],
                backgroundColor: [
                    '#28A745',
                    '#FFC107',
                    '#DC3545',
                ],
                hoverOffset: 4
            }]
        },
        options: {
            cutout: 50,
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            }
        },
    });
</script>
@endpush
