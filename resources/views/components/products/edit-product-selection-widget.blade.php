<style>
    .click__btn{
        width: 22px;
        /*background-color: #2c4bff;*/
        text-align: center;
        cursor: pointer;
        border-radius: 4px;
        cursor: pointer;
        color: #2c4bff;
        font-weight: bold;
        text-decoration: underline;
        transition: width 0.25s ease-out;
        /*border: 1px solid #e1e1e1;*/
        position: relative;
        font-size: 1.1em;
        /*line-height: 0px;*/
    }
    .add_new_attribute.active{
        width: 100%;
        border-color: #e1e1e1;
        background: #fff;
        height: 38px;
    }


    .add_new_attribute.active input[type=text]{
        background: transparent;
        border-color: transparent;
        border: 0;
        padding: 0px;
        left: 10px;
        top: 0; bottom: 0;
        margin-left: 10px !important;
        margin: auto;
        width: 70%;
        animation: showEmailContent 0.2s ease-in-out;
        animation-fill-mode: forwards;
        transform-origin: center;
        opacity: 0;
    }
    .add_new_attribute.active input[type=text]:focus{
        outline: -webkit-focus-ring-color auto 0px;
    }
    .add_new_attribute.active .send__btn{
        width: 30%;
        height: 38px;
        color:#fff;
        right: 0px;
        cursor: pointer;
        top: 0; bottom: 0;
        margin: auto;
        background: #2c4bff;
        line-height: 3;
        border-radius: 0px;
        text-align: center;
        font-size: 0.85em;
        animation: showEmailBtn 0.2s ease-in-out;
        animation-delay: 0.25s;
        animation-fill-mode: forwards;
        transform-origin: center;
        opacity: 0;
    }
    @keyframes showEmailBtn {
        0% {
            transform: scale(0);
        }
        100% {
            opacity: 1;
            transform: scale(1);
        }
    }
    @keyframes showEmailContent {
        0% {
            transform: scale(0);
            width: 250px;
        }
        100% {
            opacity: 1;
            transform: scale(1);
            width: 250px;
        }
    }
    @keyframes showThankyouContent {
        0% {  color: transparent; }
        100% { color: #fff; }
    }
    .made-by{text-align: center;}
    .treeselect-input__tags-element {
        padding: 2px 5px 2px 5px;
        position: relative;
    }
    .treeselect-input__tags-remove{
        margin-left:5px;
        color:red;
    }
    .treeselect-list__group-container .treeselect-list__item--checked .treeselect-list__item-checkbox-container, .treeselect-list__item--partial-checked .treeselect-list__item-checkbox-container{
        background-color: #2C4BFF!important;
    }
    .treeselect-list__item--checked .treeselect-list__item-checkbox-container, .treeselect-list__item--partial-checked .treeselect-list__item-checkbox-container{
        background-color: #2C4BFF!important;
    }
</style>
@push("header_scripts")
    <link rel="stylesheet" href="{{ asset('treeSelectJs/treeselectjs.css') }}" />
@endpush
<div class="bg-white-smoke p-3 rounded">
    <div class="formStyle mb-2 mb-xl-3" data-value="brands">
        <label for="brand" class="ml-1">{{trans('products_edit.brands')}}
            <x-products.edit-product-attribute-status-dot :id="951357" />
        </label>
        {{-- {{dd($settingsToggle)}} --}}
        <select name="brand[][id]" id="brand"
                data-confirm-before-leave="true"
                @if(isset($settingsToggle['brand']) && $settingsToggle['brand'] != null)
                data-rules='{"required":"{{ $settingsToggle['brand'] }}"}'
                @endif

                data-status-id="#dot_951357"

                class="form-control bg-white btn-outlined brands @error('brand') is-invalid @enderror">
            <option value="">Select..</option>
            @foreach($brands as $brand)
                <option value="{{$brand->id}}"
                        @foreach($product->brands as $b)
                        {{($b->id == $brand->id)?'selected':''}}@endforeach
                        class="Poppins regular text-color">{{$brand->name}}</option>
            @endforeach
        </select>
    </div>


    <div class="formStyle mb-2 mb-xl-3" data-value="vendors">
        <label for="vendor" class="ml-1">{{trans('products_edit.vendor')}}
            <x-products.edit-product-attribute-status-dot :id="951358" />

        </label>

        <select name="vendor[][id]" id="vendor"
                data-confirm-before-leave="true"
               @if(isset($settingsToggle['vendor']) && $settingsToggle['vendor'] != null)

               data-rules='{"required":"{{ $settingsToggle['vendor'] }}"}'
               @endif

                data-status-id="#dot_951358"

                class="form-control bg-white btn-outlined vendors @error('vendor') is-invalid @enderror">
            <option value="">Select...</option>
            @foreach($vendors as $vendor)
                <option value="{{$vendor->id}}"
                        @foreach($product->inviteVendor as $v)
                        {{($v->id == $vendor->id)?'selected':''}}
                        @endforeach
                        class="Poppins regular text-color">{{ $vendor->fname }} {{ $vendor->lname }}</option>
            @endforeach
        </select>
    </div>
    <div class="formStyle mb-1 mb-xl-1 category_treeselect" data-value="category">

        <label for="category" class="ml-1">{{trans('products_edit.category')}}
            <x-products.edit-product-attribute-status-dot :id="55345345345345346" />
        </label>

        <div class="categoryList " >
        </div>
        <input id="category_id" 
        name="category" 
        type="hidden" 
        @if(isset($settingsToggle['category']) && $settingsToggle['category'] !=null) 
        data-rules='{"required":"{{ $settingsToggle['category'] }}"}' 
        @endif
         data-status-id="#dot_55345345345345346"
        

         value="{{ $selectedCategoryIds }}">
    </div>
    <div class="selected-item-container">
    <!-- Selected item will be displayed here -->
    </div>
    <div class="formStyle mb-2 mb-xl-3" data-value="channels">
        <label for="" class="ml-1 Roboto bold text-dark">{{trans('products_edit.store')}}</label>
        <select data-confirm-before-leave="true" placeholder="Select..." id="channels" multiple="multiple" name="channels[][id]"
                class="form-control sumoselect @error('channels') is-invalid @enderror">
            @foreach($channels as $channel)
                <option value="{{$channel->id}}"
                        class="Poppins regular text-color"
                @forelse($product->channels as $cat)
                    {{($cat->id == $channel->id)?'selected':''}}
                    @empty
                    {{$loop->first?'selected':""}}
                    @endforelse>
                    {{$channel->name}}
                </option>
            @endforeach
        </select>
    </div>
</div>

{{--add js file for tree select of categories--}}
@push('footer_scripts')
    {{-- <script src="https://cdn.tiny.cloud/1/oh0o94omie4zyuas0tk96qk319s6ltqf8eq9er20jhn3d7r7/tinymce/5/tinymce.min.js"
            referrerpolicy="origin"></script> --}}
    <script src="{{ asset('treeSelectJs/treeselectjs.umd.js') }}" ></script>
    <link href="https://www.jqueryscript.net/css/jquerysctipttop.css" rel="stylesheet" type="text/css">
    <link rel="stylesheet" href="https://cdn.materialdesignicons.com/5.0.45/css/materialdesignicons.min.css">



    <script type="text/javascript">

        function CustomizedTreeSelect(){


        const options = @json($category_data);

         const treeselect = new Treeselect({
            parentHtmlContainer: document.querySelector('.categoryList'),
            options: options,
            value: [{{ $selectedCategoryIds }}],
            clearable: true,
            //isGroupedValue: true,
            //isIndependentNodes: false,
        })
        treeselect.srcElement.addEventListener('input', (e) => {
    const selectedValues = e.detail;
    $("#category_id").val(e.detail.join(','));
    const tagsContainer = document.querySelector('.treeselect-input__tags');
    // alert(e.detail)
    tagsContainer.innerHTML = '';

    selectedValues.forEach(value => {
        const selectedOption = findOptionByValue(options, value);
        if (selectedOption) {
            const parentNames = findParentNames(options, selectedOption);
            const tagText = parentNames ? `${parentNames} / ${selectedOption.name}` : selectedOption.name;

            renderTag(tagsContainer, tagText, value);
        }
    });
     // Get the selected item element
     const selectedItemElement = $('.treeselect-input__tags');

    // Get the container where you want to display the selected item
    const selectedItemContainer = $('.selected-item-container');
    var innerContent = selectedItemElement.html();

        // Insert the inner HTML into the target div
    selectedItemContainer.html(innerContent);
    selectedItemElement.empty();
    input_status($('#category_id'));

});

      function findOptionByValue(options, value) {
        for (const option of options) {
          if (option.value === value) {
            return option;
          }
          if (option.children) {
            const childOption = findOptionByValue(option.children, value);
            if (childOption) {
              return childOption;
            }
          }
        }
        return null;
      }

  


// Parse selected values from multiselect
const selectedValuesString  = $("#category_id").val();
const selectedValues = selectedValuesString.split(",");
const tagsContainer = document.querySelector('.treeselect-input__tags');
    tagsContainer.innerHTML = '';

    selectedValues.forEach(value => {
        const selectedOption = findOptionByValue(options, value);

        if (selectedOption) {
            const parentNames = findParentNames(options, selectedOption);

            const tagText = parentNames ? `${parentNames} / ${selectedOption.name}` : selectedOption.name;
            renderTag(tagsContainer, tagText, value);
        }
    });
    function findOptionByValue(options, value) {
    for (const option of options) {
        if (option.value == value) { // Use loose comparison to handle different data types
        return option;
        }
        if (option.children) {
        const childResult = findOptionByValue(option.children, value);
        if (childResult) {
            return childResult;
        }
        }
    }
    return null;
    }

    function findParentNames(options, childOption) {
    for (const option of options) {
        if (option.children && option.children.includes(childOption)) {
            const grandparents = findParentNames(options, option);
            return grandparents ? `${grandparents} / ${option.name}` : option.name;
        }
        if (option.children) {
            const fromChild = findParentNames(option.children, childOption);
            if (fromChild) return `${option.name} / ${fromChild}`;
        }
    }
    return '';

}
function renderTag(container, text, value) {
        const tagElement = document.createElement('div');
        tagElement.className = 'treeselect-input__tags-element';
        tagElement.setAttribute("data-id", value)
        tagElement.textContent = text;

        // Create a span element with the class for the remove button
        // const removeButton = document.createElement('span');
        // removeButton.className = 'treeselect-input__tags-remove';
        // removeButton.textContent = '×'; // You can use an appropriate remove icon here
        //const selectedCategoryValue = e.detail;
        // // Add an event listener to the remove button


        // // Append the remove button to the tag element
        //tagElement.appendChild(removeButton);

        // Append the complete tag to the container
        container.appendChild(tagElement);
    }
    // $(document).on('click', '.treeselect-input__tags-remove', function () {
    //     event.preventDefault();
    //     // alert("here");
    //     var dataIdToRemove = $(this).parent().attr("data-id");
    //     // Get the current value of the input field
    //     var inputValue = $('#category_id').val();

    //     // Split the input value into an array
    //     var valuesArray = inputValue.split(',');

    //     // Remove the data-id from the array
    //     var indexToRemove = valuesArray.indexOf(dataIdToRemove);
    //     if (indexToRemove !== -1) {
    //         valuesArray.splice(indexToRemove, 1);
    //     }

    //     // Join the updated array back into a string and set it as the input field value
    //     var updatedValue = valuesArray.join(',');
    //     $('#category_id').val(updatedValue);
    //     $(this).parent('.treeselect-input__tags-element').remove();
    //     // alert(treeselect);
    //     var checkedOptions = $('.treeselect-list__item input:checked');

    //     checkedOptions.each(function () {
    //         var checkedOption = $(this);
    //         var inputId = checkedOption.attr("input-id");

    //         if (inputId == dataIdToRemove) {
    //             checkedOption.prop("checked", false); // Uncheck the checkbox
    //             checkedOption.closest(".treeselect-list__item").removeClass('treeselect-list__item--checked');
    //             // Check if any parent checkboxes should also be unchecked
    //             var parentContainers = checkedOption.parents('.treeselect-list__group-container');
    //             parentContainers.each(function () {
    //                 var parentContainer = $(this);
    //                 var childCheckboxes = parentContainer.find('.treeselect-list__item input:checked');

    //                 if (childCheckboxes.length === 0) {
    //                     var parentCheckbox = parentContainer.find('.treeselect-list__item-checkbox');
    //                     parentCheckbox.prop("checked", false); // Uncheck the parent checkbox
    //                     parentCheckbox.closest(".treeselect-list__item").removeClass('treeselect-list__item--checked');
    //                 }
    //             });
    //         }
    //     });

    // });

}
 // Wait for the document to be ready
 $(document).ready(function() {
    // Get the selected item element
    const selectedItemElement = $('.treeselect-input__tags');

    // Get the container where you want to display the selected item
    const selectedItemContainer = $('.selected-item-container');
    var innerContent = selectedItemElement.html();
        // Insert the inner HTML into the target div
    selectedItemContainer.html(innerContent);
    selectedItemElement.empty();
  });
document.addEventListener("DOMContentLoaded",function(){
      CustomizedTreeSelect()

    })
   // treeselect.srcElement.addEventListener('input', (e) => {
        //     $("#category_id").val(e.detail.join(','));
        // })
        // var SampleJSONData = @json($category_data);
        // var comboTree2;
        // @if(count($product->categories) > 0)
        // $(document).ready(function($) {
        //     comboTree2 = $('#justAnotherInputBox').comboTree({
        //         source : SampleJSONData,
        //         isMultiple: false,
        //         selected: [{{$product->categories[0]->id}}]

        //     });
        //     $("#justAnotherInputBox").change(function(){
        //         let id =  comboTree2.getSelectedIds();
        //         $('#category_id').val(id);
        //     });


        // });
        // @else
        // $(document).ready(function($) {
        //     comboTree2 = $('#justAnotherInputBox').comboTree({
        //         source : SampleJSONData,
        //         isMultiple: false
        //     });
        //     $("#justAnotherInputBox").change(function(){
        //         let id =  comboTree2.getSelectedIds();
        //         $('#category_id').val(id);
        //     });

        // });

        // @endif

    </script>

    {{--  this code is use for add button --}}
    <script>
        $( document ).ready(function() {


          
            /* LARAVEL META CSRF REQUIREMENT */
            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                }
            });
            $('.click__btn').on('click', function(e){
                var $btn = $(this);
                var toggle = $btn.attr('data-toggle');
                $('#justAnotherInputBox').parent().addClass('category');
                $('#channels').parent().addClass('channels');
                var input_data=$btn.parents('.form-group').data('value');
                if (toggle === "add"){
                    if($btn.hasClass('active') == false){
                        var add_new_attribute = document.createElement('div');
                        add_new_attribute = $(add_new_attribute);
                        add_new_attribute.addClass("add_new_attribute d-flex");
                        add_new_attribute.html(`<input id="${input_data}-input" type="text" class="form-control brand-input" placeholder="Enter ${input_data}" /><div class="send__btn ${input_data}-btn">Send</div>`);
                        $btn.parent().append(add_new_attribute);

                        add_new_attribute.addClass('active');
                        $(`.${input_data}`).addClass('d-none');
                        $(`.${input_data}-text`).html('<i class="bi bi-dash-circle"></i>');
                        $btn.attr('data-toggle','minus');
                    }
                }
                else{
                    $(`.${input_data}`).removeClass('d-none');
                    $(`#${input_data}-input`).parent().remove();
                    $(`.${input_data}-btn`).remove();
                    $('.add-btn-custom-css').removeClass('active');
                    $(`.${input_data}-text`).html('<i class="bi bi-plus-circle"></i>');
                    $btn.attr('data-toggle','add');
                }

            });


            $(document).on('click', '.send__btn', function(e){
                var send_btn=$(this);
                var parent_div_value =send_btn.parents('.form-group').data('value');
                let Content =$(`#${parent_div_value}-input`).val();
                $(`.${parent_div_value}`).removeClass('d-none');
                $(`#${parent_div_value}-input`).parent().remove();
                $(`.${parent_div_value}-btn`).remove();
                $('.add-btn-custom-css').removeClass('active');
                $(`.${parent_div_value}-text`).html('<i class="bi bi-plus-circle"></i>');
                send_btn.attr('data-toggle','add');
                var route_type=parent_div_value+'.store';


                //    ajax call for data
                $.ajax({
                    url: '{{(route('brands.store'))}}',
                    type: 'POST',
                    data: {
                        'name': Content,
                    },
                    success: function( data){
                        console.log("Success");
                    },
                    error: function( jqXhr, textStatus, errorThrown ){
                        console.log(errorThrown);
                    }
                });

            });
        });

    </script>

@endpush
