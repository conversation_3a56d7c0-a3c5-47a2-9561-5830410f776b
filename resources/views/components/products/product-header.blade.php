<div>
    <div class="card border-radius shadow-none mb-4">
        <div class="card-body p-0">
            <div class="row">
                <div class="col-12 col-sm-12 col-md-6 col-lg-6">
                    <div class="d-flex flex-row mt-4">
                        <div class="pr-2">
                            <h3 class="font-24 m-0 text-light-grey">
                                <a href="{{route('products.index')}}" class="font-24 Poppins m-0 text-light-grey">
                                    {{('Products')}}
                                </a>
                                {{__(" /")}}
                            </h3>
                        </div>
                        <div class="pr-1">
                            <h3 class="text-dark font-24 text-truncate m-0" title="{{ $product->sku }}" style="width: 8rem">
                                {{ $product->sku }}
                            </h3>
                        </div>
                        <div class="">
                            <span class="badge badge-pill {{ $product->get_status_style()  }}">
                                {{__($product->get_status())}}
                            </span>
                        </div>
                    </div>
                    {{--<div class="d-flex flex-row mt-3">
                        <div class="pr-2">
                            <div class="progress mt-2" style="width: 163px">
                                <div class="progress-bar" role="progressbar"
                                     style="width: 25%;background-color: #4FBA6F" aria-valuenow="25"
                                     aria-valuemin="0" aria-valuemax="100"></div>
                            </div>
                        </div>
                        <div class="pl-2">
                            <p class="Roboto regular">{{__("20% Complete")}}</p>
                        </div>
                    </div>--}}
                </div>
                <div class="col-12 col-sm-12 col-md-6 col-lg-6 mt-2 mt-md-4 mt-lg-4">
                    <div class="d-flex">
                        <div class="pr-4 p-2 border-right ml-md-auto">
                            <a class="btn btn-outline-danger mx-sm-3 mx-md-2 mx-xl-3 hovereffect ripplelink"
                               style="width: 80px">
                                Delete
                            </a>
                            <input type="submit" id="pro_edit_pub_btn" value="{{__("Save")}}" class="btn btn-primary ripplelink disabled-with-text"
                                   style="width: 100px"/>
                        </div>

                        <div class="py-2 pl-4">
                            <a href="" class="btn btn-outline-primary hovereffect ripplelink pt-1">
                                <i class="flaticon flaticon-arrow-previous icon-size"></i>
                            </a>
                            <a href="" class="btn btn-outline-primary hovereffect ripplelink pt-1">
                                <i class="flaticon flaticon-arrow-next icon-size"></i>
                            </a>
                        </div>
                    </div>
                    {{--<input type="hidden" name="version_id" value="{{ $version_id }}">
                    @error('version_id')
                    <span class="text-danger" role="alert">
                                    <small>{{ $message }}</small>
                                </span>
                    @enderror--}}
                </div>
                <div class="col-12 col-sm-12 col-md-12 col-lg-5">
                    @if($isVersionDropdown)
                        <div class="mt-2 mt-md-4 mt-lg-4 float-left float-md-left float-lg-right">

                            <div class="d-flex flex-row">
                                {{--                            <div class="pr-2">--}}
                                {{--                                <div class="progress mt-2" style="width: 163px">--}}
                                {{--                                    <div class="progress-bar" role="progressbar"--}}
                                {{--                                         style="width: 25%;background-color: #4FBA6F" aria-valuenow="25"--}}
                                {{--                                         aria-valuemin="0" aria-valuemax="100"></div>--}}
                                {{--                                </div>--}}
                                {{--                            </div>--}}
                                {{--                            <div class="pl-2">--}}
                                {{--                                <p class="Poppins regular">{{__("20% Complete")}}</p>--}}
                                {{--                            </div>--}}
                            </div>
                            {{--                            <div class="d-flex flex-row">--}}
                            {{--dropdown with search--}}
                            <div class="dropdown">
                                <button class="btn btn-primary dropdown-toggle" type="button" id="dropdownMenuButton" data-toggle="dropdown" aria-expanded="false">
                                    {{ $current_version->name }}
                                </button>
                                <div class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                                    @foreach($versions as $version)
                                        <a class="dropdown-item" href="{{ route("products.edit", ["id" => $product->id, "version_id" => $version->id]) }}">{{ $version->name }}
                                            @foreach($product_versions as $product_version)
                                                @if($product_version->id == $version->id)
                                                    <span class="text-success"><i class="fas fa-check"></i></span>
                                                @endif
                                            @endforeach
                                        </a>
                                    @endforeach
                                </div>
                            </div>
                            <input type="hidden" name="version_id" value="{{ $version_id }}">
                            @error('version_id')
                            <span class="text-danger" role="alert">
                                    <small>{{ $message }}</small>
                                </span>
                            @enderror
                            {{--                            </div>--}}

                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
