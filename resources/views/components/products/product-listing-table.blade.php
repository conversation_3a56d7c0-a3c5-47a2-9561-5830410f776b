@if(count($products) > 0)

    <form action="" method="GET" id="product-form-listing" class="formStyle mt-2 overflow-hidden1">
        <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css">
        <div class="row gx-lg-5 drop-in">
            @if($filter)

                @if(\Illuminate\Support\Facades\Request::has('channels'))
                    <input name="channels[]" value="{{Request::get('channels')[0]}}" type="hidden">
                @endif
                <div class="col-12 col-md-12 @if($sync) col-xl-3 @else col-xl-3 col-lg-3 @endif">
                    <div class="d-flex flex-row">
                        <div class="input-group">
                            <input type="text" class="form-control" value="{{request('q')}}" name="q"
                                placeholder="Search"
                                aria-label="Search by SKU" aria-describedby="search">
                            @if(request()->has('q'))
                                <a href="{{ \Illuminate\Support\Facades\Request::url() }}"
                                class="ripplelink btn search_btn_close">
                                    <i class="fa fa-close"></i>
                                </a>
                            @endif
                            <div class="input-group-append">
                                <button class="search" id="search">
                                    <img src="{{asset('media/retailer-dashboard/Search-2.png')}}" alt="">
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-12 col-md-12 mt-3 mt-md-0 @if($sync) col-xl-9 @else col-lg-9 col-xl-9 col-md-12 @endif">
                    {{-- listing filters  start --}}
                    <div class="d-flex justify-content-lg-end flex-wrap mt-sm-3 mt-lg-0">
                        <input type="text"
                            name="tags"
                            class="form-control filter-input"
                            placeholder="Search tags"
                            value="{{$request->has('tags') ? $request->get('tags') : ''}}" style="min-width:130px">
                        @if(!$sync)
                            <div class="ms-2 ms-lg-1 mb-2" style="min-width:130px">
                                <select placeholder="Stores"
                                        name="channels[]"
                                        id="Store"
                                        multiple="multiple"
                                        class="sumoselect ms-2" title="Stores">

                                    @foreach($channels as $channel)
                                        <option
                                            value="{{ $channel->id }}"
                                            {{ $request->has("channels")
                    ? (in_array($channel->id, $request->get("channels"))
                        ? "selected"
                        : null)
                    : null }}>
                                            {{ $channel->name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                        @endif

                        <div class="ms-2 ms-lg-1 mb-2" style="min-width:130px">
                            <select multiple="multiple" placeholder="Attribute Set" name="families[]" id="AttributeSet"
                                    class="sumoselect ms-2" title="Attribute Set">
                                @foreach($families as $family)
                                    <option
                                        value="{{ $family->id }}"{{ $request->has("families") ? (in_array($family->id, $request->get("families")) ? "selected" : null) : null }}>{{ $family->name }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="ms-2 ms-lg-1 mb-2" style="min-width:130px">
                            <select placeholder="Vendor" name="vendors[]" id="Vendor" multiple="multiple"
                                    class="sumoselect ms-2" title="Vendor">

                                @foreach($vendors->sortBy('fname') as $vendor)
                                    <option
                                        value="{{ $vendor->id }}"{{ $request->has("vendors") ? (in_array($vendor->id, $request->get("vendors")) ? "selected" : null) : null }}>{{ $vendor->fname }}</option>
                                @endforeach
                            </select>
                        </div>


                        <div class="ms-2 ms-lg-1 mb-2" style="min-width:130px">
                            <select placeholder="Brand" name="brands[]" id="Brand" multiple="multiple"
                                    class="sumoselect ms-2" title="Brand">
                                @foreach($brands as $brand)
                                    <option
                                        value="{{ $brand->id }}"{{ $request->has("brands") ? (in_array($brand->id, $request->get("brands")) ? "selected" : null) : null }}>{{ $brand->name }}</option>
                                @endforeach
                            </select>
                        </div>

                        <div class="ms-2 ms-lg-1 mb-2" style="width:130px">
                            <select name="score" class="sumoselect ms-2" title="Product Quality Score">
                                <option value="" hidden>{{__("Product Quality Score")}}</option>
                                <option value="good" {{ $request->get("score") == "good" ? "selected" : "" }}>{{__("Good")}}</option>
                                <option value="fair" {{ $request->get("score") == "fair" ? "selected" : "" }}>{{__("Fair")}}</option>
                                <option value="bad" {{ $request->get("score") == "bad" ? "selected" : "" }}>{{__("Bad")}}</option>
                                <option value="highest" {{ $request->get("score") == "highest" ? "selected" : "" }}>{{__("Highest to Lowest ")}}&#x2191;</option>
                                <option value="lowest" {{ $request->get("score") == "lowest" ? "selected" : "" }}>{{__("Lowest to Highest")}} &#x2193;</option>
                            </select>
                        </div>
                        <div class="ms-2 ms-lg-1 mb-2" style="width:130px;height: 36px">
                            <select multiple="multiple" placeholder="Category" name="categories[]" id="Category"
                                    class="sumoselect ms-2 me-2" title="Category">
                                @foreach($categories as $category)
                                    <option
                                        value="{{ $category->id }}"{{ $request->has("categories") ? (in_array($category->id, $request->get("categories")) ? "selected" : null) : null }}>{{ $category->name }}</option>
                                @endforeach
                            </select>
                        </div>

                        @if($sync)
                            <div class="ms-sm-0 ms-lg-2 mb-2">
                                <select name="sync_status" {{--id="Status"--}} title="Sync Status">
                                    <option value="" class="Poppins regular text-color" hidden>{{__("Status")}}</option>
                                    <option value="update_available"
                                            class="text-color"
                                        {{ $request->has("sync_status") ? ($request->get("sync_status") == 'update_available' ? "selected" : null) : null }}>
                                        {{__("Update Available")}}</option>
                                    <option value="synced"
                                            class="text-color" {{ $request->has("sync_status") ? ($request->get("sync_status") == 'synced' ? "selected" : null) : null }}>
                                        {{__("Synced")}}</option>
                                </select>

                            </div>
                            <div class="mb-2 ms-sm-0 ms-lg-2">
                                <select name="product_list" id="product_list"
                                        class=" select-css" style="min-width:130px">
                                    <option value="" hidden>{{__("Records per page")}}</option>
                                    <option value="50" {{($request->product_list == 50) ? 'selected' : ''}} >50</option>
                                    <option value="100" {{($request->product_list == 100) ? 'selected' : ''}}>100</option>
                                    <option value="150" {{($request->product_list == 150) ? 'selected' : ''}} >150</option>
                                    <option value="200" {{($request->product_list == 200) ? 'selected' : ''}}>200</option>
                                    <option value="250" {{($request->product_list == 250) ? 'selected' : ''}}>250</option>
                                    <option value="300" {{($request->product_list == 300) ? 'selected' : ''}}>300</option>
                                    <option value="350" {{($request->product_list == 350) ? 'selected' : ''}}>350</option>
                                    <option value="400" {{($request->product_list == 400) ? 'selected' : ''}}>400</option>
                                    <option value="450" {{($request->product_list == 450) ? 'selected' : ''}}>450</option>
                                    <option value="500" {{($request->product_list == 500) ? 'selected' : ''}}>500</option>
                                </select>
                            </div>
                        @else
                            <div class="ms-2 ms-lg-1 mb-2"  style="width:130px">
                                <select name="status" {{--id="Status"--}}
                                class="regular filter-status-hover"  style="width:130px;min-width:130px;max-width:130px" title="Status">
                                    <option value="">{{__("Status")}}</option>
                                    <option
                                        value="0"{{ $request->filled("status") ? ($request->get("status") == 0 ? "selected" : null) : null }}>
                                        {{__("Draft")}}</option>
                                    <option
                                        value="1"{{ $request->filled("status") ? ($request->get("status") == 1 ? "selected" : null) : null }}>
                                        {{__("Active")}}</option>
                                </select>
                            </div>
                        @endif

                        <button class="btn btn-dark px-3 ms-2 mb-2" type="submit">
                            {{trans('products.apply_btn')}}
                        </button>

                        @if(\Illuminate\Support\Facades\Request::has('channels'))
                            <a href="{{ route("shopify.index", ['channels' => Request::get('channels')]) }}"
                            class="h4 ms-3 d-inline-flex align-self-center text-decoration-none">
                                {{trans('products.clear_all_btn')}}
                            </a>

                        @else
                            <a href="{{ route("products.index") }}"
                            class="h4 ms-3 d-inline-flex align-self-center text-decoration-none me-2">
                                Reset
                            </a>

                        @endif

                    </div>
                </div>

            @endif

        </div>
    </form>
    @if($multiselect)
        <div class="row">
            <div class="col-12 text-right" style="min-height:52px;">
                @if(Gate::allows('primeOrPaidUser'))
                <form method="post" action="{{route('shopify.bulk.sync')}}" id="bulk_sync_form">
                    @csrf
                    @if(\Illuminate\Support\Facades\Request::has('channels'))
                        <input name="channel_id" value="{{Request::get('channels')[0]}}" type="hidden">
                    @endif
                    <input value="" type="hidden" id="product_ids" name="product_id">
                    <span class="btn btn-primary mt-3 d-none" onclick="sync_all()" id="sync_all" >Sync All</span>
                </form>
                @else
                <a class="btn btn-primary mt-3 d-none" id="sync_all" data-bs-toggle="tooltip" data-bs-placement="right" title="Syncing products feature is available for paid plans only. Upgrade to a paid plan to enable this feature.">Sync All <i class="fa fa-lock text-white mt-1 ms-2"></i></a>
                @endif
            </div>
        </div>
    @endif

    {{-- Table of products--}}
    <div class="row">
        <div class="col-12">
            <div id="table-scroll2" class="table-scroll2">
                <div class="table-responsive">
                    <table class="table table-hover" id="main-table">
                        <thead>
                        <tr>
                            @if($multiselect)
                                <th scope="col" class="text-dark border-radius-left first-column-custom-css bulkEditCheckbox1"
                                >
                                    <input type="checkbox" id="multiselect_product" class="shopify-checkbox"
                                        aria-label="Checkbox for following text input" onclick="multiselect()">
                                </th>

                            @elseif($isbulkable)
                                {{--  bulk editing start    --}}
                                <th scope="col" class="text-dark border-radius-left first-column-custom-css bulkEditCheckbox1"
                                >
                                    <input type="checkbox" id="multiselect_products" class="bulk-checkbox"
                                        aria-label="Checkbox for following text input" onclick="multiselectAttribute()">
                                </th>
                                {{--  bulk editing start    --}}
                            @endif

                            <th scope="col" class="fixed-side"
                                style="width: 45px;min-width: 45px">
                                {{ __('Image') }}
                            </th>

                            <th scope="col product-name"
                                style="width: 200px;min-width: 200px">

                                {{ __('Product Name') }}
                            </th>

                            <th scope="col" class="fixed-side"
                                style="width: 80px;min-width: 80px">
                                {{ __('Product Identifier') }}
                            </th>

                            <th scope="col" class="disabled-sorting text-center"
                                style="width: 86px;min-width: 86px">
                                {{ __('No. of Variants') }}
                            </th>

                            <th scope="col" class="disabled-sorting text-center"
                                style="width: 90px;min-width: 90px">
                                {{ __('Status') }}
                            </th>
                            <th scope="col" class="disabled-sorting"
                                style="width: 110px;min-width: 110px">

                                {{ __('Product Quality Score') }}

                            </th>



                            @if($isaction)
                                @if($sync)
                                    <th scope="col" class="fixed-side text-end"
                                        style="width: 30px;min-width: 30px">
                                        {{ __('Action') }}
                                    </th>
                                @endif

                                @if($isdelete)
                                    <th scope="col" class="fixed-side text-end"
                                        style="width: 30px;min-width: 30px">
                                        {{ __('Action') }}
                                    </th>
                                @endif
                            @endif
                        </tr>
                        </thead>
                        <tbody>
                        @foreach($products as $product)
                                                <tr
                                                    @can('add_and_edit_product', [\App\Models\Organization\OrganizationUserPermission::class, auth()->user()->organization_id])
                                                    data-product-id="{{$product->id}}"
                                                    @endcan
                                                    class="cursor-pointer border-radius product_row">
                                                    @if($multiselect && $sync)
                                                        <td class="fixed-side select_product d-flex align-items-center">
                                                            @php
                                $status = $product->channels()->syncAction(Request::get('channels'), $product);
                                                            @endphp
                                                            <input type="checkbox" name="product_id"
                                                                @if($status == "sync" || $status == "in_queue")
                                                                disabled
                                                                @else
                                                                value="{{$product->id}}"
                                                                class="product_check product_{{$product->id}}"
                                                                @endif
                                                                aria-label="Checkbox for following text input"
                                                                style="pointer-events: auto;"
                                                                onclick="toggle_class('{{$product->id}}')">
                                                        </td>
                                                    @elseif($isbulkable)
                                                        {{--     bulk editing start   --}}
                                                        <td class="fixed-side select_product d-flex align-items-center">
                                                            <input type="checkbox" name="product_id"
                                                                class="bulk_product_check product Bulk_product_{{$product->id}}"
                                                                aria-label="Checkbox for following text input"
                                                                id="{{$product->id}}"
                                                                style="pointer-events: auto;"
                                                                onclick="toggle_class_Attribute('{{$product->id}}')">
                                                        </td>
                                                        {{--     bulk editing start   --}}
                                                    @endif

                                                        <td>
                                                        <div class="img-div-height my-1">
                                                            <div class="background-image-css"
                                                                style="background-image: url('{{ isset($product->files[0]) ? $product->files[0]->link : asset("img/apimio_default.jpg")}}');"></div>
                                                        </div>
                                                        </td>

                                                    <td class='product-name' title="@if (count(explode(' ', $product->get_name())) > 14){{ $product->get_name() }}@endif">
                                                        @php
                            $words = explode(' ', $product->get_name());
                            $truncated = implode(' ', array_slice($words, 0, 14));
                            if (count($words) > 14) {
                                $truncated .= '...';
                            }
                                                        @endphp
                                                        {{ $truncated }}</td>
                                                    <td class="fixed-side" title="{{ mb_strlen($product->sku) > 13 ? $product->sku : '' }}">
                                                        @php
                            $truncated = mb_substr($product->sku, 0, 13);
                            if (mb_strlen($product->sku) > 13) {
                                $truncated .= '...';
                            }
                                                        @endphp

                                                        {{ $truncated }}
                                                    </td>
                                                    <td class="text-center">{{sizeof($product->variants) == 1 ? 0 : sizeof($product->variants) }}</td>
                                                    <td class="text-center">
                                                        <span class="status {{ $product->get_status_style()}}">
                                                            {{__($product->get_status())}}
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <div class="ms-2">
                                                            <x-completeness-product-score-progress-bar :product="$product" :version="$product->versions()->first()" />
                                                        </div>


                                                    {{--</span>--}}


                                                    @if($sync)
                                                        @can('sync_product_to_shopify', [\App\Models\Organization\OrganizationUserPermission::class, auth()->user()->organization_id])
                                                            <td class="text-end">
                                                                @php
                                    $status = $product->channels()->syncAction(Request::get('channels'), $product);
                                                                @endphp
                                                                @if($status == 'sync')
                                                                    <a href="javascript:void(0)" class="text-decoration-none">
                                                                        <span class="status status-success" aria-hidden="true">Synced</span>
                                                                    </a>
                                                                @elseif($status == 'updated' || $status == 'publish' || $status == 'draft')
                                                                @if(Gate::allows('primeOrPaidUser'))
                                                                    <a class="btn btn-sm btn-outline-primary type_button text-nowrap"
                                                                    href="{{route('shopify.bulk.sync', ['product_id' => $product->id, 'channel_id' => Request::get('channels')[0], 'template_id' => [\App\Classes\Mapping\Conversion::getTemplates(['channel_id' => $product->channels[0]->id, 'type' => 'shopify'])->first()->id ?? null]])}}">
                                                                        Update Available
                                                                    </a>
                                                                @else
                                                                <a class="btn btn-sm btn-primary text-nowrap" style="display: inline-flex; align-items: center; justify-content: center;" data-bs-toggle="tooltip" data-bs-placement="right" title="Syncing products feature is available for paid plans only. Upgrade to a paid plan to enable this feature.">Update Available <i class="fa fa-lock text-white mt-1 ms-2 mb-1"></i></a>
                                                                @endif
                                                                @elseif($status == "in_queue")
                                                                    <a href="javascript:void(0)" class="text-decoration-none"><span
                                                                            class="text-info" aria-hidden="true">In Process</span>
                                                                    </a>
                                                                @else
                                                                    <a href="javascript:void(0)"
                                                                    title="product version is not present in catalog version list"
                                                                    class="text-decoration-none">
                                                                                    <span class="text-danger" aria-hidden="true">
                                                                                        {{$status}}
                                                                                    </span>
                                                                    </a>
                                                                @endif
                                                            </td>
                                                        @endcan
                                                    @endif
                                                    @if($isaction)
                                                        @if($isdelete)
                                                            <td class="text-end">
                                                                <a href="#" data-id="{{$product->id}}" data-retailer-name=""
                                                                data-bs-toggle="modal" data-bs-target="#delete-modal-{{$product->id}}"
                                                                class="btn-delete text-decoration-none">
                                                                    <i class="fa-regular fa-trash-can fs-20 text-danger"></i>
                                                                </a>
                                                            </td>
                                                        @endif
                                                    @endif
                                                </tr>
                                                <x-assets.delete-modal text="Are you sure you want to delete product?"
                                                                    button="Delete Product" title="Delete Product"
                                                                    id="{{$product->id}}"
                                                                    url="{{route('products.destroy', $product->id)}}" type="product"/>
                        @endforeach
                        </tbody>
                    </table>
                </div>
            </div>

        </div>
    </div>
    @if(!$limit)
        {{ $products->appends($request->all())->links() }}
    @endif



@else
    @if(Request::has('channels'))
        <x-general.empty-page description="{{trans('products.empty_table_description')}}" :button="false"/>
    @else
        <x-general.empty-page description="{{trans('products.empty_table_description')}}"/>
    @endif
@endif
<x-assets.delete-modal text="Are you sure you want to delete the selected products?"
                                                   button="Delete Products" title="Delete Products"
                                                   id="bulk-delete"
                                                   url="{{route('products.bulk.delete')}}" type="bulk-delete"/>
@push('footer_scripts')
    <script>
        $(document).ready(function () {
            $('#product-form-listing').on('click', function () {
                $('#product-form-listing').removeClass('overflow-hidden1')
            })
            $('.select_product').off("click");
            $('.sumoselect').SumoSelect({
                csvDispCount: 1,
                captionFormat: '({0}) Selected',
                captionFormatAllSelected: '({0}) Selected',
                okCancelInMulti: true,
                isClickAwayOk: true,
                selectAll: true,
                search: false,
                searchText: 'Search here',
                noMatch: 'No matches for {0}',

            });

            $('table tbody tr td').not(":first-child").not(":last-child").click(function () {
                let product_id = $(this).parent().data('product-id');
                var url = '{{ route("products.edit", ":id") }}';
                url = url.replace(':id', product_id);
                location.href = url;

            });
            $('input[type="checkbox"]').change(function() {
                var checkedValues = [];

                // Loop through all checkboxes with the name "product_id"
                $('input[type="checkbox"]:checked').each(function() {
                    checkedValues.push($(this).val());
                });
                // Display an alert with the selected values
                if (checkedValues.length > 1) {
                   $("#sync_all").addClass('d-inline');
                   $("#sync_all").removeClass('d-none');
                } else {
                    $("#sync_all").addClass('d-none');
                    $("#sync_all").removeClass('d-inline');
                }
            });
        });

    </script>
    {{-- on mouse wheel scroll the table data  --}}
    {{--    <script>--}}
    {{--        const element = document.querySelector(".table-wrap");--}}
    {{--        element.addEventListener('wheel', (event) => {--}}
    {{--            event.preventDefault();--}}
    {{--            element.scrollBy({--}}
    {{--                left: event.deltaY < 0 ? -30 : 30,--}}
    {{--            });--}}
    {{--        });--}}
    {{--    </script>--}}
    <script>
        function multiselect() {
            if ($('#multiselect_product').is(':checked')) {
                $('.product_check').prop('checked', true);
                $('.product_check').addClass('checked');
            } else {
                $('.product_check').prop('checked', false);
                $('.product_check').removeClass('checked');
            }
        }

        function toggle_class(id) {
            if ($('.product_' + id).hasClass('checked')) {
                $('.product_' + id).removeClass('checked');
            } else {
                $('.product_' + id).addClass('checked');
            }
        }

        function sync_all() {
            let selected = [];
            $(".checked").each(function () {
                if ($.inArray($(this).val(), selected) == -1) {
                    selected.push($(this).val());
                }
            });
            $('#product_ids').val(selected);
            if (selected.length <= 0) {
                alert('Select at least one product to sync.');
            } else {
                $('#bulk_sync_form').submit();
            }
        }


    </script>

@endpush
