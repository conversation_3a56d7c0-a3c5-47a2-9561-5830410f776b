<style>
    .custom-attribute-input-css {
        max-width: 73px;
        border: 1px solid #d7d7d7;
        background-color: #F2F2F3;
        border-radius: 0px;
        padding: 8px;
        margin-right: -1px;
    }

    .slider.round {
        border-radius: 34px;
    }
    .w-89 {
        width: 92.2%;
    }

    .slider.round:before {
        border-radius: 50%;
    }

    input::-webkit-outer-spin-button,
    input::-webkit-inner-spin-button {
        -webkit-appearance: none;
        margin: 0;
    }

    input[type=number] {
        -moz-appearance: textfield;
    }
    .select2-container--default .select2-selection--multiple {
        background-color: #F8F8F8 !important;
        border: 1px solid #E5E5E5 !important;
        height: 38px !important;
        border-radius: 4px;
        cursor: text;
        padding-bottom: 5px;
        padding-right: 5px;
        position: relative;
    }
    .select2-container--default .select2-selection--multiple .select2-selection__choice {
        background-color: #e4e4e4;
        border: 1px solid #aaa;
        border-radius: 4px;
        box-sizing: border-box;
        display: inline-block;
        margin-left: 5px;
        margin-top: 7px !important;
        padding: 0;
        padding-left: 20px;
        position: relative;
        max-width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        vertical-align: bottom;
        white-space: nowrap;
    }
    .select2-container .select2-search--inline .select2-search__field {
        box-sizing: border-box;
        border: none;
        font-size: 100%;
        margin-top: 8px !important;
        margin-left: 5px;
        padding: 0;
        max-width: 100%;
        resize: none;
        height: 18px;
        vertical-align: bottom;
        font-family: sans-serif;
        overflow: hidden;
        word-break: keep-all;
    }
    .simple-input{
        font-size: 18px;
        position: absolute;
        right: 8px;
        top: 7px;
        color: #e55858;
    }

    .select-input{
        font-size: 18px;
        position: absolute;
        right: 43px;
        top: 7px;
        color: #e55858;
    }
    .list-input {
        font-size: 18px;
        position: absolute;
        right: 42px;
        top: 29px;
        color: #e55858;
    }
    .url-input{
        font-size: 18px;
        position: absolute;
        right: 42px;
        top: 7px;
        color: #e55858;
    }
    .url1-input {
        font-size: 18px;
        position: absolute;
        right: 45px;
        top: 29px;
        color: #e55858;
    }
    .url2-input {
        font-size: 18px;
        position: absolute;
        right: 45px;
        top: 48px;
        color: #e55858;
    }

    .form-control.is-invalid, .was-validated .form-control:invalid {
        background-image: none;
    }
    .select2-container {
        border-radius: 4px !important;
    }
    .multipledata.is-invalid + span{
        border:1px solid red;
    }
    .tox.tox-tinymce{
    height:300px!important;
    }


    @media (min-width: 1200px) and (max-width: 1300px) {
        .select-input{
        font-size: 18px;
        position: absolute;
        right: 32px;
        top: 7px;
        color: #e55858;
    }
        .url1-input {
            font-size: 18px;
            position: absolute;
            right: 34px;
            top: 44px;
            color: #e55858;
        }
        .w-89 {
            width: 87%;
        }
    }
    @media (min-width: 1300px) and (max-width: 1500px) {
        .select-input{
            font-size: 18px;
            position: absolute;
            right: 32px;
            top: 7px;
            color: #e55858;
        }
        .url1-input {
            font-size: 18px;
            position: absolute;
            right: 34px;
            top: 44px;
            color: #e55858;
        }
        .w-89 {
            width: 90%;
        }
    }
    @media (min-width: 1900px) and (max-width: 2200px) {
        .select-input{
        font-size: 18px;
        position: absolute;
        right: 44px;
        top: 7px;
        color: #e55858;

    }
    }
    @media (min-width: 2200px) {
        .select-input{
        font-size: 18px;
        position: absolute;
        right: 60px;
        top: 7px;
        color: #e55858;
    }
        .url-input{
            font-size: 18px;
            position: absolute;
            right: 60px;
            top: 7px;
            color: #e55858;
        }
        .w-89 {
            width: 95.2%;
        }
    }

</style>
<div class="card">
    <div class="card-header">
        <h3 class="d-flex justify-content-between align-items-center mb-0">
            <strong>{{ ucwords($family->name) }}</strong>
            <span>
                <a href="{{route("unassign.family",['id' => $family->id, 'version_id' => $version->id, 'product_id' => $product->id])}}" onclick="unAssign('{{$family->id}}')">
                    <i class="fa fa-times text-danger" aria-hidden="true"></i>
                </a>
            </span>
        </h3>
    </div>
    <div class="card-body">
        @foreach($family->attributes as $attribute)
            @if($attribute->attribute_type_id == 1)

                <div class="formStyle mt-md-2 mt-xl-3">
                    <div class="inside-div-inputs">
                        <label for="sku"
                               @isset($attribute->description)  class="mb-0" @endisset> {{ ucfirst($attribute->name) }}
                            &nbsp; @if($attribute->is_required) <span class="text-danger">*</span> @endif
                            <x-products.edit-product-attribute-status-dot :id="$attribute->pivotId"/>
                        </label>
                        @isset($attribute->description)
                            <br>
                            <small class="mb-1">{{$attribute->description}}</small>
                        @endisset
                        @if(count($attribute->value) > 0)
                            @foreach($attribute->value as $key => $data)
                                <div @if($loop->iteration != 1)  class="mt-3 position-relative parent-div" @endif class="position-relative parent-div mr-sm-2">
                                    <input
                                        type="text"
                                        data-confirm-before-leave="true"
                                        data-rules="{{ $attribute->text_validation()}}"
                                        data-status-id="#dot_{{ $attribute->pivotId }}"
                                        class="form-control input-width-css @if(!$key) clone_input_field @endif"
                                        name="attribute[{{ $attribute->pivotId }}][]"
                                        data-value-type="{{$attribute->attribute_value_type()}}"
                                        value="{{ $data['value'] }}"
                                        data-content=""
                                        style="margin-top:0px !important; ">

                                    <i class="fa fa-light fa-exclamation-circle select-input" html="true"  data-bs-toggle="popover" data-bs-trigger="hover"
                                       data-bs-placement="right"  data-bs-content=""></i>
                                    @if(count($attribute->value) > 1 && $loop->iteration > 1)
                                        <a href="#" class="remove_field" data-btn-ref="{{$data['value']}}"
                                           style="margin-top: -27px !important;">
                                            <i class="trash-button fa fa-trash-o"></i>
                                        </a>
                                    @endif
                                </div>
                            @endforeach
                        @else
                            <input
                                type="text"
                                data-confirm-before-leave="true"
                                data-rules="{{ $attribute->text_validation()}}"
                                data-status-id="#dot_{{ $attribute->pivotId }}"
                                class="form-control clone_input_field input-width-css"
                                name="attribute[{{ $attribute->pivotId }}][]"
                                data-value-type="{{$attribute->attribute_value_type()}}">
                        @endif
                    </div>
                    @if($attribute->attribute_value_type() == "list")
                        <Button class="add_field_button" type="button">+ Add new field</Button>
                    @endif
                </div>
            @elseif($attribute->attribute_type_id == 2)
                <div class="formStyle mt-md-2 mt-xl-3">
                    <div class="inside-div-inputs">
                        <label for="sku"
                               @isset($attribute->description)  class="mb-0" @endisset> {{ ucfirst($attribute->name) }}
                            &nbsp; @if($attribute->is_required) <span style="color: #ff8178">*</span> @endif
                            <x-products.edit-product-attribute-status-dot :id="$attribute->pivotId"/>
                        </label>
                        @isset($attribute->description)
                            <br>
                            <small class="mb-1">{{$attribute->description}}</small>
                        @endisset
                        @if(count($attribute->value) > 0)
                            @foreach($attribute->value as $key => $data)
                                {{--                                 {{-!input number!--}}
                                <div class="d-flex align-items-end">
                                    <div class="formStyle parent-div d-flex input-width-css position-relative @if(!$key) clone_input_field @endif"
                                         style="@if($loop->iteration > 1)margin-top:16px; !important;@endif"
                                         @if( $loop->iteration > 1) @else data-value-type="{{$attribute->attribute_value_type()}}"
                                         @endif  data-btn-ref="{{$loop->iteration > 1 ? $data['id'] : "kjhd87sdyf"}}">
                                        @if(isset(json_decode($attribute->rules,true)['type']) && json_decode($attribute->rules,true)['type'] == 'price')
                                            <div class="input-group-prepend">
                                                <input type="text" data-content="" class="custom-attribute-input-css"
                                                       name="attribute[{{ $attribute->pivotId }}][measurement][]"
                                                       value="{{$version->currency}}" readonly>
                                            </div>

                                        @endif
                                        <input
                                            type="text"
                                            class="{{isset(json_decode($attribute->rules,true)['type']) ? (json_decode($attribute->rules,true)['type'] == 'price' ? 'w-95' : 'w-100') : 'w-95'}} input-custom-css"
                                            data-confirm-before-leave="true"
                                            data-rules="{{ $attribute->text_validation()}}"
                                            data-status-id="#dot_{{ $attribute->pivotId }}"
                                            name="attribute[{{ $attribute->pivotId }}][value][]"
                                            value="{{ $data['value'] }}"
                                            oninput="this.value = this.value.replace(/[^0-9.]/g, '').replace(/(\..*)\./g, '$1');"
                                            @if(isset(json_decode($attribute->rules,true)['type']) && (json_decode($attribute->rules,true)['type'] == 'integer' ))
                                            onkeypress="return (event.charCode == 8 || event.charCode == 0) ? null : event.charCode >= 48 && event.charCode <= 57"
                                            @endif>
                                        <i class="fa fa-light fa-exclamation-circle simple-input d-none" html="true"  data-bs-toggle="popover" data-bs-trigger="hover"
                                           data-bs-placement="right"  data-bs-content=""></i>
                                    </div>
                                    @if(count($attribute->value) > 1 && $loop->iteration > 1)
                                        <a href="#" class="remove_field save-input" data-btn-ref="{{$data['id']}}">
                                            <i class="trash-button fa fa-trash-o"></i>
                                        </a>
                                    @endif
                                </div>
                            @endforeach
                        @else

                            <div class="input-group mr-sm-2 clone_input_field"
                                 data-value-type="{{$attribute->attribute_value_type()}}">
                                @if(isset(json_decode($attribute->rules,true)['type']) && json_decode($attribute->rules,true)['type'] == 'price')
                                    <div class="input-group-prepend">
                                        <input type="text" class="custom-attribute-input-css"
                                               name="attribute[{{ $attribute->pivotId }}][measurement][]"
                                               value="{{$version->currency}}" readonly>
                                    </div>
                                @endif
                                <input
                                    type="text"
                                    class="{{isset(json_decode($attribute->rules,true)['type']) ? (json_decode($attribute->rules,true)['type'] == 'price' ? 'w-89' : 'w-100') : 'w-89'}}"
                                    data-confirm-before-leave="true"
                                    data-rules="{{ $attribute->text_validation()}}"
                                    data-status-id="#dot_{{ $attribute->pivotId }}"
                                    name="attribute[{{ $attribute->pivotId }}][value][]"
                                    oninput="this.value = this.value.replace(/[^0-9.]/g, '').replace(/(\..*)\./g, '$1');">

                            </div>
                        @endif
                    </div>
                    @if($attribute->attribute_value_type() == "list")
                        <Button class="add_field_button" type="button">+ Add new field</Button>
                    @endif
                </div>
            @elseif($attribute->attribute_type_id == 3)
           
                <div class="formStyle mt-md-2 mt-xl-3">
                    <div class="inside-div-inputs">
                        <label for="sku"
                               @isset($attribute->description)  class="mb-0" @endisset> {{ ucfirst($attribute->name) }}
                            &nbsp; @if($attribute->is_required) <span style="color: #ff8178">*</span> @endif
                            <x-products.edit-product-attribute-status-dot :id="$attribute->pivotId"/>
                        </label>
                        @isset($attribute->description)
                            <br>
                            <small>{{$attribute->description}}</small>
                        @endisset
                        @if(count($attribute->value) > 0)

                            @foreach($attribute->value as $data)
                            <div class="position-relative ">

                                <div class="myTextarea1">
                                    <img src={{asset('assets/images/tinymce-header.png')}} style="max-width:100%;min-width:100%;width:100%;" class="border">
                                    <div class="border p-3" style="width:100%;height:190px;cursor:text;overflow-y:auto;">
                                       {!! $data['value'] !!}
                                    </div>

                                </div>

                                <textarea
                                    class="form-control clone_input_field input-width-css multi-line-text"
                                    style="opacity:0;height:0;min-height:0;"

                                    data-confirm-before-leave="true"
                                    data-rules="{{ $attribute->text_validation()}}"
                                    data-status-id="#dot_{{ $attribute->pivotId }}"
                                    name="attribute[{{ $attribute->pivotId }}][]"
                                    data-value-type="{{$attribute->attribute_value_type()}}"

                                >{{ $data['value'] }}</textarea>
                                </div>

                            @endforeach
                        @else
                        <div class="position-relative ">

                            <div class="myTextarea1">
                                <img src={{asset('assets/images/tinymce-header.png')}} style="max-width:100%;min-width:100%;width:100%;" class="border">
                                <div class="border p-3" style="width:100%;height:190px;cursor:text;overflow-y:auto;">
                                </div>
                            </div> 

                                <textarea
                                    class="form-control input-width-css multi-line-text"
                                    style="opacity:0;height:0;min-height:0;"

                                    data-confirm-before-leave="true"
                                    data-rules="{{ $attribute->text_validation()}}"
                                    data-status-id="#dot_{{ $attribute->pivotId }}"
                                    name="attribute[{{ $attribute->pivotId }}][]"
                                    data-value-type="{{$attribute->attribute_value_type()}}"></textarea>
                             </div>

                        @endif
                         
                    </div>
                </div>
            @elseif($attribute->attribute_type_id == 4)

                @if(count($attribute->value) > 0)
                    @php($multi_array = array())
                    @foreach($attribute->value as $data)
                        @php($multi_array[] = strtolower($data['value']))
                    @endforeach


                    <div class="form-group mt-4">
                        <label for="sku"
                               @isset($attribute->description)  class="mb-0" @endisset> {{ ucfirst($attribute->name) }}
                            &nbsp; @if($attribute->is_required) <span style="color: #ff8178">*</span> @endif
                            <x-products.edit-product-attribute-status-dot :id="$attribute->pivotId"/>
                        </label>
                        @isset($attribute->description)
                            <br>
                            <small>{{$attribute->description}}</small>
                        @endisset


                        <select
                            @if(json_decode($attribute->rules,true)['value_type'] == 'list') class="form-control js-states multipledata input-width-css select-attr" multiple @else class="form-control input-width-css select-attr" @endif


                        data-confirm-before-leave="true"

                            data-rules="{{ $attribute->text_validation()}}"
                            data-status-id="#dot_{{ $attribute->pivotId }}"
                            data-value-type="{{$attribute->attribute_value_type()}}"
                            name="attribute[{{ $attribute->pivotId }}][]">
                            <option value="">Select an option</option>
                            @foreach($attribute->attribute_options()->get() as $option)
                                <option value="{{ $option->name }}" {{in_array(strtolower($option->name) ,$multi_array)  
                                ? "selected" : null }}>{{ $option->name }}</option>
                            @endforeach
                        </select>

                        {{--below code will run only when user selects multi value of multiselect--}}
                        @if(json_decode($attribute->rules,true)['value_type'] == 'list')
                            <input type="hidden" name="multiselect" value="1">
                        @endif
                    </div>

                @else
                    {{--  select and list of select --}}
                    <div class="formStyle mt-lg-2 mt-xl-3 inside-div-inputs position-relative parent-div">
                        <label for="sku"
                               @isset($attribute->description)  class="mb-0" @endisset> {{ ucfirst($attribute->name) }}
                            &nbsp; @if($attribute->is_required) <span style="color: #ff8178">*</span> @endif
                            <x-products.edit-product-attribute-status-dot :id="$attribute->pivotId"/>
                        </label>
                        @isset($attribute->description)
                            <br>
                            <small>{{$attribute->description}}</small>
                        @endisset
                        <select
                            @if(json_decode($attribute->rules,true)['value_type'] == 'list') class="js-states form-control multipledata input-width-css select-attr" multiple @else class="form-control input-width-css" @endif

                        data-confirm-before-leave="true"
                            data-rules="{{ $attribute->text_validation()}}"
                            data-status-id="#dot_{{ $attribute->pivotId }}"
                            data-value-type="{{$attribute->attribute_value_type()}}"
                            name="attribute[{{ $attribute->pivotId }}][]">
                            <option value="">Select an option</option>
                            @foreach($attribute->attribute_options()->get() as $option)

                               <option value="{{ $option->name }}" 
                                {{ 
                                    is_array($attribute->value) 
                                    ? (in_array(strtolower($option->name), array_map('strtolower', $attribute->value)) ? 'selected' : null) 
                                    : (strtolower($attribute->value) == strtolower($option->name) ? 'selected' : null)
                                }}>
                                {{ $option->name }}
                            </option>
                            @endforeach
                        </select>
                        <i class="fa fa-light fa-exclamation-circle list-input" html="true"  data-bs-toggle="popover" data-bs-trigger="hover"
                           data-bs-placement="right"  data-bs-content=""></i>
                        {{--below code will run only when user selects multi value of multiselect--}}
                        @if(json_decode($attribute->rules,true)['value_type'] == 'list')
                            <input type="hidden" name="multiselect" value="1">
                        @endif
                    </div>
                @endif
            @elseif($attribute->attribute_type_id == 5)
                <div class="formStyle mt-lg-2 mt-xl-3">
                    <div class="inside-div-inputs">
                        <label for="sku"
                               @isset($attribute->description)  class="mb-0" @endisset> {{ ucfirst($attribute->name) }}
                            &nbsp; @if($attribute->is_required) <span style="color: #ff8178">*</span> @endif
                            <x-products.edit-product-attribute-status-dot :id="$attribute->pivotId"/>
                        </label>
                        @isset($attribute->description)
                            <br>
                            <small>{{$attribute->description}}</small>
                        @endisset
                        <div class="row">
                            <div class="col-12">
                                <div class="form-group mb-0 position-relative inside-div-inputs">
                                    @if(count($attribute->value) > 0)
                                        @foreach($attribute->value as $key => $data)
                                            <div class="d-flex aligns-items-center position-relative parent-div"
                                                 style="@if($loop->iteration > 1)margin-top:16px !important;@endif">
                                                <input id="start_date_time"
                                                       type={{json_decode($attribute->rules,true)['type'] == 'date' ? 'date' : (json_decode($attribute->rules,true)['type'] == 'date_and_time' ? 'datetime-local' : '')}}
                                                           data-confirm-before-leave="true"
                                                       min="{{json_decode($attribute->rules,true)['type'] == 'date' ? (isset(json_decode($attribute->rules,true)['min']) ? substr(json_decode($attribute->rules,true)['min'], 0, -9) : '') :
                                            (isset(json_decode($attribute->rules,true)['min']) ? json_decode($attribute->rules,true)['min'] : '')}}"
                                                       max="{{json_decode($attribute->rules,true)['type'] == 'date' ? (isset(json_decode($attribute->rules,true)['max']) ? substr(json_decode($attribute->rules,true)['max'], 0, -9) : '') :
                                            (isset(json_decode($attribute->rules,true)['max']) ? json_decode($attribute->rules,true)['max'] : '')}}"
                                                       data-rules="{{ $attribute->text_validation()}}"
                                                       data-status-id="#dot_{{ $attribute->pivotId }}"
                                                       class="form-control w-100 input-width-css @if(!$key) clone_input_field @endif"
                                                       name="attribute[{{ $attribute->pivotId }}][]"
                                                       data-value-type="{{$attribute->attribute_value_type()}}"
                                                       value="{{ $data['value'] }}"
                                                       data-btn-ref="{{$data['value']}}"
                                                       data-content=""
                                                >
                                                <i class="fa fa-light fa-exclamation-circle select-input" html="true"  data-bs-toggle="popover" data-bs-trigger="hover"
                                                   data-bs-placement="right"  data-bs-content=""></i>

                                                @if(count($attribute->value) > 1 && $loop->iteration > 1)
                                                    <a href="#" class="remove_field" data-btn-ref="{{$data['value']}}"
                                                       style="padding-left: 10px;">
                                                        <i class="trash-button fa fa-trash-o"></i>
                                                    </a>
                                                @endif
                                            </div>
                                        @endforeach
                                    @else
                                        <input id="start_date_time"
                                               type={{json_decode($attribute->rules,true)['type'] == 'date' ? 'date' : (json_decode($attribute->rules,true)['type'] == 'date_and_time' ? 'datetime-local' : '')}}
                                                   data-confirm-before-leave="true"
                                               min="{{json_decode($attribute->rules,true)['type'] == 'date' ? (isset(json_decode($attribute->rules,true)['min']) ? substr(json_decode($attribute->rules,true)['min'], 0, -9) : '') :
                                            (isset(json_decode($attribute->rules,true)['min']) ? json_decode($attribute->rules,true)['min'] : '')}}"
                                               max="{{json_decode($attribute->rules,true)['type'] == 'date' ? (isset(json_decode($attribute->rules,true)['max']) ? substr(json_decode($attribute->rules,true)['max'], 0, -9) : '') :
                                            (isset(json_decode($attribute->rules,true)['max']) ? json_decode($attribute->rules,true)['max'] : '')}}"
                                               data-rules="{{ $attribute->text_validation()}}"
                                               data-status-id="#dot_{{ $attribute->pivotId }}"
                                               class="form-control clone_input_field"
                                               name="attribute[{{ $attribute->pivotId }}][]"
                                               data-value-type="{{$attribute->attribute_value_type()}}">
                                        <i class="fa fa-light fa-exclamation-circle select-input d-none" html="true"  data-bs-toggle="popover" data-bs-trigger="hover"
                                           data-bs-placement="right"  data-bs-content=""></i>
                                    @endif
                                    @error('start_date_time')
                                    <span class="text-danger">
                                    <small>{{$message}}</small>
                                </span>
                                    @enderror
                                </div>
                            </div>
                        </div>
                    </div>
                    @if($attribute->attribute_value_type() == "list")
                        <Button class="add_field_button" type="button">+ Add new field</Button>
                    @endif
                </div>
            @elseif($attribute->attribute_type_id == 6)
                <div class="form-group">
                    <label for="sku "
                           @isset($attribute->description)  class="mb-0" @endisset> {{ ucfirst($attribute->name) }}
                        &nbsp; @if($attribute->is_required) <span style="color: #ff8178">*</span> @endif
                        <x-products.edit-product-attribute-status-dot :id="$attribute->pivotId"/>
                    </label>
                    @isset($attribute->description)
                        <br>
                        <small>{{$attribute->description}}</small>
                    @endisset
                    @if(count($attribute->value) > 0)
                        @foreach($attribute->value as $data)
                            <input
                                type="file"
                                data-confirm-before-leave="true"
                                data-rules="{{$attribute->file_attribute_validation()}}"
                                data-status-id="#dot_{{ $attribute->pivotId }}"
                                class="form-control input-width-css"
                                name="attribute[{{ $attribute->pivotId }}][]"
                                data-value-type="{{$attribute->attribute_value_type()}}"
                                value="{{ $data['value'] }}"
                                data-btn-ref="{{$data['value']}}">
                            @if(count($attribute->value) > 1 && $loop->iteration > 1)
                                <a href="#" class="remove_field" data-btn-ref="{{$data['value']}}">
                                    <i class="trash-button fa fa-trash-o"></i>
                                </a>
                            @endif
                        @endforeach
                    @else
                        <input
                            type="file"
                            data-confirm-before-leave="true"
                            data-rules="{{$attribute->file_attribute_validation()}}"
                            data-status-id="#dot_{{ $attribute->pivotId }}"
                            class="form-control"
                            name="attribute[{{ $attribute->pivotId }}][]"
                            data-value-type="{{$attribute->attribute_value_type()}}">
                    @endif
                </div>
            @elseif($attribute->attribute_type_id == 7)
                <div class="formStyle mt-lg-2 mt-xl-3">
                    <div class="inside-div-inputs">
                        <label for="sku" class="mt-2"> {{ ucfirst($attribute->name) }}
                            &nbsp; @if($attribute->is_required)<span style="color: #ff8178">*</span> @endif
                            <x-products.edit-product-attribute-status-dot :id="$attribute->pivotId"/>
                        </label>
                        @isset($attribute->description)
                            <br>
                            <small>{{$attribute->description}}</small>
                        @endisset
                        @if(count($attribute->value) > 0)
                            @foreach($attribute->value as $key => $data)
                                <div class="input-group parent-div mr-sm-2 @if(!$key) clone_input_field @endif"
                                     style="@if($loop->iteration > 1)margin-top:16px !important;@endif"
                                     data-value-type="{{$attribute->attribute_value_type()}}">
                                    <div class="input-group-prepend">

                                        <select name="attribute[{{ $attribute->pivotId }}][measurement][]"
                                                data-confirm-before-leave="true"
                                                class="form-control input-prepend-select pr-2 check-box-product ">
                                            @if(isset(json_decode($attribute->rules,true)['type']))
                                                <x-attribute.measurement
                                                    :type="json_decode($attribute->rules,true)['type']"
                                                    unit="{{isset($data['unit']) ? $data['unit'] : null}}"/>
                                            @endif
                                        </select>

                                    </div>
                                    <input type="text"
                                           data-confirm-before-leave="true"
                                           data-rules="{{ $attribute->text_validation()}}"
                                           data-status-id="#dot_{{ $attribute->pivotId }}"
                                           class="form-control selected-input-css-border"
                                           name="attribute[{{ $attribute->pivotId }}][value][]"
                                           oninput="this.value = this.value.replace(/[^0-9.]/g, '').replace(/(\..*?)\..*/g, '$1');"
                                           value="{{ $data['value'] }}"
                                           data-btn-ref="{{$data['value']}}">
                                    <i class="fa fa-light fa-exclamation-circle @if($loop->iteration > 1)select-input @else simple-input @endif " html="true"  data-bs-toggle="popover" data-bs-trigger="hover"
                                       data-bs-placement="right"  data-bs-content="" style="z-index:99"></i>
                                    @if(count($attribute->value) > 1 && $loop->iteration > 1)
                                        <a href="#" class="remove_field" data-btn-ref="{{$data['value']}}"
                                           style="padding-left: 10px">
                                            <i class="trash-button fa fa-trash-o"></i>
                                        </a>
                                    @endif
                                </div>
                            @endforeach
                        @else
                            <div class="input-group parent-div mr-sm-2 clone_input_field"
                                 data-value-type="{{$attribute->attribute_value_type()}}">
                                <div class="input-group-prepend">
                                    <select name="attribute[{{ $attribute->pivotId }}][measurement][]"
                                            data-confirm-before-leave="true"
                                            class="form-control input-prepend-select pr-2 check-box-product">
                                        @if(isset(json_decode($attribute->rules,true)['type']))
                                            <x-attribute.measurement :type="json_decode($attribute->rules,true)['type']"
                                                                     unit="{{isset(json_decode($attribute->rules,true)['min_unit']) ? json_decode($attribute->rules,true)['min_unit'] : null}}"/>
                                        @endif
                                    </select>
                                </div>
                                <input type="text"
                                       data-confirm-before-leave="true"
                                       data-rules="{{ $attribute->text_validation()}}"
                                       data-status-id="#dot_{{ $attribute->pivotId }}"
                                       oninput="this.value = this.value.replace(/[^0-9.]/g, '').replace(/(\..*?)\..*/g, '$1');"
                                       class="form-control selected-input-css-border"
                                       name="attribute[{{ $attribute->pivotId }}][value][]" data-bs-content="">
                                <i class="fa fa-light fa-exclamation-circle simple-input" html="true"  data-bs-toggle="popover" data-bs-trigger="hover"
                                                                   data-bs-placement="right"  data-bs-content="" style="z-index:99"></i>
                            </div>
                        @endif
                    </div>
                    @if($attribute->attribute_value_type() == "list")
                        <Button class="add_field_button" type="button">+ Add new field</Button>
                    @endif
                </div>
            @elseif($attribute->attribute_type_id == 8)
                <div class="formStyle mt-lg-2 mt-xl-3">
                    <div>
                        <label for="sku"
                               @isset($attribute->description)  class="mb-0" @endisset> {{ ucfirst($attribute->name) }}
                            &nbsp; @if($attribute->is_required)<span style="color: #ff8178">*</span> @endif
                            <x-products.edit-product-attribute-status-dot :id="$attribute->pivotId"/>
                        </label>
                        @isset($attribute->description)
                            <br>
                            <small>{{$attribute->description}}</small>
                        @endisset
                        @if(count($attribute->value) > 0)
                            @foreach($attribute->value as $key => $data)
                                <div class="d-flex align-items-center"
                                     style="@if($loop->iteration > 1)margin-top:16px !important;@endif">
                                    <div class="range-slider input-width-css @if(!$key) clone_input_field @endif">
                                        <input class="range-slider__range form-range" type="range" value="{{ $data['value'] }}"
                                               min="{{isset(json_decode($attribute->rules,true)['min']) ? json_decode($attribute->rules,true)['min'] : ''}}"
                                               max="{{isset(json_decode($attribute->rules,true)['max']) ? json_decode($attribute->rules,true)['max'] : ''}}"
                                               name="attribute[{{ $attribute->pivotId }}][]"
                                               data-value-type="{{$attribute->attribute_value_type()}}"
                                               data-confirm-before-leave="true"
                                               data-rules="{{ $attribute->text_validation()}}"
                                               data-status-id="#dot_{{ $attribute->pivotId }}"
                                               data-btn-ref="{{$data['value']}}">
                                        <span class="range-slider__value">{{$data['value']}}</span>
                                    </div>
                                    @if(count($attribute->value) > 1 && $loop->iteration > 1)
                                        <a href="#" class="remove_field" data-btn-ref="{{$data['value']}}"
                                           style="margin-left:10px">
                                            <i class="trash-button fa fa-trash-o"></i>
                                        </a>
                                    @endif
                                </div>
                            @endforeach
                        @else
                            <div class="range-slider clone_input_field">
                                <input class="range-slider__range form-range" type="range"
                                       min="{{isset(json_decode($attribute->rules,true)['min']) ? json_decode($attribute->rules,true)['min'] : ''}}"
                                       max="{{isset(json_decode($attribute->rules,true)['max']) ? json_decode($attribute->rules,true)['max'] : ''}}"
                                       name="attribute[{{ $attribute->pivotId }}][]"
                                       data-value-type="{{$attribute->attribute_value_type()}}"
                                       data-confirm-before-leave="true"
                                       data-rules="{{ $attribute->text_validation()}}"
                                       data-status-id="#dot_{{ $attribute->pivotId }}"
                                >
                                <span
                                    class="range-slider__value">{{isset(json_decode($attribute->rules,true)['min']) ? json_decode($attribute->rules,true)['min'] : 0}}</span>
                            </div>
                        @endif
                    </div>
                    @if($attribute->attribute_value_type() == "list")
                        <Button class="add_field_button" type="button">+ Add new field</Button>
                    @endif
                </div>
            @elseif($attribute->attribute_type_id == 9)
                <div class="formStyle mt-lg-2 mt-xl-3">
                    <label for="sku"
                           @isset($attribute->description)  class="mb-0" @endisset> {{ ucfirst($attribute->name) }}
                        &nbsp; @if($attribute->is_required) <span style="color: #ff8178">*</span> @endif
                        <x-products.edit-product-attribute-status-dot :id="$attribute->pivotId"/>
                    </label>
                    @isset($attribute->description)
                        <br>
                        <small>{{$attribute->description}}</small>
                    @endisset
                    @if(count($attribute->value) > 0)
                        @foreach($attribute->value as $data)
                            <textarea
                                class="form-control input-width-css multi-line-text"
                                data-confirm-before-leave="true"
                                data-rules="{{ $attribute->validate_single_values()}}"
                                data-status-id="#dot_{{ $attribute->pivotId }}"
                                name="attribute[{{ $attribute->pivotId }}][]"

                                data-value-type="{{$attribute->attribute_value_type()}}">{{ $data['value'] }}</textarea>
                        @endforeach

                    @else
                        <textarea
                            class="form-control input-width-css multi-line-text"
                            data-confirm-before-leave="true"
                            data-rules="{{ $attribute->validate_single_values()}}"
                            data-status-id="#dot_{{ $attribute->pivotId }}"
                            name="attribute[{{ $attribute->pivotId }}][]"
                            data-value-type="{{$attribute->attribute_value_type()}}"></textarea>
                    @endif
                </div>
            @elseif($attribute->attribute_type_id == 10)
                <div class="formStyle mt-lg-2 mt-xl-3 mt-3">
                    <label for="sku"
                           @isset($attribute->description)  class="mb-0" @endisset> {{ ucfirst($attribute->name) }}
                        &nbsp; @if($attribute->is_required) <span style="color: #ff8178">*</span> @endif
                        <x-products.edit-product-attribute-status-dot :id="$attribute->pivotId"/>
                    </label>
                    @isset($attribute->description)
                        <br>
                        <small>{{$attribute->description}}</small>
                    @endisset
                    <div>
                        @if(count($attribute->value) > 0)

                            @foreach($attribute->value as $data)
                                <label class="switch form-check form-switch">
                                    <input type="checkbox" class="form-check-input"
                                           data-confirm-before-leave="true"
                                           data-rules="{{ $attribute->validate_single_values()}}"
                                           data-status-id="#dot_{{ $attribute->pivotId }}"
                                           data-value-type="{{$attribute->attribute_value_type()}}"
                                           value="{{ (isset($data['value']) && (int)$data['value']) > 0 ? 1 : 0 }}" {{isset($data['value'])  ? (((int)$data['value']) == 1 ? 'checked' : '') : 0}}>
                                    <span class="slider round"></span>
                                </label>
                                <input type="hidden" class="checkbox_id"
                                       name="attribute[{{ $attribute->pivotId }}][]"
                                       value="{{ (isset($data['value']) && (int)$data['value']) > 0 ? 1 : 0 }}">
                            @endforeach
                        @else
                            <label class="switch form-check form-switch">
                                <input type="checkbox" class="form-check-input"
                                       data-confirm-before-leave="true"
                                       data-rules="{{ $attribute->validate_single_values()}}"
                                       data-status-id="#dot_{{ $attribute->pivotId }}"
                                       data-value-type="{{$attribute->attribute_value_type()}}"
                                       value="{{isset($data['value']) ? (((int)$data['value']) == 1 ? 1 : 0) : 0 }}">
                                <span class="slider round"></span>
                            </label>
                            <input type="hidden" class="checkbox_id"
                                   name="attribute[{{ $attribute->pivotId }}][]"
                                   value="{{isset($data['value']) ? (((int)$data['value']) == 1 ? 1 : 0) : 0 }}">
                        @endif
                    </div>
                </div>
            @elseif($attribute->attribute_type_id == 11)
                <div class="formStyle mt-lg-2 mt-3">
                    <div class="position-relative inside-div-inputs">
                        <label for="sku"
                               @isset($attribute->description)  class="mb-0" @endisset> {{ ucfirst($attribute->name) }}
                            &nbsp; @if($attribute->is_required) <span style="color: #ff8178">*</span> @endif
                            <x-products.edit-product-attribute-status-dot :id="$attribute->pivotId"/>
                        </label>
                        @isset($attribute->description)
                            <br>
                            <small>{{$attribute->description}}</small>
                        @endisset
                        @if(count($attribute->value) > 0)
                            @foreach($attribute->value as $key => $data)
                                <div class="d-flex align-items-center position-relative parent-div"
                                     style="@if($loop->iteration > 1)margin-top:16px !important;@endif">
                                    <input
                                        type="url"
                                        data-confirm-before-leave="true"
                                        data-rules="{{ $attribute->validate_single_values()}}"
                                        data-status-id="#dot_{{ $attribute->pivotId }}"
                                        class="form-control input-width-css @if(!$key) clone_input_field @endif"
                                        name="attribute[{{ $attribute->pivotId }}][]"
                                        data-value-type="{{$attribute->attribute_value_type()}}"
                                        value="{{ $data['value'] }}"
                                        data-btn-ref="{{$data['value']}}"
                                        data-content=""
                                    >
                                    <i class="fa fa-light fa-exclamation-circle url-input" html="true"  data-bs-toggle="popover" data-bs-trigger="hover"
                                       data-bs-placement="right"  data-bs-content=""></i>
                                    @if(count($attribute->value) > 1 && $loop->iteration > 1)
                                        <a href="#" class="remove_field" data-btn-ref="{{$data['value']}}"
                                           style="margin-left:10px">
                                            <i class="trash-button fa fa-trash-o"></i>
                                        </a>
                                    @endif
                                </div>
                            @endforeach
                        @else
                            <input
                                type="url"
                                data-confirm-before-leave="true"
                                data-rules="{{ $attribute->validate_single_values()}}"
                                data-status-id="#dot_{{ $attribute->pivotId }}"
                                class="form-control clone_input_field"
                                name="attribute[{{ $attribute->pivotId }}][]"
                                data-value-type="{{$attribute->attribute_value_type()}}">
                            <i class="fa fa-light fa-exclamation-circle @if($attribute->description) url2-input @else url1-input  @endif" html="true"  data-bs-toggle="popover" data-bs-trigger="hover"
                               data-bs-placement="right"  data-bs-content=""></i>
                        @endif
                    </div>
                    @if($attribute->attribute_value_type() == "list")
                        <Button class="add_field_button" type="button">+ Add new field</Button>
                    @endif
                </div>
            @elseif($attribute->attribute_type_id == 12)
                <div class="formStyle mt-lg-2 mt-xl-3">
                    <div>
                        <label for="sku"
                               @isset($attribute->description)  class="mb-0" @endisset> {{ ucfirst($attribute->name) }}
                            &nbsp; @if($attribute->is_required) <span style="color: #ff8178">*</span> @endif
                            <x-products.edit-product-attribute-status-dot :id="$attribute->pivotId"/>
                        </label>
                        @isset($attribute->description)
                            <br>
                            <small>{{$attribute->description}}</small>
                        @endisset
                        @if(count($attribute->value) > 0)
                            @foreach($attribute->value as $key => $data)
                                <div class="d-flex align-items-end"
                                     style="@if($loop->iteration > 1)margin-top:16px !important;@endif">
                                    <input
                                        type="color"
                                        data-confirm-before-leave="true"
                                        data-rules="{{ $attribute->validate_single_values()}}"
                                        data-status-id="#dot_{{ $attribute->pivotId }}"
                                        class="form-control input-width-css @if(!$key) clone_input_field @endif"
                                        name="attribute[{{ $attribute->pivotId }}][]"
                                        data-value-type="{{$attribute->attribute_value_type()}}"
                                        value="{{ $data['value'] }}"
                                        data-btn-ref="{{$data['value']}}">
                                    @if(count($attribute->value) > 1 && $loop->iteration > 1)
                                        <a href="#" class="remove_field ms-1" data-btn-ref="{{$data['value']}}"
                                           style="margin-left:10px;">
                                            <i class="trash-button fa fa-trash-o"></i>
                                        </a>
                                    @endif
                                </div>
                            @endforeach
                        @else
                            <input
                                type="color"
                                data-confirm-before-leave="true"
                                data-rules="{{ $attribute->validate_single_values()}}"
                                data-status-id="#dot_{{ $attribute->pivotId }}"
                                class="form-control clone_input_field"
                                name="attribute[{{ $attribute->pivotId }}][]"
                                data-value-type="{{$attribute->attribute_value_type()}}">
                        @endif
                    </div>
                    @if($attribute->attribute_value_type() == "list")
                        <Button class="add_field_button" type="button">+ Add new field</Button>
                    @endif
                </div>
            @endif
        @endforeach
    </div>
</div>
@push('footer_scripts')

    <script>

        /**
         * Input status change
         * */


            // new code start

        class Validation1 {
            /* In constructor we pass Rule and its values */
            constructor(rule, value, rulesAll = null, booleanval = null) {
                this.rulesAll = rulesAll;
                this.booleanval = booleanval;
                this.rule = rule;
                this.value = value;
                this.res = true;
                this.ruleStringToArray();

            }


            /* This method split the values after this symbol "|" */
            ruleStringToArray() {
                let ruleArray = this.rule;
                this.validate1(ruleArray);
            }

            // check type of data
            checkType(type) {
                if(this.rulesAll.type){
                    return this.rulesAll.type===type;
                }else{
                    return this.rulesAll[0]===type;
                }

            }

            /* Rules for validation for all methods */
            validate1(ruleArray) {
                const required = /required/g;
                const integer = /min:*/g;
                const decimal = /min:*/g;
                const max = /max:*/g;
                const min = /min:*/g;
                const url = /url/g;
                const urlValidator = /(http|https):\/\/(\w+:{0,1}\w*@)?(\S+)(:[0-9]+)?(\/|\/([\w#!:.?+=&%@!\-\/]))?/;
                const regex = /regex/g;
                const precision = /precision/g;
                const dateRegax = /dateRegax/g;
                const before = /before/g;
                const after = /after/g;
                this.messages = {
                    required: "The value is required.",
                    max: "The value should be equal to or smaller than",
                    min: "The value should be equal to or greater than",
                    format: "Please enter the correct format",
                    integer:"Values must be numbers without a decimal",
                    url: "The Url is not valid",
                    regex: "Not valid",
                    precision: "digit after point",
                    dateRegax: "The Date should be in between min and max dates",
                    after: "The value is not valid for after",
                    before: "The value is not valid for before",
                    character: "Please Enter the character"
                };
                this.errors = [];


                for (let i in ruleArray) {
                    /* This rule is used when filed is mandatory */
                    if(ruleArray[i].match(required)) {
                        if(Array.isArray(this.value)){
                            var array_data= this.value.join(" ");
                            if(!array_data.split("|")[i]) {
                                this.res = false;
                                this.errors.push(this.messages.required);
                            }
                        }else{
                            if(!this.value.split("|")[i]) {
                                this.res = false;
                                this.errors.push(this.messages.required);
                            }

                        }

                    }

                    // boolean values rule
                    if (ruleArray[i].match(required) && this.checkType("boolean") && !((this.checkType("decimal") || this.checkType("integer") || this.checkType("list") || this.checkType("dimension") || this.checkType("weight") || this.checkType("volume") || this.checkType("price") || this.checkType("date") || this.checkType("date_and_time")))) {
                        if (!this.booleanval) {
                            this.res = !this.res;
                            this.errors.push(this.messages.required);
                        }
                    }
                    /* The rule is used for the maximum character length. */
                    if (ruleArray[i].match(max)) {
                        let matcher = ruleArray;
                        if((this.checkType("decimal") || this.checkType("integer") || this.checkType("list") || this.checkType("dimension") || this.checkType("weight") || this.checkType("volume") || this.checkType("price") ) && (parseFloat(this.value) > parseFloat(matcher[1]))){
                            if(this.checkType("dimension") || this.checkType("weight") || this.checkType("volume") || this.checkType("integer") || this.checkType("decimal") || this.checkType("price")){
                                if(parseFloat(matcher[1])>0){
                                    this.res = false;
                                    this.errors.push(`${this.messages.max} ${parseFloat(matcher[1])}`);
                                }

                            }
                        }

                        if(this.checkType("single_line") || this.checkType('multi_line')){
                            if (this.value.length > parseFloat(matcher[1])) {
                                this.res = false;
                                this.errors.push(`${this.messages.max} ${parseFloat(matcher[1])}`);

                            }
                        }

                        if (this.checkType("date") || this.checkType("date_and_time")) {
                            let minDate;
                            if(this.value){
                                minDate = new Date(this?.value).toISOString();
                            }
                            let dateMatcher;
                            if(this.checkType("date")){
                                dateMatcher = matcher[1].substring(0, 10);
                            }else{
                                dateMatcher = matcher[1];
                            }

                            const maxDate = new Date(dateMatcher.replace(' ', 'T')).toISOString();
                            if(minDate > maxDate){
                                this.res = false;
                                this.errors.push(`${this.messages.max} ${dateMatcher}`);
                                }else{
                                this.res = true;
                            }
                        }
                    }
                    /* This rule applies with the length of minimum. */
                    if (ruleArray[i].match(min) ) {
                        let matcher = ruleArray;
                        if (this.checkType("date") || this.checkType("date_and_time")) {
                            let minDate;
                            if(this.value){
                                minDate = new Date(this?.value).toISOString();
                            }
                            let dateMatcher;
                            if(this.checkType("date")){
                                dateMatcher = matcher[1].substring(0, 10);
                            }else{
                                dateMatcher = matcher[1];
                            }

                            const maxDate = new Date(dateMatcher?.replace(' ', 'T')).toISOString();
                            if(minDate < maxDate ){
                                this.res = false;
                                this.errors.push(`${this.messages.min} ${dateMatcher}`);
                            }else{
                                this.res = true;
                            }
                        }
                        if (!(this.checkType("dimension") || this.checkType("weight") || this.checkType("volume") || this.checkType("integer") || this.checkType("decimal") || this.checkType("price")) && !(this.checkType("date") || this.checkType("date_and_time"))) {
                            if (this.value.length < parseFloat(matcher[1])) {
                                this.res = false;
                                this.errors.push(`${this.messages.min} ${parseFloat(matcher[1])}`);
                            }
                        }
                    }

                    // Rule for integer
                    if (ruleArray[i].match(integer) || ruleArray[i].match(decimal) && !((this.checkType("date") || this.checkType("date_and_time")))) {
                        let matcher = ruleArray;
                        if (this.checkType("dimension") || this.checkType("weight") || this.checkType("volume") || this.checkType("integer") || this.checkType("decimal") || this.checkType("price")) {
                            if ((parseFloat(this.value) < parseFloat(matcher[1]))) {
                                this.res = false;
                                this.errors.push(`${this.messages.min} ${parseFloat(matcher[1])}`);
                            }
                        }

                    }

                    // rule for weight
                    if (ruleArray[i].match(min) && (this.checkType("weight") || this.checkType("integer") || this.checkType("volume") || this.checkType("decimal") || this.checkType("dimension") || this.checkType("price")) && this.value.length === 0) {
                        let matcher = ruleArray;
                        if(parseFloat(matcher[1])>0){
                            this.res = false;
                            this.errors.push(`${this.messages.min} ${parseFloat(matcher[1])}`);
                        }
                    }


                    // Rule for dimension
                    if ((this.checkType("dimension") || this.checkType("weight") || this.checkType("volume") || this.checkType("volume") || this.checkType("integer") || this.checkType("integer")) && this.value.length > 0) {
                        let matcher = ruleArray;
                        if (!$.isNumeric(this.value)) {
                            this.res = false;
                            this.errors.push(`${this.messages.format}`);
                        }

                    }


                    // Rule for decimal number
                    if (
                        (ruleArray[i].match(decimal) && this.value.length > 0 && this.checkType("decimal")||
                            this.checkType("decimal") && this.value.length > 0 ||
                            this.checkType("dimension") && this.value.length > 0 ||
                            this.checkType("integer") && this.value.length > 0) && !(this.checkType("date") ||
                        this.checkType("date_and_time")) && !(this.checkType("single") ||
                        this.checkType("list"))) {
                        let matcher = ruleArray;
                        if (!$.isNumeric(this.value)) {
                            this.res = false;
                            this.errors.push(`${this.messages.format}`);
                        }

                    }
                    /* It is used to validate URLs.*/
                    if (ruleArray[i].match(url) || this.checkType('url')) {
                        if (!this.value.match(urlValidator)) {
                            this.res = false;
                            this.errors.push(this.messages.url);
                        }
                    }
                    /* This ruler is used for checking the regex. */
                    if (ruleArray[i].match(regex)) {
                        let matcher = ruleArray;
                        const check_regex=/^\/(.+)\/([a-z]*)$/;
                        const decode_json= matcher[1];
                        var a=decode_json.substring(1);
                        const str = a.slice(0, -1);
                        var decode = new RegExp(str, 'g');
                        var check_value=decode.test(this.value);
                        if(!check_regex.test(decode)){
                            this.res = false;
                            this.errors.push(this.messages.regex);
                        }
                        if(!check_value){
                            this.res = false;
                            this.errors.push(this.messages.regex);
                        }
                    }
                    /* This rule is used to check the values after point */
                    if (ruleArray[i].match(precision)) {
                        let matcher = ruleArray;
                        if ((this.value + "").split(".")[1]?.length > parseInt(matcher[1])) {
                            this.res = false;
                            this.errors.push(`Allow ${parseInt(matcher[1])} ${this.messages.precision}`);
                            return 0;
                        }
                    }
                    /* This rule is used to check the date in between two dates */
                    if (ruleArray[i].match(dateRegax)) {
                        let matcher = ruleArray[i].split(":");
                        if (!((Date.parse(this.value) >= Date.parse(matcher[1])) && (Date.parse(this.value) <= Date.parse(matcher[2])))) {
                            this.res = false;
                            this.errors.push(this.messages.dateRegax);
                        }
                    }
                    /* This rule is used to check the before date */
                    if (ruleArray[i].match(before)) {
                        let matcher = ruleArray[i].split(":");
                        if ((Date.parse(this.value) >= Date.parse(matcher[1]))) {
                            this.res = false;
                            this.errors.push(this.messages.before);
                        }
                    }
                    /* This rule is used to check the after date */

                    if (ruleArray[i].match(after)) {
                        let matcher = ruleArray[i].split(":");
                        if ((Date.parse(this.value) <= Date.parse(matcher[1]))) {
                            this.res = false;
                            this.errors.push(this.messages.after);
                        }
                    }

                    /* This rule is used to check the integer value */
                    if (ruleArray[i].match(integer) && this.checkType("integer") || this.checkType("integer")) {
                        if (this.value.indexOf(".") !== -1) {
                            this.res = false;
                            this.errors.push(this.messages.integer);
                        }
                    }

                    return false;


                }
            }

            /* This method is used to show the result true or false */
            result() {
                return this.res;
            }

            errorMessages() {
                return this.errors;
            }
        }

        /* Validation("Rule:value", "value") */



        // end


        function input_status1(obj) {
            if ($(obj).attr('data-rules')) {
                let data_id = $(obj).attr('data-status-id');
                let self = $(obj);
                let flag = true; // true = success, false = error
                let rules_array=$(obj).attr('data-rules');
                var data_rule=JSON.parse(rules_array);
                const keys = Object.keys(data_rule);
                keys.forEach((key, index) => {
                    const item=[];
                    item[0]=key;
                    item[1]=data_rule[key];
                    //  const item=key+":"+data_rule[key];
                    let validation1 = new Validation1(item, self.val(), data_rule, self[0].checked);
                    if (!validation1.result() || self.val() < 0) {
                        let chars = validation1.errorMessages();
                        let unique_data = [...new Set(chars)];
                        $(data_id).attr("data-bs-content", unique_data)
                        $(obj).attr('data-bs-content', unique_data)
                        flag = false;
                        return 0;
                    }
                });

                if (flag) {
                    $(obj).removeClass('is-invalid');
                    $(obj).parent('.parent-div').find('.fa-exclamation-circle').addClass('d-none');
                    if(!$(obj).hasClass('.is-invalid').length>0){
                        $(obj).attr("data-bs-content",'');
                        $(obj).parent('.parent-div').find('.fa-exclamation-circle').addClass('d-none');
                    }
                    if($(obj).parents('.inside-div-inputs').find('.is-invalid').length>0){
                        $(data_id).attr("data-bs-content",$(obj).parents('.parent-div').find('.is-invalid').attr('data-bs-content'));
                        $(data_id).removeClass("circle-success", 1000);
                        $(data_id).addClass("circle-error", 1000);
                    }else{
                        $(data_id).attr("data-bs-content",'✔');
                        $(data_id).removeClass("circle-error", 1000);
                        $(data_id).addClass("circle-success", 1000);

                    }

                } else {
                    $(data_id).attr("data-bs-content",$(obj).attr('data-bs-content'));
                    $(obj).addClass('is-invalid');
                    $(data_id).removeClass("circle-success", 1000);
                    $(data_id).addClass("circle-error", 1000);
                    if($(obj).hasClass('is-invalid')){
                        $(obj).parents('.parent-div').find('.fa-exclamation-circle').removeClass('d-none');
                        $(obj).parents('.parent-div').find('.fa-exclamation-circle').attr('data-bs-content',$(obj).attr('data-bs-content'));

                    }
                }
            }
        }

        /**
         * Trigger input status change on keyup
         * */
        $('input, select.select-attr, textarea').keyup(function () {

            input_status1(this)
        });
        $('select.select-attr').change(function () {

            input_status1(this)
        });
        /**
         * Trigger input status change on page refresh | page load
         * */
        $(document).ready(function () {
            $("input, select.select-attr, textarea").each(function () {

                input_status1(this)
            });
        });

        $(document).ready(function () {
            const productEl = document.querySelectorAll('.fa-exclamation-circle');
            productEl.forEach(function (element) {
                var popover = new bootstrap.Popover(element, {
                    content: function () {
                        return "This is the dynamic content";
                    }
                });
            });
        });



        $(document).ready(function () {
            $('input[type=date]').change(function () {
                input_status1(this)
            });
        });
        $(document).ready(function () {
            $('input[type=datetime-local]').change(function () {
                input_status1(this)
            });
        });

        $(document).ready(function () {
            $('#justAnotherInputBox').change(function () {
                input_status1(this)
            });
        });
        jQuery( document ).ready(function( $ ) {
            $(".multipledata").select2({
                placeholder: {
                    id: '-1', // the value of the option
                    text: 'Select an option',
                },
                allowClear: true,

            });
        });
        $(document).ready(function () {
            $('.multipledata').on('select2:unselect', function (e) {
                var data = e.params.data;
                var multiselect_input = "input[name='"+this.name+"']";
                if(!this.value){
                    var hidden_element = document.createElement('input');
                    hidden_element = $(hidden_element);
                    hidden_element.attr('name',this.name);
                    hidden_element.attr('type',"hidden");
                    hidden_element.attr('value','');
                    $(this).parent().append(hidden_element);
                }
                else{
                    if($(multiselect_input).length){
                        $(multiselect_input).remove();
                    }
                }
                input_status1(this);
            });

            $('.multipledata').on('select2:select', function (e) {
                var data = e.params.data;
                var multiselect_input = "input[name='"+this.name+"']";
                if($(multiselect_input).length){
                    $(multiselect_input).remove();
                }
                input_status1(this);

            });
        });

        $(document).ready(function () {
            $("#checkbox_id").attr('value', 0)
            $('input[type=checkbox]').change(function () {
                cb = $(this);
                if (this.checked != true) {
                    cb.parents(':eq(1)').find('.checkbox_id').val(0);
                    input_status1(this)
                } else {
                    cb.parents(':eq(1)').find('.checkbox_id').val(1);
                }
                input_status1(this)
            });
        });


         class Validation {
            /* In constructor we pass Rule and its values */
            constructor(rule, value, rulesAll = null) {
                this.rule = rule;
                this.value = value;
                this.rulesAll = rulesAll;

                this.res = true;
                this.ruleStringToArray();

            }


            /* This method split the values after this symbol "|" */
            ruleStringToArray() {
                let ruleArray = this.rule;
                this.validate(ruleArray);

            }

            // check type of data
            checkType(type) {
                if (this.rulesAll.type) {
                    return this.rulesAll.type === type;
                } else {
                    return this.rulesAll[0] === type;
                }
            }

            /* Rules for validation for all methods */
            validate(ruleArray) {
                const required = /required/g;
                const integer = /min:*/g;
                const decimal = /min:*/g;
                const max = /max:*/g;
                const min = /min:*/g;
                const slug = /slug/g;
                const urlValidator = /(http|https):\/\/(\w+:{0,1}\w*@)?(\S+)(:[0-9]+)?(\/|\/([\w#!:.?+=&%@!\-\/]))?/;
                const regex = /regex/g;
                const precision = /precision/g;
                const dateRegax = /dateRegax/g;
                const before = /before/g;
                const after = /after/g;
                this.messages = {
                    required: "The value is required.",
                    max: "The value should be equal to or smaller than",
                    min: "The value should be equal to or greater than",
                    format: "Please enter the correct format",
                    integer: "Values must be numbers without a decimal",
                    url: "The Url is not valid",
                    regex: "Not valid",
                    precision: "digit after point",
                    after: "The value is not valid for after",
                    before: "The value is not valid for before",
                    character: "Please Enter the character"
                };
                this.errors = [];

                for (let i in ruleArray) {
                    /* This rule is used when filed is mandatory */
                    if (ruleArray[i].match(required)) {
                        if (!this.value) {
                            this.res = false;
                            this.errors.push(this.messages.required);
                        }

                    }
                     /* This rule applies with the length of minimum. */
                     if (ruleArray[i].match(min)) {
                            let matcher = ruleArray;

                            if (($('input[type="text"]')) && !($.isNumeric(this.value)) && (this.value.length < parseFloat(
                                matcher[1]))) {
                                this.res = false;
                                this.errors.push(`${this.messages.min} ${parseFloat(matcher[1])}`);
                            }
                            if (this.checkType("date") || this.checkType("datetime-local")) {
                                if (!((Date.parse(this.value) >= Date.parse(matcher[1])) && (Date.parse(this.value) <= Date
                                    .parse(matcher[2])))) {
                                    this.res = false;
                                    this.errors.push(this.messages.dateRegax);
                                }
                                this.res = true;
                            }
                            if ((this.checkType("dimension") || this.checkType("weight") || this.checkType("volume") || this
                                .checkType("integer") || this.checkType("decimal") || this.checkType("price"))) {
                                if (this.value < matcher[1]) {
                                    this.res = false;
                                    this.errors.push(`${this.messages.min} ${parseFloat(matcher[1])}`);
                                }
                            }
                        }
                    /* The ruler is used for the maximum character length. */
                    if (ruleArray[i].match(max)) {
                        let matcher = ruleArray;
                        if ((this.checkType("decimal") || this.checkType("integer") || this.checkType("list") || this
                            .checkType("dimension") || this.checkType("weight") || this.checkType("volume") || this
                            .checkType("price")) && (parseFloat(this.value) > parseFloat(matcher[1]))) {
                            if (this.checkType("dimension") || this.checkType("weight") || this.checkType("volume") || this
                                .checkType("integer") || this.checkType("decimal") || this.checkType("price")) {
                                if (parseFloat(matcher[1]) > 0) {
                                    this.res = false;
                                    this.errors.push(`${this.messages.max} ${parseFloat(matcher[1])}`);
                                }

                            }
                        }
                        if (this.value.length > parseFloat(matcher[1])) {
                            this.res = false;
                            this.errors.push(`${this.messages.max} ${parseFloat(matcher[1])}`);

                        }
                        if (this.checkType("date") || this.checkType("date_and_time")) {
                            if (!((Date.parse(this.value) >= Date.parse(matcher[1])) && (Date.parse(this.value) <= Date
                                .parse(matcher[2])))) {
                                this.res = false;
                                this.errors.push(this.messages.dateRegax);
                            }
                            this.res = true;
                        }
                        /* This rule is used to check the upc code */
                    }

                    // Rule for integer
                    if (ruleArray[i].match(integer)) {
                        let matcher = ruleArray;
                        if ((parseFloat(this.value) < parseFloat(matcher[1]))) {
                            this.res = false;
                            this.errors.push(`${this.messages.min} ${parseFloat(matcher[1])}`);
                        }

                    }
                    // Rule for decimal number
                    if (ruleArray[i].match(decimal) && this.value.length > 0 || this.checkType("decimal") && this.value
                        .length > 0 || this.checkType("integer") && this.value.length > 0) {
                        let matcher = ruleArray;
                        if (!$.isNumeric(this.value.split("|")[i])) {
                            this.res = false;
                            this.errors.push(`${this.messages.format}`);
                        }

                    }
                    /* It is used to validate URLs.*/
                    if (ruleArray[i].match(slug)) {
                        if (!this.value.match(urlValidator)) {
                            this.res = false;
                            this.errors.push(this.messages.slug);
                        }
                    }
                    /* This ruler is used for checking the regex. */
                    if (ruleArray[i].match(regex)) {
                        let matcher = ruleArray;
                        if (!this.value.match(matcher[1])) {
                            this.res = false;
                            this.errors.push(this.messages.regex);
                        }
                    }
                    /* This rule is used to check the values after point */
                    if (ruleArray[i].match(precision)) {
                        let matcher = ruleArray;

                        if ((this.value + "").split(".")[1].length > parseInt(matcher[1])) {
                            this.res = false;
                            this.errors.push(this.messages.precision);
                        }
                    }
                    /* This rule is used to check the before date */
                    if (ruleArray[i].match(before)) {
                        let matcher = ruleArray;
                        if ((Date.parse(this.value) >= Date.parse(matcher[1]))) {
                            this.res = false;
                            this.errors.push(this.messages.before);
                        }
                    }

                    /* This rule is used to check the after date */

                    if (ruleArray[i].match(after)) {
                        let matcher = ruleArray[i].split(":");
                        if ((Date.parse(this.value) <= Date.parse(matcher[1]))) {
                            this.res = false;
                            this.errors.push(this.messages.after);
                        }
                    }
                    /* This rule is used to check the upc_bar code */
                    if (this.checkType("upc_barcode") && this.value.length > 0) {
                        let matcher = ruleArray;
                        if (!$.isNumeric(this.value)) {
                            this.res = false;
                            this.errors.push(this.messages.format);
                        }
                    }
                    /* This rule is used to check the integer value */
                    if (ruleArray[i].match(integer) && this.checkType("integer") || this.checkType("integer")) {
                        if (this.value.indexOf(".") !== -1) {
                            this.res = false;
                            this.errors.push(this.messages.integer);
                        }
                    }
                    return false;

                }
            }

            /* This method is used to show the result true or false */
            result() {
                return this.res;
            }

            errorMessages() {
                return this.errors;
            }
        }

        /* Validation("Rule:value", "value") */

        function input_status(obj) {
            if ($(obj).attr('data-rules')) {
                let rules_array = $(obj).attr('data-rules');
                let data_id = $(obj).attr('data-status-id');
                var data_rule = JSON.parse(rules_array);
                const keys = Object.keys(data_rule);
                let self = $(obj);
                let flag = true; // true = success, false = error
                keys.forEach((key, index) => {
                    const item = [];
                    if (data_rule[key] == "0"){
                    item[0] = "";
                    item[1] = "";
                    }else{
                    item[0] = key;
                    item[1] = data_rule[key];
                    }
                   
                    //  const item=key+":"+data_rule[key];
                    let validation = new Validation(item, self.val(), data_rule);
                    if (!validation.result() || self.val() < 0) {
                        let chars = validation.errorMessages();
                        let unique_data = [...new Set(chars)];
                        $(data_id).attr("data-bs-content", unique_data)
                        flag = false;
                        return 0;
                    }
                });
                

                if (flag) {
                console.log(data_id, "true");
                    $(data_id).attr("data-bs-content", '✔');
                    $(data_id).removeClass("circle-error", 1000);
                    $(data_id).addClass("circle-success", 1000);


                } else {
                console.log(data_id, "false")
                    $(data_id).removeClass("circle-success", 1000);
                    $(data_id).addClass("circle-error", 1000);
                }

            }
        }

        /**
         * Trigger input status change on keyup
         * */
        $('select.brands, select.vendors').keyup(function () {
            input_status(this)
        });
        $('select.brands, select.vendors').change(function () {

            input_status(this)
        });
        $('#category_id').change(function () {
            input_status(this)
        });
     
        /**
         * Trigger input status change on page refresh | page load
         * */
        $(document).ready(function () {
        input_status($('#category_id'));
            $("select.brands, select.vendors ").each(function () {
                input_status(this)
            });
        });
    </script>

    <script>
        var rangeSlider = function () {
            var slider = $('.range-slider'),
                range = $('.range-slider__range'),
                value = $('.range-slider__value');

            slider.each(function () {
                value.each(function () {
                    var value = $(this).prev().attr('value');
                    $(this).html(value);
                });

                range.on('input', function () {
                    $(this).next(value).html(this.value);
                });
            });
        };

        rangeSlider();
    </script>

    <script>
    $(document).on('click', '.myTextarea1', function() {
    $(this).addClass('d-none');

    tinymce.init({
                selector: '.multi-line-text',
                verify_html: false,
                plugins: [
                    'a11ychecker', 'advcode', 'advlist', 'anchor', 'autolink', 'codesample', 'fullscreen', 'help',
                    'image', 'editimage', 'tinydrive', 'lists', 'link', 'media', 'powerpaste', 'preview',
                    'searchreplace', 'table', 'template', 'tinymcespellchecker', 'visualblocks', 'wordcount'
                ], 
                toolbar: ' undo redo | bold italic | forecolor backcolor |  codesample | alignleft aligncenter alignright alignjustify | bullist numlist ',
                valid_elements: '*[*]',
                setup: function (editor) {
                    editor.on('keyup', function (event) {
                    isDirty = true;
                        // Handle the change event here
                       var content = editor.getContent({ format: 'text' });
                        const editorElement = editor.getElement();
                        const formStyleElement = editorElement.closest('.formStyle');
                        const labelStatus = formStyleElement.querySelector('span');
                        const rules = editorElement.getAttribute('data-rules');
                        const parsedRules = JSON.parse(rules);
                        const isRequired = parsedRules.required;

                        if (isRequired && content.trim().length <= 0) {
                            labelStatus.classList.replace('circle-success', 'circle-error');
                            labelStatus.setAttribute('data-bs-content', 'The value is required.');
                        } else if (content.trim().length > 63000) {
                            labelStatus.classList.replace('circle-success', 'circle-error');
                            labelStatus.setAttribute('data-bs-content', 'The value should be equal to or smaller than 63000.');
                        } else {
                            labelStatus.classList.replace('circle-error', 'circle-success');
                            labelStatus.setAttribute('data-bs-content', '✔');
                        }
                    });
                }
            });
        });
    </script>
    <script src="https://cdn.tiny.cloud/1/oh0o94omie4zyuas0tk96qk319s6ltqf8eq9er20jhn3d7r7/tinymce/6/tinymce.min.js">
    </script> 

@endpush
