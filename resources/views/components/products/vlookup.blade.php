{{--
    @deprecated This file is deprecated and will be removed in the future.
--}}
<div class="modal fade" id="vlookup_product" data-backdrop="static" data-keyboard="false" tabindex="-1"
     aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-static modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                {{--visible only on edit and add modal--}}
                <button type="button" class="close m-0 py-0 pl-0 d-none" id="v_back">
                    <span aria-hidden="true">
                        <img src="https://img.icons8.com/fluency-systems-regular/20/000000/back.png"/>
                    </span>
                </button>

                <h5 class="modal-title Poppins semibold" id="vlookup_modal_head"
                    style="font-size: 20px">{{__("V-lookup")}}</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body pb-4">
                <div id="vlookup_main">
                    <div class="form-group">
                        <button type="button" id="vlookup_add"
                                class="btn btn-primary btn-block ripplelink my-4">{{__("Create New Vlookup")}}</button>
                        <button type="button" id="vlookup_edit"
                                class="btn btn-outline-primary btn-block hovereffect ripplelink my-4">{{__("Update Existing Vlookup")}}</button>
                    </div>
                </div>
                <div id="vlookup_create" class="d-none">
                    {{--                    <button id="v_back" class="btn btn-secondary">back</button>--}}
                    <form action="javascript:void(0)" method="post" id="vlookup_form">
                        @csrf
                        {{--for add new--}}
                        <div class="form-group d-none" id="for_add_lookup">
                            <label>{{__("Name")}}&nbsp;<span style="color: #ff8178">*</span></label>
                            <input type="text" class="form-control" name="name" placeholder="Color etc..." required>
                            <span class="text-danger p-1 error" data-name="name"></span>
                        </div>

                        {{--for edit--}}
                        <div class="form-group d-none" id="for_edit_lookup">
                            <label>{{__("Name")}}&nbsp;<span style="color: #ff8178">*</span></label>
                            <select id="vlookup_id" type="text" name="id" class="form-control vlookup_class" required>
                                <option value="">Choose</option>
                                @foreach($vlookups as $vlookup)
                                    <option value="{{$vlookup->id}}">{{$vlookup->name}}</option>
                                @endforeach

                            </select>
                            <span class="text-danger p-1 error" data-name="name"></span>
                        </div>

                        <div class="form-group mt-2 mb-5">
                            <label for="description">Values</label>
                            <textarea type="text" class="form-control " id="lookup_values" name="values"
                                      placeholder="blk,Balck&#10;ylw,Yellow&#10;grn,Green&#10;etc..." rows="5"
                                      required></textarea>
                            <span class="text-danger p-1 error" data-name="values"></span>
                        </div>
                        <div class="form-group">
                            <input type="hidden" name="submit_type" id="submit_type" value="">
                            <button type="button" id="vlookup_btn"
                                    class="btn btn-primary btn-block ripplelink">{{__("Create")}}</button>
                        </div>
                    </form>
                </div>
                <div id="vlookup_success" class="text-center d-none">

                    <h5 class="bold text-dark">
                        VLookup created successfully.
                    </h5>

                    <img
                        src="https://cdn.dribbble.com/users/2185205/screenshots/7886140/media/90211520c82920dcaf6aea7604aeb029.gif"
                        alt="" class="img-fluid">

                    <button class="btn btn-primary btn-block ripplelink mb-4" id="v_back_btn">
                        Create or Edit V-lookup
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

@push('footer_scripts')
    <script>
        $(document).ready(function () {


            $("#vlookup_add").on('click', function () {
                $(".error").html("");
                $("#vlookup_form").trigger("reset");
                $('#vlookup_main').addClass('d-none');
                $('#vlookup_create').removeClass('d-none');

                $('#v_back').removeClass('d-none');
                $('#vlookup_modal_head').html("Create New V-lookup");
                $('#vlookup_btn').html("Create");

                $('#for_edit_lookup').addClass('d-none');
                $('#for_add_lookup').removeClass('d-none');
                $('#submit_type').val("add");
            });

            $("#vlookup_edit").on('click', function () {
                $(".error").html("");
                $("#vlookup_form").trigger("reset");
                $('#vlookup_main').addClass('d-none');
                $('#vlookup_create').removeClass('d-none');

                $('#v_back').removeClass('d-none');
                $('#vlookup_modal_head').html("Update V-lookup");
                $('#vlookup_btn').html("Update");

                $('#for_add_lookup').addClass('d-none');
                $('#for_edit_lookup').removeClass('d-none');
                $('#submit_type').val("edit");
            });

            $("#v_back, #v_back_btn").on('click', function () {
                $(".error").html("");
                $("#vlookup_form").trigger("reset");
                $("#lookup_values").text("");
                $('#vlookup_create').addClass('d-none');
                $('#v_back').addClass('d-none');
                $('#vlookup_main').removeClass('d-none');
                $('#vlookup_modal_head').html("V-lookup");
                $('#vlookup_success').addClass('d-none');

            });

            $("#vlookup_btn").on('click', function () {
                $(".error").html("");
                $("#vlookup_form").submit();
            });

            //hide-back button here
            $('#vlookup_product').on('hidden.bs.modal', function () {
                $('#vlookup_success').addClass('d-none');
                $('#vlookup_create').removeClass('d-none');
                /*$('#vlookup_modal_head').html("V-lookup");
                $("#vlookup_btn").html('Create');*/
                $("#v_back").trigger("click");
                $("#vlookup_form").trigger("reset");
                $("#lookup_values").text("");

            });

            $("#vlookup_form").on('submit', function (form) {
                $.ajaxSetup({
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    }
                });
                $.ajax({
                    url: '{{route('vlookup.store')}}',
                    type: "POST",
                    data: $('#vlookup_form').serialize(),
                    success: function (response) {
                        $("#vlookup_btn").html('<span class="spinner-border spinner-border-sm ml-2" role="status" aria-hidden="true"></span>');
                        setTimeout(function () {
                            $('#vlookup_modal_head').html(""/*response.message*/);
                            $('#vlookup_create').addClass('d-none');
                            $('#vlookup_success').removeClass('d-none');
                            $('#v_back').addClass('d-none');
                        }, 2000);
                        var data = response.data;
                        if ($(".vlookup_class option[value='" + data['id'] + "']").length <= 0) {
                            $('.vlookup_class').append($('<option>', {
                                value: data['id'],
                                text: data['name']
                            }));
                        }
                    },
                    error: function (response) {
                        console.log('error');
                        const resp = response.responseJSON.data;
                        Object.entries(resp).forEach(function (item, index) {
                            $('span[data-name="' + item[0] + '"]').html(item[1][0]);
                        });
                    }
                });
            });

            $("#vlookup_id").on('change', function (id) {
                $("#lookup_values").val("");
                $('input[name="name"]').val($(this).children(':selected').text());
                $.ajaxSetup({
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    }
                });
                $.ajax({
                    url: '{{route('vlookup.fetch')}}',
                    type: "POST",
                    data: $('#vlookup_form').serialize(),
                    success: function (response) {
                        $("#lookup_values").val(response.data);
                    },
                    error: function (response) {
                        console.log('error');
                        $("#lookup_values").text("");
                    }
                });
            });
        });
    </script>

@endpush
