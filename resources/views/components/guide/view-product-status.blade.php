<div class="col-12 mt-4">
    <div class="border rounded-3 shadow-sm px-4 py-4">
        <h3 class="fw-700 text-uppercase mb-0">{{trans('channel_shopify_channels.product_status')}}</h3>
        <div class="d-flex flex-column flex-sm-row justify-content-between mt-4 ms-xxl-4">
            <div class="field-score-chart d-inline-flex align-self-center">
            {{--                <canvas id="imageQualityScore1" width="110" height="110" style="display: block; box-sizing: border-box; height: 110px; width: 110px;"></canvas>--}}
            @if(isset($channel))
                <!--graph for channel page -->
                    @if(sizeof($channel) > 0)
                        <canvas id="doughnutChart3" width="110" height="110" style="display: block; box-sizing: border-box; height: 110px; width: 110px;"></canvas>
                    @else
                        @if(isset($product_stats) ? ($product_stats['publish_product'] == 0 && $product_stats['draft_product'] ==0 && $product_stats['sync_product']== 0) : true)
                            <x-dashboard.empty-doughnut-chart />
                        @else
                            <div>
                                <canvas id="doughnutChart2" width="110" height="110" style="display: block; box-sizing: border-box; height: 110px; width: 110px;"></canvas>
                            </div>
                        @endif
                    @endif
                @else
                <!--graph without channel id -->
                    <x-dashboard.empty-doughnut-chart/>

                @endif
            </div>
            <div class="d-flex flex-column align-items-start align-items-md-center  justify-content-around ms-4">
                <span class="score-label score-label-publish mt-4 mt-md-0"></span>
                @if(isset($channel) && sizeof($channel) >0)
                    <span class="score-label score-label-success mt-4 mt-md-0"></span>
                @else
                    <span class="score-label score-label-draft mt-4 mt-md-0"></span>
                @endif
            </div>
            {{--Nechy if else condition main redundent code iqtidar ny kia hai mery pc py --}}

            <div class="d-flex flex-column justify-content-sm-start justify-content-md-center mt-4 mt-md-0 ms-4 flex-grow-1">
                @if(isset($channel) && sizeof($channel) >0)

                    <h4 class="mb-0 fw-700">{{ trans('apimio_dashboard.update_available') }}</h4>
                    <p class="mb-0 ">{{trans('apimio_dashboard.update_available_description') }}</p>


                    <h4 class=" mb-0 fw-700 mt-1"> {{ trans('apimio_dashboard.synced') }}</h4>
                    <p class="mb-0">{{ trans('apimio_dashboard.synced_description') }}</p>

                @else

                    <h4 class="mb-0 fw-700">  {{ trans('apimio_dashboard.published') }}</h4>
                    <p class="mb-0 ">{{ trans('apimio_dashboard.published_description') }}</p>


                    <h4 class=" mb-0 fw-700 mt-1">{{ trans('apimio_dashboard.draft') }}</h4>
                    <p class="mb-0">{{ trans('apimio_dashboard.draft_description') }}</p>
                @endif
            </div>
            @if(isset($channel) && sizeof($channel) >0)
                <div
                    class="d-flex flex-column align-items-start align-items-md-center justify-content-around mt-1 mt-md-0 ms-4">
                    <span class="status status-publish mt-4 mt-md-0">{{$product_stats['update_available_product']??0}}</span>
                    <span class="status status-success mt-4 mt-md-0"> {{ $product_stats['sync_product']??0 }}</span>
                </div>
            @else
                <div
                    class="d-flex flex-column align-items-start align-items-md-center justify-content-around mt-1 mt-md-0 ms-4">
                    <span class="status status-publish mt-4 mt-md-0">{{$product_stats['publish_product']??0}}</span>
                    <span class="status status-draft mt-4 mt-md-0">{{$product_stats['draft_product']??0}}</span>
                </div>
            @endif
        </div>


        <div class="col-12 mt-3 pt-1">
            <p class="m-0">
                {{--{!! trans('apimio_dashboard.sku_count', ['total_sku' => '<strong>'.$data['remaining_skus']." SKU's".'</strong>']) !!}--}}
            </p>
        </div>

    </div>
</div>

@push('footer_scripts')
    @if(isset($channel) && sizeof($channel) >0)

        <script>
            let data2 = {
                labels: ['Synced' , 'Update available'],
                datasets: [{
                    label: ['Synced', 'Update available'],
                    data:{{$product_stats['graph'] ?? 0}},
                    backgroundColor: [
                        "#72CC60",
                        "#2C4BFF",
                    ],
                }],
            };
            new Chart("doughnutChart3", {
                data: data2,
                type: 'doughnut',
                options: {
                    cutout: 35,
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    }
                }
            });
        </script>

    @else

        <script>
            let data2 = {
                labels: ['Published', 'Draft'],
                datasets: [{
                    label: ['Published', 'Draft'],
                    data:{{$product_stats['graph'] ?? 0}},
                    backgroundColor: [
                        "#2C4BFF",
                        "#A5A5A5",
                    ],
                }],
            };
            new Chart("doughnutChart2", {
                data: data2,
                type: 'doughnut',
                options: {
                    cutout: 55,
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    }
                }
            });
        </script>

    @endif
@endpush
