@extends('layouts.app',['sidebar_display'=>false])
@section('titles','Billing')
@section('content')
    <div class="container">
        <div class="card border-radius shadow-none mb-4">
            <div class="card-body p-0">
                <div class="row">
                    <div class="col-12">
                        <div class="mt-4">
                            <h3 class="m-0 font-24 Roboto bold text-dark">{{trans('billing.plan_page_title')}}</h3>
                            <div class="my-3 Roboto regular">{{trans('billing.plan_page_description')}}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <hr class="mt-1 mb-4 divider">
        <x-billing.plans name="channel.shopify.bill.store" shopifyChannelsId="{{ $shopify_channels_id }}" :user="\Illuminate\Support\Facades\Auth::user()" stripe="Null" shopifyTrial="{{ $check_trial == 'trial_started' ? true : false }}"/>

    </div>
@endsection
