@extends('layouts.app_new',['sidebar_display'=>false])
@section('titles','Organization')
@section('content')
    <div class="row">
        <div class="col-xs-12 col-md-12 col-sm-12 col-lg-12 align-content-center">
            <div class="flex-column-fluid d-flex flex-column text-center my-4">
                <!--Logo-->
                <div class="d-flex flex-column flex-column-auto mt-4">
                    <a href="{{env('APIMIO_URL')}}">
                        <img src="{{asset('/media/logo.png')}}" class="h-40" alt="Logo">
                    </a>
                </div>
                <div class="row d-flex justify-content-center">
                    <div class="col-12 col-md-10 col-xl-5 mt-5">
                        <h2 class="">{{ trans('organization.page_title') }}</h2>
                        <p class="clr-grey">{{ trans('organization.page_description') }}</p>
                        <div class="form-group mb-3">
                            <a href="{{ route("organization.create") }}"
                               class="btn btn-outline-dashed-primary w-100 text-decoration-none">
                                <img src="{{asset('/media/icon_new.png')}}" alt=""> &nbsp;
                                {{ trans('organization.create_org_btn') }}

                            </a>
                        </div>
                        <form method="POST" id="workspace_form" action="">
                            @csrf
                            <div class="pb-4 ">
                                @foreach($organizations as $key => $organization)
                                    <div class=" mb-4 text-left">
                                        <div class="card">
                                            <div class="card-body p-3">
                                                <div class="row d-flex justify-content-between align-items-center">
                                                    <div class="col text-start">
                                                        <a href="{{ route("organization.active", $organization->id) }}" class="clr-black text-decoration-none mb-0">
                                                           {{ $organization->name }}
                                                        </a>
                                                    </div>
                                                    <div class="col text-end">
                                                        <div class="float-right">
                                                            <div
                                                                class="workspaces-tigger float-right position-relative round">
                                                                <a id="org_select_btn"
                                                                   href="{{ route("organization.active", $organization->id) }}"
                                                                   class="btn btn-primary pt-2 ms-2 arrow-btn">
                                                                   <i class="icon icon-arrow-right"></i>
                                                                </a>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </form>
                        <div class="form-group mb-4">
                            @if(!isset(auth()->user()->organzation_id))
                                <div class="d-flex justify-content-center">
                                    <a id="sidebar_logout_btn" href="{{--{{ route('logout') }}--}}" title="Logout"
                                       class="btn btn-outline-primary w-50 pt-2"
                                       onclick="event.preventDefault(); document.getElementById('logout-form').submit();"
                                       aria-label="Logout" data-microtip-position="right" role="tooltip">
                                        <div class="d-flex justify-content-center align-items-center">
                                            <i class="icon icon-logout fs-20"></i>
                                            <span class=" ms-2 {{(Request::segment(1) == 'products')?'d-none':((Request::segment(1) == 'channel')?'d-none':((Request::segment(1) == 'product')?'d-none':'d-block'))}}">
                                        {{ trans('sidebar.logout') }}
                                    </span>
                                        </div>
                                        <form id="logout-form" action="{{ route('logout') }}" method="POST" style="display: none;">
                                            @csrf
                                        </form>
                                    </a>
                                </div>
                            @endisset
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>
@endsection
@push('footer_scripts')
    <script>
        function addRetailer(id) {
            $('#' + id).prop('checked', true);
            $('.type').val('add');
            $('#workspace_form').submit();
        }
        $(".workspaces-tigger").click(function () {
            $('.type').val('');
            let inputs = $(this).find("input[name='workspaces_id']");
            if (inputs.checked) {
                inputs.checked = false;
            } else {
                inputs.checked = true;
            }
        });
        function get_random_color() {
            let letters = '0123456789ABCDEF'.split('');
            let color = '#';
            for (let i = 0; i < 6; i++) {
                color += letters[Math.round(Math.random() * 15)];
            }
            return color;
        }
        $(document).ready(function () {
            $(".colors").each(function () {
                $(this).css("background-color", get_random_color());
            });
        })
    </script>
@endpush

