@extends('layouts.app_new',['sidebar_display'=>false])
@section('titles','Organization Checkpoint')
@section('content')
    <div class="row h-100">

        <div class="col-12 justify-content-center mt-5 pt-4 center-align" >
            <div class="row justify-content-center text-center" >
                <div class="col-12 col-sm-10 col-md-10 col-lg-6 col-xl-4 px-3">
                    <!--Logo-->
                    <div class="d-flex flex-column flex-column-auto mt-3 text-center">
                        <a href="{{env('APIMIO_URL')}}">
                            <img src="{{asset('media/logo.png')}}" class="h-40" alt="Logo">
                        </a>
                    </div>
                    <div class="mt-3">
                        <div class="mb-4 pb-3">
                            <h2 class="text-center">
                                {{ trans('organization_checkpoint.page_title') }}
                            </h2>
                            <p class="text-center">
                                {{ trans('organization_checkpoint.page_description') }}
                            </p>
                        </div>
                    </div>
                    <form id="org_cp_form" method="POST" action="" class="formStyle">
                        @csrf
                        <div class="form-group mb-4">
                            <button id="org_cp_shop_btn" type="button"
                                    data-bs-toggle="modal" data-bs-target="#exampleModal"
                                    class="btn border w-100 d-flex justify-content-center align-items-center shopify-btn-hover">
                                <img class="shopify-img me-1" src="https://img.icons8.com/color/100/000000/shopify.png"/>

                                {{ trans('organization_checkpoint.shopify_btn') }}

                                {{--<svg class="float-right" xmlns="http://www.w3.org/2000/svg" x="0px" y="0px"
                                     width="26" height="26"
                                     viewBox="0 0 172 172"
                                     style="fill:#000000;">
                                    <g fill="none" fill-rule="nonzero" stroke="none" stroke-width="1" stroke-linecap="butt" stroke-linejoin="miter"
                                       stroke-miterlimit="10" stroke-dasharray="" stroke-dashoffset="0" font-family="none" font-weight="none" font-size="none"
                                       text-anchor="none" style="mix-blend-mode: normal">
                                        <path d="M0,172v-172h172v172z" fill="none"></path>
                                        <g fill="#ffffff">
                                            <path d="M145.48333,43.35833l-84.56667,84.56667l-34.4,-34.4l-10.03333,10.39167l44.43333,44.075l94.6,-94.6z"></path>
                                        </g>
                                    </g>
                                </svg>--}}

                            </button>
                        </div>

                        <div class="form-group mb-4">
                            <button type="button"
                                    class="btn w-100 d-flex justify-content-center align-items-center" disabled>
                                <img class="shopify-img me-1 mb-1" src="https://img.icons8.com/color/75/ffffff/magento.png"/>
                                <span class="clr-grey ms-2">{{ trans('organization_checkpoint.magento_btn') }}</span>
                            </button>
                        </div>

                        <div class="form-group mb-5">
                            <button type="button"
                                    class="btn w-100 d-flex justify-content-center align-items-center" disabled>
                                <img class="shopify-img me-1 mb-1" src="{{asset('media/new-flow/woo.png')}}"/>
                                <span class="clr-grey ms-2">{{ trans('organization_checkpoint.woocommerce') }}</span>
                            </button>
                        </div>

                        <div class="form-group ">
                            <a id="org_cp_done_btn" href="{{ route("organization.edit", \Illuminate\Support\Facades\Auth::user()->organization_id) }}" id="next-btn"
                                    class="btn btn-primary w-100">
                                {{ trans('organization_checkpoint.connect_later_btn') }}
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <!-- Modal -->
    <div class="modal fade" id="exampleModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-lg" style="width: 40em;">
            <div class="modal-content">
                <form id="org_cp_con_shop_form" method="GET" action="{{ route("channel.shopify.install") }}" class="formStyle">
                    <div class="modal-header pb-3">
                        <h3 class="modal-title" id="exampleModalLabel">{{ trans('organization_checkpoint.modal_title') }}</h3>
                        <button type="button" class="btn-sm border-0 bg-white fs-24 px-0 pt-0" data-bs-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body border-top border-bottom">
                        <input name="should_not_billed" type="hidden" value="1">
                        <label for="shop">{{ trans('organization_checkpoint.shop_url') }}</label>
                        <input name="shop" type="text"
                               class="form-control @error('name') is-invalid @enderror"
                               placeholder="your-shop-url.myshopify.com" required />
                        <br>
                        <div class="form-group">
                            <label >{{ trans('organization_checkpoint.sync_shopify_products') }}</label>
                            <select name="sync_product" class="form-control"
                                    required >
                                <option value="">Select ...</option>
                                <option value="yes">Yes</option>
                                <option value="no">No</option>
                            </select>
                        </div>
                        <div class="pl-1 py-2">
                            <small style="color: #6b7177">
                                {{ trans('organization_checkpoint.no_store') }}
                                <a href="https://www.shopify.com/" target="_blank" style="color: #008060!important;"
                                   class="inline-block text-decoration-none Roboto forgot-text">
                                    {{ trans('organization_checkpoint.shopify_url') }}
                                </a>
                            </small>
                        </div>
                    </div>
                    <div class="modal-footer justify-content-end py-3">
                        <button type="button" class="mr-3 btn btn-outline-danger btn-shopify-secondary" data-bs-dismiss="modal">
                            {{ trans('organization_checkpoint.cancel_btn') }}</button>
                        <button id="org_cp_con_btn" type="submit" class="btn btn-primary btn-shopify-primary">{{ trans('organization_checkpoint.connect_btn') }}</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
@endsection
