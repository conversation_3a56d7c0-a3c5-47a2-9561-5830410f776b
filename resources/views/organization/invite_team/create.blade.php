@extends('layouts.app_new')
@section('titles','Invite Team Member')
@section('content')
    <style>
        .SumoSelect > .CaptionCont {background-color: #F8F8F8;}
        .SumoSelect.open .search-txt {height: 34px;}
    </style>
    <div>
        <x-products.page-title name="{{trans('invite_team_create.page_title')}}" description="{{trans('invite_team_create.page_description')}}" links="false" button="false">
        </x-products.page-title>


        <form action="{{ route('organization.invite_team.store') }}" method="POST" id="add_retailer_form" class="formStyle">
            @csrf
            <input type="hidden" value="retailer" name="type">
            <div class="row">
                <div class="col-lg-6 col-md-6 col-xl-5 col-12">
                    <div class="row">
                        <div class=" col-12">
                            <div class="form-group mb-3">
                                <label for="fname">
                                    {{trans('invite_team_create.first_name')}}&nbsp;<span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control @error('first_name') is-invalid @enderror"
                                       name="first_name" value="{{ old("first_name") }}" required>
                                @error('first_name')
                                <span class="text-danger" role="alert">
                                    <small>{{ $message }}</small>
                                </span>
                                @enderror
                            </div>
                        </div>
                        <div class="col-12 ">
                            <div class="form-group mb-3">
                                <label for="lname">
                                    {{trans('invite_team_create.last_name')}} <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control @error('last_name') is-invalid @enderror"
                                       name="last_name" value="{{ old("last_name") }}">
                                @error('last_name')
                                <span class="text-danger" role="alert">
                                    <small>{{ $message }}</small>
                                </span>
                                @enderror
                            </div>
                        </div>
                    </div>
                    <div class="form-group mb-3">
                        <label for="email">
                            {{trans('invite_team_create.email')}}&nbsp;<span class="text-danger">*</span>
                        </label>
                        <input type="text" class="form-control @error('email') is-invalid @enderror"
                               name="email" value="{{ old("email") }}" required>
                        @error('email')
                        <span class="text-danger" role="alert">
                            <small>{{ $message }}</small>
                        </span>
                        @enderror
                    </div>

                    <p class="mb-0 clr-grey">{{trans('invite_team_create.assign_member')}}</p>
                    <hr class="divider mt-2 mb-4">
                    <div class="form-group">
                        <label for="permissions">
                            {{trans('invite_team_create.permission_select')}}&nbsp;<span class="text-danger">*</span>
                        </label>
                        <select id="permissions" multiple="multiple" name="permission_ids[]"
                                class="form-control bg-white-smoke btn @error('permission_ids') is-invalid @enderror">
                            @foreach($permissions as $permission)
                                <option value="{{ $permission->id }}" class="text-color"
                                    {{ old("permission_ids") ? in_array($permission->id, old("permission_ids"))? "selected":null: null }}>
                                    {{ $permission->name }}
                                </option>
                            @endforeach
                        </select>

                        @error('permission_ids')
                        <span class="text-danger" role="alert">
                            <small>{{ $message }}</small>
                        </span>
                        @enderror

                    </div>
                    <div class="d-flex justify-content-end mt-5">

                        <a id="cancel-vendor" href="{{ route('organization.invite_team.index') }}"
                           class="btn btn-outline-danger">
                            {{trans('invite_team_create.cancel_btn')}}
                        </a>

                        <button type="submit" id="add-vendor" href="" class="btn btn-primary ms-2">
                            {{trans('invite_team_create.add_and_invite_btn')}}
                        </button>
                    </div>
                </div>
            </div>
        </form>
    </div>

@endsection
@push('footer_scripts')
    <!--Phone Flags-->
    <script>
        function searchKeyPress(e)
        {
            e = e || window.event;
            if (e.keyCode ===13)
            {
                document.getElementById('registeration_next_btn_id').click();
                return false;
            }
            return true;
        }
    </script>
    <script>

        $('#permissions').SumoSelect({
            placeholder: 'Select Permissions',
            csvDispCount: 3,
            captionFormat: '{0} Selected',
            captionFormatAllSelected: 'All Selected',
            okCancelInMulti: true,
            isClickAwayOk: true,
            selectAll: true,
            search: true,
            searchText: 'Search here',
            noMatch: 'No matches for {0}',

        });
    </script>
@endpush
