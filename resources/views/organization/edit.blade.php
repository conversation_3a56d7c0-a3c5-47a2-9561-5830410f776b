@extends('layouts.app',['sidebar_display'=>false])
@section('titles','Add Organization')
@section('content')
<div class="row h-100">

    <div class="col-12 justify-content-center mt-4 " style="max-width: 750px; margin: 0 auto">
        <!--Logo-->
        <div class="d-flex flex-column flex-column-auto  text-center">
            <a href="{{env('APIMIO_URL')}}">
                <img src="{{asset('media/logo.png')}}" alt="Logo">
            </a>
        </div>

        <div class="row justify-content-center">

            <div class="col-12 col-sm-10 col-md-10 col-lg-10 px-3 mt-3">
                <form method="POST" action="{{route('organization.update', [$organization->id])}}">
                    @csrf
                    @method('PUT')
                    <div class="pb-4">
                        <div class="mb-4">
                            <h3 class="Poppins bold text-dark font-32 line-height-45">
                                {{__("What's the name of your company or team?")}}
                            </h3>
                        </div>

                        <div class="d-flex mb-5">
                            <p class="Roboto text-left text-dark m-0">
                                {{__("This will be the name of your Apimio workspace. Choose something your team will recognize.")}}
                            </p>
                        </div>

                        <div class="form-group">
                            <label for="workspace">{{__("Name")}}&nbsp;<span style="color: #ff8178">*</span></label>
                            <input id="workspace" name="name" value="{{ $organization->name }}" type="text"
                                class="form-control @error('name') is-invalid @enderror"
                                placeholder="Organization Name e.g apimio" autofocus required />
                            @error('name')
                            <span class="text-danger" role="alert">
                                <small>{{ $message }}</small>
                            </span>
                            @enderror
                        </div>
                        <div class="form-group">
                            <label for="timezone">Region</label>
                            <select id="timezone" class="form-control @error('region') is-invalid @enderror"
                                name="region" required>
                                <option value="" disabled="" selected="">Select region</option>
                                <option value="AP">Asia Pacific</option>
                                <option value="AM">American</option>
                                <option value="EU">Europe</option>
                                <option value="ME">Middle East</option>
                                <option value="AF">Africa</option>
                            </select>
                            @error('region')
                            <span class="text-danger" role="alert">
                                <small>{{ $message }}</small>
                            </span>
                            @enderror
                        </div>
                        <div class="form-group">
                            <label for="units">Units</label>
                            <select id="units" class="form-control @error('units') is-invalid @enderror" name="units"
                                required>
                                <option value="" disabled="" selected="">Select unit</option>
                                <option value="SI">Metrics System</option>
                                <option value="Imperial">Imperial System</option>
                            </select>
                            @error('units')
                            <span class="text-danger" role="alert">
                                <small>{{ $message }}</small>
                            </span>
                            @enderror

                        </div>

                        <div class="form-group">
                            <label for="currency">Currency</label>
                            <select id="currency" class="form-control @error('currency') is-invalid @enderror"
                                name="currency" required>
                                <option value="" disabled="" selected="">Select currency</option>
                                <option value="EUR">Euro</option>
                                <option value="USD">Dollar</option>
                                <option value="GBP">Pound</option>
                            </select>
                            @error('currency')
                            <span class="text-danger" role="alert">
                                <small>{{ $message }}</small>
                            </span>
                            @enderror


                        </div>

                    </div>

                    <div class="form-group mt-4">
                        <button id="workspace_btn" type="submit" class="form-control btn btn-primary ripplelink"
                            onclick="workspaceCreating()">
                            {{__('Update Workspace')}}
                        </button>
                        <span id="loading_btn" class="form-control btn btn-primary ">
                            {{__('Creating workspace...')}}
                            <span class="spinner-border spinner-border-sm ml-2" role="status" aria-hidden="true"></span>
                        </span>
                    </div>

                </form>
            </div>

        </div>

    </div>
</div>
@endsection
@push('footer_scripts')
<script>
let timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
$('#timezone').val(timezone).change();

function workspaceCreating() {
    let workspace = $('#workspace').val();
    let timezone = $('#timezone').val();
    let units = $('#units').val();
    let currency = $('#currency').val();

    if (workspace != "" && timezone != null && units != null && currency != null) {
        $('#workspace_btn').hide();
        $('#loading_btn').css('display', 'block');
    }
}
</script>
@endpush