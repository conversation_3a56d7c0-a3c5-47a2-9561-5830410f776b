<?php ?>
@extends('layouts.app_new')
@section('titles','All Retailers')
@section('content')
    <div>
        <x-products.page-title name="{{trans('retailer.page_title')}}"
                               description="{{trans('retailer.page_description')}}"
                               links="false"
                               button="true">
            <x-slot name="addbutton">
                <a href="{{route('retailer.create')}}"id="add-retailer"
                   class="btn btn-primary float-lg-right float-md-right only-disabled">{{trans('retailer.invite_retailer_btn')}}</a>
            </x-slot>
        </x-products.page-title>

        <form action="" method="GET" id="search-retailer-listing">
            <div class="col-12 col-md-12 col-lg-12 col-xl-4">
                <div class="d-flex flex-row">
                    <div class="input-group">
                        <input type="text" class="form-control" value="{{request('q')}}" name="q"
                               placeholder="Search by Email"
                               aria-label="Search by Email" aria-describedby="search">
                        @if(request()->has('q'))
                            <a href="{{ \Illuminate\Support\Facades\Request::url() }}"> </a>
                        @endif
                        <div class="input-group-append">
                            <button class="search" type="submit" id="search">
                                <img src="{{asset('media/retailer-dashboard/Search-2.png')}}" alt="search">
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </form>

        @if(count($data["retailers"]) > 0)
            <div class="table-responsive mt-3">
                <table class="table table-hover table-borderless">
                    <thead>
                    <tr>
                        <th scope="col">{{__("NAME")}}</th>
                        <th>{{__("EMAIL")}}</th>
                        <th>{{__("STATUS")}}</th>
                        <th scope="col">{{__("SHARED Stores")}}</th>
                        <th scope="col" class="text-end">{{__("Actions")}}</th>
                    </tr>
                    </thead>
                    <tbody>
                    @foreach($data["retailers"] as $retailer)
                        <tr onclick="location.href ='{{ route("retailer.show", $retailer->id) }}'"
                            class="cursor-pointer">
                            {{--                            When invite is sent--}}
                            @if($retailer->get_status() == 4)
                                <td class="text-capitalize">{{ $retailer->fname . " " . $retailer->lname }}</td>
                                <x-invite.arrow :emailAuth="$retailer->email" :email="$retailer->email"/>
                                <td>{!! $retailer->get_status_badge() !!}</td>
                                {{--                                when invite is connected--}}
                            @elseif($retailer->get_status() == 1)
                                {{--                                when connected invite is sent by user--}}
                                @if($retailer->email != auth()->user()->email)
                                    <td>{{  $retailer->user_sender_without_scope()->pluck("fname")->first() . " " .$retailer->user_sender_without_scope()->pluck("lname")->first() }}</td>
                                    <x-invite.arrow :emailAuth="$retailer->email" :email="$retailer->email"/>
                                    <td>{!! $retailer->get_status_badge() !!}</td>
                                    {{--                                    when connected invite is recieved by user--}}
                                @else
                                    <td>{{ $retailer->organization_sender_without_scope->users_without_scope->first()->fname . " " .$retailer->organization_sender_without_scope->users_without_scope->first()->lname   }}</td>
                                    <x-invite.arrow :emailAuth="$retailer->email" :email="$retailer->organization_sender_without_scope->users_without_scope->first()->email"/>
                                    <td>{!! $retailer->organization_sender_without_scope->users_without_scope->first()->get_user_status($retailer) !!}</td>
                                @endif
                                {{--                                when invite is declined or disconnected--}}
                            @elseif(($retailer->get_status() == 2 || 5) && ($retailer->email != auth()->user()->email) )
                                <td>{{ \App\User::where('email',$retailer->email)->pluck('fname')->first() . " " . \App\User::where('email',$retailer->email)->pluck('lname')->first() }}</td>
                                <x-invite.arrow :emailAuth="$retailer->email" :email="$retailer->email"/>
                                <td>{!! $retailer->get_status_badge() !!}</td>
                            @else
                                <td class="text-capitalize">{{ $retailer->organization_sender_without_scope->users_without_scope->first()->fname . " " . $retailer->organization_sender_without_scope->users_without_scope->first()->lname }}</td>
                                <x-invite.arrow :emailAuth="$retailer->email" :email="$retailer->organization_sender_without_scope->users_without_scope->first()->email"/>
                                <td>{!! $retailer->organization_sender_without_scope->users_without_scope->first()->get_user_status($retailer) !!}</td>
                            @endif
                            <td>{{$retailer->get_catalogs($retailer) }}</td>
                            {{--                        Edit button when invite is recieved--}}
                            @if( ($retailer->get_status() == 3) || ($retailer->email == auth()->user()->email) )
                                <td
                                    style="filter:invert(1) grayscale(100%); cursor: default">
                                    <a class="pro_ven_edit mr-3 text-decoration-none"
                                       title="Disabled:Incoming Invite"
                                       style="cursor: default">
                                        <i class="fa-regular fa-pen-to-square fs-20"></i>
                                    </a>
                                </td>
                            @else
                                <td class="text-end">
                                    <a href="{{route('retailer.edit', $retailer->id)}}"
                                       class="pro_ven_edit mr-3 edit-btn text-decoration-none">
                                        <i class="fa-regular fa-pen-to-square fs-20"></i>
                                    </a>
                                </td>
                            @endif
                        </tr>
                    @endforeach

                    </tbody>
                </table>
            </div>
        @else
            <x-general.empty-page description="{{trans('retailer.page_empty')}}"/>
        @endif
    </div>

    <!-- Modal DELETE-->
    <div class="modal fade" id="delete-modal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title Poppins semibold"
                        id="exampleModalLabel">{{trans('retailer.modal_title')}}</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <p class="Poppins regular">{{trans('retailer.modal_description')}}
                        {{--                        <strong><span id="name">{{__('name')}}</span></strong>--}}
                    </p>

                    <div class="modal-footer p-0">
                        <button type="button" data-dismiss="modal" id="delete-cancel-btn"
                                class="btn btn-black text-dark Roboto bold float-left shadow"
                                style="width: 120px;">
                            {{trans('retailer.cancel_btn')}}
                        </button>
                        <form action="#" id="delete-family" method="post">
                            @csrf
                            @method('DELETE')
                            <a href="#"
                               id="pro_fam_del_btn"
                               class="ripplelink border-radius Poppins semibold btn del-btn shadow"
                               onclick="del()">
                                {{trans('retailer.delete_btn')}}
                            </a>
                        </form>
                    </div>

                </div>
            </div>
        </div>
    </div>

@endsection
@push('footer_scripts')
@endpush
