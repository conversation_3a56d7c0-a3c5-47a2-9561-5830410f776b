@extends('layouts.app_new', ['sidebar_display' => false])
@section('titles', 'Signup')
@section('content')
    @push('meta_tags')
        <link rel=“canonical” href=“https://app.apimio.com/” />
        <meta name="title" content="Sign up to create a new account - Apimio">
        <meta name="description"
            content="Sign up for a free 14-day trial and gain access to all of Apimio’s features. No credit card information is required.">
        <meta name="keywords" content="Create Account, Sign up,  Apimio, PIM software, Cloud PIM, What is PIM">
        <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.3.1/jquery.min.js"></script>
        {!! RecaptchaV3::initJs() !!}
    @endpush
    @push('header_scripts')
        <!--For Phone Flags-->
        <link rel="stylesheet" href="{{ asset('css/plugin/intlTelInput.css') }}" type="text/css" />
        <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.3.1/jquery.min.js"></script>
        {!! RecaptchaV3::initJs() !!}
    @endpush

    <div class="row">
        <!--Left side-->
        <div id="Signup-page" class="col-12 col-sm-12 col-md-12 col-lg-7 pt-4">
            <!--Signup Form-->
            <div class="row justify-content-center align-items-center">
                <div class="col-12 col-sm-8 px-3 mt-5 auth-screen-width">
                    <div class="d-flex flex-column flex-column-auto mb-4 text-center">
                        <a href="{{ env('APIMIO_URL') }}">
                            <img src="{{ asset('media/logo.png') }}" class="h-40" alt="Logo">
                        </a>
                    </div>
                    <div class="mb-4">
                        <h1 class="text-center text-dark mb-0">
                            {{ trans('register.page_title') }}
                        </h1>
                        <!--google signup button-->
                        <x-auth.google-button :link="route('google.auth')" id="reg_google_btn" />
                        <div class="d-flex mt-4 align-items-center">
                            <div class="border-top-design w-100 me-2"></div>
                            <span class="clr-grey">{{ __('OR') }}</span>
                            <div class="border-top-design w-100 ms-2"></div>
                        </div>

                    </div>


                    <form id="reg_signup_form" role="form" name="validateform" method="POST"
                        action="{{ route('create.account') }}" class="formStyle">
                        @csrf
                        <input name="ip" value="" id="ip_field" type="hidden">
                        <div class="row mt-3">
                            <div class="col-12 col-lg-6 form-group mb-3">
                                <label for="fname">
                                    {{ trans('register.first_name') }}
                                    &nbsp;<span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control @error('fname') is-invalid @enderror"
                                    id="fname" name="fname" value="{{ Request::get('fname') ?? old('fname') }}"
                                    placeholder="First Name" required autofocus />
                                @error('fname')
                                    <span class="text-danger" role="alert">
                                        <small>{{ $message }}</small>
                                    </span>
                                @enderror
                            </div>
                            <div class="col-12 col-lg-6 form-group mb-3">
                                <label for="lname">
                                    {{ trans('register.last_name') }}
                                    &nbsp;<span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control @error('lname') is-invalid @enderror"
                                    id="lname" name="lname" value="{{ Request::get('lname') ?? old('lname') }}"
                                    placeholder="Last Name" onkeypress="return searchKeyPress(event);" required />
                                <input name="type" value="manual" type="hidden" />
                                @error('lname')
                                    <span class="text-danger" role="alert">
                                        <small>{{ $message }}</small>
                                    </span>
                                @enderror
                            </div>
                        </div>
                        <div class="form-group ">
                            <label for="email">
                                {{ trans('register.email') }}
                                &nbsp;<span class="text-danger">*</span>
                            </label>
                            <input type="email" class="form-control @error('email') is-invalid @enderror" id="email"
                                placeholder="Email" name="email" value="{{ Request::get('email') ?? old('email') }}"
                                onkeypress="return searchKeyPress(event);" required />
                            @error('email')
                                <span class="text-danger" role="alert">
                                    <small>{{ $message }}</small>
                                </span>
                            @enderror
                        </div>
                        <div class="row mt-3">
                            <div class="col-12 form-group">
                                <label for="pass">
                                    {{ trans('register.password') }}
                                    &nbsp;<span class="text-danger">*</span>
                                </label>
                                <input type="password" class="form-control @error('password') is-invalid @enderror"
                                    id="pass" placeholder="*******" name="password" value="{{ old('password') }}"
                                    onkeypress="return searchKeyPress(event);" required />
                                @error('password')
                                    <span class="text-danger" role="alert">
                                        <small>{{ $message }}</small>
                                    </span>
                                @enderror
                            </div>
                            <div class="col-12 form-group mt-3">
                                <label for="confirmpass">
                                    {{ trans('register.confirm_password') }}
                                    &nbsp;<span class="text-danger">*</span>
                                </label>
                                <input type="password" class="form-control @error('password') is-invalid @enderror"
                                    id="confirmpass" placeholder="*******" name="password_confirmation"
                                    value="{{ old('password_confirmation') }}" onkeypress="return searchKeyPress(event);"
                                    required />
                                @error('password')
                                    <span class="text-danger" role="alert">
                                        <small>{{ $message }}</small>
                                    </span>
                                @enderror
                            </div>
                        </div>

                        <div class="form-group mt-3 ">
                            <label id="phone-label" for="phone">
                                {{ trans('register.phone') }}
                                <span class="text-danger">*</span>
                            </label>
                            <input type="tel" class="form-control @error('phone') is-invalid @enderror"
                                id="phone" name="oldphone" maxlength="20"
                                value="{{ Request::get('phone') ?? old('phone') }}"
                                oninput="this.value = this.value.replace(/[^0-9.]/g, '').replace(/(\..*?)\..*/g, '$1');" />
                            <input id="full_phone" name="phone" type="hidden">
                            @error('phone')
                                <span class="text-danger" role="alert">
                                    <small>{{ $message }}</small>
                                </span>
                            @enderror
                        </div>

                        <div class="form-group {{ $errors->has('g-recaptcha-response') ? ' has-error' : '' }}">
                            <div class="col-md-12">
                                {!! RecaptchaV3::field('register') !!}
                                @if ($errors->has('g-recaptcha-response'))
                                    <span class="help-block">
                                        <strong>{{ $errors->first('g-recaptcha-response') }}</strong>
                                    </span>
                                @endif
                            </div>
                        </div>

                        <div class="form-group mt-40">
                            <button id="reg_signup_btn" type="submit" class="btn btn-primary w-100">
                                {{ trans('register.signup_btn') }}
                            </button>
                        </div>
                    </form>

                    <div class="form-group text-center mt-3">
                        <p class="text-break text-dark">
                            {{ trans('register.terms_and_privacy') }} <br>
                            <a href="{{ env('PRIVACY_POLICY_URL') }}" class="fw-700 clr-black" target="_blank">
                                {{ trans('register.privacy_policy') }}
                            </a>
                            {{ trans('register.and') }}
                            <a href="{{ env('TERM_OF_SERVICE_URL') }}" class="fw-700 clr-black" target="_blank">
                                {{ trans('register.terms-of_service') }}
                            </a>.
                        </p>
                    </div>
                    <div class="form-group text-center">
                        <label class="fw-400">
                            {{ trans('register.already_a_account') }}&nbsp;
                        </label>
                        <a href="{{ route('login', ['role' => Request::get('role')]) }}"
                            class="text-decoration-none f-700 clr-blue">
                            {{ trans('register.login_btn') }}</a>
                    </div>

                </div>
            </div>


        </div>
        <!--Right side-->
        <div id="Signup-page" class="col-5 d-none d-lg-block onBoarding-rightSide ">
            <div class="d-lg-block d-none text-center login-logo mt-80">
                <img src="{{ asset('assets/images/signup.png') }}" class="w-75 mt-4" alt="" />
            </div>
            <div class="mt-4">
                <div class="pb-5">
                    <h1 class="text-uppercase fw-700 text-center"><span>Healthy</span> <img
                            src="{{ asset('assets/images/heading.png') }}" alt=""
                            style="width: 30px;margin-top: -10px"><span> Parcel</span></h1>
                    <p class="fs-20 fw-600 text-center mb-1 review-text">
                        "Managing our product information has never been easier since we started using Apimio. We've seen a
                        noticeable boost in customer satisfaction as a result."
                    </p>
                    <p class="text-center fs-20 mb-1">Kareena</p>
                    <p class="text-center clr-grey fs-20">CEO, Healthy Parcel</p>
                    {{--                    <h3 class="fw-700"> --}}
                    {{--                        {{ trans('register.right_side_heading') }}</h3> --}}
                    {{--                    <div class="text-left pl-2 py-2 mx-4 mb-2 content-text"> --}}
                    {{--                        <ul class="px-2 mt-2"> --}}
                    {{--                            <li class="fw-400 mb-2">{{ trans('register.right_side_content_1') }}</li> --}}
                    {{--                            <li class="fw-400 mb-2">{{ trans('register.right_side_content_2') }}</li> --}}
                    {{--                            <li class="fw-400 mb-2">{{ trans('register.right_side_content_3') }}</li> --}}
                    {{--                            <li class="fw-400 mb-2">{{ trans('register.right_side_content_4') }}</li> --}}
                    {{--                        </ul> --}}
                    {{--                    </div> --}}
                </div>
            </div>

        </div>
    </div>

    @push('footer_scripts')
        <script>
            $.getJSON('https://api.ipify.org?format=jsonp&callback=?', function(data) {
                const d = (data);
                console.log(d.ip);
                $('#ip_field').val(d.ip);
            });
        </script>
        <!--Phone Flags-->
        <script>
            let input = document.querySelector("#phone");
            let iti = null;
            ipLookUp();

            // get current user country
            function ipLookUp() {
                $.ajax({
                    dataType: 'json',
                    url: 'https://api.hostip.info/get_json.php',
                    success: function(data) {
                        let $ip = data['ip'],
                            $city = data['city'],
                            $countryCode = data['country_code'],
                            $countryName = data['country_name'];
                        if ($countryCode == "XX") {
                            iti = window.intlTelInput(input, ({
                                separateDialCode: true,
                                preferredCountries: ["us", "ca"],
                                utilsScript: "{{ asset('public/js/utils.js') }}",
                                nationalMode: false
                            }));
                        } else {
                            iti = window.intlTelInput(input, ({
                                separateDialCode: true,
                                preferredCountries: ["us", "ca"],
                                utilsScript: "{{ asset('public/js/utils.js') }}",
                                nationalMode: false,
                                initialCountry: $countryCode
                            }));
                        }
                        $("#full_phone").val(iti.getSelectedCountryData().dialCode + input.value);
                    },
                    error: function(jqXHR, textStatus) {
                        iti = window.intlTelInput(input, ({
                            separateDialCode: true,
                            preferredCountries: ["us", "ca"],
                            utilsScript: "{{ asset('public/js/utils.js') }}",
                            nationalMode: false,
                        }));
                        $("#full_phone").val(iti.getSelectedCountryData().dialCode + input.value);
                    }
                });

                input.addEventListener("countrychange", function() {
                    $("#full_phone").val("(+" + iti.getSelectedCountryData().dialCode + ") " + input.value);
                });
                input.addEventListener("focusout", function() {
                    $("#full_phone").val("(+" + iti.getSelectedCountryData().dialCode + ") " + input.value);
                });
                $("#reg_signup_form").submit(function() {
                    $("#full_phone").val("(+" + iti.getSelectedCountryData().dialCode + ") " + input.value);
                });


                return 0;
            }

            function searchKeyPress(e) {
                e = e || window.event;
                if (e.keyCode === 13) {
                    document.getElementById('registeration_next_btn_id').click();
                    return false;
                }
                return true;
            }
        </script>
    @endpush

@endsection
