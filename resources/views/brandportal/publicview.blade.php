@extends('layouts.brand_portal_public_layout')
@if (Request::segment(4) == 'edit')
    @section('titles', 'Brand Portal')
@else
    @section('titles', 'Brand Portal')
@endif
@section('content')
    <div id="brand-public" data-portals='{{ json_encode($brandsPortal) }}' data-templates='{{ json_encode($templates) }}'
        data-token='{{ json_encode($token) }}'>
    </div>
    @vite('resources/js/components/brandportal/PublicView.jsx')
@endsection
