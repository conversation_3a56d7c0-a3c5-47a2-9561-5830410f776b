<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Dashboard Language Lines
    |--------------------------------------------------------------------------
    |
    | The following language lines are used during dashboard for various
    | messages that we need to display to the user. You are free to modify
    | these language lines according to your application's requirements.
    |
    */
    //dashboard card
    'heading' => ' Apimio',
    'subheading' => 'Create your first product and start syncing products to your online stores. To connect your Shopify store, navigate to the Store page.',
    'add_product_btn' => 'Create Product',
    'bulk_import_btn' => 'Import CSV',

    //invites
    'retailers' => 'Retailer Invites',
    'vendors' => 'Vendor Invites',
    'invites' => 'pending invite request|PENDING INVITE Requests   ',
    'organization' => 'Organization: ',
    'seemore' => '...',

    //overview card
    'overview' => 'OVERVIEW',
    'products' => 'Products',
    'sku' => "SKU's",
    'brands' => 'Brands',

    //billing card
    'billing' => 'BILLING',
    'update_plan_btn' => 'Update Plan',

    //guide card
    'guide_heading' => 'See what more you can do with Apimio!',
    'product_status' => 'View Product Status',
    'catalog_partners' => 'Store Partners',
    'import_products' => 'Import Products to Channel',
    'export_products' => 'Export Products',

    //guide card -> View Product Status
    'published' => 'Published',
    'published_description' => 'Products are updated.',
    'draft' => 'Draft',
    'draft_description' => 'No products are assigned.',
    'synced' => 'Synced',
    'synced_description' => 'Products are synced with shopify.',
    'sku_count' => '(You have :total_sku remaining in your current plan)',
    'view_all_products_btn' => 'View All Products',
    'update_available'=>'Update Available',
    'update_available_description'=> 'Products are updated and not synced to Shopify.',

    //guide card -> Catalog Partners
    'vendor_title' => 'Vendors',
    'vendor_description' => 'Invite your vendors and share product Stores with them.',
    'invite_vendor_btn' => 'Invite a Vendor',

    'retailer_title' => 'Retailers',
    'retailer_description' => 'Invite your retailers and share product Stores with them.',
    'invite_retailer_btn' => 'Invite a Retailer',

    //guide card -> Import Products to Channel
    'catalog_title' => 'Manage Stores',
    'catalog_description' => 'Connect your stores with Apimio. You can start publishing products to stores once it is connected',
    'add_catalog_btn' => 'Manage Stores',

    //guide card -> Export Products
    'export_title' => 'Export Products',
    'export_description' => 'You have :no_of_product products added. Click button below to see options in which you can export your products.',
    'export_product_btn' => 'Export Products',

    //Products Card
    'product_heading' => 'PRODUCTS',

    //Products Card -> empty
    'empty_table_description' => "You have not added any products yet.",

    //Products Card -> Products table
    'view_all_products' => 'VIEW ALL PRODUCTS',

];
