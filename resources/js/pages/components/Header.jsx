// Header.js
import React, { useState, useMemo } from "react";
import { Row, Col, Button, Switch, Tag, Tooltip } from "antd";
import { ExclamationCircleOutlined, SyncOutlined, FileTextOutlined, SaveOutlined, PlusOutlined } from "@ant-design/icons";
import ModalComponent from "./ModalComponent"; // Import the ModalComponent
import { router } from "@inertiajs/react";

const Header = ({ totalRows = 0, mappedRows = 0, dataRequired, nodes }) => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [skipUnmatched, setSkipUnmatched] = useState(true); // Default to true (Skip Unmatched columns)
  const [isSaveTemplate, setIsSaveTemplate] = useState(false); // Track if save template button was clicked

  // Get the import action from dataRequired
  const importAction = dataRequired?.import_action ?? 0;
  console.log("Import Action:", importAction);

  // Track which identifiers are mapped (for Import_Action=1)
  const [mappedIdentifiers, setMappedIdentifiers] = useState({});

  // Check if required identifier is mapped in any row based on import action
  const isRequiredIdentifierMapped = useMemo(() => {
    if (!nodes || nodes.length === 0) {
      return false;
    }

    // Determine which identifier(s) to check based on import action
    let identifiersToCheck = [];

    if (importAction == 1) {
      // For Import_Action=1, check both identifiers
      identifiersToCheck = ["Variant,sku", "Default,handle"];
      console.log(
        `Checking for both identifiers: Variant,sku and Default,handle based on Import_Action: ${importAction}`
      );
    } else {
      // For Import_Action=2 or 3, check the specific identifier
      const identifierToCheck =
        importAction == 2 ? "Variant,sku" : "Default,handle";
      identifiersToCheck = [identifierToCheck];
      console.log(
        `Checking for identifier: ${identifierToCheck} based on Import_Action: ${importAction}`
      );
    }

    // For Import_Action=1, we need to check if both identifiers are mapped
    if (importAction == 1) {
      // Create a map to track which identifiers are mapped
      const newMappedIdentifiers = {};

      // Check each node to see if it maps to any of the required identifiers
      nodes.forEach((node) => {
        // Check if the node is fully mapped based on its formula
        const isFullyMapped = (() => {
          switch (node.with_formula) {
            case "split":
              return (
                node?.from?.length > 0 &&
                node?.to?.length === 2 &&
                node?.to?.[0] &&
                node?.to?.[1] &&
                node?.with &&
                node?.with.trim() !== ""
              );
            case "merge":
              return (
                node?.from?.length > 1 &&
                node?.to?.length > 0 &&
                node?.with &&
                node?.with.trim() !== ""
              );
            case "replace":
              return (
                node?.from?.length > 0 &&
                node?.to?.length > 0 &&
                node?.replace &&
                node?.replace.trim() !== "" &&
                node?.with &&
                node?.with.trim() !== ""
              );
            case "expand":
              return (
                node?.from?.length > 0 &&
                node?.to?.length > 0 &&
                node?.with &&
                node?.with.trim() !== ""
              );
            case "calculate":
              return (
                node?.from?.length > 0 &&
                node?.to?.length > 0 &&
                node?.with &&
                node?.with.trim() !== ""
              );
            case "slug":
            case "assign":
            default:
              return node?.from?.length > 0 && node?.to?.length > 0;
          }
        })();

        // If the node is fully mapped, check which identifiers it maps to
        if (isFullyMapped) {
          identifiersToCheck.forEach((identifier) => {
            if (node?.to?.includes(identifier)) {
              newMappedIdentifiers[identifier] = true;
              console.log(
                `Node ${node.id} - Maps to Required Identifier (${identifier})`
              );
            }
          });
        }
      });

      // Update the mappedIdentifiers state
      setMappedIdentifiers(newMappedIdentifiers);

      // Check if all required identifiers are mapped
      const allIdentifiersMapped = identifiersToCheck.every(
        (identifier) => newMappedIdentifiers[identifier]
      );
      console.log(`All identifiers mapped: ${allIdentifiersMapped}`);

      // For Import_Action=1, we need both identifiers to be mapped
      return allIdentifiersMapped;
    }
    // For Import_Action=2 or 3, we just need the specific identifier to be mapped
    else {
      const identifierToCheck = identifiersToCheck[0];

      return nodes.some((node) => {
        // Check if the node is fully mapped based on its formula
        const isFullyMapped = (() => {
          switch (node.with_formula) {
            case "split":
              return (
                node?.from?.length > 0 &&
                node?.to?.length === 2 &&
                node?.to?.[0] &&
                node?.to?.[1] &&
                node?.with &&
                node?.with.trim() !== ""
              );
            case "merge":
              return (
                node?.from?.length > 1 &&
                node?.to?.length > 0 &&
                node?.with &&
                node?.with.trim() !== ""
              );
            case "replace":
              return (
                node?.from?.length > 0 &&
                node?.to?.length > 0 &&
                node?.replace &&
                node?.replace.trim() !== "" &&
                node?.with &&
                node?.with.trim() !== ""
              );
            case "expand":
              return (
                node?.from?.length > 0 &&
                node?.to?.length > 0 &&
                node?.with &&
                node?.with.trim() !== ""
              );
            case "calculate":
              return (
                node?.from?.length > 0 &&
                node?.to?.length > 0 &&
                node?.with &&
                node?.with.trim() !== ""
              );
            case "slug":
            case "assign":
            default:
              return node?.from?.length > 0 && node?.to?.length > 0;
          }
        })();

        // Check if this row maps to the required identifier
        const mapsToRequiredIdentifier = node?.to?.includes(identifierToCheck);

        console.log(
          `Node ${node.id} - Fully Mapped: ${isFullyMapped}, Maps to Required Identifier (${identifierToCheck}): ${mapsToRequiredIdentifier}`
        );

        return isFullyMapped && mapsToRequiredIdentifier;
      });
    }
  }, [nodes, importAction]);

  console.log("Is Required Identifier Mapped:", isRequiredIdentifierMapped);

  // Check if all rows are mapped
  const allRowsMapped = useMemo(() => {
    return totalRows > 0 && mappedRows === totalRows;
  }, [totalRows, mappedRows]);

  // Check if all conditions are met (all rows mapped AND required identifiers mapped)
  const allConditionsMet = useMemo(() => {
    return allRowsMapped && isRequiredIdentifierMapped;
  }, [allRowsMapped, isRequiredIdentifierMapped]);

  // Determine if the Next button should be enabled
  const isNextButtonEnabled = useMemo(() => {
    if (importAction == 1 || importAction == 2 || importAction == 3) {
      // If all conditions are met (all rows mapped AND required identifiers mapped), always enable the Next button
      if (allConditionsMet) {
        return true;
      }
      // Otherwise, check if required identifier is mapped and Skip Unmatched is enabled
      return isRequiredIdentifierMapped && skipUnmatched;
    }
    return true; // For other import actions, always enable
  }, [
    importAction,
    isRequiredIdentifierMapped,
    skipUnmatched,
    allConditionsMet,
  ]);

  // Function to handle the Skip Unmatched toggle
  const handleSkipUnmatchedChange = (checked) => {
    setSkipUnmatched(checked);
  };

  // Function to handle opening the Modal for Next button
  const showModal = () => {
    if (isNextButtonEnabled) {
      setIsSaveTemplate(false);
      setIsModalVisible(true);
    }
  };

  // Function to handle opening the Modal for Save Template button
  const showSaveTemplateModal = () => {
    setIsSaveTemplate(true);
    setIsModalVisible(true);
  };

  // Function to handle closing the Modal
  const handleCancel = () => {
    setIsModalVisible(false);
    setIsSaveTemplate(false);
  };

  // Function to handle Create New Template button click
  const handleCreateNewTemplate = (e) => {
    e.preventDefault()
            router.get("/products/import/create-template", {}, {
                onSuccess: () => {
                    message.success("Now you can map data and then create a template.");
                    // Add logic to handle success, e.g., refreshing the template list
                },
                onError: (error) => {
                    console.error("Error creating template:", error);
                    message.error("Failed to map data and create a template. Please try again.");
                },
            });
  };

  return (
    <div>
      <Row gutter={[20, 20]} className="pb-[20px]">
        <Col span={12}>
          <div>
            <p className="text-[#252525] font-[600] text-[18px]">
              Mapping Products
            </p>
            <p className="text-[#626262] font-normal text-[14px]">
              Mapping conversion of all your products
            </p>
            {/* Display selected template information if available */}
            {dataRequired?.selected_template && (
              <div className="mt-2">
                <Tooltip title="Template applied to this mapping">
                  <Tag color="blue" icon={<FileTextOutlined />} className="px-2 py-1">
                    Template: {dataRequired.selected_template.temp_name}
                  </Tag>
                </Tooltip>
                <Button
                  icon={<PlusOutlined />}
                  className="border border-[#D9D9D9] rounded-[6px]"
                  onClick={handleCreateNewTemplate}
                >
                </Button>
              </div>
            )}
          </div>
        </Col>
        <Col span={12}>
          <div className="flex flex-col gap-2">
            {/* Action Buttons and Switch */}
            <div className="flex items-center justify-end gap-4">
              {/* Warning Messages or Success Message */}
              <div className="rounded">
                {allConditionsMet ? (
                  <p className="flex items-center text-xs text-[#4CAF50] mb-1">
                    <ExclamationCircleOutlined
                      style={{
                        color: "#4CAF50",
                        fontSize: "15px",
                        marginRight: "4px",
                      }}
                    />
                    All rows are mapped successfully!
                  </p>
                ) : (
                  <>
                    {(importAction == 1 ||
                      importAction == 2 ||
                      importAction == 3) &&
                      !isRequiredIdentifierMapped && (
                        <p className="flex items-center text-xs text-[#ff0000] mb-1">
                          <ExclamationCircleOutlined
                            style={{
                              color: "red",
                              fontSize: "15px",
                              marginRight: "4px",
                            }}
                          />
                          {importAction == 1
                            ? mappedIdentifiers["Variant,sku"]
                              ? "Product Identifier column is not mapped."
                              : mappedIdentifiers["Default,handle"]
                              ? "SKU column is not mapped."
                              : "Both SKU and Product Identifier columns must be mapped."
                            : importAction == 2
                            ? "SKU column is not mapped."
                            : "Product Identifier column is not mapped."}
                        </p>
                      )}

                    {/* Only show unmapped rows count when not all conditions are met */}
                    {!allConditionsMet && (
                      <p className="mb-0 text-xs">
                        You have {totalRows - mappedRows} / {totalRows} unmapped
                        rows
                      </p>
                    )}
                    {!allConditionsMet && (
                      <div className="flex items-center gap-2">
                        <label className="text-xs">
                          {skipUnmatched
                            ? "Skip Unmatched columns"
                            : "Don't Skip Unmatched columns"}
                        </label>
                        <Switch
                          checked={skipUnmatched}
                          onChange={handleSkipUnmatchedChange}
                          disabled={
                            (importAction == 1 ||
                              importAction == 2 ||
                              importAction == 3) &&
                            !isRequiredIdentifierMapped
                          }
                        />
                      </div>
                    )}
                  </>
                )}
              </div>

              <div className="flex items-center gap-2">
                <Button
                  icon={<SyncOutlined />}
                  className="h-[32px] border border-[#D9D9D9] rounded-[4px]"
                >
                  Reset
                </Button>
                <Button
                  icon={<SaveOutlined />}
                  className="h-[32px] border border-[#D9D9D9] rounded-[4px]"
                  onClick={showSaveTemplateModal}
                >
                  Save template
                </Button>
                
                <Button
                  className="bg-[#740898] text-white border border-[#740898] rounded-[4px]"
                  onClick={showModal} // Open Modal on click
                  disabled={!isNextButtonEnabled}
                >
                  Next
                </Button>
              </div>
            </div>
          </div>
        </Col>
      </Row>

      {/* Modal Component */}
      <ModalComponent
        isVisible={isModalVisible}
        nodes={nodes}
        dataRequired={dataRequired}
        onCancel={handleCancel} // Pass the close handler to the modal
        isSaveTemplate={isSaveTemplate} // Pass the save template flag
      />
    </div>
  );
};

export default Header;
