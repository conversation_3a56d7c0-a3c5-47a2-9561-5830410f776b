import React, { useState, useEffect } from "react";
import { Input, Form } from "antd";
import SelectAttribute from "../components/SelectAttribute";

const Split = ({ node,  onFieldChange, convertedOutputArray }) => {
   const [separator, setSeparator] = useState(node.with || "");
  const [attributeOne, setAttributeOne] = useState(node.to?.[0] || null);
  const [attributeTwo, setAttributeTwo] = useState(node.to?.[1] || null);

  const updateParent = (newSeparator, newAttr1, newAttr2) => {
    if (newSeparator && newAttr1 && newAttr2) {
      onFieldChange(node.id, {
        with: newSeparator,
        to: [newAttr1, newAttr2],
      });
    }
  };

  const handleSeparatorChange = (e) => {
    const newSep = e.target.value;
    setSeparator(newSep);
  };

  // Update parent when separator input loses focus
  const handleSeparatorBlur = () => {
    updateParent(separator, attributeOne, attributeTwo);
  };

  const handleAttributeOneChange = (val) => {
    setAttributeOne(val);
    updateParent(separator, val, attributeTwo);
  };

  const handleAttributeTwoChange = (val) => {
    setAttributeTwo(val);
    updateParent(separator, attributeOne, val);
  };

  useEffect(() => {
    console.log("Local state in <Split>:", { separator, attributeOne, attributeTwo });
  }, [separator, attributeOne, attributeTwo]);

  useEffect(() => {
    console.log("Split received node prop:", node);
  }, [node]);

  return (
    <div className="flex gap-4">
      <div className="flex flex-col gap-1">
        <Form.Item label="Separator" style={{ marginBottom: 0 }}>
          <Input
            style={{ width: 100 }}
            value={separator} // ensures we see the local state
            onChange={handleSeparatorChange}
            onBlur={handleSeparatorBlur}
          />
        </Form.Item>
      </div>

      <div className="flex flex-col gap-1">
        <Form.Item label="Apimio Attribute (1)" style={{ marginBottom: 0 }}>
          <SelectAttribute
            value={attributeOne} // ensure we pass the local attributeOne as the current value
            options={convertedOutputArray}
            onChange={handleAttributeOneChange}
          />
        </Form.Item>
      </div>

      <div className="flex flex-col gap-1">
        <Form.Item label="Apimio Attribute (2)" style={{ marginBottom: 0 }}>
          <SelectAttribute
            value={attributeTwo} // ensure we pass the local attributeTwo as the current value
            options={convertedOutputArray}
            onChange={handleAttributeTwoChange}
          />
        </Form.Item>
      </div>
    </div>
  );
};

export default Split;
