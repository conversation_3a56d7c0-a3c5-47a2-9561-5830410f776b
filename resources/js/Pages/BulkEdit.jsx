import React, { useState, useEffect } from "react";
import { createRoot } from "react-dom/client";
import axios from "axios";
import Select from "react-select";
import _, { update } from "lodash";
import { Editor } from "@tinymce/tinymce-react";
import { ToastContainer, toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import TagsInput from "react-tagsinput";
import "react-tagsinput/react-tagsinput.css";

import { Head } from "@inertiajs/react";

const BulkEdit = ({ bulk_edit }) => {
    const [data, setData] = useState(null);
    const [value, setValue] = useState("");
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [headerData, setHeaderData] = useState("");
    const [filteredData, setFilteredData] = useState([]);
    const [products, setProducts] = useState([]);
    const [changedAttributes, setChangedAttributes] = useState([]);
    const [selectedStores, setSelectedStores] = useState();
    const [selectedCategories, setSelectedCategories] = useState();
    const [currentPage, setCurrentPage] = useState(1);
    const [totalPageCount, setTotalPageCount] = useState(1);
    const [selectedSingleOptions, setSelectedSingleOptions] = useState({});
    const [showConfirmationModal, setShowConfirmationModal] = useState(false);
    const [pageNumber, setPageNumber] = useState();
    const [searchValue, setSearchValue] = useState();
    const [openIndex, setOpenIndex] = useState(null);
    const [inputValues, setInputValues] = useState({});
    const handleAccordionClick = (index) => {
        setOpenIndex(openIndex === index ? null : index);
    };

    // Function to toggle accordion state for inventory data
    const removeAllFalse = {
        ClearIndicator: null,
    };
    const [isOpenArray, setIsOpenArray] = useState(Array(bulk_edit.products.length).fill(false));
    const [activeIndex, setActiveIndex] = useState(-1);
    const toggleAccordion = (index) => {
        const isOpen = isOpenArray[index];

        // Copy the current array state and close all open toggles if the clicked toggle is currently closed
        const newIsOpenArray = isOpenArray.map((isOpen, i) => isOpen || (index === i && !isOpen));

        // If the clicked toggle is currently closed and any other toggle is open, close the open toggles
        if (!isOpen && newIsOpenArray.some((isOpen, i) => isOpen && i !== index)) {
            newIsOpenArray.fill(false);
        }

        // Toggle the state of the clicked accordion
        newIsOpenArray[index] = !isOpen;

        // Set the new state
        setIsOpenArray(newIsOpenArray);

        // Set active index
        setActiveIndex(newIsOpenArray[index] ? index : -1);
    };
    const goToPage = (pageNumber) => {
        if (changedAttributes.length === 0) {
            $(".loader").removeClass("hidden");
            const dataRequired = data.data_required;
            const updatedDataRequired = {
                ...dataRequired,
                target_page: pageNumber,
            };
            axios.defaults.headers.common["X-CSRF-TOKEN"] = document.querySelector('meta[name="csrf-token"]').getAttribute("content");
            axios
                .post("/products/bulk/edit/save", {
                    data_required: updatedDataRequired, // Sending the updated data_required object
                })
                .then((response) => {
                    if (response.data) {
                        const paginatedData = _.cloneDeep(response.data);
                        handlePageChange(pageNumber, paginatedData);
                        setCurrentPage(pageNumber);
                        setChangedAttributes([]);
                    }
                })
                .catch((error) => console.log(error));
        } else {
            setShowConfirmationModal(true);
        }

        setPageNumber(pageNumber);
    };
    const handleBackButton = () => {
        if (changedAttributes.length === 0) {
            window.location.href = "/products";
        } else {
            setShowConfirmationModal(true);
        }
    };
    const renderPageNumbers = () => {
        const pageNumbers = [];

        // Define how many page numbers you want to show on each side of the current page
        const maxPagesToShow = 3;

        // Add the first page number
        pageNumbers.push(1);

        // Add page numbers around the current page
        for (let i = Math.max(2, currentPage - maxPagesToShow); i <= Math.min(totalPageCount - 1, currentPage + maxPagesToShow); i++) {
            pageNumbers.push(i);
        }

        // Add the last page number
        pageNumbers.push(totalPageCount);

        const uniquePageNumbers = [...new Set(pageNumbers)]; // Remove duplicates

        // Construct the pagination buttons
        const buttons = uniquePageNumbers.map((pageNumber, index) => {
            if (index === 1 && pageNumber - 1 > 1) {
                // If the first page is clicked and there are more than maxPagesToShow + 1 pages between the first and second page
                return <span key="ellipsis-start">...</span>;
            } else if (index === uniquePageNumbers.length - 2 && totalPageCount - pageNumber > maxPagesToShow) {
                // If the last page is clicked and there are more than maxPagesToShow pages between the last and second-to-last page
                return <span key="ellipsis-end">...</span>;
            } else {
                return (
                    <button
                        key={pageNumber}
                        className={`rounded-full py-1 px-2 mx-1 border border-[#2C4BFF] ${
                            currentPage === pageNumber ? "active bg-[#2C4BFF] text-white" : ""
                        }`}
                        onClick={() => goToPage(pageNumber)}
                    >
                        {pageNumber}
                    </button>
                );
            }
        });

        return buttons;
    };
    const handleSaveChanges = () => {
        setShowConfirmationModal(false);

        $(".loader").removeClass("hidden");
        if (pageNumber === undefined) {
            let restructuredObject = {
                products: changedAttributes,
                data_required: data.data_required,
            };
            console.log(restructuredObject);
            axios.defaults.headers.common["X-CSRF-TOKEN"] = document.querySelector('meta[name="csrf-token"]').getAttribute("content");
            axios
                .post("/products/bulk/edit/save", restructuredObject)
                .then((response) => {
                    toast.success("Product updated Succesfully");
                    window.location.href = "/products";
                })
                .catch((error) => toast.error("Error! Please try again later"));
        } else {
            let restructuredObject = {
                products: changedAttributes,
                data_required: data.data_required,
            };
            const dataRequired = data.data_required;
            const updatedDataRequired = {
                ...dataRequired,
                target_page: pageNumber,
            };
            axios.defaults.headers.common["X-CSRF-TOKEN"] = document.querySelector('meta[name="csrf-token"]').getAttribute("content");
            axios
                .post("/products/bulk/edit/save", restructuredObject)
                .then((response) => {
                    toast.success("Product updated Succesfully");
                })
                .catch((error) => toast.error("Error! Please try again later"));
            axios.defaults.headers.common["X-CSRF-TOKEN"] = document.querySelector('meta[name="csrf-token"]').getAttribute("content");
            axios.post("/products/bulk/edit/save", restructuredObject).then((response) => {
                axios.defaults.headers.common["X-CSRF-TOKEN"] = document.querySelector('meta[name="csrf-token"]').getAttribute("content");
                axios
                    .post("/products/bulk/edit/save", {
                        data_required: updatedDataRequired, // Sending the updated data_required object
                    })
                    .then((response) => {
                        const paginatedData = _.cloneDeep(response.data);
                        handlePageChange(pageNumber, paginatedData);
                        setCurrentPage(pageNumber);
                        setChangedAttributes([]);
                        window.location.href = "/products";
                    })
                    .catch((error) => toast.error("Error! Please try again later"));
            });
        }
    };
    const handleDiscardChanges = () => {
        setShowConfirmationModal(false);
        $(".loader").removeClass("hidden");
        const dataRequired = data.data_required;
        const updatedDataRequired = {
            ...dataRequired,
            target_page: pageNumber,
        };

        if (pageNumber === undefined) {
            toast.error("Products Not Updated");
            window.location.href = "/products";
        } else {
            toast.error("Products Not Updated");
            axios.defaults.headers.common["X-CSRF-TOKEN"] = document.querySelector('meta[name="csrf-token"]').getAttribute("content");
            axios
                .post("/products/bulk/edit/save", {
                    data_required: updatedDataRequired, // Sending the updated data_required object
                })
                .then((response) => {
                    const paginatedData = _.cloneDeep(response.data);
                    handlePageChange(pageNumber, paginatedData);
                    setCurrentPage(pageNumber);
                    setChangedAttributes([]);
                })
                .catch((error) => toast("Error please try again later"));
        }

        $(".loader").addClass("hidden");
    };
    const [invalidFields, setInvalidFields] = useState({});
    const handleBulkEditingSave = () => {
        $(".loader").removeClass("hidden");
        // Filter out the attributes that have not been changed
        for (const productId in changedAttributes) {
            for (const familyName in changedAttributes[productId]) {
                if (changedAttributes[productId][familyName]["store"] && changedAttributes[productId][familyName]["store"].length === 0) {
                    toast.error("Error! Please select at least one store for all products.");
                    $(".loader").addClass("hidden");
                    return;
                }
            }
        }
        let restructuredObject = {
            products: changedAttributes,
            data_required: data.data_required,
        };
        console.log(restructuredObject);
        axios.defaults.headers.common["X-CSRF-TOKEN"] = document.querySelector('meta[name="csrf-token"]').getAttribute("content");
        axios
            .post("/products/bulk/edit/save", restructuredObject)
            .then((response) => {
                toast.success("Product updated Succesfully");
                window.location.reload();
            })
            .catch((error) => toast("Error! Please try again later"));
        $(".loader").addClass("hidden");
    };
    const MAX_TOTAL_CHARACTERS = 250;
    const handleTagChange = (tags, productId, familyName, attributeHandle, pivotId) => {
        const hasDuplicate = (tags) => {
            const lowerCaseTags = tags.map((tag) => tag.toLowerCase());
            return new Set(lowerCaseTags).size !== tags.length;
        };
        if (hasDuplicate(tags)) {
            toast.error("Duplicate tags are not allowed.");
            return;
        }
        const totalCharacters = tags.reduce((acc, tag) => acc + tag.length, 0);

        if (totalCharacters <= MAX_TOTAL_CHARACTERS) {
            const newValue = tags.join(", ");
            setInputValues((prevValues) => ({
                ...prevValues,
                [productId]: tags,
            }));
            handleInputChange(productId, familyName, attributeHandle, newValue, pivotId, null);
        } else {
            toast.error(`Tags limit reached. Maximum ${MAX_TOTAL_CHARACTERS} characters allowed`);
        }
    };
    const handleInputChange = (productId, familyName, attributeHandle, newValue, pivotId, unit) => {
        if (newValue === null || newValue === undefined || newValue === "") {
            setValue({ value: newValue, unit: "" });
        } else {
            setValue(newValue);
        }
        setChangedAttributes((prevAttributes) => {
            const updatedAttributes = { ...prevAttributes };

            if (!updatedAttributes[productId]) {
                updatedAttributes[productId] = {};
            }

            if (!updatedAttributes[productId][familyName]) {
                updatedAttributes[productId][familyName] = {};
            }
            if (pivotId === 1) {
                updatedAttributes[productId][familyName][pivotId] = {
                    value: newValue,
                };
            } else {
                updatedAttributes[productId][familyName][pivotId] = {
                    value: newValue,
                    unit: unit || null,
                };
            }

            return updatedAttributes;
        });
    };
    const handleMultiInputChange = (productId, familyName, attributeHandle, newValuesArray, pivotId) => {
        setChangedAttributes((prevAttributes) => {
            const updatedAttributes = { ...prevAttributes };

            if (!updatedAttributes[productId]) {
                updatedAttributes[productId] = {};
            }

            if (!updatedAttributes[productId][familyName]) {
                updatedAttributes[productId][familyName] = {};
            }

            if (!updatedAttributes[productId][familyName][pivotId]) {
                updatedAttributes[productId][familyName][pivotId] = newValuesArray.map((value) => ({ value, unit: null }));
            } else {
                updatedAttributes[productId][familyName][pivotId] = newValuesArray.map((value, index) => ({
                    value,
                    unit: updatedAttributes[productId][familyName][pivotId][index]?.unit || null,
                }));
            }
            return updatedAttributes;
        });
    };
    const handleDeleteInput = (index, inputIndex, pivotId, productID, familyName, attributeHandle) => {
        const valuesArray = value.value || data.products?.[index]?.[familyName]?.[attributeHandle]?.value || [];

        valuesArray.splice(inputIndex, 1);
        setValue(valuesArray);
        setChangedAttributes((prevAttributes) => {
            const updatedAttributes = { ...prevAttributes };

            // Ensure the structure exists before attempting to update
            if (!updatedAttributes[productID]) {
                updatedAttributes[productID] = {};
            }
            if (!updatedAttributes[productID][familyName]) {
                updatedAttributes[productID][familyName] = {};
            }
            if (!updatedAttributes[productID][familyName][pivotId]) {
                // Create the structure if it doesn't exist
                updatedAttributes[productID][familyName][pivotId] = valuesArray.map((value, index) => ({
                    id: value.id,
                    value: value.value,
                    unit: value.unit || null,
                }));
            } else {
                // Find the index and delete the value from the existing structure
                const updatedValues = updatedAttributes[productID][familyName][pivotId].filter((_, idx) => idx !== inputIndex);
                updatedAttributes[productID][familyName][pivotId] = updatedValues;
            }

            return updatedAttributes;
        });
    };
    const getNextUniqueId = (array) => {
        const maxId = Math.max(0, ...array.map((item) => parseInt(item.id || 0)));
        return maxId + 1;
    };
    const handleAddInput = (index, pivotId, productID, familyName, attributeHandle) => {
        const valuesArray = value.value || data.products?.[index]?.[familyName]?.[attributeHandle]?.value || [];

        valuesArray.push({ id: getNextUniqueId(valuesArray), value: "", unit: "" });
        setValue(valuesArray);

        setChangedAttributes((prevAttributes) => {
            const updatedAttributes = { ...prevAttributes };

            // Ensure the structure exists before attempting to update
            if (!updatedAttributes[productID]) {
                updatedAttributes[productID] = {};
            }
            if (!updatedAttributes[productID][familyName]) {
                updatedAttributes[productID][familyName] = {};
            }
            if (!updatedAttributes[productID][familyName][pivotId]) {
                // Create the structure if it doesn't exist
                updatedAttributes[productID][familyName][pivotId] = valuesArray.map((value, index) => ({
                    value: value.value,
                    unit: value.unit || null,
                }));
            }

            return updatedAttributes;
        });
    };
    useEffect(() => {
        console.log(changedAttributes, "changedAttributes");
    }, [changedAttributes]);
    const handleMeasurmentUnitChange = (productId, familyName, attributeHandle, selectedUnit, pivotId, selectedValue) => {
        //const selectedValue = $("this").next("input").val();
        setValue(selectedUnit);

        setChangedAttributes((prevAttributes) => {
            const updatedAttributes = { ...prevAttributes };

            if (!updatedAttributes[productId]) {
                updatedAttributes[productId] = {};
            }

            if (!updatedAttributes[productId][familyName]) {
                updatedAttributes[productId][familyName] = {};
            }

            updatedAttributes[productId][familyName][pivotId] = {
                value: selectedValue,
                unit: selectedUnit || null,
            };

            return updatedAttributes;
        });
    };
    const handleUnitChange = (index, e, productID, familyName, handle, pivotId) => {
        console.log(value, "value");
        const selectedUnits = value.map((_, idx) => (idx === index ? e.target.value : _.unit));
        const allValues = value.map((v) => v.value);
        handleMultipleUnitsChange(productID, familyName, handle, pivotId, selectedUnits, allValues);
    };
    const handleMultipleUnitsChange = (productId, familyName, attributeHandle, pivotId, units, allValues) => {
        console.log(units, "all units");
        console.log(allValues, "all values");
        setChangedAttributes((prevAttributes) => {
            const updatedAttributes = { ...prevAttributes };

            if (!updatedAttributes[productId]) {
                updatedAttributes[productId] = {};
            }

            if (!updatedAttributes[productId][familyName]) {
                updatedAttributes[productId][familyName] = {};
            }

            updatedAttributes[productId][familyName][pivotId] = units.map((unit, index) => ({
                value: allValues[index] || null,
                unit: unit || null,
            }));

            return updatedAttributes;
        });
    };
    const handleMeasurmentInputChange = (productId, familyName, attributeHandle, inputValue, pivotId, selectedUnit) => {
        setChangedAttributes((prevAttributes) => {
            const updatedAttributes = { ...prevAttributes };

            if (!updatedAttributes[productId]) {
                updatedAttributes[productId] = {};
            }

            if (!updatedAttributes[productId][familyName]) {
                updatedAttributes[productId][familyName] = {};
            }

            updatedAttributes[productId][familyName][pivotId] = {
                value: inputValue,
                unit: selectedUnit || null,
            };

            return updatedAttributes;
        });
    };
    const handleMultipleMeasurmentInputChange = (productId, familyName, attributeHandle, allValues, allUnits, pivotId) => {
        setChangedAttributes((prevAttributes) => {
            const updatedAttributes = { ...prevAttributes };

            if (!updatedAttributes[productId]) {
                updatedAttributes[productId] = {};
            }

            if (!updatedAttributes[productId][familyName]) {
                updatedAttributes[productId][familyName] = {};
            }

            updatedAttributes[productId][familyName][pivotId] = allValues.map((value, index) => ({
                value: value,
                unit: allUnits[index] || null,
            }));

            return updatedAttributes;
        });
    };

    const handleBrandChange = (productId, familyName, brandHandle, selectedValue, rowIndex) => {
        setChangedAttributes((prevAttributes) => {
            const updatedAttributes = { ...prevAttributes };
            // Check if productId exists in updatedAttributes, if not create default structure
            if (!updatedAttributes[productId]) {
                updatedAttributes[productId] = {};
            }
            // Check if familyName exists in productId, if not create default structure
            if (!updatedAttributes[productId][familyName]) {
                updatedAttributes[productId][familyName] = {};
            }

            // Create or update the 'brand' property with the selected value
            updatedAttributes[productId][familyName]["brand"] = [];
            updatedAttributes[productId][familyName]["brand"] = [selectedValue];

            data.products[rowIndex].Default.brand = [];
            data.products[rowIndex].Default.brand.push({ id: selectedValue });

            return updatedAttributes;
        });
    };
    const handleVendorChange = (productId, familyName, vendorHandle, selectedValue, rowIndex) => {
        setChangedAttributes((prevAttributes) => {
            const updatedAttributes = { ...prevAttributes };
            // Check if productId exists in updatedAttributes, if not create default structure
            if (!updatedAttributes[productId]) {
                updatedAttributes[productId] = {};
            }
            // Check if familyName exists in productId, if not create default structure
            if (!updatedAttributes[productId][familyName]) {
                updatedAttributes[productId][familyName] = {};
            }

            // Create or update the 'brand' property with the selected value
            updatedAttributes[productId][familyName]["vendor"] = [];
            updatedAttributes[productId][familyName]["vendor"] = [selectedValue];

            data.products[rowIndex].Default.vendor = [];
            data.products[rowIndex].Default.vendor.push({ id: selectedValue });

            return updatedAttributes;
        });
    };

    // Function to handle changes in select fields
    const handleSelectChange = (productId, familyName, attributeHandle, selectedValues, pivotId) => {
        selectedSingleOptions[pivotId] = selectedValues;
        setChangedAttributes((prevAttributes) => {
            const updatedAttributes = { ...prevAttributes };

            if (!updatedAttributes[productId]) {
                updatedAttributes[productId] = {};
            }

            if (!updatedAttributes[productId][familyName]) {
                updatedAttributes[productId][familyName] = {};
            }

            updatedAttributes[productId][familyName][pivotId] = selectedValues.map((value, index) => ({
                value: value.label,
                unit: null,
            }));

            return updatedAttributes;
        });
    };
    const handleMultiSelectChange = (productId, familyName, attributeHandle, selectedValues, pivotId) => {
        selectedSingleOptions[pivotId] = selectedValues;
        setChangedAttributes((prevAttributes) => {
            const updatedAttributes = { ...prevAttributes };

            if (!updatedAttributes[productId]) {
                updatedAttributes[productId] = {};
            }

            if (!updatedAttributes[productId][familyName]) {
                updatedAttributes[productId][familyName] = {};
            }

            const valuesToStore = Array.isArray(selectedValues)
                ? selectedValues.map((value, index) => ({
                      value: value.label,
                      unit: null,
                  }))
                : [selectedValues.value];
            updatedAttributes[productId][familyName][pivotId] = valuesToStore;

            return updatedAttributes;
        });
    };

    const handleCategoryChange = (productId, familyName, categoryHandle, selectedValues) => {
        setChangedAttributes((prevAttributes) => {
            const updatedAttributes = { ...prevAttributes };

            if (!updatedAttributes[productId]) {
                updatedAttributes[productId] = {};
            }

            if (!updatedAttributes[productId][familyName]) {
                updatedAttributes[productId][familyName] = {};
            }

            updatedAttributes[productId][familyName]["category"] = selectedValues.map((selectedValue) => selectedValue.value);

            return updatedAttributes;
        });
    };
    const handleStoreChange = (productId, familyName, categoryHandle, selectedValues) => {
        setChangedAttributes((prevAttributes) => {
            const updatedAttributes = { ...prevAttributes };

            if (!updatedAttributes[productId]) {
                updatedAttributes[productId] = {};
            }

            if (!updatedAttributes[productId][familyName]) {
                updatedAttributes[productId][familyName] = {};
            }

            updatedAttributes[productId][familyName]["store"] = selectedValues.map((selectedValue) => selectedValue.value);
            // Check for empty selected values and update the invalid state
            if (selectedValues.length === 0) {
                setInvalidFields((prevInvalidFields) => ({
                    ...prevInvalidFields,
                    [`${productId}-${familyName}`]: true,
                }));
            } else {
                setInvalidFields((prevInvalidFields) => {
                    const updatedInvalidFields = { ...prevInvalidFields };
                    delete updatedInvalidFields[`${productId}-${familyName}`];
                    return updatedInvalidFields;
                });
            }
            return updatedAttributes;
        });
    };

    // Function to handle changes in textarea fields
    const handleTextareaChange = (productId, familyName, attributeHandle, newValue, pivotId) => {
        setChangedAttributes((prevAttributes) => {
            const updatedAttributes = { ...prevAttributes };

            if (!updatedAttributes[productId]) {
                updatedAttributes[productId] = {};
            }

            if (!updatedAttributes[productId][familyName]) {
                updatedAttributes[productId][familyName] = {};
            }

            updatedAttributes[productId][familyName][pivotId] = {
                value: newValue,
                unit: null,
            };

            return updatedAttributes;
        });
    };
    const handleEditorChange = (productId, familyName, attributeHandle, newValue, pivotId) => {
        setValue(newValue);
        setChangedAttributes((prevAttributes) => {
            const updatedAttributes = { ...prevAttributes };

            if (!updatedAttributes[productId]) {
                updatedAttributes[productId] = {};
            }

            if (!updatedAttributes[productId][familyName]) {
                updatedAttributes[productId][familyName] = {};
            }

            updatedAttributes[productId][familyName][pivotId] = {
                value: newValue,
                unit: null,
            };

            return updatedAttributes;
        });
    };

    const [activeEditor, setActiveEditor] = useState({ familyName: "", attributeHandle: "" });

    const handleTextEditorChange = (index, familyName, attributeHandle) => {
        $(".loader").removeClass("hidden");
        setActiveEditor({ index, familyName, attributeHandle });
        setTimeout(function () {
            $(".loader").addClass("hidden");
        }, 2000);
    };
    const handleDescriptionBackdropClick = () => {
        setActiveEditor({ familyName: "", attributeHandle: "" });
    };
    const handleVariantInputChange = (productIndex, productId, variantIndex, field, value, variantId) => {
        // Update the local state with the modified value
        const updatedData = [...filteredData];
        // Update the value of the specified field in the variant object
        updatedData[productIndex].Default.variants[variantIndex][field] = value;
        setFilteredData(updatedData);
        setChangedAttributes((prevAttributes) => {
            const updatedAttributes = { ...prevAttributes };

            if (!updatedAttributes[productId]) {
                updatedAttributes[productId] = {};
            }
            if (!updatedAttributes[productId].Default) {
                // If not, initialize it with an empty object
                updatedAttributes[productId].Default = {
                    variants: [], // Initialize variants array
                };
            }
            if (!updatedAttributes[productId].Default.variants[variantIndex]) {
                updatedAttributes[productId].Default.variants[variantIndex] = {};
            }

            const existingVariant = updatedAttributes[productId].Default.variants[variantIndex];

            // If variantId exists, update the field
            if (existingVariant.id === variantId) {
                updatedAttributes[productId].Default.variants[variantIndex][field] = value;
            } else {
                // If variantId doesn't exist, add it with the new field
                updatedAttributes[productId].Default.variants[variantIndex] = {
                    ...updatedAttributes[productId].Default.variants[variantIndex], // Preserve existing fields
                    [field]: value, // Update or add the specified field
                    id: variantId, // Add the variantId
                };
            }

            return updatedAttributes;
        });
    };
    const handleCheckboxChange = (productIndex, productId, variantIndex, field, value, variantId, settingsId) => {
        const updatedData = [...filteredData];
        updatedData[productIndex].Default.variants[variantIndex].settings[field] = value;
        setFilteredData(updatedData);

        setChangedAttributes((prevAttributes) => {
            const updatedAttributes = { ...prevAttributes };

            if (!updatedAttributes[productId]) {
                updatedAttributes[productId] = {};
            }
            if (!updatedAttributes[productId].Default) {
                updatedAttributes[productId].Default = {
                    variants: [],
                };
            }
            if (!updatedAttributes[productId].Default.variants[variantIndex]) {
                updatedAttributes[productId].Default.variants[variantIndex] = {
                    settings: {},
                };
            } else if (!updatedAttributes[productId].Default.variants[variantIndex].settings) {
                updatedAttributes[productId].Default.variants[variantIndex].settings = {};
            }
            const existingVariant = updatedAttributes[productId].Default.variants[variantIndex];

            if (existingVariant.id === variantId) {
                updatedAttributes[productId].Default.variants[variantIndex].settings[field] = value;
            } else {
                updatedAttributes[productId].Default.variants[variantIndex] = {
                    ...existingVariant,
                    settings: {
                        ...existingVariant.settings,
                        id: settingsId,
                        [field]: value,
                    },
                    id: variantId,
                };
            }

            return updatedAttributes;
        });
    };
    const handleInventoryInputChange = (productIndex, productId, variantIndex, inventoryIndex, value, inventoryId, variantID) => {
        const updatedData = [...filteredData]; // Clone the data array

        // Find the product and variant that needs updating
        const productToUpdate = updatedData[productIndex];
        const variantToUpdate = productToUpdate.Default.variants[variantIndex];

        // Find the specific inventory item and update its available_quantity
        const updatedInventories = Object.keys(variantToUpdate.inventories).reduce((acc, key) => {
            const inventory = variantToUpdate.inventories[key];
            if (inventory.id === inventoryId) {
                acc[key] = {
                    ...inventory,
                    available_quantity: parseInt(value, 10),
                };
            } else {
                acc[key] = inventory;
            }
            return acc;
        }, {});

        // Update the variant with the modified inventories
        const updatedVariant = {
            ...variantToUpdate,
            inventories: updatedInventories,
        };

        // Update the product with the modified variant
        updatedData[productIndex] = {
            ...productToUpdate,
            Default: {
                ...productToUpdate.Default,
                variants: [
                    ...productToUpdate.Default.variants.slice(0, variantIndex),
                    updatedVariant,
                    ...productToUpdate.Default.variants.slice(variantIndex + 1),
                ],
            },
        };

        // Now you can set the updated data back to state or wherever you're using it
        setFilteredData(updatedData);

        setChangedAttributes((prevAttributes) => {
            const updatedAttributes = { ...prevAttributes };

            if (!updatedAttributes[productId]) {
                updatedAttributes[productId] = { Default: { variants: [] } };
            }
            if (!updatedAttributes[productId].Default) {
                updatedAttributes[productId].Default = { variants: [] };
            }
            // Ensure the variant and inventories exist in updatedAttributes
            if (!updatedAttributes[productId].Default.variants[variantIndex]) {
                updatedAttributes[productId].Default.variants[variantIndex] = { id: variantID, inventories: [] };
            }

            // Directly update the specific inventory using inventoryIndex
            updatedAttributes[productId].Default.variants[variantIndex].inventories =
                updatedData[productIndex].Default.variants[variantIndex].inventories;
            return updatedAttributes;
        });
    };
    const handlePageChange = (pageNumber, paginatedData) => {
        const paginatedDataFromServer = _.cloneDeep(paginatedData);
        setCurrentPage(pageNumber);
        setData(paginatedDataFromServer);
        setHeaderData(paginatedDataFromServer.filter_attributes);
        setFilteredData(paginatedDataFromServer.products); // Set filteredData with initial data based on filter_attributes
        setProducts(paginatedDataFromServer.products);
        if (paginatedDataFromServer && paginatedDataFromServer.products) {
            const selectedOptionsByProduct = {};

            // Loop through the products array
            paginatedDataFromServer.products.forEach((product) => {
                const selectedOptions = {};

                Object.keys(product).forEach((key) => {
                    const family = product[key];

                    if (key !== "Default") {
                        Object.values(family).forEach((attribute) => {
                            if (attribute && attribute.attribute_type_id === 4 && attribute.value && Array.isArray(attribute.value)) {
                                const pivotId = attribute.pivotId;
                                const productID = product.Default.product_id;

                                if (!selectedOptions[pivotId]) {
                                    selectedOptions[pivotId] = [];
                                }

                                const attributeValues = attribute.value;

                                const selectValuesOptions = attributeValues.map((value) => ({
                                    value: value.id,
                                    label: value.value,
                                    pivotId: pivotId,
                                }));

                                selectedOptions[pivotId].push(...selectValuesOptions);
                            }
                        });
                    }
                });

                selectedOptionsByProduct[product.Default.product_id] = { ...selectedOptions };
            });

            setSelectedSingleOptions(selectedOptionsByProduct);
        }
        if (paginatedDataFromServer && paginatedDataFromServer.products && paginatedDataFromServer.products.length > 0) {
            const selectedCategoriesObj = {}; // Object to store selected categories for each product

            // Loop through the products array
            paginatedDataFromServer.products.forEach((product) => {
                if (product.Default) {
                    const selectedCategories = product.Default.category;
                    if (selectedCategories && Array.isArray(selectedCategories)) {
                        const selectCategoryOptions = selectedCategories.map((category) => ({
                            value: category.id,
                            label: category.name,
                        }));

                        // Store selected categories for this product ID in the object
                        selectedCategoriesObj[product.Default.product_id] = selectCategoryOptions;
                    }
                }
            });

            // Set the state with the accumulated selected categories object
            setSelectedCategories(selectedCategoriesObj);
        }
        if (paginatedDataFromServer && paginatedDataFromServer.products && paginatedDataFromServer.products.length > 0) {
            const selectedStoresObj = {}; // Object to store selected categories for each product

            // Loop through the products array
            paginatedDataFromServer.products.forEach((product) => {
                if (product.Default) {
                    const selectedStore = product.Default.store;
                    if (selectedStore && Array.isArray(selectedStore)) {
                        const selectStoreOptions = selectedStore.map((store) => ({
                            value: store.id,
                            label: store.name,
                        }));

                        // Store selected categories for this product ID in the object
                        selectedStoresObj[product.Default.product_id] = selectStoreOptions;
                    }
                }
            });

            // Set the state with the accumulated selected categories object
            setSelectedStores(selectedStoresObj);
        }
        $(".loader").addClass("hidden");
    };
    const [columns, setColumns] = useState([]);
    const [isInitialDataLoaded, setIsInitialDataLoaded] = useState(false);
    const [columnSearchValue, setColumnSearchValue] = useState("");
    const [filteredColumns, setFilteredColumns] = useState([]);
    const toggleColumn = (hierName) => {
        const updatedColumns = columns.map((column) => (column.hierName === hierName ? { ...column, checked: !column.checked } : column));
        setColumns(updatedColumns);
        setFilteredColumns(updatedColumns);
    };
    const handleModalSearch = (columnSearchValue) => {
        const searchValue = columnSearchValue.toLowerCase();
        setColumnSearchValue(columnSearchValue);

        // Filter the original columns based on the search value
        const filtered = data.attributes.reduce((result, family) => {
            const filteredAttributes = family.attributes.filter((attribute) => attribute.name.toLowerCase().includes(searchValue));

            if (filteredAttributes.length > 0) {
                result.push({
                    label: family.name,
                    isParent: true,
                });

                filteredAttributes.forEach((attribute) => {
                    const familyName = family.name;
                    const attributeLabel = attribute.name;
                    const isChecked = headerData[familyName] && headerData[familyName][attribute.handle] ? "checked" : "";

                    result.push({
                        key: attribute.handle,
                        label: attributeLabel,
                        checked: isChecked,
                        isParent: false,
                        hierName: familyName + "," + attribute.handle,
                    });
                });
            }
            return result;
        }, []); // Initialize filtered as an array

        // Update the filtered columns state
        setFilteredColumns(filtered);
    };
    const openModal = () => {
        setIsModalOpen(true);
        const flattenAttributes = (attributes) => {
            const result = [];
            for (const family of attributes) {
                const familyName = family.name;
                const parent = { label: familyName, isParent: true };
                result.push(parent);
                for (const attribute of family.attributes) {
                    const attributeLabel = attribute.name;
                    let isChecked;
                    let child;
                    if (headerData[familyName] && headerData[familyName][attribute.handle]) {
                        isChecked = "checked";
                    } else {
                        isChecked = "";
                    }
                    if (attribute.handle == "sku" || attribute.handle == "product_name") {
                        child = {
                            key: attribute.handle,
                            label: attributeLabel,
                            checked: "checked",
                            isParent: false,
                            hierName: familyName + "," + attribute.handle,
                        };
                    } else {
                        child = {
                            key: attribute.handle,
                            label: attributeLabel,
                            checked: isChecked,
                            isParent: false,
                            hierName: familyName + "," + attribute.handle,
                        };
                    }
                    result.push(child);
                }
            }
            return result;
        };

        if (open) {
            const initialColumns = flattenAttributes(data.attributes);
            setColumns(initialColumns);
            setFilteredColumns(initialColumns);

            setIsInitialDataLoaded(true);
        } else {
            setIsInitialDataLoaded(false);
        }
    };

    const closeModalbutton = () => {
        setIsInitialDataLoaded(false);

        closeModal();
    };
    const closeModal = () => {
        setIsModalOpen(false);
        filterAndRenderTable(filteredData);
    };
    const handleBackdropClick = (e) => {
        if (e.target === e.currentTarget) {
            closeModal();
        }
    };
    const handleSaveClick = () => {
        $(".loader").removeClass("hidden");
        var filter_attributes = {};

        // Loop through all the checkboxes
        $('input[type="checkbox"]').each(function () {
            if ($(this).is(":checked")) {
                var values = $(this).val().split(",");
                var familyName = values[0] ? values[0].trim() : "";
                var attributeHandle = values[1] ? values[1].trim() : "";
                var attributeLabel = $(this).next("label").text();
                if (!filter_attributes[familyName]) {
                    filter_attributes[familyName] = {};
                }

                filter_attributes[familyName][attributeHandle] = attributeLabel;
            }
        });

        const structuredObject = {
            payload: filter_attributes,
        };

        axios.defaults.headers.common["X-CSRF-TOKEN"] = document.querySelector('meta[name="csrf-token"]').getAttribute("content");
        axios
            .post("/products/bulk/edit/filter/save", structuredObject)
            .then((response) => {
                toast.success("Columns Updated Succesfully");
                closeModal();
                setTimeout(function () {
                    $(".loader").addClass("hidden");
                }, 2000);
            })
            .catch((error) => console.log(error));
        setHeaderData(filter_attributes);
        renderTableHeader();
        renderTableBody();
        closeModal();
    };

    const handleSearch = (searchValue) => {
        // Filter the data based on SKU
        const initialData = data.products;
        const filtered = initialData.filter((item) => {
            const productName = getProductName(item);
            const handle = item.Default.handle.toLowerCase();
            const searchLowerCase = searchValue.toLowerCase();

            return (
                productName.includes(searchLowerCase) || // Filter by product name
                handle.includes(searchLowerCase) // Filter by SKU
            );
        });
        setFilteredData(filtered);
    };
    function getProductName(item) {
        return (item?.General?.product_name?.value?.[0]?.value || "").toLowerCase();
    }
    const filterAndRenderTable = (selectedColumns) => {
        // Create a filtered array of product data
        const filteredProductData = data.products.map((product) => {
            const filteredFamilies = {};
            for (const familyName in product) {
                if (selectedColumns[familyName]) {
                    filteredFamilies[familyName] = {};
                    for (const attributeKey in product[familyName]) {
                        if (selectedColumns[familyName][attributeKey]) {
                            filteredFamilies[familyName][attributeKey] = product[familyName][attributeKey];
                        }
                    }
                }
            }
            return { ...filteredFamilies };
        });

        // Update the products state with the filtered product data
        setProducts(filteredProductData);
    };

    $(document).ready(function () {
        $(document).on("click", ".multiplevalues", function (e) {
            $(".multiplevalues").removeClass("active");
            e.stopPropagation();
            $(this).addClass("active");
        });
        $(document).on("click", function (e) {
            var parentDiv = $(".multiplevalues");
            if (!$(e.target).is(parentDiv) && parentDiv.has(e.target).length === 0) {
                // Remove all classes
                parentDiv.removeClass("active");
            }
        });
    });
    const [categoryNewOptions, setCategoryNewOptions] = useState([]);
    const buildCategoryTree = (categories) => {
        const categoryMap = {};
        categories.forEach((category) => {
            category.children = []; // Initialize children array
            categoryMap[category.id] = category;
        });

        const tree = [];
        categories.forEach((category) => {
            if (category.category_id && categoryMap[category.category_id]) {
                categoryMap[category.category_id].children.push(category);
            } else {
                tree.push(category);
            }
        });

        return { tree, categoryMap };
    };

    const buildOptions = (categories, depth = 0, seen = new Set()) => {
        let options = [];
        console.log(categories, "categories");
        categories.forEach((category) => {
            if (!seen.has(category.id)) {
                seen.add(category.id);
                const label = category.name;
                const isChild = category.category_id ? true : false;
                options.push({ value: category.id, label: label, isChild: isChild, depth: depth });
                if (category.children.length > 0) {
                    options = options.concat(buildOptions(category.children, depth + 1, seen));
                }
            }
        });
        return options;
    };

    const removeEmptyParents = (categoryId, categoryMap) => {
        console.log(categoryMap, "categoryMap");
        const category = categoryMap[categoryId];
        if (category && category.id) {
            const parentCategory = categoryMap[category.category_id];
            delete categoryMap[categoryId];
            if (parentCategory) {
                parentCategory.children = parentCategory.children.filter((child) => child.id !== category.id);
                if (parentCategory.children.length === 0) {
                    //delete categoryMap[parentCategory.id];
                    console.log(categoryMap, "categoryMap");
                    removeEmptyParents(parentCategory.id, categoryMap);
                }
            }
        }
    };

    const updateOptionsAfterSelection = (selectedCategories, categoryMap) => {
        selectedCategories.forEach((selectedCategory) => {
            //removeEmptyParents(selectedCategory.value, categoryMap);
        });
        return buildOptions(Object.values(categoryMap).filter((category) => category.children.length > 0 || !category.category_id));
    };

    const handleChange = (productId, familyName, categoryHandle, selectedValues, categoryMap) => {
        setSelectedCategories((prevState) => ({
            ...prevState,
            [productId]: selectedValues,
        }));

        setChangedAttributes((prevAttributes) => {
            const updatedAttributes = { ...prevAttributes };

            if (!updatedAttributes[productId]) {
                updatedAttributes[productId] = {};
            }

            if (!updatedAttributes[productId][familyName]) {
                updatedAttributes[productId][familyName] = {};
            }

            updatedAttributes[productId][familyName]["category"] = selectedValues.map((selectedValue) => selectedValue.value);

            return updatedAttributes;
        });
        console.log(selectedValues, "selectedValues");
        const updatedOptions = updateOptionsAfterSelection(selectedValues, categoryMap);
        console.log(updatedOptions, "updatedOptions");
        setCategoryNewOptions(updatedOptions);
    };

    useEffect(() => {
        const data = bulk_edit;
        setData(data);
        console.log(data);
        setHeaderData(data.filter_attributes);
        setFilteredData(data.products);
        const totalPages = data.data_required.total_pages;
        const initialTagValues = {};
        data.products.forEach((product) => {
            const family = product.SEO; // Adjust based on your data structure
            const attribute = family.seo_keyword; // Adjust based on your data structure
            if (attribute && attribute.value && attribute.value.length > 0) {
                initialTagValues[product.Default.product_id] = attribute.value[0].value ? attribute.value[0].value.split(", ") : [];
            } else {
                initialTagValues[product.Default.product_id] = [];
            }
        });
        setInputValues(initialTagValues);
        setTotalPageCount(totalPages);
        setProducts(data.products);
        if (data && data.products) {
            const selectedOptionsByProduct = {};

            // Loop through the products array
            data.products.forEach((product) => {
                const selectedOptions = {};

                Object.keys(product).forEach((key) => {
                    const family = product[key];

                    if (key !== "Default") {
                        Object.values(family).forEach((attribute) => {
                            if (attribute && attribute.attribute_type_id === 4 && attribute.value && Array.isArray(attribute.value)) {
                                const pivotId = attribute.pivotId;
                                const productID = product.Default.product_id;

                                if (!selectedOptions[pivotId]) {
                                    selectedOptions[pivotId] = [];
                                }

                                const attributeValues = attribute.value;

                                const selectValuesOptions = attributeValues.map((value) => ({
                                    value: value.id,
                                    label: value.value,
                                    pivotId: pivotId,
                                }));

                                selectedOptions[pivotId].push(...selectValuesOptions);
                            }
                        });
                    }
                });

                selectedOptionsByProduct[product.Default.product_id] = { ...selectedOptions };
            });

            setSelectedSingleOptions(selectedOptionsByProduct);
        }
        if (data && data.products && data.products.length > 0) {
            const selectedCategoriesObj = {}; // Object to store selected categories for each product
            const buildCategoryTree = (categories) => {
                const categoryMap = {};
                categories.forEach((category) => {
                    category.children = []; // Initialize children array
                    categoryMap[category.id] = category;
                });

                const tree = [];
                categories.forEach((category) => {
                    if (category.category_id && categoryMap[category.category_id]) {
                        categoryMap[category.category_id].children.push(category);
                    } else {
                        tree.push(category);
                    }
                });

                return { tree, categoryMap };
            };

            // Function to find the full path of a category
            const findCategoryPath = (category, categoryMap) => {
                const path = [];
                while (category) {
                    path.unshift(category.name);
                    category = categoryMap[category.category_id];
                }
                return path.join("/");
            };

            // Build the category tree and map
            const categoryOptions = data.attributes[0].attributes[3].attribute_options;
            const categoryTree = buildCategoryTree(categoryOptions);
            const categoryMap = {};
            categoryOptions.forEach((category) => {
                categoryMap[category.id] = category;
            });
            const buildOptions = (categories, depth = 0) => {
                let options = [];
                categories.forEach((category) => {
                    const label = `${category.name}`;
                    const isChild = category.category_id ? true : false;
                    options.push({ value: category.id, label: label, isChild: isChild, depth: depth });
                    console.log(options, "options");
                    if (category.children.length > 0) {
                        options = options.concat(buildOptions(category.children, depth + 1));
                    }
                });
                return options;
            };
            const catoptions = buildOptions(categoryTree.tree);
            console.log(catoptions, "catoptions");
            setCategoryNewOptions(catoptions);
            // Loop through the products array
            data.products.forEach((product) => {
                if (product.Default) {
                    const selectedCategories = product.Default.category;
                    if (selectedCategories && Array.isArray(selectedCategories)) {
                        const selectCategoryOptions = selectedCategories.map((category) => {
                            const fullPath = findCategoryPath(category, categoryMap);
                            return {
                                value: category.id,
                                label: fullPath,
                            };
                        });

                        // Store selected categories for this product ID in the object
                        selectedCategoriesObj[product.Default.product_id] = selectCategoryOptions;
                    }
                }
            });
            console.log(selectedCategoriesObj, "selectedCategoriesObj");
            // Set the state with the accumulated selected categories object
            setSelectedCategories(selectedCategoriesObj);
        }
        if (data && data.products && data.products.length > 0) {
            const selectedStoresObj = {}; // Object to store selected categories for each product

            // Loop through the products array
            data.products.forEach((product) => {
                if (product.Default) {
                    const selectedStore = product.Default.store;
                    if (selectedStore && Array.isArray(selectedStore)) {
                        const selectStoreOptions = selectedStore.map((store) => ({
                            value: store.id,
                            label: store.name,
                        }));

                        // Store selected categories for this product ID in the object
                        selectedStoresObj[product.Default.product_id] = selectStoreOptions;
                    }
                }
            });

            // Set the state with the accumulated selected categories object
            setSelectedStores(selectedStoresObj);
        }
        // })
        // .catch((error) => console.log(error));
    }, []);

    if (!data) {
        // You can display a loading message or spinner here
        return (
            <div className="loader fixed w-full h-full bg-[rgba(255,255,255,0.5)] left-0 top-0">
                <div role="status" className="flex justify-center items-center h-full">
                    <svg
                        aria-hidden="true"
                        className="w-16 h-16 text-gray-200 animate-spin dark:text-gray-600 fill-blue-600"
                        viewBox="0 0 100 101"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                    >
                        <path
                            d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                            fill="currentColor"
                        />
                        <path
                            d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                            fill="currentFill"
                        />
                    </svg>
                    <span className="sr-only">Loading...</span>
                </div>
            </div>
        );
    }
    const headerCells = [];
    const renderTableHeader = () => {
        headerCells.push(
            <th
                style={{
                    width: "60px",
                    maxWidth: "60px",
                    minWidth: "60px",
                    whiteSpace: "nowrap",
                    overflow: "hidden",
                    textOverflow: "ellipsis",
                }}
                title="Variants"
            >
                Variants
            </th>,
            <th
                key="image"
                className="text-black mappingnodes[index]?.replace text-center"
                style={{
                    width: "60px",
                    maxWidth: "60px",
                    minWidth: "60px",
                    whiteSpace: "nowrap",
                    overflow: "hidden",
                    textOverflow: "ellipsis",
                }}
                title="Images"
            >
                Images
            </th>,
            <th className="hidden" key="product_id">
                Product ID
            </th>,
            <th
                className="text-black mappingnodes[index]?.replace "
                key="product_name"
                title="Product Name"
                style={{ width: "170px", minWidth: "170px", maxWidth: "170px" }}
            >
                Product Name
            </th>
        );
        // Iterate through the attributes and families to create header cells
        data.attributes.forEach((family) => {
            // Skip the "Default" family
            if (family.name === "Default") {
                const brandExists = headerData[family.name] && headerData[family.name]["brand"];
                const categoryExists = headerData[family.name] && headerData[family.name]["category"];
                const storeExists = headerData[family.name] && headerData[family.name]["store"];
                const vendorExists = headerData[family.name] && headerData[family.name]["vendor"];
                if (brandExists) {
                    headerCells.push(
                        <th
                            key="brand"
                            className="text-black mappingnodes[index]?.replace"
                            style={{ width: "150px", minWidth: "150px", maxWidth: "150px" }}
                        >
                            Brand
                        </th>
                    );
                }
                if (vendorExists) {
                    headerCells.push(
                        <th
                            key="vendor"
                            className="text-black mappingnodes[index]?.replace"
                            style={{ width: "250px", minWidth: "250px", maxWidth: "250px" }}
                        >
                            Vendors
                        </th>
                    );
                }
                if (categoryExists) {
                    headerCells.push(
                        <th
                            key="category"
                            className="text-black mappingnodes[index]?.replace"
                            style={{ width: "250px", minWidth: "250px", maxWidth: "250px" }}
                        >
                            Category
                        </th>
                    );
                }
                if (storeExists) {
                    headerCells.push(
                        <th
                            key="store"
                            className="text-black mappingnodes[index]?.replace"
                            style={{ width: "250px", minWidth: "250px", maxWidth: "250px" }}
                        >
                            Store
                        </th>
                    );
                }
            }

            Object.values(family.attributes).forEach((attribute) => {
                if (headerData[family.name] && headerData[family.name][attribute.handle]) {
                    if (family.name === "Default") {
                    } else if (attribute.handle !== "product_name") {
                        headerCells.push(
                            <th
                                id={`${family.name},${attribute.handle}`}
                                className="text-black mappingnodes[index]?.replace"
                                key={`${family.name}-${attribute.handle}`}
                            >
                                {attribute.name}
                            </th>
                        );
                    }
                }
            });
        });
        return headerCells;
    };
    const defaultImage = "/img/apimio_default.jpg";
    const imageArr = filteredData.map((product) => {
        const displayImage = product?.Default?.file?.link;
        const img = new Image();
        img.src = displayImage;

        if (img.complete && img.height !== 0) {
            return displayImage;
        } else {
            return defaultImage;
        }
    });
    const renderTableBody = () => {
        if (!data || !data.products) {
            return null;
        }

        const productRows = [];

        // Iterate through the product data to create rows
        filteredData.forEach((product, index) => {
            const rowCells = [];
            const variantData = [];
            const imageSrc = imageArr[index];
            let variantsDisplayed = false;
            // Add SKU, Product ID, and File Link values
            rowCells.push(
                <td style={{ width: "60px", maxWidth: "60px", minWidth: "60px" }}>
                    {product.Default && product.Default.variants && product.Default.variants.length > 0 ? (
                        <button
                            key={product.Default.variants.map((variant) => variant.id).join("")}
                            className="variant-button text-left w-full py-2 relative"
                            type="button"
                            onClick={() => toggleAccordion(index)}
                        >
                            <h2 className="variant-header  font-semibold text-sm text-[#222]">
                                {variantsDisplayed ? "" : product.Default.variants.length}
                                <span className="sm:ml-2 xl:ml-4 ml-1">
                                    <i className={`fa text-lg ${isOpenArray[index] ? "fa-caret-up" : "fa-caret-down"}`}></i>
                                </span>
                            </h2>
                        </button>
                    ) : (
                        ""
                    )}
                </td>,
                <td
                    key={`image-${index}`}
                    className="text-center object-cover"
                    style={{ width: "40px", maxWidth: "40px", minWidth: "40px" }}
                >
                    <img
                        src={imageSrc}
                        alt={product?.Default?.file?.name || "Default Image"}
                        style={{
                            width: "40px",
                            height: "40px",
                            display: "inline-block",
                            marginRight: "10px",
                            borderRadius: "5px",
                            border: "1px solid #E6E6E6",
                            objectFit: "contain",
                        }}
                    />
                </td>,

                <td key={`product_id-${index}`} className="hidden">
                    {product.Default.product_id}
                </td>,
                <td key={`product_name-${index}`} style={{ width: "170px", minWidth: "170px" }}>
                    <div className="singlevalue">
                        <input
                            className="w-full bg-[#F8F8F8]"
                            type="text"
                            value={product.General?.product_name?.value[0]?.value || ""}
                            onChange={(e) => {
                                product.General.product_name.value[0].value = [...value];
                                product.General.product_name.value[0].value = e.target.value;
                                handleInputChange(
                                    product.Default.product_id,
                                    "General",
                                    "product_name",
                                    product.General.product_name.value[0].value,
                                    product.General.product_name.pivotId
                                );
                            }}
                        />
                    </div>
                </td>
            );

            data.attributes.forEach((family) => {
                if (family.name === "Default") {
                    // Check if "brand," "category," and "store" exist in data.filter_attributes
                    const brandExists = headerData[family.name] && headerData[family.name]["brand"];
                    const categoryExists = headerData[family.name] && headerData[family.name]["category"];
                    const storeExists = headerData[family.name] && headerData[family.name]["store"];
                    const vendorExists = headerData[family.name] && headerData[family.name]["vendor"];

                    if (brandExists) {
                        const brandOptions = data.attributes[0].attributes[1].attribute_options;
                        if (brandOptions.length > 0) {
                            rowCells.push(
                                <td key={`brand-${index}`} style={{ width: "150px", minWidth: "150px", maxWidth: "150px" }}>
                                    <select
                                        className="w-full h-[36px]"
                                        value={product.Default.brand[0]?.id || ""}
                                        onChange={(e) =>
                                            handleBrandChange(
                                                product.Default.product_id,
                                                family.name,
                                                product.Default.brand.handle,
                                                e.target.value,
                                                index
                                            )
                                        }
                                    >
                                        <option value="">Select an option</option>
                                        {brandOptions.map((option) => (
                                            <option key={option.id} value={option.id}>
                                                {option.name}
                                            </option>
                                        ))}
                                    </select>
                                </td>
                            );
                        } else {
                            rowCells.push(
                                <td key={`brand-${index}`} className="w-[150px] text-orange-600">
                                    <div className="w-full text-center block cursor-not-allowed ">
                                        {/* <span className="bg-gray-600 h-2 w-8 block text-black mx-auto font-semibold border border-gray-600 cursor-not-allowed text-xs"></span> */}
                                        <h6 className="bg-gray-500 w-[10px] h-[2px]  mx-auto" title="No Brands to Select"></h6>
                                    </div>
                                </td>
                            );
                        }
                    }
                    if (vendorExists) {
                        const vendorOptions = data.attributes[0].attributes[2].attribute_options;
                        if (vendorOptions.length > 0) {
                            rowCells.push(
                                <td key={`vemdor-${index}`} style={{ width: "150px", minWidth: "150px", maxWidth: "150px" }}>
                                    <select
                                        className="w-full h-[36px]"
                                        value={product.Default.vendor[0]?.id || ""}
                                        onChange={(e) =>
                                            handleVendorChange(
                                                product.Default.product_id,
                                                family.name,
                                                product.Default.vendor.handle,
                                                e.target.value,
                                                index
                                            )
                                        }
                                    >
                                        <option value="">Select an option</option>
                                        {vendorOptions.map((option) => (
                                            <option key={option.id} value={option.id}>
                                                {option.fname}
                                            </option>
                                        ))}
                                    </select>
                                </td>
                            );
                        } else {
                            rowCells.push(
                                <td key={`vendor-${index}`} className="w-[150px] text-orange-600">
                                    <div className="w-full text-center block cursor-not-allowed ">
                                        {/* <span className="bg-gray-600 h-2 w-8 block text-black mx-auto font-semibold border border-gray-600 cursor-not-allowed text-xs"></span> */}
                                        <h6 className="bg-gray-500 w-[10px] h-[2px]  mx-auto" title="No Vendors to Select"></h6>
                                    </div>
                                </td>
                            );
                        }
                    }

                    if (categoryExists) {
                        const productID = product.Default.product_id;

                        console.log(productID, "productID");
                        const selectedCategoriesForProduct = selectedCategories[productID] || [];

                        const categoryOptions = data.attributes[0].attributes[3].attribute_options;
                        const selectedAttributeValues = Array.isArray(value) ? value.map((item) => item.value) : [];

                        const CustomParentOption = ({ data, isSelected, onChange }) => (
                            <div
                                style={{
                                    display: "flex",
                                    alignItems: "center",
                                    paddingTop: "5px",
                                    paddingBottom: "5px",
                                    paddingLeft: "10px",
                                }}
                            >
                                <input
                                    type="checkbox"
                                    value={data.value}
                                    checked={isSelected}
                                    onChange={() =>
                                        handleParentCheckboxChange(
                                            data,
                                            !isSelected,
                                            productID,
                                            family.name,
                                            product.Default.category.handle
                                        )
                                    }
                                    style={{ marginRight: "8px" }}
                                />
                                <strong>{data.label}</strong>
                            </div>
                        );

                        const CustomChildOption = ({ data, isSelected, onChange, depth }) => (
                            <div
                                style={{
                                    display: "flex",
                                    alignItems: "center",
                                    marginLeft: `${depth * 20}px`,
                                    paddingTop: "5px",
                                    paddingBottom: "5px",
                                    paddingLeft: "10px",
                                }}
                            >
                                <input
                                    type="checkbox"
                                    value={data.value}
                                    checked={isSelected}
                                    onChange={() =>
                                        handleChildCheckboxChange(
                                            data,
                                            !isSelected,
                                            productID,
                                            family.name,
                                            product.Default.category.handle
                                        )
                                    }
                                    style={{ marginRight: "8px" }}
                                />
                                <span>{data.label}</span>
                            </div>
                        );

                        const handleParentCheckboxChange = (parentOption, checked, productId, familyName, categoryhandle) => {
                            let updatedSelectedValues = selectedCategories[productId] || [];

                            if (checked) {
                                const childCategories = categoryMap[parentOption.value].children || [];
                                if (childCategories.length === 0) {
                                    // If no child categories, add the parent category directly
                                    const newItem = { value: parentOption.value, label: findCategoryPath(parentOption.value, categoryMap) };

                                    // Add the new item only if it does not already exist
                                    if (!updatedSelectedValues.some((item) => item.value === newItem.value)) {
                                        updatedSelectedValues = [...updatedSelectedValues, newItem];
                                    }
                                } else {
                                    childCategories.forEach((child) => {
                                        let newItem;
                                        const dynamicChildCategories = Array.isArray(child.children) ? child.children : [];
                                        console.log(dynamicChildCategories, "dynamicChildCategories");
                                        if (dynamicChildCategories.length > 0) {
                                            dynamicChildCategories.forEach((child) => {
                                                newItem = { value: child.id, label: findCategoryPath(child.id, categoryMap) };

                                                // Add the new item only if it does not already exist
                                                if (!updatedSelectedValues.some((item) => item.value === newItem.value)) {
                                                    updatedSelectedValues = [...updatedSelectedValues, newItem];
                                                }
                                            }, []);
                                        } else {
                                            newItem = { value: child.id, label: findCategoryPath(child.id, categoryMap) };
                                        }
                                        console.log(newItem, "newItem");
                                        // Add the new item only if it does not already exist
                                        if (!updatedSelectedValues.some((item) => item.value === newItem.value)) {
                                            updatedSelectedValues = [...updatedSelectedValues, newItem];
                                        }
                                    });
                                }
                            } else {
                                updatedSelectedValues = updatedSelectedValues.filter((item) => item.value !== parentOption.value);
                            }

                            handleChange(productId, familyName, categoryhandle, updatedSelectedValues, categoryMap);

                            const updatedOptions = updateOptionsAfterSelection(updatedSelectedValues, categoryMap);

                            setCategoryNewOptions(updatedOptions);
                        };

                        const handleChildCheckboxChange = (childOption, checked, productId, familyName, categoryhandle) => {
                            let updatedSelectedValues = selectedCategories[productId] || [];
                            if (checked) {
                                const childCategories = categoryMap[childOption.value].children || [];
                                if (childCategories.length === 0) {
                                    updatedSelectedValues = [
                                        ...updatedSelectedValues,
                                        { value: childOption.value, label: findCategoryPath(childOption.value, categoryMap) },
                                    ];
                                } else {
                                    childCategories.forEach((child) => {
                                        let newItem;
                                        const dynamicChildCategories = Array.isArray(child.children) ? child.children : [];
                                        console.log(dynamicChildCategories, "dynamicChildCategories");
                                        if (dynamicChildCategories.length > 0) {
                                            dynamicChildCategories.forEach((child) => {
                                                newItem = { value: child.id, label: findCategoryPath(child.id, categoryMap) };

                                                // Add the new item only if it does not already exist
                                                if (!updatedSelectedValues.some((item) => item.value === newItem.value)) {
                                                    updatedSelectedValues = [...updatedSelectedValues, newItem];
                                                }
                                            }, []);
                                        } else {
                                            newItem = { value: child.id, label: findCategoryPath(child.id, categoryMap) };
                                        }
                                        console.log(newItem, "newItem");
                                        // Add the new item only if it does not already exist
                                        if (!updatedSelectedValues.some((item) => item.value === newItem.value)) {
                                            updatedSelectedValues = [...updatedSelectedValues, newItem];
                                        }
                                    });
                                }
                            } else {
                                updatedSelectedValues = updatedSelectedValues.filter((item) => item.value !== childOption.value);
                            }

                            handleChange(productId, familyName, categoryhandle, updatedSelectedValues, categoryMap);
                            const updatedOptions = updateOptionsAfterSelection(updatedSelectedValues, categoryMap);

                            setCategoryNewOptions(updatedOptions);
                        };

                        const findCategoryPath = (categoryId, categoryMap) => {
                            const category = categoryMap[categoryId];
                            if (!category) return "";
                            const path = [category.name];
                            let currentCategory = category;
                            while (currentCategory.category_id) {
                                currentCategory = categoryMap[currentCategory.category_id];
                                if (currentCategory) path.unshift(currentCategory.name);
                            }
                            return path.join("/");
                        };

                        const { tree, categoryMap } = buildCategoryTree(categoryOptions);
                        let options = buildOptions(tree);

                        if (options.length > 0) {
                            rowCells.push(
                                <td key={`category-${index}`} style={{ width: "250px", minWidth: "250px", maxWidth: "250px" }}>
                                    <Select
                                        key={`category-${productID}`}
                                        className="apimio-apimio-react-select-container"
                                        classNamePrefix="apimio-react-select"
                                        options={categoryNewOptions.map((option) => ({
                                            ...option,
                                            component: option.isChild ? CustomChildOption : CustomParentOption,
                                        }))}
                                        closeMenuOnSelect={false}
                                        isMulti
                                        value={selectedCategories[productID]}
                                        getOptionLabel={(option) => option.label}
                                        getOptionValue={(option) => option.value}
                                        onChange={(selected) => {
                                            const updatedSelectedCategories = selected.map((option) => ({
                                                value: option.value,
                                                label: option.label,
                                            }));

                                            setSelectedCategories((prevState) => ({
                                                ...prevState,
                                                [productID]: updatedSelectedCategories,
                                            }));
                                            handleChange(
                                                productID,
                                                family.name,
                                                product.Default.category.handle,
                                                updatedSelectedCategories,
                                                categoryMap
                                            );
                                            const updatedOptions = updateOptionsAfterSelection(updatedSelectedCategories, categoryMap);

                                            setCategoryNewOptions(updatedOptions);
                                        }}
                                        components={{
                                            Option: (props) => {
                                                const Component = props.data.component;
                                                return (
                                                    <Component
                                                        {...props}
                                                        onChange={props.selectProps.onChange}
                                                        isSelected={props.isSelected}
                                                        depth={props.data.depth}
                                                    />
                                                );
                                            },
                                        }}
                                    />
                                </td>
                            );
                        } else {
                            rowCells.push(
                                <td key={`category-${index}`} className="w-[150px] text-orange-600">
                                    <div className="w-full text-center block cursor-not-allowed">
                                        <h6 className="bg-gray-500 w-[10px] h-[2px] mx-auto" title="No Category to Select"></h6>
                                    </div>
                                </td>
                            );
                        }
                    }

                    if (storeExists) {
                        const productID = product.Default.product_id;
                        //const selectedStoresForProduct = selectedStores[productID] || [];

                        const storesOptions = data.attributes[0].attributes[4].attribute_options;
                        const selectedAttributeValues = Array.isArray(value) ? value.map((item) => item.value) : [];

                        const options = storesOptions.map((option) => ({
                            value: option.id,
                            label: option.name,
                            isSelected: selectedAttributeValues.includes(option.id.toString()),
                        }));
                        const isInvalid = invalidFields[`${productID}-${family.name}`];
                        rowCells.push(
                            <td key={`store-${index}`} style={{ width: "250px", minWidth: "250px", maxWidth: "250px" }}>
                                <Select
                                    className={`apimio-apimio-react-select-container rounded-sm ${
                                        isInvalid ? "border border-danger " : ""
                                    }`}
                                    classNamePrefix="apimio-react-select"
                                    options={options}
                                    isMulti
                                    value={selectedStores[productID]}
                                    getOptionLabel={(option) => option.label}
                                    getOptionValue={(option) => option.value}
                                    components={removeAllFalse}
                                    onChange={(selected) => {
                                        const updatedSelectedStores = selected.map((option) => ({
                                            value: option.value,
                                            label: option.label,
                                        }));

                                        setSelectedStores((prevState) => ({
                                            ...prevState,
                                            [productID]: updatedSelectedStores,
                                        }));

                                        handleStoreChange(productID, family.name, product.Default.store.handle, updatedSelectedStores);
                                    }}
                                />
                            </td>
                        );
                    }
                } else {
                    Object.values(family.attributes).forEach((attribute) => {
                        if (headerData[family.name] && headerData[family.name][attribute.handle]) {
                            if (family.name === "Default") {
                            } else if (attribute.handle !== "product_name") {
                                const productValue = product[family.name];
                                let cellContent;
                                let selectedValuesForSelect = [];
                                // Check if the product has the attribute and handle the case where it doesn't exist

                                if (productValue && productValue[attribute.handle]) {
                                    const value = productValue[attribute.handle].value;
                                    if (value.length === 0) {
                                        value.push({ value: "", unit: "" });
                                    }
                                    if (value) {
                                        if (family.name === "SEO" && attribute.handle === "seo_keyword") {
                                            cellContent = (
                                                <div className="singlevalue">
                                                    <TagsInput
                                                        value={inputValues[product.Default.product_id] || []}
                                                        onChange={(tags) =>
                                                            handleTagChange(
                                                                tags,
                                                                product.Default.product_id,
                                                                "SEO",
                                                                "seo_keyword",
                                                                attribute.pivotId
                                                            )
                                                        }
                                                    />
                                                    {/* <input
                                                            className="w-full bg-[#F8F8F8]"
                                                            type="text"
                                                            value={value && value[0] ? value[0].value || "" : ""}
                                                            onChange={(e) => {
                                                                const updatedValue = [...value];
                                                                updatedValue[0].value = e.target.value;
                                                                setValue(e.target.value);
                                                                handleInputChange(
                                                                    product.Default.product_id,
                                                                    family.name,
                                                                    attribute.handle,
                                                                    e.target.value,
                                                                    attribute.pivotId
                                                                );
                                                            }}
                                                        /> */}
                                                </div>
                                            );
                                        } else if (family.name === "SEO" && attribute.handle == "seo_description") {
                                            cellContent = (
                                                <div className="singlevalue">
                                                    <textarea
                                                        className="w-full bg-[#F8F8F8] h-[36px]"
                                                        onChange={(e) => {
                                                            const updatedValue = [...value];
                                                            updatedValue[0].value = e.target.value;
                                                            setValue(updatedValue);
                                                            handleTextareaChange(
                                                                product.Default.product_id,
                                                                family.name,
                                                                attribute.handle,
                                                                e.target.value,
                                                                attribute.pivotId
                                                            );
                                                        }}
                                                        value={value[0]?.value || ""}
                                                    ></textarea>
                                                </div>
                                            );
                                        } else {
                                            if (attribute.attribute_type_id === 1 && attribute.handle !== "title") {
                                                const rules = JSON.parse(attribute.rules);
                                                const value_type = rules.value_type;
                                                if (value_type == "list") {
                                                    cellContent = (
                                                        <div className="multiplevalues">
                                                            <div className="values-container w-full relative">
                                                                <div className="w-10/12 inline-block overflow-hidden">
                                                                    {value.map((inputValue, inputIndex) =>
                                                                        inputValue?.value && inputValue.value.trim() !== "" ? (
                                                                            inputValue.value + ", "
                                                                        ) : (
                                                                            <span className="text-xs text-gray-400">No Value , </span>
                                                                        )
                                                                    )}
                                                                </div>
                                                                <i className="fa fa-chevron-down absolute z-10 right-1"></i>
                                                            </div>

                                                            <div className="value-inner">
                                                                {value.map((inputValue, inputIndex) => (
                                                                    <div className="flex justify-between">
                                                                        <input
                                                                            className="flex bg-[#F8F8F8]"
                                                                            key={`input-${inputIndex}`}
                                                                            type="text"
                                                                            value={inputValue?.value != null ? inputValue.value : ""}
                                                                            id={attribute.pivotId}
                                                                            onChange={(e) => {
                                                                                const updatedValue = [...value];
                                                                                updatedValue[inputIndex].value = e.target.value;
                                                                                setValue(updatedValue);

                                                                                // Collect all values for a pivot ID and pass them to handleInputChange
                                                                                const allValues = value.map(
                                                                                    (inputValue) => inputValue.value
                                                                                );
                                                                                handleMultiInputChange(
                                                                                    product.Default.product_id,
                                                                                    family.name,
                                                                                    attribute.handle,
                                                                                    allValues,
                                                                                    attribute.pivotId
                                                                                );
                                                                            }}
                                                                        />
                                                                        {inputIndex !== 0 && (
                                                                            <button
                                                                                className="flex ml-2 mt-2"
                                                                                onClick={() =>
                                                                                    handleDeleteInput(
                                                                                        index,
                                                                                        inputIndex,
                                                                                        attribute.pivotId,
                                                                                        product.Default.product_id,
                                                                                        family.name,
                                                                                        attribute.handle
                                                                                    )
                                                                                }
                                                                            >
                                                                                <i className="fa fa-trash text-red-500"></i>
                                                                            </button>
                                                                        )}
                                                                    </div>
                                                                ))}
                                                                <button
                                                                    onClick={() =>
                                                                        handleAddInput(
                                                                            index,
                                                                            attribute.pivotId,
                                                                            product.Default.product_id,
                                                                            family.name,
                                                                            attribute.handle
                                                                        )
                                                                    }
                                                                    className="text-xs text-black font-semibold"
                                                                    type="button"
                                                                >
                                                                    + Add new field
                                                                </button>
                                                            </div>
                                                        </div>
                                                    );
                                                } else if (value && value.length === 1) {
                                                    cellContent = (
                                                        <div className="singlevalue with">
                                                            <input
                                                                className="w-full bg-[#F8F8F8]"
                                                                type="text"
                                                                value={
                                                                    value && value[0] ? (value[0].value != null ? value[0].value : "") : ""
                                                                }
                                                                onChange={(e) => {
                                                                    const updatedValue = [...value];
                                                                    updatedValue[0].value = e.target.value;
                                                                    setValue(e.target.value);

                                                                    handleInputChange(
                                                                        product.Default.product_id,
                                                                        family.name,
                                                                        attribute.handle,
                                                                        e.target.value,
                                                                        attribute.pivotId
                                                                    );
                                                                }}
                                                            />
                                                        </div>
                                                    );
                                                } else {
                                                    cellContent = (
                                                        <div className="singlevalue empty">
                                                            <input
                                                                className="w-full bg-[#F8F8F8]"
                                                                type="text"
                                                                value={
                                                                    value && value[0] ? (value[0].value != null ? value[0].value : "") : ""
                                                                }
                                                                onChange={(e) => {
                                                                    const updatedValue = [{ value: "", unit: "" }];
                                                                    updatedValue[0].value = e.target.value;
                                                                    setValue(e.target.value);
                                                                    handleInputChange(
                                                                        product.Default.product_id,
                                                                        family.name,
                                                                        attribute.handle,
                                                                        e.target.value,
                                                                        attribute.pivotId
                                                                    );
                                                                }}
                                                            />
                                                        </div>
                                                    );
                                                }
                                            } else if (attribute.attribute_type_id === 2) {
                                                const rules = JSON.parse(attribute.rules);
                                                const { min: minValue, max: maxValue } = rules;
                                                const value_type = rules.value_type;
                                                if (value_type == "list") {
                                                    cellContent = (
                                                        <div className="multiplevalues">
                                                            <div className="values-container w-full relative">
                                                                <div className="w-10/12 inline-block overflow-hidden">
                                                                    {value.map((numberValue, numberIndex) =>
                                                                        typeof numberValue.value === "number" ||
                                                                        (typeof numberValue.value === "string" &&
                                                                            numberValue.value.trim() !== "") ? (
                                                                            numberValue.value + ", "
                                                                        ) : (
                                                                            <span className="text-xs text-gray-400">No Value , </span>
                                                                        )
                                                                    )}
                                                                </div>
                                                                <i className="fa fa-chevron-down absolute z-10 right-1"></i>
                                                            </div>

                                                            <div className="value-inner">
                                                                {value.map((numberValue, numberIndex) => (
                                                                    <input
                                                                        className="w-full bg-[#F8F8F8]"
                                                                        key={`number-${numberIndex}`}
                                                                        type="number"
                                                                        value={numberValue?.value != null ? numberValue.value : ""}
                                                                        id={attribute.pivotId}
                                                                        onChange={(e) => {
                                                                            let newValue = parseFloat(e.target.value); // Convert the input value to a number

                                                                            if (isNaN(newValue)) {
                                                                                newValue = ""; // If the value is not a number, reset to an empty string
                                                                            } else {
                                                                                if (newValue < minValue) {
                                                                                    newValue = minValue;
                                                                                } else if (newValue > maxValue) {
                                                                                    newValue = maxValue;
                                                                                }
                                                                            }
                                                                            const updatedValue = [...value];
                                                                            updatedValue[numberIndex].value = newValue;
                                                                            setValue(updatedValue);

                                                                            // Collect all values for a pivot ID and pass them to handleInputChange
                                                                            const allValues = value.map((numberValue) => numberValue.value);
                                                                            handleMultiInputChange(
                                                                                product.Default.product_id,
                                                                                family.name,
                                                                                attribute.handle,
                                                                                allValues,
                                                                                attribute.pivotId
                                                                            );
                                                                        }}
                                                                    />
                                                                ))}{" "}
                                                            </div>
                                                        </div>
                                                    );
                                                } else {
                                                    cellContent = (
                                                        <div className="singlevalue">
                                                            <input
                                                                className="w-full bg-[#F8F8F8]"
                                                                type="number"
                                                                value={
                                                                    value && value[0] ? (value[0].value != null ? value[0].value : "") : ""
                                                                }
                                                                onChange={(e) => {
                                                                    let newValue = parseFloat(e.target.value); // Convert the input value to a number

                                                                    if (isNaN(newValue)) {
                                                                        newValue = ""; // If the value is not a number, reset to an empty string
                                                                    } else {
                                                                        if (newValue < minValue) {
                                                                            newValue = minValue;
                                                                        } else if (newValue > maxValue) {
                                                                            newValue = maxValue;
                                                                        }
                                                                    }
                                                                    const updatedValue = [...value];
                                                                    updatedValue[0].value = newValue;
                                                                    setValue(e.target.value);
                                                                    handleInputChange(
                                                                        product.Default.product_id,
                                                                        family.name,
                                                                        attribute.handle,
                                                                        e.target.value,
                                                                        attribute.pivotId
                                                                    );
                                                                }}
                                                            />
                                                        </div>
                                                    );
                                                }
                                            } else if (attribute.attribute_type_id === 3 && attribute.handle !== "seo_description") {
                                                const rules = JSON.parse(attribute.rules);
                                                let { min: minValue, max: maxValue } = rules;
                                                cellContent = (
                                                    <>
                                                        <textarea
                                                            className="w-full bg-[#F8F8F8] h-[36px]"
                                                            readOnly="readonly"
                                                            onClick={() => {
                                                                const attributeHandle = attribute.handle;
                                                                const familyname = family.name;
                                                                // check if it's closed
                                                                handleTextEditorChange(index, familyname, attributeHandle);
                                                            }}
                                                            value={value[0]?.value || ""}
                                                        ></textarea>
                                                        {activeEditor.index === index &&
                                                            activeEditor.familyName === family.name &&
                                                            activeEditor.attributeHandle === attribute.handle && (
                                                                <>
                                                                    <div
                                                                        key={index}
                                                                        className="absolute p-4 z-[999] w-full h-full top-0 left-0"
                                                                        onClick={() => handleDescriptionBackdropClick(index)}
                                                                    ></div>
                                                                    <div className="relative">
                                                                        <div className="absolute z-[1001] w-[100%] h-[200px] right-[50%] top-0">
                                                                            <Editor
                                                                                apiKey="fttq4ztpm1uz2b3vzgmsjk5b49puunxfx4g6hi59r83ouygm"
                                                                                value={value[0]?.value || ""}
                                                                                init={{
                                                                                    menubar: false,
                                                                                    plugins: [
                                                                                        "a11ychecker",
                                                                                        "advcode",
                                                                                        "advlist",
                                                                                        "anchor",
                                                                                        "autolink",
                                                                                        "codesample",
                                                                                        "fullscreen",
                                                                                        "help",
                                                                                        "image",
                                                                                        "editimage",
                                                                                        "tinydrive",
                                                                                        "lists",
                                                                                        "link",
                                                                                        "media",
                                                                                        "powerpaste",
                                                                                        "preview",
                                                                                        "searchreplace",
                                                                                        "table",
                                                                                        "template",
                                                                                        "tinymcespellchecker",
                                                                                        "visualblocks",
                                                                                        "wordcount",
                                                                                        "charmap",
                                                                                    ],
                                                                                    toolbar:
                                                                                        " undo redo | bold italic | forecolor backcolor |  codesample | alignleft aligncenter alignright alignjustify | bullist numlist ",
                                                                                    readonly: false,
                                                                                }}
                                                                                onEditorChange={(newValue, editor) => {
                                                                                    let checkedValue = newValue;

                                                                                    // Check if the length is within the min and max range
                                                                                    if (checkedValue.length < minValue) {
                                                                                        checkedValue = checkedValue.padEnd(minValue, " "); // Ensure min length by padding with spaces
                                                                                    } else if (checkedValue.length > maxValue) {
                                                                                        checkedValue = checkedValue.substring(0, maxValue); // Trim to max length
                                                                                        toast.error(
                                                                                            "Max Character Limit Reached, 255 Characters"
                                                                                        );
                                                                                    }

                                                                                    const updatedValue = [...value];
                                                                                    updatedValue[0].value = checkedValue; // Update the entire value array with the new value

                                                                                    setValue(newValue);
                                                                                    handleEditorChange(
                                                                                        product.Default.product_id,
                                                                                        family.name,
                                                                                        attribute.handle,
                                                                                        newValue,
                                                                                        attribute.pivotId
                                                                                    );
                                                                                }}
                                                                            />
                                                                        </div>
                                                                    </div>
                                                                </>
                                                            )}
                                                    </>
                                                );
                                            } else if (attribute.attribute_type_id === 4) {
                                                const attributeOptions = [...attribute.attribute_options];
                                                const productID = product.Default.product_id;
                                                let selectedOptions = [];
                                                const options = attributeOptions.map((option) => ({
                                                    value: option.name,
                                                    label: option.name,
                                                }));

                                                const pivotId = attribute.pivotId;

                                                if (selectedSingleOptions[productID]) {
                                                    // Check if the product ID exists
                                                    const productSelectedOptions = selectedSingleOptions[productID];

                                                    if (productSelectedOptions[pivotId]) {
                                                        // Check if the pivot ID exists for this product
                                                        const pivotSelectedOptions = productSelectedOptions[pivotId];

                                                        if (pivotSelectedOptions.length > 0) {
                                                            // Get the selected options for this pivot ID in this product
                                                            selectedOptions = pivotSelectedOptions.map((item) => ({
                                                                id: item.value,
                                                                value: item.label,
                                                                label: item.label,
                                                            }));
                                                        } else {
                                                            console.log("No matching pivotId found in selectedSingle array.");
                                                        }
                                                    } else {
                                                        console.log("pivotId not found in selectedSingle.");
                                                    }
                                                } else {
                                                    console.log("productID not found in selectedSingle.");
                                                }

                                                const rules = JSON.parse(attribute.rules);
                                                const rulesType = rules.value_type;

                                                cellContent = (
                                                    <div>
                                                        <Select
                                                            className="apimio-apimio-react-select-container"
                                                            classNamePrefix="apimio-react-select"
                                                            options={options}
                                                            isMulti={rulesType === "list"}
                                                            value={selectedOptions}
                                                            getOptionLabel={(option) => option.label}
                                                            getOptionValue={(option) => option.value}
                                                            components={removeAllFalse}
                                                            onChange={(selected) => {
                                                                const updatedSelectedList = Array.isArray(selected)
                                                                    ? selected.map((option) => ({
                                                                          id: option.value,
                                                                          value: option.name,
                                                                          label: option.label,
                                                                      }))
                                                                    : [selected];

                                                                setSelectedSingleOptions((prevState) => ({
                                                                    ...prevState,
                                                                    [productID]: {
                                                                        ...prevState[productID],
                                                                        [pivotId]: updatedSelectedList,
                                                                    },
                                                                }));

                                                                if (rulesType === "list") {
                                                                    handleMultiSelectChange(
                                                                        productID,
                                                                        family.name,
                                                                        attribute.handle,
                                                                        updatedSelectedList,
                                                                        attribute.pivotId
                                                                    );
                                                                } else {
                                                                    handleSelectChange(
                                                                        productID,
                                                                        family.name,
                                                                        attribute.handle,
                                                                        updatedSelectedList,
                                                                        attribute.pivotId
                                                                    );
                                                                }
                                                            }}
                                                        />
                                                    </div>
                                                );
                                            } else if (attribute.attribute_type_id === 5) {
                                                const rules = JSON.parse(attribute.rules);
                                                let { type: rulesType, min: minValue, max: maxValue } = rules;
                                                const value_type = rules.value_type;
                                                if (rulesType == "date") {
                                                    if (value_type == "list") {
                                                        cellContent = (
                                                            <div className="multiplevalues">
                                                                <div className="values-container w-full relative">
                                                                    <div className="w-10/12 inline-block overflow-hidden">
                                                                        {value.map((datetimeValue, datetimeIndex) =>
                                                                            datetimeValue?.value && datetimeValue.value.trim() !== "" ? (
                                                                                datetimeValue.value + ", "
                                                                            ) : (
                                                                                <span className="text-xs text-gray-400">No Value , </span>
                                                                            )
                                                                        )}
                                                                    </div>
                                                                    <i className="fa fa-chevron-down absolute z-10 right-1"></i>
                                                                </div>

                                                                <div className="value-inner">
                                                                    {value.map((dateValue, dateIndex) => (
                                                                        <input
                                                                            className="w-full bg-[#F8F8F8]"
                                                                            key={`datetime-input-${dateIndex}`}
                                                                            type="date"
                                                                            min={minValue}
                                                                            max={maxValue}
                                                                            value={dateValue?.value != null ? dateValue.value : ""}
                                                                            id={attribute.pivotId}
                                                                            onChange={(e) => {
                                                                                const updatedValue = [...value];
                                                                                updatedValue[dateIndex].value = e.target.value;
                                                                                setValue(updatedValue);

                                                                                // Collect all values for a pivot ID and pass them to handleInputChange
                                                                                const allValues = value.map((dateValue) => dateValue.value);
                                                                                handleMultiInputChange(
                                                                                    product.Default.product_id,
                                                                                    family.name,
                                                                                    attribute.handle,
                                                                                    allValues,
                                                                                    attribute.pivotId
                                                                                );
                                                                            }}
                                                                        />
                                                                    ))}
                                                                </div>
                                                            </div>
                                                        );
                                                    } else {
                                                        cellContent = (
                                                            <div className="singlevalue">
                                                                <input
                                                                    className="w-full bg-[#F8F8F8]"
                                                                    type="date"
                                                                    min={minValue}
                                                                    max={maxValue}
                                                                    value={
                                                                        value && value[0]
                                                                            ? value[0].value != null
                                                                                ? value[0].value
                                                                                : ""
                                                                            : ""
                                                                    }
                                                                    onChange={(e) => {
                                                                        const updatedValue = [...value];
                                                                        updatedValue[0].value = e.target.value;
                                                                        setValue(updatedValue);
                                                                        handleInputChange(
                                                                            product.Default.product_id,
                                                                            family.name,
                                                                            attribute.handle,
                                                                            e.target.value,
                                                                            attribute.pivotId
                                                                        );
                                                                    }}
                                                                />
                                                            </div>
                                                        );
                                                    }
                                                } else if (rulesType == "date_and_time") {
                                                    if (value_type == "list") {
                                                        cellContent = (
                                                            <div className="multiplevalues">
                                                                <div className="values-container w-full relative">
                                                                    <div className="w-10/12 inline-block overflow-hidden">
                                                                        {value.map((datetimeValue, datetimeIndex) =>
                                                                            datetimeValue?.value && datetimeValue.value.trim() !== "" ? (
                                                                                datetimeValue.value + ", "
                                                                            ) : (
                                                                                <span className="text-xs text-gray-400">No Value , </span>
                                                                            )
                                                                        )}
                                                                    </div>
                                                                    <i className="fa fa-chevron-down absolute z-10 right-1"></i>
                                                                </div>

                                                                <div className="value-inner">
                                                                    {value.map((datetimeValue, datetimeIndex) => (
                                                                        <input
                                                                            className="w-full bg-[#F8F8F8]"
                                                                            key={`datetime-input-${datetimeIndex}`}
                                                                            type="datetime-local"
                                                                            min={minValue}
                                                                            max={maxValue}
                                                                            value={datetimeValue?.value != null ? datetimeValue.value : ""}
                                                                            id={attribute.pivotId}
                                                                            onChange={(e) => {
                                                                                const updatedValue = [...value];
                                                                                updatedValue[datetimeIndex].value = e.target.value;
                                                                                setValue(updatedValue);

                                                                                // Collect all values for a pivot ID and pass them to handleInputChange
                                                                                const allValues = value.map(
                                                                                    (datetimeValue) => datetimeValue.value
                                                                                );
                                                                                handleMultiInputChange(
                                                                                    product.Default.product_id,
                                                                                    family.name,
                                                                                    attribute.handle,
                                                                                    allValues,
                                                                                    attribute.pivotId
                                                                                );
                                                                            }}
                                                                        />
                                                                    ))}
                                                                </div>
                                                            </div>
                                                        );
                                                    } else {
                                                        cellContent = (
                                                            <div className="singlevalue">
                                                                <input
                                                                    className="w-full bg-[#F8F8F8]"
                                                                    type="datetime-local"
                                                                    value={
                                                                        value && value[0]
                                                                            ? value[0].value != null
                                                                                ? value[0].value
                                                                                : ""
                                                                            : ""
                                                                    }
                                                                    min={minValue}
                                                                    max={maxValue}
                                                                    onChange={(e) => {
                                                                        const updatedValue = [...value];
                                                                        updatedValue[0].value = e.target.value;
                                                                        setValue(updatedValue);
                                                                        handleInputChange(
                                                                            product.Default.product_id,
                                                                            family.name,
                                                                            attribute.handle,
                                                                            e.target.value,
                                                                            attribute.pivotId
                                                                        );
                                                                    }}
                                                                />
                                                            </div>
                                                        );
                                                    }
                                                }
                                            } else if (attribute.attribute_type_id === 7) {
                                                const rules = JSON.parse(attribute.rules);
                                                const { type: rulesType, min: minValue, max: maxValue } = rules;
                                                const value_type = rules.value_type;
                                                let selectOptions;
                                                const { weight, volume, dimension } = data.data_required.units;
                                                if (value_type == "list") {
                                                    cellContent = (
                                                        <div className="multiplevalues">
                                                            <div className="values-container w-full relative">
                                                                <div className="w-10/12 inline-block overflow-hidden">
                                                                    {value.map((measurmentValue, measurmentIndex) =>
                                                                        typeof measurmentValue.value === "number" ||
                                                                        (typeof measurmentValue.value === "string" &&
                                                                            measurmentValue.value.trim() !== "") ? (
                                                                            measurmentValue.value + ", "
                                                                        ) : (
                                                                            <span className="text-xs text-gray-400">No Value , </span>
                                                                        )
                                                                    )}
                                                                </div>

                                                                <i className="fa fa-chevron-down absolute z-10 right-1"></i>
                                                            </div>
                                                            <div className="value-inner">
                                                                {value.map((measurmentValue, measurmentIndex) => (
                                                                    <div className="inputgroup" key={`inputgroup-${measurmentIndex}`}>
                                                                        {rulesType === "weight" && (
                                                                            <select
                                                                                name="weight"
                                                                                className={`select-unit-weight${measurmentIndex} inline-block bg-[#F8F8F8] rounded-l border border-r-0 border-solid border-neutral-300 px-1 py-2 w-auto text-gray-400 font-bold text-xs`}
                                                                                onChange={(e) => {
                                                                                    const updatedValue = [...value];
                                                                                    updatedValue[measurmentIndex].unit = e.target.value;
                                                                                    setValue(updatedValue);

                                                                                    handleUnitChange(
                                                                                        measurmentIndex,
                                                                                        e,
                                                                                        product.Default.product_id,
                                                                                        family.name,
                                                                                        attribute.handle,
                                                                                        attribute.pivotId
                                                                                    );
                                                                                }}
                                                                            >
                                                                                {weight &&
                                                                                    Object.keys(weight).map((key) => (
                                                                                        <option
                                                                                            key={key}
                                                                                            value={key}
                                                                                            data-unit={weight[key]}
                                                                                            selected={
                                                                                                measurmentValue.unit === key
                                                                                                    ? "selected"
                                                                                                    : ""
                                                                                            }
                                                                                        >
                                                                                            {[key]}
                                                                                        </option>
                                                                                    ))}
                                                                            </select>
                                                                        )}
                                                                        {rulesType === "volume" && (
                                                                            <select
                                                                                name="volume"
                                                                                className={`select-unit-volume${measurmentIndex} inline-block bg-[#F8F8F8] rounded-l border border-r-0 border-solid border-neutral-300 px-1 py-2 w-auto text-gray-400 font-bold text-xs`}
                                                                                onChange={(e) => {
                                                                                    const updatedValue = [...value];
                                                                                    updatedValue[measurmentIndex].unit = e.target.value;
                                                                                    setValue(updatedValue);

                                                                                    handleUnitChange(
                                                                                        measurmentIndex,
                                                                                        e,
                                                                                        product.Default.product_id,
                                                                                        family.name,
                                                                                        attribute.handle,
                                                                                        attribute.pivotId
                                                                                    );
                                                                                }}
                                                                            >
                                                                                {volume &&
                                                                                    Object.keys(volume).map((key) =>
                                                                                        key === "m3" ? (
                                                                                            <option
                                                                                                key={key}
                                                                                                value={key}
                                                                                                data-unit={volume[key]}
                                                                                                selected={
                                                                                                    measurmentValue.unit === key
                                                                                                        ? "selected"
                                                                                                        : ""
                                                                                                }
                                                                                            >
                                                                                                m&sup3;
                                                                                            </option>
                                                                                        ) : (
                                                                                            <option
                                                                                                key={key}
                                                                                                value={key}
                                                                                                data-unit={volume[key]}
                                                                                                selected={
                                                                                                    measurmentValue.unit === key
                                                                                                        ? "selected"
                                                                                                        : ""
                                                                                                }
                                                                                            >
                                                                                                {[key]}
                                                                                            </option>
                                                                                        )
                                                                                    )}
                                                                            </select>
                                                                        )}
                                                                        {rulesType === "dimension" && (
                                                                            <select
                                                                                name="dimension"
                                                                                className={`select-unit-dimension${measurmentIndex} inline-block bg-[#F8F8F8] rounded-l border border-r-0 border-solid border-neutral-300 px-1 py-2 w-auto text-gray-400 font-bold text-xs`}
                                                                                onChange={(e) => {
                                                                                    const updatedValue = [...value];
                                                                                    updatedValue[measurmentIndex].unit = e.target.value;
                                                                                    setValue(updatedValue);

                                                                                    handleUnitChange(
                                                                                        measurmentIndex,
                                                                                        e,
                                                                                        product.Default.product_id,
                                                                                        family.name,
                                                                                        attribute.handle,
                                                                                        attribute.pivotId
                                                                                    );
                                                                                }}
                                                                            >
                                                                                {dimension &&
                                                                                    Object.keys(dimension).map((key) => (
                                                                                        <option
                                                                                            key={key}
                                                                                            value={key}
                                                                                            data-unit={dimension[key]}
                                                                                            selected={
                                                                                                measurmentValue.unit === key
                                                                                                    ? "selected"
                                                                                                    : ""
                                                                                            }
                                                                                        >
                                                                                            {[key]}
                                                                                        </option>
                                                                                    ))}
                                                                            </select>
                                                                        )}
                                                                        <input
                                                                            className="w-full bg-[#F8F8F8]"
                                                                            key={`input-${measurmentValue}`}
                                                                            type="number"
                                                                            value={
                                                                                measurmentValue?.value != null ? measurmentValue.value : ""
                                                                            }
                                                                            min={minValue}
                                                                            max={maxValue}
                                                                            id={`measurement-${attribute.pivotId}`}
                                                                            onChange={(e) => {
                                                                                let newValue = parseFloat(e.target.value); // Convert the input value to a number

                                                                                if (isNaN(newValue)) {
                                                                                    newValue = ""; // If the value is not a number, reset to an empty string
                                                                                } else {
                                                                                    if (newValue < minValue) {
                                                                                        newValue = minValue;
                                                                                    } else if (newValue > maxValue) {
                                                                                        newValue = maxValue;
                                                                                    }
                                                                                }
                                                                                const updatedValue = [...value];
                                                                                updatedValue[measurmentIndex].value = newValue;
                                                                                setValue(updatedValue);

                                                                                const allValues = value.map(
                                                                                    (measurementValue) => measurementValue.value
                                                                                );
                                                                                const selectedUnitName =
                                                                                    e.target.previousElementSibling.name;

                                                                                const allUnits = value.map((_, index) => {
                                                                                    const selectElement = document.querySelector(
                                                                                        `.select-unit-${selectedUnitName}${index}`
                                                                                    );
                                                                                    return selectElement ? selectElement.value : null;
                                                                                });
                                                                                console.log(allUnits);
                                                                                handleMultipleMeasurmentInputChange(
                                                                                    product.Default.product_id,
                                                                                    family.name,
                                                                                    attribute.handle,
                                                                                    allValues,
                                                                                    allUnits,
                                                                                    attribute.pivotId
                                                                                );
                                                                            }}
                                                                        />
                                                                    </div>
                                                                ))}
                                                            </div>
                                                        </div>
                                                    );
                                                } else {
                                                    if (rulesType === "weight") {
                                                        selectOptions = (
                                                            <select
                                                                name="weight"
                                                                className="inline-block bg-[#F8F8F8] rounded-l border border-r-0 border-solid border-neutral-300 px-1 py-2 w-auto text-gray-400 font-bold text-xs"
                                                                id="weight_select"
                                                                onChange={(e) =>
                                                                    handleMeasurmentUnitChange(
                                                                        product.Default.product_id,
                                                                        family.name,
                                                                        attribute.handle,
                                                                        e.target.value, // Pass the selected value
                                                                        attribute.pivotId,
                                                                        e.target.nextElementSibling.value
                                                                    )
                                                                }
                                                            >
                                                                {weight &&
                                                                    Object.keys(weight).map((key) => (
                                                                        <option
                                                                            key={key}
                                                                            value={key}
                                                                            data-unit={weight[key].unit}
                                                                            selected={value && value[0]?.unit === key ? "selected" : ""}
                                                                        >
                                                                            {[key]}
                                                                        </option>
                                                                    ))}
                                                            </select>
                                                        );
                                                    } else if (rulesType === "volume") {
                                                        selectOptions = (
                                                            <select
                                                                name="volume"
                                                                className="inline-block bg-[#F8F8F8] rounded-l border border-r-0 border-solid border-neutral-300 px-1 py-2 w-auto text-gray-400 font-bold text-xs"
                                                                id="volume_select"
                                                                onChange={(e) =>
                                                                    handleMeasurmentUnitChange(
                                                                        product.Default.product_id,
                                                                        family.name,
                                                                        attribute.handle,
                                                                        e.target.value, // Pass the selected value
                                                                        attribute.pivotId,
                                                                        e.target.nextElementSibling.value // Pass the unit associated with the selected option
                                                                    )
                                                                }
                                                            >
                                                                {volume &&
                                                                    Object.keys(volume).map((key) =>
                                                                        key === "m3" ? (
                                                                            <option
                                                                                key={key}
                                                                                value={key}
                                                                                data-unit={volume[key.unit]}
                                                                                selected={value && value[0]?.unit === key ? "selected" : ""}
                                                                            >
                                                                                m&sup3;
                                                                            </option>
                                                                        ) : (
                                                                            <option
                                                                                key={key}
                                                                                value={key}
                                                                                data-unit={volume[key].unit}
                                                                                selected={value && value[0]?.unit === key ? "selected" : ""}
                                                                            >
                                                                                {key.replace(/us_/g, "").replace(/_/g, " ")}
                                                                            </option>
                                                                        )
                                                                    )}
                                                                {/* Add other options for volume */}
                                                            </select>
                                                        );
                                                    } else if (rulesType === "dimension") {
                                                        selectOptions = (
                                                            <select
                                                                name="dimensions"
                                                                className="inline-block bg-[#F8F8F8] rounded-l border border-r-0 border-solid border-neutral-300 px-1 py-2 w-auto text-gray-400 font-bold text-xs"
                                                                id="dimension_select"
                                                                onChange={(e) => {
                                                                    const selectedValue = e.target.nextElementSibling.value;
                                                                    console.log(selectedValue, "selectedvalue");
                                                                    handleMeasurmentUnitChange(
                                                                        product.Default.product_id,
                                                                        family.name,
                                                                        attribute.handle,
                                                                        e.target.value, // Pass the selected value
                                                                        attribute.pivotId,
                                                                        e.target.nextElementSibling.value
                                                                    );
                                                                }}
                                                            >
                                                                {dimension &&
                                                                    Object.keys(dimension).map((key) => (
                                                                        <option
                                                                            key={key}
                                                                            value={key}
                                                                            data-unit={dimension[key].unit}
                                                                            selected={value && value[0]?.unit === key ? "selected" : ""}
                                                                        >
                                                                            {[key]}
                                                                        </option>
                                                                    ))}
                                                            </select>
                                                        );
                                                    }

                                                    cellContent = (
                                                        <div className="singlevalue">
                                                            <div className="inputgroup">
                                                                {selectOptions}
                                                                <input
                                                                    type="number "
                                                                    className="w-full bg-[#F8F8F8]"
                                                                    value={
                                                                        value && value[0]
                                                                            ? value[0].value != null
                                                                                ? value[0].value
                                                                                : ""
                                                                            : ""
                                                                    }
                                                                    min={minValue}
                                                                    max={maxValue}
                                                                    onChange={(e) => {
                                                                        const selectedUnit = e.target.previousElementSibling.value;
                                                                        let newValue = parseFloat(e.target.value); // Convert the input value to a number

                                                                        if (isNaN(newValue)) {
                                                                            newValue = ""; // If the value is not a number, reset to an empty string
                                                                        } else {
                                                                            if (newValue < minValue) {
                                                                                newValue = minValue;
                                                                            } else if (newValue > maxValue) {
                                                                                newValue = maxValue;
                                                                            }
                                                                        }
                                                                        const updatedValue = [...value];
                                                                        updatedValue[0].value = newValue;
                                                                        setValue(updatedValue);
                                                                        handleMeasurmentInputChange(
                                                                            product.Default.product_id,
                                                                            family.name,
                                                                            attribute.handle,
                                                                            e.target.value,
                                                                            attribute.pivotId,
                                                                            selectedUnit
                                                                        );
                                                                    }}
                                                                />
                                                            </div>
                                                        </div>
                                                    );
                                                }
                                            } else if (attribute.attribute_type_id === 8) {
                                                const rules = JSON.parse(attribute.rules);
                                                const { min: minValue, max: maxValue } = rules;
                                                const value_type = rules.value_type;
                                                if (value_type == "list") {
                                                    cellContent = (
                                                        <div className="multiplevalues">
                                                            <div className="values-container w-full relative">
                                                                <div className="w-10/12 inline-block overflow-hidden">
                                                                    {value.map((rangeValue, rangeIndex) =>
                                                                        typeof rangeValue.value === "number" ||
                                                                        (typeof rangeValue.value === "string" &&
                                                                            rangeValue.value.trim() !== "") ? (
                                                                            rangeValue.value + ", "
                                                                        ) : (
                                                                            <span className="text-xs text-gray-400">No Value , </span>
                                                                        )
                                                                    )}
                                                                </div>

                                                                <i className="fa fa-chevron-down absolute z-10 right-1"></i>
                                                            </div>
                                                            <div className="value-inner">
                                                                {value.map((rangeValue, rangeIndex) => (
                                                                    <div>
                                                                        <input
                                                                            key={`range-input-${rangeIndex}`}
                                                                            className="range-slider__range form-range w-full"
                                                                            type="range"
                                                                            min={minValue}
                                                                            max={maxValue}
                                                                            value={rangeValue?.value != null ? rangeValue.value : ""}
                                                                            onChange={(e) => {
                                                                                let newValue = parseFloat(e.target.value); // Convert the input value to a number

                                                                                if (isNaN(newValue)) {
                                                                                    newValue = ""; // If the value is not a number, reset to an empty string
                                                                                } else {
                                                                                    if (newValue < minValue) {
                                                                                        newValue = minValue;
                                                                                    } else if (newValue > maxValue) {
                                                                                        newValue = maxValue;
                                                                                    }
                                                                                }
                                                                                const updatedValue = [...value];
                                                                                updatedValue[rangeIndex].value = newValue;
                                                                                setValue(updatedValue);

                                                                                // Collect all values for a pivot ID and pass them to handleInputChange
                                                                                const allValues = value.map(
                                                                                    (rangeValue) => rangeValue.value
                                                                                );
                                                                                handleMultiInputChange(
                                                                                    product.Default.product_id,
                                                                                    family.name,
                                                                                    attribute.handle,
                                                                                    allValues,
                                                                                    attribute.pivotId
                                                                                );
                                                                            }}
                                                                        />
                                                                        <span className="range-slider__value">
                                                                            {rangeValue?.value != null ? rangeValue.value : ""}
                                                                        </span>
                                                                    </div>
                                                                ))}
                                                            </div>
                                                        </div>
                                                    );
                                                } else {
                                                    cellContent = (
                                                        <div className="singlevalue">
                                                            <input
                                                                className="range-slider__range form-range w-full"
                                                                type="range"
                                                                min={minValue}
                                                                max={maxValue}
                                                                value={
                                                                    value && value[0] ? (value[0].value != null ? value[0].value : "") : ""
                                                                }
                                                                onChange={(e) => {
                                                                    let newValue = parseFloat(e.target.value); // Convert the input value to a number

                                                                    if (isNaN(newValue)) {
                                                                        newValue = ""; // If the value is not a number, reset to an empty string
                                                                    } else {
                                                                        if (newValue < minValue) {
                                                                            newValue = minValue;
                                                                        } else if (newValue > maxValue) {
                                                                            newValue = maxValue;
                                                                        }
                                                                    }
                                                                    const updatedValue = [...value];
                                                                    updatedValue[0].value = newValue;
                                                                    setValue(updatedValue);
                                                                    handleInputChange(
                                                                        product.Default.product_id,
                                                                        family.name,
                                                                        attribute.handle,
                                                                        e.target.value,
                                                                        attribute.pivotId
                                                                    );
                                                                }}
                                                            />
                                                            <span className="range-slider__value">
                                                                {value && value[0] ? (value[0].value != null ? value[0].value : "") : ""}
                                                            </span>
                                                        </div>
                                                    );
                                                }
                                            } else if (attribute.attribute_type_id === 9) {
                                                cellContent = (
                                                    <>
                                                        <textarea
                                                            className="w-full bg-[#F8F8F8] h-[36px]"
                                                            readOnly="readonly"
                                                            onClick={() => {
                                                                const attributeHandle = attribute.handle;
                                                                const familyname = family.name;
                                                                // check if it's closed
                                                                handleTextEditorChange(index, familyname, attributeHandle);
                                                            }}
                                                            value={value[0]?.value || ""}
                                                        ></textarea>
                                                        {activeEditor.index === index &&
                                                            activeEditor.familyName === family.name &&
                                                            activeEditor.attributeHandle === attribute.handle && (
                                                                <>
                                                                    <div
                                                                        key={index}
                                                                        className="absolute p-4 z-[999] w-full h-full top-0 left-0"
                                                                        onClick={() => handleDescriptionBackdropClick(index)}
                                                                    ></div>
                                                                    <div className="relative">
                                                                        <div className="absolute z-[1001] w-[100%] h-[200px] right-[50%] top-0">
                                                                            <Editor
                                                                                apiKey="fttq4ztpm1uz2b3vzgmsjk5b49puunxfx4g6hi59r83ouygm"
                                                                                value={value[0]?.value || ""}
                                                                                init={{
                                                                                    menubar: false,
                                                                                    plugins: [
                                                                                        "a11ychecker",
                                                                                        "advcode",
                                                                                        "advlist",
                                                                                        "anchor",
                                                                                        "autolink",
                                                                                        "codesample",
                                                                                        "fullscreen",
                                                                                        "help",
                                                                                        "image",
                                                                                        "editimage",
                                                                                        "tinydrive",
                                                                                        "lists",
                                                                                        "link",
                                                                                        "media",
                                                                                        "powerpaste",
                                                                                        "preview",
                                                                                        "searchreplace",
                                                                                        "table",
                                                                                        "template",
                                                                                        "tinymcespellchecker",
                                                                                        "visualblocks",
                                                                                        "wordcount",
                                                                                    ],
                                                                                    toolbar:
                                                                                        " undo redo | bold italic | forecolor backcolor |  codesample | alignleft aligncenter alignright alignjustify | bullist numlist ",
                                                                                    readonly: false,
                                                                                }}
                                                                                onEditorChange={(newValue, editor) => {
                                                                                    const updatedValue = [...value];
                                                                                    updatedValue[0].value = newValue; // Update the entire value array with the new value

                                                                                    setValue(newValue);
                                                                                    handleEditorChange(
                                                                                        product.Default.product_id,
                                                                                        family.name,
                                                                                        attribute.handle,
                                                                                        newValue,
                                                                                        attribute.pivotId
                                                                                    );
                                                                                }}
                                                                            />
                                                                        </div>
                                                                    </div>
                                                                </>
                                                            )}
                                                    </>
                                                );
                                            } else if (attribute.attribute_type_id === 10) {
                                                const isChecked = value && value[0] && value[0].value === "1" ? true : false;

                                                cellContent = (
                                                    <div className="formStyle">
                                                        <label className="switch form-check form-switch">
                                                            <input
                                                                type="checkbox"
                                                                className="form-check-input"
                                                                defaultChecked={isChecked}
                                                                value={
                                                                    value && value[0] ? (value[0].value != null ? value[0].value : "") : ""
                                                                }
                                                                onChange={(e) => {
                                                                    const updatedValue = e.target.checked ? "1" : "0"; // Update based on checked status
                                                                    setValue([updatedValue]);
                                                                    handleInputChange(
                                                                        product.Default.product_id,
                                                                        family.name,
                                                                        attribute.handle,
                                                                        updatedValue,
                                                                        attribute.pivotId
                                                                    );
                                                                }}
                                                            />
                                                            <span className="slider round"></span>
                                                        </label>
                                                    </div>
                                                );
                                            } else if (attribute.attribute_type_id === 11) {
                                                const rules = JSON.parse(attribute.rules);
                                                const value_type = rules.value_type;
                                                if (value_type == "list") {
                                                    cellContent = (
                                                        <div className="multiplevalues">
                                                            <div className="values-container w-full relative">
                                                                <div className="w-10/12 inline-block overflow-hidden">
                                                                    {value.map((urlValue, urlIndex) =>
                                                                        urlValue?.value && urlValue.value.trim() !== "" ? (
                                                                            urlValue.value + ", "
                                                                        ) : (
                                                                            <span className="text-xs text-gray-400">No Value , </span>
                                                                        )
                                                                    )}
                                                                </div>
                                                                <i className="fa fa-chevron-down absolute z-10 right-1"></i>{" "}
                                                            </div>
                                                            <div className="value-inner">
                                                                {value.map((urlValue, urlIndex) => (
                                                                    <input
                                                                        className="w-full bg-[#F8F8F8]"
                                                                        key={`url-input-${urlIndex}`}
                                                                        type="text"
                                                                        value={urlValue?.value != null ? urlValue.value : ""}
                                                                        onChange={(e) => {
                                                                            const updatedValue = [...value];
                                                                            updatedValue[urlIndex].value = e.target.value;
                                                                            setValue(updatedValue);

                                                                            // Collect all values for a pivot ID and pass them to handleInputChange
                                                                            const allValues = value.map((urlValue) => urlValue.value);
                                                                            handleMultiInputChange(
                                                                                product.Default.product_id,
                                                                                family.name,
                                                                                attribute.handle,
                                                                                allValues,
                                                                                attribute.pivotId
                                                                            );
                                                                        }}
                                                                    />
                                                                ))}
                                                            </div>
                                                        </div>
                                                    );
                                                } else {
                                                    cellContent = (
                                                        <div className="singlevalue">
                                                            <input
                                                                className="w-full bg-[#F8F8F8]"
                                                                type="text"
                                                                value={
                                                                    value && value[0] ? (value[0].value != null ? value[0].value : "") : ""
                                                                }
                                                                onChange={(e) => {
                                                                    const updatedValue = [...value];
                                                                    updatedValue[0].value = e.target.value;
                                                                    setValue(updatedValue);
                                                                    handleInputChange(
                                                                        product.Default.product_id,
                                                                        family.name,
                                                                        attribute.handle,
                                                                        e.target.value,
                                                                        attribute.pivotId
                                                                    );
                                                                }}
                                                            />
                                                        </div>
                                                    );
                                                }
                                            } else if (attribute.attribute_type_id === 12) {
                                                const rules = JSON.parse(attribute.rules);
                                                const value_type = rules.value_type;
                                                if (value_type == "list") {
                                                    cellContent = (
                                                        <div className="multiplevalues">
                                                            <div className="values-container w-full relative">
                                                                <div className="w-10/12 inline-block">
                                                                    {value.map((colorValue, colorIndex) => (
                                                                        <div className="mr-2 inline-flex w-[40px]">
                                                                            <input
                                                                                className="w-16 mr-2"
                                                                                key={`color-input-${colorIndex}`}
                                                                                type="color"
                                                                                value={colorValue?.value != null ? colorValue.value : ""}
                                                                                disabled
                                                                            />{" "}
                                                                        </div>
                                                                    ))}
                                                                </div>
                                                                <i className="fa fa-chevron-down absolute z-10 right-1"></i>
                                                            </div>

                                                            <div className="value-inner">
                                                                {value.map((colorValue, colorIndex) => (
                                                                    <div className="flex justify-between items-center">
                                                                        <input
                                                                            className="w-16 mr-2"
                                                                            key={`color-input-${colorIndex}`}
                                                                            type="color"
                                                                            value={colorValue?.value != null ? colorValue.value : ""}
                                                                            onChange={(e) => {
                                                                                const updatedValue = [...value];
                                                                                updatedValue[colorIndex].value = e.target.value;
                                                                                setValue(updatedValue);

                                                                                // Collect all values for a pivot ID and pass them to handleInputChange
                                                                                const allValues = updatedValue.map(
                                                                                    (colorValue) => colorValue.value
                                                                                );
                                                                                handleMultiInputChange(
                                                                                    product.Default.product_id,
                                                                                    family.name,
                                                                                    attribute.handle,
                                                                                    allValues,
                                                                                    attribute.pivotId
                                                                                );
                                                                            }}
                                                                        />{" "}
                                                                        {colorValue.value || ""}
                                                                    </div>
                                                                ))}
                                                            </div>
                                                        </div>
                                                    );
                                                } else {
                                                    cellContent = (
                                                        <div className="singlevalue">
                                                            <input
                                                                className="w-full bg-[#F8F8F8]"
                                                                type="color"
                                                                value={value && value[0] ? value[0].value || "" : ""}
                                                                onChange={(e) => {
                                                                    const updatedValue = [...value];
                                                                    updatedValue[0].value = e.target.value;
                                                                    setValue(updatedValue);
                                                                    handleInputChange(
                                                                        product.Default.product_id,
                                                                        family.name,
                                                                        attribute.handle,
                                                                        e.target.value,
                                                                        attribute.pivotId
                                                                    );
                                                                }}
                                                            />
                                                        </div>
                                                    );
                                                }
                                            } else {
                                                // Display data inside an input field
                                                cellContent = (
                                                    <input
                                                        className="w-full bg-[#F8F8F8]"
                                                        type="text"
                                                        value={value && value[0] ? value[0].value || "" : ""}
                                                        onChange={(e) => {
                                                            const updatedValue = [...value];
                                                            updatedValue.value = e.target.value;
                                                            setValue(updatedValue);
                                                            handleInputChange(
                                                                product.Default.product_id,
                                                                family.name,
                                                                attribute.handle,
                                                                e.target.value,
                                                                attribute.pivotId
                                                            );
                                                        }}
                                                    />
                                                );
                                            }
                                        }
                                    } else {
                                        cellContent = (
                                            <div className="w-full text-center block cursor-not-allowed ">
                                                <h6 className="bg-gray-500 w-[10px] h-[2px]  mx-auto" title="Attribute Not Assigned"></h6>
                                            </div>
                                        );
                                    }
                                    const cellKey = `${family.name}-${attribute.handle}-${index}`;
                                    if (family.name !== "Default" && attribute.handle !== "product_name") {
                                        rowCells.push(
                                            <td key={cellKey} id={`${attribute.pivotId}`} style={{ width: "250px", minWidth: "250px" }}>
                                                {cellContent}
                                            </td>
                                        );
                                    }
                                } else {
                                    rowCells.push(
                                        <td
                                            key={`not-assigned-${attribute.handle}`}
                                            id={`not-assigned-${attribute.handle}`}
                                            style={{ width: "250px", minWidth: "250px" }}
                                        >
                                            <div className="w-full text-center block cursor-not-allowed ">
                                                <h6 className="bg-gray-500 w-[10px] h-[2px]  mx-auto" title="Attribute Not Assigned"></h6>
                                            </div>
                                        </td>
                                    );
                                }
                            }
                        }
                    });
                }
            });
            // Check if variants exist under 'Default'
            if (product.Default && product.Default.variants && product.Default.variants.length > 0) {
                let variantsDisplayed = false; // Flag to ensure variant count displayed only once
                const weightOptions = data.data_required.units.weight;
                variantData.push(
                    <>
                        {isOpenArray[index] && (
                            <tr className="variant-row">
                                <td colSpan={headerCells.length} className="">
                                    <div className="variant-item py-4 mb-2 bg-[#fff]">
                                        <div className="variant w-full block mx-4">
                                            <div className="variant-body ">
                                                <div className="variant-table">
                                                    <h3 className="text-[16px] text-[#252525] font-bold">
                                                        Variants ({variantsDisplayed ? "" : product.Default.variants.length})
                                                    </h3>
                                                    <table key={`variant-${index}`} className="bg-[#fff] min-w-[1200px] max-w-[1500px]">
                                                        <thead className="">
                                                            <tr>
                                                                <th
                                                                    key={`variant-locations-${index}`}
                                                                    className="px-2 pb-2"
                                                                    title="Inventory"
                                                                    style={{
                                                                        width: "60px",
                                                                        maxWidth: "60px",
                                                                        minWidth: "60px",
                                                                        whiteSpace: "nowrap",
                                                                        overflow: "hidden",
                                                                        textOverflow: "ellipsis",
                                                                    }}
                                                                >
                                                                    Inventory
                                                                </th>
                                                                <th
                                                                    key={`variant-sku-${index}`}
                                                                    className="px-2 pb-2"
                                                                    title="SKU"
                                                                    style={{
                                                                        width: "170px",
                                                                        maxWidth: "170px",
                                                                        minWidth: "170px",
                                                                        whiteSpace: "nowrap",
                                                                        overflow: "hidden",
                                                                        textOverflow: "ellipsis",
                                                                    }}
                                                                >
                                                                    SKU
                                                                </th>

                                                                <th
                                                                    key={`variant-name-${index}`}
                                                                    className="px-2 pb-2"
                                                                    title="Name"
                                                                    style={{
                                                                        width: "170px",
                                                                        maxWidth: "170px",
                                                                        minWidth: "170px",
                                                                        whiteSpace: "nowrap",
                                                                        overflow: "hidden",
                                                                        textOverflow: "ellipsis",
                                                                    }}
                                                                >
                                                                    Name
                                                                </th>
                                                                <th
                                                                    key={`variant-barcode-${index}`}
                                                                    className="px-2 pb-2"
                                                                    title="UPC/EAN/ISBN"
                                                                    style={{
                                                                        width: "170px",
                                                                        maxWidth: "170px",
                                                                        minWidth: "170px",
                                                                        whiteSpace: "nowrap",
                                                                        overflow: "hidden",
                                                                        textOverflow: "ellipsis",
                                                                    }}
                                                                >
                                                                    UPC/EAN/ISBN
                                                                </th>
                                                                <th
                                                                    key={`variant-price-${index}`}
                                                                    className="px-2 pb-2"
                                                                    title="Price"
                                                                    style={{
                                                                        width: "170px",
                                                                        maxWidth: "170px",
                                                                        minWidth: "170px",
                                                                        whiteSpace: "nowrap",
                                                                        overflow: "hidden",
                                                                        textOverflow: "ellipsis",
                                                                    }}
                                                                >
                                                                    Price
                                                                </th>
                                                                <th
                                                                    key={`variant-compare-${index}`}
                                                                    className="px-2 pb-2"
                                                                    title="Compare at Price"
                                                                    style={{
                                                                        width: "170px",
                                                                        maxWidth: "170px",
                                                                        minWidth: "170px",
                                                                        whiteSpace: "nowrap",
                                                                        overflow: "hidden",
                                                                        textOverflow: "ellipsis",
                                                                    }}
                                                                >
                                                                    Compare at Price
                                                                </th>
                                                                <th
                                                                    key={`variant-cost-${index}`}
                                                                    className="px-2 pb-2"
                                                                    title="Cost Price"
                                                                    style={{
                                                                        width: "170px",
                                                                        maxWidth: "170px",
                                                                        minWidth: "170px",
                                                                        whiteSpace: "nowrap",
                                                                        overflow: "hidden",
                                                                        textOverflow: "ellipsis",
                                                                    }}
                                                                >
                                                                    Cost Price
                                                                </th>

                                                                <th
                                                                    key={`variant-Weight-${index}`}
                                                                    className="px-2 pb-2"
                                                                    title="Weight"
                                                                    style={{
                                                                        width: "170px",
                                                                        maxWidth: "170px",
                                                                        minWidth: "170px",
                                                                        whiteSpace: "nowrap",
                                                                        overflow: "hidden",
                                                                        textOverflow: "ellipsis",
                                                                    }}
                                                                >
                                                                    Weight
                                                                </th>
                                                                <th key={`variant-continueselling-${index}`} className="px-2 pb-2">
                                                                    Continue Selling
                                                                </th>
                                                                <th key={`variant-trackquantity-${index}`} className="px-2 pb-2">
                                                                    Track Quantity
                                                                </th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            {product.Default.variants.map((variant, variantIndex) => (
                                                                <>
                                                                    <tr>
                                                                        <td
                                                                            style={{
                                                                                width: "60px",
                                                                                maxWidth: "60px",
                                                                                minWidth: "60px",
                                                                                whiteSpace: "nowrap",
                                                                                overflow: "hidden",
                                                                                textOverflow: "ellipsis",
                                                                            }}
                                                                        >
                                                                            <button
                                                                                className="accordion-head w-full text-left"
                                                                                onClick={() => handleAccordionClick(variantIndex)}
                                                                            >
                                                                                <h2 className="variant-header  font-semibold text-sm text-[#222]">
                                                                                    {Object.keys(variant.inventories).length}
                                                                                    <span className="ml-6">
                                                                                        <i
                                                                                            className={`${
                                                                                                openIndex === variantIndex
                                                                                                    ? "fa fa-caret-up"
                                                                                                    : "fa fa-caret-down"
                                                                                            }`}
                                                                                        ></i>
                                                                                    </span>
                                                                                </h2>
                                                                            </button>
                                                                        </td>
                                                                        <td
                                                                            className="px-2"
                                                                            style={{
                                                                                width: "170px",
                                                                                maxWidth: "170px",
                                                                                minWidth: "170px",
                                                                                whiteSpace: "nowrap",
                                                                                overflow: "hidden",
                                                                                textOverflow: "ellipsis",
                                                                            }}
                                                                        >
                                                                            <input
                                                                                type="text"
                                                                                id={`variant-sku-${variantIndex}`}
                                                                                className="form-control bg-[#F8F8F8]"
                                                                                value={variant.sku}
                                                                                onChange={(e) =>
                                                                                    handleVariantInputChange(
                                                                                        index,
                                                                                        product.Default.product_id,
                                                                                        variantIndex,
                                                                                        "sku",
                                                                                        e.target.value,
                                                                                        variant.id
                                                                                    )
                                                                                }
                                                                            />
                                                                        </td>
                                                                        <td
                                                                            className="px-2"
                                                                            style={{
                                                                                width: "170px",
                                                                                maxWidth: "170px",
                                                                                minWidth: "170px",
                                                                                whiteSpace: "nowrap",
                                                                                overflow: "hidden",
                                                                                textOverflow: "ellipsis",
                                                                            }}
                                                                        >
                                                                            {product.Default.variants.length === 1 ? (
                                                                                <div className="w-full text-center block cursor-not-allowed ">
                                                                                    <h6
                                                                                        className="bg-gray-500 w-[10px] h-[2px]  mx-auto"
                                                                                        title={product.Default.handle}
                                                                                    ></h6>
                                                                                </div>
                                                                            ) : (
                                                                                <input
                                                                                    type="text"
                                                                                    id={`variant-name-${variantIndex}`}
                                                                                    className="form-control bg-[#F8F8F8]"
                                                                                    value={variant.name}
                                                                                    onChange={(e) =>
                                                                                        handleVariantInputChange(
                                                                                            index,
                                                                                            product.Default.product_id,
                                                                                            variantIndex,
                                                                                            "name",
                                                                                            e.target.value,
                                                                                            variant.id
                                                                                        )
                                                                                    }
                                                                                />
                                                                            )}
                                                                        </td>
                                                                        <td
                                                                            className="px-2"
                                                                            style={{
                                                                                width: "170px",
                                                                                maxWidth: "170px",
                                                                                minWidth: "170px",
                                                                                whiteSpace: "nowrap",
                                                                                overflow: "hidden",
                                                                                textOverflow: "ellipsis",
                                                                            }}
                                                                        >
                                                                            <input
                                                                                type="text"
                                                                                id={`variant-barcode-${variantIndex}`}
                                                                                className="form-control bg-[#F8F8F8]"
                                                                                value={variant.barcode}
                                                                                onChange={(e) =>
                                                                                    handleVariantInputChange(
                                                                                        index,
                                                                                        product.Default.product_id,
                                                                                        variantIndex,
                                                                                        "barcode",
                                                                                        e.target.value,
                                                                                        variant.id
                                                                                    )
                                                                                }
                                                                            />
                                                                        </td>
                                                                        <td
                                                                            className="px-2"
                                                                            style={{
                                                                                width: "170px",
                                                                                maxWidth: "170px",
                                                                                minWidth: "170px",
                                                                                whiteSpace: "nowrap",
                                                                                overflow: "hidden",
                                                                                textOverflow: "ellipsis",
                                                                            }}
                                                                        >
                                                                            <input
                                                                                type="number"
                                                                                id={`variant-price-${variantIndex}`}
                                                                                className="form-control bg-[#F8F8F8]"
                                                                                value={variant.price}
                                                                                onChange={(e) =>
                                                                                    handleVariantInputChange(
                                                                                        index,
                                                                                        product.Default.product_id,
                                                                                        variantIndex,
                                                                                        "price",
                                                                                        e.target.value,
                                                                                        variant.id
                                                                                    )
                                                                                }
                                                                            />
                                                                        </td>
                                                                        <td
                                                                            className="px-2"
                                                                            style={{
                                                                                width: "170px",
                                                                                maxWidth: "170px",
                                                                                minWidth: "170px",
                                                                                whiteSpace: "nowrap",
                                                                                overflow: "hidden",
                                                                                textOverflow: "ellipsis",
                                                                            }}
                                                                        >
                                                                            <input
                                                                                type="number"
                                                                                id={`variant-compare-${variantIndex}`}
                                                                                className="form-control bg-[#F8F8F8]"
                                                                                value={variant.compare_at_price}
                                                                                onChange={(e) =>
                                                                                    handleVariantInputChange(
                                                                                        index,
                                                                                        product.Default.product_id,
                                                                                        variantIndex,
                                                                                        "compare_at_price",
                                                                                        e.target.value,
                                                                                        variant.id
                                                                                    )
                                                                                }
                                                                            />
                                                                        </td>
                                                                        <td
                                                                            className="px-2"
                                                                            style={{
                                                                                width: "170px",
                                                                                maxWidth: "170px",
                                                                                minWidth: "170px",
                                                                                whiteSpace: "nowrap",
                                                                                overflow: "hidden",
                                                                                textOverflow: "ellipsis",
                                                                            }}
                                                                        >
                                                                            <input
                                                                                type="number"
                                                                                id={`variant-cost-${variantIndex}`}
                                                                                className="form-control bg-[#F8F8F8]"
                                                                                value={variant.cost_price}
                                                                                onChange={(e) =>
                                                                                    handleVariantInputChange(
                                                                                        index,
                                                                                        product.Default.product_id,
                                                                                        variantIndex,
                                                                                        "cost_price",
                                                                                        e.target.value,
                                                                                        variant.id
                                                                                    )
                                                                                }
                                                                            />
                                                                        </td>

                                                                        <td
                                                                            className="px-2 flex mt-2"
                                                                            style={{
                                                                                width: "170px",
                                                                                maxWidth: "170px",
                                                                                minWidth: "170px",
                                                                                whiteSpace: "nowrap",
                                                                                overflow: "hidden",
                                                                                textOverflow: "ellipsis",
                                                                            }}
                                                                        >
                                                                            <select
                                                                                name="weight_unit"
                                                                                className={`select-unit-weight${variantIndex} inline-block bg-[#F8F8F8] rounded-l border border-r-0 border-solid border-neutral-300 px-1 py-2 w-auto text-gray-400 font-bold text-xs`}
                                                                                value={
                                                                                    variant.weight_unit
                                                                                        ? variant.weight_unit
                                                                                        : data.data_required.default_unit
                                                                                }
                                                                                onChange={(e) =>
                                                                                    handleVariantInputChange(
                                                                                        index,
                                                                                        product.Default.product_id,
                                                                                        variantIndex,
                                                                                        "weight_unit",
                                                                                        e.target.value,
                                                                                        variant.id
                                                                                    )
                                                                                }
                                                                            >
                                                                                {weightOptions &&
                                                                                    Object.keys(weightOptions).map((key) => (
                                                                                        <option key={key} value={key}>
                                                                                            {[key]}
                                                                                        </option>
                                                                                    ))}
                                                                            </select>

                                                                            <input
                                                                                type="number"
                                                                                id={`variant-weight-${variantIndex}`}
                                                                                className="form-control w-10/12 rounded-l-none weight-input  bg-[#F8F8F8]"
                                                                                value={variant.weight}
                                                                                onChange={(e) =>
                                                                                    handleVariantInputChange(
                                                                                        index,
                                                                                        product.Default.product_id,
                                                                                        variantIndex,
                                                                                        "weight",
                                                                                        e.target.value,
                                                                                        variant.id
                                                                                    )
                                                                                }
                                                                            />
                                                                        </td>
                                                                        {variant.settings ? (
                                                                            <>
                                                                                <td
                                                                                    className="px-2 text-center"
                                                                                    style={{
                                                                                        width: "170px",
                                                                                        maxWidth: "170px",
                                                                                        minWidth: "170px",
                                                                                        whiteSpace: "nowrap",
                                                                                        overflow: "hidden",
                                                                                        textOverflow: "ellipsis",
                                                                                    }}
                                                                                >
                                                                                    <input
                                                                                        type="checkbox"
                                                                                        name="continue_selling"
                                                                                        checked={variant.settings.continue_selling === 1}
                                                                                        onChange={(e) =>
                                                                                            handleCheckboxChange(
                                                                                                index,
                                                                                                product.Default.product_id,
                                                                                                variantIndex,
                                                                                                "continue_selling",
                                                                                                e.target.checked ? 1 : 0,
                                                                                                variant.id,
                                                                                                variant.settings.id
                                                                                            )
                                                                                        }
                                                                                    />
                                                                                </td>
                                                                            </>
                                                                        ) : (
                                                                            <>
                                                                                <td
                                                                                    style={{
                                                                                        width: "170px",
                                                                                        maxWidth: "170px",
                                                                                        minWidth: "170px",
                                                                                        whiteSpace: "nowrap",
                                                                                        overflow: "hidden",
                                                                                        textOverflow: "ellipsis",
                                                                                    }}
                                                                                >
                                                                                    <div className="w-full text-center block cursor-not-allowed ">
                                                                                        <h6
                                                                                            className="bg-gray-500 w-[10px] h-[2px]  mx-auto"
                                                                                            title="Inventory Not Assigned"
                                                                                        ></h6>
                                                                                    </div>
                                                                                </td>
                                                                            </>
                                                                        )}
                                                                        {variant.settings ? (
                                                                            <>
                                                                                <td
                                                                                    className="px-2 text-center"
                                                                                    style={{
                                                                                        width: "170px",
                                                                                        maxWidth: "170px",
                                                                                        minWidth: "170px",
                                                                                        whiteSpace: "nowrap",
                                                                                        overflow: "hidden",
                                                                                        textOverflow: "ellipsis",
                                                                                    }}
                                                                                >
                                                                                    <input
                                                                                        type="checkbox"
                                                                                        checked={variant.settings.track_quantity === 1}
                                                                                        name="track_quantity"
                                                                                        onChange={(e) =>
                                                                                            handleCheckboxChange(
                                                                                                index,
                                                                                                product.Default.product_id,
                                                                                                variantIndex,
                                                                                                "track_quantity",
                                                                                                e.target.checked ? 1 : 0,
                                                                                                variant.id,
                                                                                                variant.settings.id
                                                                                            )
                                                                                        }
                                                                                    />
                                                                                </td>
                                                                            </>
                                                                        ) : (
                                                                            <>
                                                                                <td
                                                                                    style={{
                                                                                        width: "170px",
                                                                                        maxWidth: "170px",
                                                                                        minWidth: "170px",
                                                                                        whiteSpace: "nowrap",
                                                                                        overflow: "hidden",
                                                                                        textOverflow: "ellipsis",
                                                                                    }}
                                                                                >
                                                                                    <div className="w-full text-center block cursor-not-allowed ">
                                                                                        <h6
                                                                                            className="bg-gray-500 w-[10px] h-[2px]  mx-auto"
                                                                                            title="Inventory Not Assigned"
                                                                                        ></h6>
                                                                                    </div>
                                                                                </td>
                                                                            </>
                                                                        )}
                                                                    </tr>

                                                                    {openIndex === variantIndex && (
                                                                        <tr className="variant-inventory-row">
                                                                            <td colSpan="8" className="p-0">
                                                                                <div key={variantIndex} className="w-full block ">
                                                                                    <div className="inventory-body">
                                                                                        <div className="inventory-body mx-4">
                                                                                            <div className="inventory-table">
                                                                                                <table
                                                                                                    key={`inventory-${index}`}
                                                                                                    className="bg-[#fff]"
                                                                                                    style={{
                                                                                                        maxWidth: "800px",
                                                                                                        width: "800px",
                                                                                                        minWidth: "800px",
                                                                                                    }}
                                                                                                >
                                                                                                    <thead>
                                                                                                        <tr>
                                                                                                            <th
                                                                                                                style={{
                                                                                                                    maxWidth: "100px",
                                                                                                                    width: "100px",
                                                                                                                    minWidth: "100px",
                                                                                                                }}
                                                                                                            >
                                                                                                                Locations
                                                                                                            </th>
                                                                                                            <th
                                                                                                                style={{
                                                                                                                    maxWidth: "100px",
                                                                                                                    width: "100px",
                                                                                                                    minWidth: "100px",
                                                                                                                }}
                                                                                                            >
                                                                                                                Available Quantity
                                                                                                            </th>
                                                                                                        </tr>
                                                                                                    </thead>
                                                                                                    <tbody>
                                                                                                        {variant.inventories &&
                                                                                                            Object.values(
                                                                                                                variant.inventories
                                                                                                            ).map(
                                                                                                                (
                                                                                                                    variantInventory,
                                                                                                                    variantInventoryIndex
                                                                                                                ) => (
                                                                                                                    <tr
                                                                                                                        key={`inventory-${index}-${variantInventoryIndex}`}
                                                                                                                    >
                                                                                                                        <td
                                                                                                                            style={{
                                                                                                                                maxWidth:
                                                                                                                                    "100px",
                                                                                                                                width: "100px",
                                                                                                                                minWidth:
                                                                                                                                    "100px",
                                                                                                                            }}
                                                                                                                        >
                                                                                                                            {
                                                                                                                                variantInventory.location_name
                                                                                                                            }
                                                                                                                        </td>
                                                                                                                        <td
                                                                                                                            key={`availablequantity-${index}-${variantInventoryIndex}`}
                                                                                                                            style={{
                                                                                                                                maxWidth:
                                                                                                                                    "100px",
                                                                                                                                width: "100px",
                                                                                                                                minWidth:
                                                                                                                                    "100px",
                                                                                                                            }}
                                                                                                                        >
                                                                                                                            <input
                                                                                                                                type="number"
                                                                                                                                value={
                                                                                                                                    variantInventory.available_quantity ??
                                                                                                                                    0
                                                                                                                                }
                                                                                                                                onChange={(
                                                                                                                                    e
                                                                                                                                ) =>
                                                                                                                                    handleInventoryInputChange(
                                                                                                                                        index,
                                                                                                                                        product
                                                                                                                                            .Default
                                                                                                                                            .product_id,
                                                                                                                                        variantIndex,
                                                                                                                                        variantInventoryIndex,
                                                                                                                                        e
                                                                                                                                            .target
                                                                                                                                            .value,
                                                                                                                                        variantInventory.id,
                                                                                                                                        variant.id
                                                                                                                                    )
                                                                                                                                }
                                                                                                                            />
                                                                                                                        </td>
                                                                                                                    </tr>
                                                                                                                )
                                                                                                            )}
                                                                                                    </tbody>
                                                                                                </table>
                                                                                            </div>
                                                                                        </div>
                                                                                    </div>
                                                                                </div>
                                                                            </td>
                                                                        </tr>
                                                                    )}
                                                                </>
                                                            ))}
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        )}
                    </>
                );
            } else {
                console.log("No variants found for this product.");
            }

            const rowKey = `row-${index}`;
            productRows.push(
                <>
                    <tr key={rowKey} className={activeIndex === index ? "active " : ""}>
                        {rowCells}
                    </tr>
                    {variantData}
                </>
            );
        });

        return productRows;
    };

    return (
        <div className="bulkedit">
            <Head title="Bulk Edit" />
            <ToastContainer
                position="top-right"
                autoClose={2000}
                hideProgressBar={false}
                newestOnTop={false}
                closeOnClick
                rtl={false}
                pauseOnFocusLoss
                draggable
                pauseOnHover
                theme="light"
            />
            <div className="loader fixed w-full h-full bg-[rgba(255,255,255,0.5)] left-0 top-0 hidden">
                <div role="status" className="flex justify-center items-center h-full">
                    <svg
                        aria-hidden="true"
                        className="w-16 h-16 text-gray-200 animate-spin dark:text-gray-600 fill-blue-600"
                        viewBox="0 0 100 101"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                    >
                        <path
                            d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                            fill="currentColor"
                        />
                        <path
                            d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                            fill="currentFill"
                        />
                    </svg>
                    <span className="sr-only">Loading...</span>
                </div>
            </div>
            <header className="flex  bg-white pt-2 relative top-0 w-full left-0 z-90 items-center">
                <div className="lg:w-4/12 sm:w-3/12 w-full flex items-center h-full">
                    <button
                        onClick={handleBackButton}
                        // className=" bg-[#2C4BFF] text-[#fff] py-2 px-4  inline-block h-[59px] -mt-1 no-underline hover:text-gray-300 hover:bg-[#243ed9]"
                        className="text-[14px] text-[#252525]"
                    >
                        <i className="fa fa-arrow-left fs-20 mr-1 ml-2 align-middle"></i> Back
                    </button>
                    {/* <div className="inline-block p-2 border-l border-[#ccc] pl-4">
                        <a href="/" className="no-underline">
                            <img id="logo_sidebar" src="/assets/images/apimio-small.png" className="inline-block" alt="logo" />
                            <span className="ml-2 text-black  font-bold text-2xl align-middle">Apimio</span>
                        </a>
                    </div> */}
                    <p className="font-semibold text-[16px] ml-4 text-[#2C4BFF]">Editing {data.products.length} products</p>
                    {/* <a href="/" className="text-lg font-bold no-underline inline-block sm:hidden ml-auto mr-2 ">
                        <span className="icon icon-category mr-2 "></span>Dashboard
                    </a> */}
                </div>
                <>
                    {/* <h2 className="font-24 m-0 mt-2">Bulk editing</h2>
                    <p className="px-2 sm:px-0 text-center sm:text-[14px]] lg:text-[16px] text-[12px]">
                        Add, edit, bulk import, and assign products to your channel partners.
                    </p> */}
                    {data.products.length > 0 ? (
                        <div className="lg:w-8/12 sm:w-9/12 justify-between w-full flex items-center flex-row px-2">
                            <div className="manage-column relative flex lg:w-5/12 sm:w-5/12 w-full justify-center">
                                <input
                                    placeholder="Search Products"
                                    value={searchValue}
                                    onChange={(e) => {
                                        setSearchValue(e.target.value);
                                        handleSearch(e.target.value);
                                    }}
                                    className="h-10 rounded-md  w-full  bg-[#f4f4f4] border border-[#ccc] px-4 text-[#222] transition-all duration-200 ease-in-out focus:bg-white focus:drop-shadow-lg"
                                />
                            </div>

                            <div className="manage-column relative flex lg:w-4/12 sm:w-5/12 w-full justify-end">
                                <div className=" text-right lg:mr-4 sm:mr-2 ">
                                    <button
                                        onClick={openModal}
                                        className="border-solid border-1 border-[#2C4BFF] h-8 w-[150px] py-2 px-4 text-md font-semibold rounded-md text-[#2C4BFF]"
                                    >
                                        <i className="fa-solid fa-columns"></i> Columns
                                    </button>
                                    {isModalOpen ? (
                                        <>
                                            <div
                                                className="backdrop fixed w-full h-full left-0 top-0 z-[999]"
                                                onClick={handleBackdropClick}
                                            ></div>

                                            <div className="absolute right-28 left-auto top-3 w-80 z-[1000]">
                                                <div className="modal-dialog modal-dialog-centered ">
                                                    <div className="modal-content relative flex flex-column outline-0 rounded-sm w-full border-[#00000033] border pointer-auto">
                                                        <div className="p-3 border-b border-[#dee2e6] flex items-center justify-between">
                                                            <h2>Manage Columns</h2>
                                                        </div>
                                                        <div className="modal-body h-96 overflow-scroll">
                                                            <div className="row ">
                                                                <div>
                                                                    <input
                                                                        placeholder="Search Columns"
                                                                        value={columnSearchValue}
                                                                        onChange={(e) => {
                                                                            const searchValue = e.target.value;
                                                                            handleModalSearch(searchValue);
                                                                        }}
                                                                        className="h-[36px] rounded-md  w-full  bg-[#f4f4f4] mb-1 border border-[#ccc] px-4 text-[#222] transition-all duration-200 ease-in-out focus:bg-white focus:drop-shadow-lg"
                                                                    />
                                                                </div>

                                                                {filteredColumns.map((column, index) => (
                                                                    <div key={index} className="column-div">
                                                                        <div
                                                                            className={`py-1 flex flex-row ${
                                                                                column.isParent ? "font-bold" : ""
                                                                            }`}
                                                                            key={index}
                                                                        >
                                                                            {!column.isParent && ( // Display checkboxes only for child columns
                                                                                <input
                                                                                    type="checkbox"
                                                                                    checked={column.checked}
                                                                                    onChange={() => toggleColumn(column.hierName)}
                                                                                    className="mr-3 h-5 flex w-5 modal-checkbox"
                                                                                    name={`headerchecked-${index}`}
                                                                                    id={`headerchecked-${index}`}
                                                                                    value={column.hierName}
                                                                                    disabled={
                                                                                        column.hierName === "Default,sku" ||
                                                                                        column.hierName === "Default,handle"
                                                                                    }
                                                                                />
                                                                            )}
                                                                            <label
                                                                                htmlFor={`headerchecked-${index}`}
                                                                                className="flex items-center"
                                                                                id={column.hierName}
                                                                            >
                                                                                {column.label}
                                                                            </label>
                                                                        </div>
                                                                    </div>
                                                                ))}
                                                            </div>
                                                        </div>
                                                        <div className="px-3 py-1 border-t border-[#dee2e6]">
                                                            <div className="footer-buttons py-1 px-3 flex items-center w-full justify-between">
                                                                <button
                                                                    className=" bg-[#fff] text-primary border-solid border-1 border-[#2C4BFF] py-2 px-4 rounded-md text-md font-semibold flex justify-start"
                                                                    onClick={closeModalbutton}
                                                                >
                                                                    Cancel
                                                                </button>
                                                                <button
                                                                    className=" bg-[#2C4BFF] border-solid border-1 border-[#2C4BFF]  py-2 px-4 text-md font-semibold rounded-md text-white flex justify-end"
                                                                    onClick={handleSaveClick}
                                                                >
                                                                    Save
                                                                </button>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </>
                                    ) : (
                                        ""
                                    )}
                                </div>
                                <div className="bulkedit-footer text-right  lg:pl-4 sm:pl-2  ">
                                    <button
                                        onClick={handleBulkEditingSave}
                                        className=" bg-[#2C4BFF] border-solid border-1 border-[#2C4BFF] h-8 w-[150px] py-2 px-4 text-md font-semibold rounded-md text-white"
                                    >
                                        Save
                                    </button>
                                </div>
                            </div>
                        </div>
                    ) : (
                        ""
                    )}
                </>
                {/* <div className="sm:w-4/12 w-full pr-6 sm:flex items-center justify-end hidden ">
                    <a href="/" className="text-lg font-bold no-underline">
                        <span className="icon icon-category mr-2 "></span>Dashboard
                    </a>
                </div> */}
            </header>

            {showConfirmationModal && (
                <div className="fixed w-full h-full bg-[rgba(0,0,0,0.7)] left-0 top-0 h-full flex items-center confirmation-modal">
                    <div className="w-[400px] mx-auto bg-white rounded-md text-center p-8 border border-[#ccc] shadow-xl relative">
                        <div className="absolute top-0 right-0 p-2 cursor-pointer" onClick={() => setShowConfirmationModal(false)}>
                            <i className="fa fa-times"></i>
                        </div>
                        <p>Do you want to save changes before moving to another page?</p>
                        <div className="flex justify-between mt-4">
                            <button
                                className="bg-[#2C4BFF] border-solid border-1 border-[#2C4BFF] h-8 w-45 py-2 px-4 text-md font-semibold rounded-md text-white"
                                onClick={handleSaveChanges}
                            >
                                Save Changes
                            </button>
                            <button
                                className=" border-solid border-1 border-[#2C4BFF] h-8 w-45 py-2 px-4 text-md font-semibold rounded-md text-[#2C4BFF]"
                                onClick={handleDiscardChanges}
                            >
                                Discard Changes
                            </button>
                        </div>
                    </div>
                </div>
            )}
            <div className="w-full overflow-auto  bulkedit-content">
                {data.products.length > 0 ? (
                    <table className="bulk-edit-table">
                        <thead className="bg-[#f4f4f4] ">
                            <tr>{renderTableHeader()}</tr>
                        </thead>
                        <tbody>{renderTableBody()}</tbody>
                    </table>
                ) : (
                    <div class="d-flex flex-column ">
                        <div class="p-2 mt-5 mx-auto">
                            <img src="/media/emptypage/empty_page.png" alt="empty page" />
                        </div>
                        <div class="p-2 mx-auto">
                            <p class="Roboto">No Products Found in the selected Language</p>
                        </div>
                    </div>
                )}
                {data.products.length > 0 && (
                    <div className="flex justify-start w-full">
                        {totalPageCount > 1 && <div className="pagination border sm:mt-0 flex rounded-xl p-1 ">{renderPageNumbers()}</div>}
                    </div>
                )}
            </div>
        </div>
    );
};

export default BulkEdit;
