import React from "react";
import Logo from "../../../../public/v2/images/logo.png";
import WelcomeBck from "../../../../public/v2/images/welcome-bck.png";
import WelcomeVideoBck from "../../../../public/v2/images/welcome-video-bck.png";
import PlayBtn from "../../../../public/v2/icons/play-btn.svg";
import ProgressBarIcon from "../../../../public/v2/icons/progress-bar-icon.svg";

import { Input, Button, Checkbox } from "antd";
import { GoogleOutlined } from "@ant-design/icons";
const OnboardingEleven = () => {
    return (
        <div className="flex h-screen">
            {/* Left Section */}
            <div className="w-1/2 bg-white flex flex-col justify-center items-start relative">
                {/* Logo */}
                <div className="absolute top-8 left-8">
                    <img src={Logo} alt="Logo" className="w-36" />
                </div>

                <div className=" px-[60px]">
                    <p className="text-[#606060] text-[14px] font-[400]">Syncing your products...</p>
                    <img src={ProgressBarIcon} alt="progress bar icon" />
                    <h1 className="xl:text-[60px] sm:text-3xl pt-[20px] leading-[60px] font-[700] mb-8 text-start text-[#252525]">
                        Welcome to Apimio
                    </h1>
                    <p className="text-[#606060] text-[14px] font-[400]">
                        Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna
                        aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.
                        Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.
                    </p>
                    <ul className="pl-[20px] list-disc text-[#606060] text-[14px] font-[400] pt-[20px]">
                        <li>Lorem ipsum dolor sit amet, consectetur adipiscing elit.</li>
                        <li>Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. </li>
                        <li>Ut enim ad minim veniam.</li>
                        <li>Quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. </li>
                    </ul>
                    <div className="flex items-center pt-[60px]">
                        <Button disabled>Continue</Button>
                    </div>
                </div>
            </div>

            {/* Right Section */}
            <div
                className="w-1/2 bg-purple-900 flex flex-col justify-center items-center text-white bg-cover bg-center"
                style={{
                    backgroundImage: `url(${WelcomeBck})`,
                    backgroundPosition: "center",
                    backgroundSize: "cover",
                    backgroundRepeat: "no-repeat",
                }}
            >
                <h2 className="xl:text-[60px] sm:text-3xl font-[800] leading-[60px] mb-4 text-white">How to use Apimio</h2>
                <p className="text-[20px] mb-6 text-white font-[400] px-[124px] leading-[24px]">Watch demo video</p>
                <div className="relative flex items-center justify-center cursor-pointer">
                    <img src={WelcomeVideoBck} alt="welcome video background" />
                    <img src={PlayBtn} alt="play button" className="absolute" />
                </div>
            </div>
        </div>
    );
};

export default OnboardingEleven;
