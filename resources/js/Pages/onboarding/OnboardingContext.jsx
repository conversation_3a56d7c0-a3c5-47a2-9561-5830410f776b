import React, { createContext, useState, useEffect } from "react";

// Create the context
export const OnboardingContext = createContext();

// Create the provider component
export const OnboardingProvider = ({ children }) => {
    const [currentStepIndex, setCurrentStepIndex] = useState(0);
    const totalSteps = 8; // Update this based on the total number of steps

    // Initialize form data as an empty object
    const [formData, setFormData] = useState({});
    const [shopifyurl, setShopifyurl] = useState("");

    const handleNext = () => {
        setCurrentStepIndex((prev) => Math.min(prev + 1, totalSteps));
    };

    const handleBack = () => {
        setCurrentStepIndex((prev) => Math.max(prev - 1, 0));
    };

    // Function to update form data for a specific step
    const updateFormData = (stepKey, data) => {
        setFormData((prev) => ({
            ...prev,
            [stepKey]: data,
        }));
    };
    console.log("form data", shopifyurl, currentStepIndex);

    const value = {
        currentStepIndex,
        handleNext,
        handleBack,
        totalSteps,
        formData,
        updateFormData,
        shopifyurl,
        setShopifyurl,
    };

    return <OnboardingContext.Provider value={value}>{children}</OnboardingContext.Provider>;
};
