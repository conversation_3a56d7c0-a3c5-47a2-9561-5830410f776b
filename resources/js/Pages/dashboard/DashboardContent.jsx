import React, { useEffect, useState } from "react";
import { Row, Col, Button, Modal, Form, Input, message, Spin } from "antd";
import DashboardOverview from "./DashboardOverview";
import DashboardProductQualityScores from "./DashboardProductQualityScores";
import DashboardImageQualityScores from "./DashboardImageQualityScores";
import DashboardShopifySync from "./DashboardShopifySync";
import DashboardSeoQualityScore from "./DashboardSeoQualityScore";
import DashboardImoprtExport from "./DashboardImoprtExport";
import axios from "axios";
import { get } from "../../axios";
import DashboardShopifySynced from "./DashboardShopifySynced";
import { LoadingOutlined } from "@ant-design/icons";
import { usePage } from "@inertiajs/react";

// Custom loader component with consistent styling
const LoadingIndicator = ({ text = "Loading..." }) => (
    <div className="flex flex-col items-center justify-center w-full h-full py-10">
        <Spin indicator={<LoadingOutlined style={{ fontSize: 24, color: "#740898" }} spin />} />
        <div className="mt-2 text-[#626262]">{text}</div>
    </div>
);

const DashboardContent = () => {
    const [dashboardData, setDashboardData] = useState(null);
    const [isLoadingDashboard, setIsLoadingDashboard] = useState(true);
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [form] = Form.useForm();
    const [shopifySyncData, setShopifySyncData] = useState([]);
    const [isLoadingShopifyData, setIsLoadingShopifyData] = useState(true);
    const [isLoadingProductQuality, setIsLoadingProductQuality] = useState(true);
    const [isLoadingImageQuality, setIsLoadingImageQuality] = useState(true);
    const { authUser } = usePage().props;

    useEffect(() => {
        function fetchDashboardData() {
            setIsLoadingDashboard(true);
            axios
                .get("/api/2024-12/dashboard")
                .then((response) => {
                    setDashboardData(response.data);
                })
                .catch((error) => {
                    console.error("Error fetching dashboard data:", error);
                    message.error("Failed to load dashboard overview data");
                })
                .finally(() => {
                    setIsLoadingDashboard(false);
                });
        }
        fetchDashboardData();

        // Fetch Shopify sync status data
        fetchShopifySyncData();

        // Simulate loading states for other components
        // These should be replaced with actual API calls in a real implementation
        setTimeout(() => setIsLoadingProductQuality(false), 1200);
        setTimeout(() => setIsLoadingImageQuality(false), 1500);
    }, []);

    const fetchShopifySyncData = async () => {
        try {
            setIsLoadingShopifyData(true);
            const response = await get("shopify-sync-status");
            if (response && response.shopifySyncStatus) {
                setShopifySyncData(response.shopifySyncStatus);
            }
        } catch (error) {
            console.error("Error fetching Shopify sync status:", error);
            message.error("Failed to load Shopify sync data");
        } finally {
            setIsLoadingShopifyData(false);
        }
    };

    const showModal = () => {
        setIsModalOpen(true);
    };

    const handleCancel = () => {
        form.resetFields();
        setIsModalOpen(false);
    };

    const handleSubmit = async (values) => {
        try {
            setIsSubmitting(true);
            const response = await axios.post("/api/2024-12/products", {
                sku: values.productName,
                name: values.productName,
                organization_id: null, // This will be set by the backend using the authenticated user
            });

            message.success("Product created successfully!");
            form.resetFields();
            setIsModalOpen(false);

            // Refresh dashboard data
            setIsLoadingDashboard(true);
            axios.get("/api/2024-12/dashboard").then((response) => {
                setDashboardData(response.data);
                setIsLoadingDashboard(false);
            });
        } catch (error) {
            console.error("Error creating product:", error);
            message.error(error.response?.data?.message || "Failed to create product");
        } finally {
            setIsSubmitting(false);
        }
    };

    return (
        <div className="p-5 min-h-screen">
            <Row className="mb-4" gutter={[20, 20]}>
                <Col span={24}>
                    <div className="flex justify-between items-center">
                        <h1 className="text-[18px] text-[#740898] font-[700] capitalize">Good Morning! {authUser?.fname}</h1>
                        <div className="flex gap-3">
                            <Button
                                onClick={() => (window.location.href = "/products/import/step1")}
                                className="border-[#740898] text-[#740898] h-9 font-medium hover:bg-[#f9f0ff] hover:border-[#8c26ad] hover:text-[#8c26ad]"
                            >
                                Import CSV
                            </Button>
                            <Button onClick={showModal} className="bg-[#740898] text-white h-9 font-medium hover:bg-[#8c26ad]">
                                Create Product
                            </Button>
                        </div>
                    </div>
                </Col>
            </Row>
            <div className="grid grid-cols-1 min-[1920px]:grid-cols-2 gap-5">
                <div className="min-h-[271px] bg-white border border-[#DBDBDB] rounded-[16px]">
                    {isLoadingDashboard ? (
                        <LoadingIndicator text="Loading dashboard overview..." />
                    ) : (
                        <DashboardOverview dashboardData={dashboardData} />
                    )}
                </div>
                <div className="min-h-[271px] flex items-center justify-center bg-white border border-[#DBDBDB] rounded-[16px]">
                    {isLoadingProductQuality ? (
                        <LoadingIndicator text="Loading product quality data..." />
                    ) : (
                        <DashboardProductQualityScores />
                    )}
                </div>
                <div className="min-h-[271px] bg-white flex items-center justify-center border border-[#DBDBDB] rounded-[16px]">
                    {isLoadingShopifyData ? (
                        <LoadingIndicator text="Loading Shopify sync data..." />
                    ) : shopifySyncData.length === 0 ? (
                        <DashboardShopifySync />
                    ) : (
                        <DashboardShopifySynced shopifySyncData={shopifySyncData} />
                    )}
                </div>
                <div className="min-h-[271px] bg-white flex items-center justify-center border border-[#DBDBDB] rounded-[16px]">
                    {isLoadingImageQuality ? <LoadingIndicator text="Loading image quality data..." /> : <DashboardImageQualityScores />}
                </div>
                <div className="col-span-1 min-[1920px]:col-span-2">
                    <DashboardImoprtExport />
                </div>
            </div>

            {/* Create Product Modal */}
            <Modal
                title={<div className="text-[#740898] text-xl font-semibold">Create New Product</div>}
                open={isModalOpen}
                onCancel={handleCancel}
                footer={null}
                centered
                width={500}
                maskClosable={false}
                className="create-product-modal"
            >
                <div className="text-[#626262] text-sm mb-4">
                    Enter a product name to create a new product. This will be used as the product's SKU.
                </div>
                <Form form={form} layout="vertical" onFinish={handleSubmit} className="mt-4">
                    <Form.Item
                        label={<span className="text-base font-medium">Product Name</span>}
                        name="productName"
                        rules={[
                            {
                                required: true,
                                message: "Please enter a product name",
                            },
                            {
                                min: 3,
                                message: "Product name must be at least 3 characters long",
                            },
                        ]}
                        tooltip="This name will be used as the product's SKU identifier"
                    >
                        <Input placeholder="Enter product name" className="py-2" autoFocus />
                    </Form.Item>

                    <div className="flex justify-end gap-2 mt-6">
                        <Button onClick={handleCancel} className="h-10 px-4">
                            Cancel
                        </Button>
                        <Button
                            type="primary"
                            htmlType="submit"
                            loading={isSubmitting}
                            className="bg-[#740898] text-white border border-[#740898] h-10 px-6"
                        >
                            Create
                        </Button>
                    </div>
                </Form>
            </Modal>
        </div>
    );
};

export default DashboardContent;
