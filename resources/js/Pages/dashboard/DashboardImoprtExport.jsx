import React from "react";
import { Row, Col } from "antd";
import ImportTemplateIcon from "../../../../public/v2/icons/importtemplate-icon.svg";
import ExportTemplateIcon from "../../../../public/v2/icons/exporttemplate-icon.svg";
const DashboardImoprtExport = () => {
    return (
        <div>
            <Row gutter={[20, 20]}>
                <Col
                    span={12}
                    onClick={() => {
                        window.location.href = "/products/import/step1";
                    }}
                    className="cursor-pointer"
                >
                    <div className="min-h-[271px] bg-white gap-1 flex items-center justify-center flex-col border border-[#DBDBDB] rounded-[16px]">
                        <img src={ImportTemplateIcon} alt="import template icon" />
                        <p className="text-[#252525] font-[600] text-[18px]">Import Template</p>
                        <p className="text-[#626262] font-normal text-[14px]">Click to import CSV of all your products.</p>
                        <p className="text-[#740898] font-[600] text-[14px] px-[12px] py-[4px] bg-[#F1E6F5] rounded-[14px]">5 Templates</p>
                    </div>
                </Col>
                <Col
                    className="cursor-pointer"
                    span={12}
                    onClick={() => {
                        window.location.href = "/products/export/step1";
                    }}
                >
                    <div className="min-h-[271px] p-[31px] bg-white gap-1 flex items-center flex-col justify-center border border-[#DBDBDB] rounded-[16px]">
                        <img src={ExportTemplateIcon} alt="import template icon" />
                        <p className="text-[#252525] font-[600] text-[18px]">Export Template</p>
                        <p className="text-[#626262] font-normal text-[14px] text-center">
                            Filter the products you want to export, select a template and export..
                        </p>
                        <p className="text-[#740898] font-[600] text-[14px] px-[12px] py-[4px] bg-[#F1E6F5] rounded-[14px]">5 Templates</p>
                    </div>
                </Col>
            </Row>
        </div>
    );
};

export default DashboardImoprtExport;
