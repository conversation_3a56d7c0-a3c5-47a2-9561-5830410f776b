import React from "react";
import { Progress } from "antd";

/**
 * Reusable component for showing quality score progress indicator
 *
 * @param {Object} props - Component props
 * @param {number} props.percentage - The completion percentage (0-100)
 * @param {string} props.title - The title text
 * @param {string} props.description - The description text
 * @param {string} props.className - Additional CSS classes
 * @param {boolean} props.showHeader - Whether to show the title and description header
 */
const QualityScoreIndicator = ({ percentage, title, description, className = "", showHeader = true }) => {
    return (
        <div className={`mb-4 ${className}`}>
            {showHeader && (
                <div className="mb-3 flex justify-between items-center">
                    <div className="max-w-[60%]">
                        <p className="text-[#252525] font-[600] text-[18px] m-0">{title}</p>
                        <p className="text-[#626262] font-normal text-[14px] m-0">{description}</p>
                    </div>
                    <div className="flex flex-col items-end w-[30%]">
                        <Progress percent={percentage} strokeColor="#15D476" size="small" showInfo={false} />
                        <div className="text-[14px] font-medium text-[#626262] mb-1 self-start">{percentage}% completed</div>
                    </div>
                </div>
            )}
        </div>
    );
};

export default QualityScoreIndicator;
