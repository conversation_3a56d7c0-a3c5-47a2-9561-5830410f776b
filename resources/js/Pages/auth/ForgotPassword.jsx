import React, { useState } from "react";
import { use<PERSON><PERSON>, <PERSON>, Link } from "@inertiajs/react";
import { Input, Button, message } from "antd";
import Logo from "../../../../public/v2/images/logo.png";
import RegisterBack from "../../../../public/v2/images/signup-img.png";

const ForgotPassword = ({ status }) => {
    const { data, setData, post, processing, errors } = useForm({
        email: "",
    });

    const handleSubmit = () => {
        post(route("password.email"), {
            onSuccess: () => {
                message.success("Password reset link has been sent to your email!");
            },
            onError: (errors) => {
                if (Object.keys(errors).length === 0) {
                    message.error("An error occurred. Please try again.");
                }
            },
            preserveScroll: true,
        });
    };

    return (
        <div className="flex h-screen">
            <Head title="Forgot Password" />
            {/* Left Section */}
            <div className="w-7/12 bg-white flex flex-col justify-center items-center relative">
                {/* Logo */}
                <div className="absolute top-8 left-8">
                    <img src={Logo} alt="Logo" className="w-36" />
                </div>

                <div className="max-w-md w-[450px]">
                    <h1 className="xl:text-[40px] sm:text-3xl font-[700] xl:mb-8 sm:mb-0 text-center text-[#252525]">Forgot Password</h1>

                    {status && <div className="mb-4 font-medium text-sm text-green-600">{status}</div>}

                    <form className="space-y-[10px]">
                        {/* Email Field */}
                        <div>
                            <label htmlFor="email" className="block mb-1 font-[14px]">
                                Business Email
                            </label>
                            <Input
                                status={errors.email ? "error" : ""}
                                id="email"
                                type="email"
                                value={data.email}
                                onChange={(e) => setData("email", e.target.value)}
                                placeholder="Enter your email address"
                                className="p-2 h-8 border rounded-md"
                                required
                            />
                            {errors.email && <div className="text-red-500 text-sm mt-1">{errors.email}</div>}
                        </div>

                        {/* Submit Button */}
                        <Button
                            type="primary"
                            onClick={handleSubmit}
                            loading={processing}
                            disabled={processing}
                            block
                            className="h-8 bg-purple-900 flex items-center justify-center"
                        >
                            Send Password Reset Link
                        </Button>
                    </form>

                    <div className="loginlink text-center mt-4">
                        <span className="text-[14px]">Remember your password? </span>
                        <Link href={route("login")} className="text-[#1890FF] text-[14px]">
                            Back to Login
                        </Link>
                    </div>
                </div>
            </div>

            {/* Right Section */}
            <div
                className="w-5/12 bg-purple-900 flex flex-col text-white bg-cover bg-center"
                style={{
                    backgroundImage: `url(${RegisterBack})`,
                }}
            >
                <div className="xl:pt-[74px] sm:pt-16 rounded-lg text-center ">
                    <h2 className="xl:text-4xl sm:text-3xl font-bold xl:mb-4 sm:mb-0 text-white">Healthy Parcel</h2>
                    <p className="xl:text-[20px] sm:text-sm xl:mb-6 sm:mb-0 text-white font-[400] px-[124px] sm:px-5 leading-[24px]">
                        Managing our product information has never been easier since we started using Apimio. We've seen a noticeable boost
                        in customer satisfaction as a result.
                    </p>
                    <p className="font-[600]">KAREENA</p>
                    <p className="font-[400]">CEO Healthy Parcel</p>
                </div>
            </div>
        </div>
    );
};

export default ForgotPassword;
