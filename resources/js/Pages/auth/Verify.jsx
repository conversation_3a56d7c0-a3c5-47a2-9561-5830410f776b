import React, { useState, useEffect, useRef } from "react";
import ReactDOM from "react-dom/client";
import Logo from "../../../../public/v2/images/logo.png";
import VerifyBck from "../../../../public/v2/images/verify-bck.png";
import VerifyImg from "../../../../public/v2/images/verify-img.png";

import { Input, Button, Checkbox } from "antd";
import { GoogleOutlined } from "@ant-design/icons";
import VerifyIcon from "../../../../public/v2/icons/verify-icon.svg";
import { set } from "lodash";

import { usePage } from "@inertiajs/react";

const Verify = () => {
    const logoutFormRef = useRef(null);
    const resendFormRef = useRef(null);
    const { user } = usePage().props;
    console.log("user", user);
    const [email, setEmail] = useState(user.email);

    useEffect(() => {
        const storedEmail = localStorage.getItem("email");
        if (storedEmail) {
            setEmail(storedEmail);
        }
    }, []);

    return (
        <div className="flex h-screen">
            {/* Hidden forms */}
            <form ref={resendFormRef} method="POST" action="/email/resend" className="hidden"></form>

            <form ref={logoutFormRef} method="POST" action="/logout" className="hidden"></form>

            {/* Left Section */}
            <div className="w-7/12 bg-white flex flex-col justify-center items-center relative">
                {/* Logo */}
                <div className="absolute top-8 left-8">
                    <img src={Logo} alt="Logo" className="w-36" />
                </div>

                <div className="max-w-md">
                    <div className="flex justify-center items-center">
                        <img src={VerifyIcon} alt="" />
                    </div>
                    <h1 className="xl:text-[40px] sm:text-3xl font-[700] text-center text-[#252525]">Verify your account</h1>

                    <p className="text-[#252525] xl:text-[14px] sm:text-sm font-[400] uppercase text-center tracking-[4px]">
                        Thank you for joining
                    </p>
                    <p className="text-[#252525] font-normal pt-[34px] text-center">
                        We have sent a verification link. Please click on the link to verify your account and start using Apimio.
                    </p>
                    <p className="text-center pt-[16px]">
                        Didn't receive any email?{" "}
                        <span
                            className="text-[#740898] font-[700] cursor-pointer"
                            onClick={() => {
                                resendFormRef.current.submit();
                            }}
                        >
                            Resend link
                        </span>
                    </p>
                    <p className="text-center pt-[16px]">
                        Not your email address [<span className="font-[700]">{email}</span>]{" "}
                        <span
                            className="text-[#740898] font-[700] cursor-pointer"
                            onClick={() => {
                                logoutFormRef.current.submit();
                            }}
                        >
                            Signup Again
                        </span>
                    </p>
                </div>
            </div>

            {/* Right Section */}
            <div
                className="w-5/12 bg-purple-900 flex flex-col text-white bg-cover bg-center"
                style={{
                    backgroundImage: `url(${VerifyBck})`,
                    backgroundSize: "cover",
                    backgroundRepeat: "no-repeat",
                    backgroundPosition: "bottom",
                }}
            >
                <div className="xl:pt-[74px] sm:pt-16 rounded-lg text-center ">
                    <h2 className="xl:text-[60px] sm:text-3xl leading-[60px] font-[800] mb-4 text-white">Validate email</h2>
                    <p className="xl:text-[20px] sm:text-sm mb-6 text-white font-[400] xl:px-[124px] sm:px-5 leading-[24px] ">
                        Unlock the power of unified product content. Apimio streamlines your product information management, making it
                        accessible and consistent across all channels.
                    </p>
                </div>
                <div className="flex justify-end items-end">
                    <img src={VerifyImg} alt="verify image" />
                </div>
            </div>
        </div>
    );
};

export default Verify;
