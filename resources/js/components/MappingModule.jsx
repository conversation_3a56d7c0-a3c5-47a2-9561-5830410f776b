import React, { useState, useEffect } from "react";
import { createRoot } from "react-dom/client";
import { Provider, useSelector, useDispatch } from "react-redux";
import store from "../reduxStore/store";
import { FixedSizeList as List } from "react-window";
import MappingForm from "./MappingForm";
import { setNodes, addNode, calculateCounts } from "../reduxStore/nodeSlice";
import { Button } from "antd";
import SubmitModal from "./producttabsfields/SubmitModal";
import { ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";

import { map, set } from "lodash";
const MappingModule = ({ data }) => {
  const dispatch = useDispatch();
  const nodesRedux = useSelector((state) => state.nodes.nodes);
  const disableAddNewRow = useSelector((state) => state.nodes.disableAddNewRow);

  const handleAddNewNode = () => {
    dispatch(addNode());
  };
  const inputArray = data.input_array;
  const outputArray = data.output_array;
  const [nodes, setNodes] = useState([]);
  const [submitModal, setSubmitModal] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  useEffect(() => {
    console.log(nodes);
  }, [nodes]);
  const [rowCount, setRowCount] = useState(0);
  const [totalRowCount, setTotalRowCount] = useState(0);
  const [isWarningHidden, setIsWarningHidden] = useState(true);
  const [isImportMethod, setIsImportMethod] = useState(false);
  const [isProceedButtonDisabled, setIsProceedButtonDisabled] = useState(true);
  //const [disableAddNewRow, setDisableAddNewRow] = useState(false);
  const [limitReached, setLimitReached] = useState(0);
  useEffect(() => {
    const countEmptyNodes = (nodes) => {
      return nodes.filter((node) => {
        const isToOrFromEmpty = !node.to || !node.from;
        const isWithEmptyWhenSplit =
          node.with_formula === "split" && !node.with;
        return isToOrFromEmpty || isWithEmptyWhenSplit;
      }).length;
    };

    // Usage example
    const rowCount = countEmptyNodes(nodes);
    setRowCount(rowCount);
    setTotalRowCount(nodes.length);

    // if (nodes.length >= 300) {
    //   setDisableAddNewRow(true);
    //   setLimitReached(1);
    // } else {
    //   setDisableAddNewRow(false);
    //   setLimitReached(0);
    // }
    // Determine if the template method type is import
    const importMethod = data.data_required.template_method_type === "import";
    setIsImportMethod(importMethod);

    if (rowCount === 0) {
      // All rows are mapped
      document.querySelector(".row-count").classList.add("hidden");
      document.querySelector(".all-mapped").classList.remove("hidden");

      if (importMethod) {
        if (isWarningHidden) {
          setIsProceedButtonDisabled(false);
        }
      } else {
        setIsProceedButtonDisabled(false);
      }

      document.querySelector("#ignore_unmapped").checked = true;
      document.querySelector(".proceed-data-switch").classList.add("hidden");
      document.querySelector(".proceed-data-switch").classList.remove("d-flex");
    } else {
      // There are unmapped rows
      document.querySelector(".row-count").classList.remove("hidden");
      document.querySelector(".all-mapped").classList.add("hidden");
      document.querySelector(".proceed-data-switch").classList.remove("hidden");
      document.querySelector(".proceed-data-switch").classList.add("d-flex");
      setIsProceedButtonDisabled(true);
    }
  }, [nodes, isWarningHidden]);

  // const handleAddRow = () => {
  //   setDisplayedNodes(nodes);
  //   // mappingnodes = [...nodes];
  //   // const newNode = { from: "", with_formula: "assign", to: "" };
  //   // mappingnodes.push(newNode);
  //   // setNodes(mappingnodes);
  //   let updatedNodes = [...nodes];
  //   $(".mapping-item").each(function (index) {
  //     // Extract values from the current mapping item
  //     var fromValue = $(this).find(".mapping-from-select").val(); // Get the value of 'from' select
  //     var formulaValue = $(this).find(".formula_field").val(); // Get the value of 'formula' select
  //     var toValue = $(this).find(".mapping-to-select").val(); // Get the value of 'to' select
  //     var withInputValue = $(this).find(".mapping-with-input").val(); // Get the value of 'mapping-with-input' input
  //     var replaceInputValue = $(this).find(".mapping-replace-input").val(); // Get the value of 'mapping-replace-input' input
  //     console.log(updatedNodes, "updatednodes", index, "index");
  //     // Update the corresponding object in the updated_nodes array
  //     updatedNodes[index].from = fromValue;
  //     updatedNodes[index].with_formula = formulaValue;
  //     updatedNodes[index].to = toValue;
  //     if (formulaValue === "merge") {
  //       // Create an array to hold multiple 'to' values
  //       updatedNodes[index].from = [];

  //       // Get the values for 'to[0]' and 'to[1]'
  //       var fromValue1 = $(this).find(".mapping-from-select1").val();
  //       var fromValue2 = $(this).find(".mapping-from-select2").val();

  //       // Push the values to the 'to' array
  //       updatedNodes[index].from.push(); // Placeholder for first 'to' value
  //       updatedNodes[index].from.push(); // Placeholder for second 'to' value

  //       // Assign values to 'to[0]' and 'to[1]'
  //       updatedNodes[index].from[0] = fromValue1;
  //       updatedNodes[index].from[1] = fromValue2;
  //     } else if (formulaValue === "split") {
  //       // Create an array to hold multiple 'to' values
  //       updatedNodes[index].to = [];

  //       // Get the values for 'to[0]' and 'to[1]'
  //       var toValue1 = $(this).find(".mapping-to-select1").val();
  //       var toValue2 = $(this).find(".mapping-to-select2").val();

  //       // Push the values to the 'to' array
  //       updatedNodes[index].to.push(); // Placeholder for first 'to' value
  //       updatedNodes[index].to.push(); // Placeholder for second 'to' value

  //       // Assign values to 'to[0]' and 'to[1]'
  //       updatedNodes[index].to[0] = toValue1;
  //       updatedNodes[index].to[1] = toValue2;
  //     }
  //     if (withInputValue !== undefined || withInputValue !== "") {
  //       updatedNodes[index].with = withInputValue;
  //     } else {
  //       // Remove 'with' property if it's undefined or empty
  //       delete updatedNodes[index].with;
  //     }
  //     if (replaceInputValue !== undefined && replaceInputValue !== "") {
  //       updatedNodes[index].replace = replaceInputValue;
  //     } else {
  //       // Remove 'with' property if it's undefined or empty
  //       delete updatedNodes[index].replace;
  //     }
  //   });
  //   const newNode = { from: "", with_formula: "assign", to: "" };
  //   setNodes((updatedNodes) => [...updatedNodes, newNode]);
  //   setDisplayedNodes(nodes);
  //   console.log(updatedNodes, "updatedNodes");
  // };

  // const handleFormulaChange = (index, value, savedFromValue) => {};
  // const handleRemoveNode = (removeIndex) => {
  //   let updatedNodes = [...nodes];
  //   console.log(updatedNodes, removeIndex);
  //   updatedNodes = updatedNodes.filter((_, i) => i !== removeIndex);
  //   $(".mapping-item").each(function (index) {
  //     if (index >= updatedNodes.length) {
  //       return false; // Break the loop if index exceeds the updated array length
  //     }
  //     // Extract values from the current mapping item
  //     var fromValue = $(this).find(".mapping-from-select").val(); // Get the value of 'from' select
  //     var formulaValue = $(this).find(".formula_field").val(); // Get the value of 'formula' select
  //     var toValue = $(this).find(".mapping-to-select").val(); // Get the value of 'to' select
  //     var withInputValue = $(this).find(".mapping-with-input").val(); // Get the value of 'mapping-with-input' input
  //     var replaceInputValue = $(this).find(".mapping-replace-input").val(); // Get the value of 'mapping-replace-input' input

  //     // Update the corresponding object in the updated_nodes array
  //     updatedNodes[index].from = fromValue;
  //     updatedNodes[index].with_formula = formulaValue;
  //     updatedNodes[index].to = toValue;
  //     if (formulaValue === "merge") {
  //       // Create an array to hold multiple 'to' values
  //       updatedNodes[index].from = [];

  //       // Get the values for 'to[0]' and 'to[1]'
  //       var fromValue1 = $(this).find(".mapping-from-select1").val();
  //       var fromValue2 = $(this).find(".mapping-from-select2").val();

  //       // Push the values to the 'to' array
  //       updatedNodes[index].from.push(); // Placeholder for first 'to' value
  //       updatedNodes[index].from.push(); // Placeholder for second 'to' value

  //       // Assign values to 'to[0]' and 'to[1]'
  //       updatedNodes[index].from[0] = fromValue1;
  //       updatedNodes[index].from[1] = fromValue2;
  //     } else if (formulaValue === "split") {
  //       // Create an array to hold multiple 'to' values
  //       updatedNodes[index].to = [];

  //       // Get the values for 'to[0]' and 'to[1]'
  //       var toValue1 = $(this).find(".mapping-to-select1").val();
  //       var toValue2 = $(this).find(".mapping-to-select2").val();

  //       // Push the values to the 'to' array
  //       updatedNodes[index].to.push(); // Placeholder for first 'to' value
  //       updatedNodes[index].to.push(); // Placeholder for second 'to' value

  //       // Assign values to 'to[0]' and 'to[1]'
  //       updatedNodes[index].to[0] = toValue1;
  //       updatedNodes[index].to[1] = toValue2;
  //     }
  //     if (withInputValue !== undefined || withInputValue !== "") {
  //       updatedNodes[index].with = withInputValue;
  //     } else {
  //       // Remove 'with' property if it's undefined or empty
  //       delete updatedNodes[index].with;
  //     }
  //     if (replaceInputValue !== undefined && replaceInputValue !== "") {
  //       updatedNodes[index].replace = replaceInputValue;
  //     } else {
  //       // Remove 'with' property if it's undefined or empty
  //       delete updatedNodes[index].replace;
  //     }
  //   });
  //   setNodes(updatedNodes);
  //   setDisplayedNodes(updatedNodes.slice(0, chunkSize));
  //   console.log(updatedNodes, "updatedNodes nodes");
  //   var rowCount = $(".warning-msg:not(.hidden)").length;
  //   $("#invalid_row_count").text(rowCount);
  //   if (rowCount === 0) {
  //     $(".row-count").addClass("hidden");
  //     $(".all-mapped").removeClass("hidden");
  //     if (data.data_required.template_method_type === "import") {
  //       if ($("#handle_warning").hasClass("hidden")) {
  //         $("#pro_imp_btn").removeAttr("disabled", "disabled");
  //       } else {
  //         // $("#pro_imp_btn").attr("disabled", "disabled");
  //       }
  //     } else {
  //       $("#pro_imp_btn").removeAttr("disabled", "disabled");
  //     }
  //     $("#ignore_unmapped").prop("checked", true);
  //     $(".proceed-data-switch").addClass("hidden");
  //     $(".proceed-data-switch").removeClass("d-flex");
  //   } else {
  //     $(".row-count").removeClass("hidden");
  //     $(".all-mapped").addClass("hidden");
  //     $(".proceed-data-switch").removeClass("hidden");
  //     $(".proceed-data-switch").addClass("d-flex");
  //     // $("#pro_imp_btn").attr("disabled", "disabled");
  //   }
  // };

  const [resetedNodes, setResetedNodes] = useState([]);
  const [displayedNodes, setDisplayedNodes] = useState([]); // Nodes currently displayed
  const [hasMore, setHasMore] = useState(true);
  const chunkSize = 30;
  let mappingnodes = [];
  const createMappingNodes = () => {
    const generateUniqueId = () =>
      "_" + Math.random().toString(36).substr(2, 9);
    if (data.template_attributes.length == 0) {
      if (data.data_required.template_method_type === "import") {
        for (const familyName in inputArray.nodes) {
          const attributes = inputArray.nodes[familyName];

          attributes.forEach((attributeName, index) => {
            // Stop creating new rows if nodes reach 300
            if (mappingnodes.length >= 300) return;

            const formulaValue = "assign";
            const fromValue = `${familyName},${attributeName}`;
            const withValue = inputArray.nodes[familyName][index].with
              ? inputArray.nodes[familyName][index].with
              : "";
            const replaceValue = inputArray.nodes[familyName][index].replace
              ? inputArray.nodes[familyName][index].replace
              : "";
            let to = "";

            for (const outputNode of outputArray.nodes) {
              if (outputNode.attributes) {
                const outputAttributes = Object.keys(outputNode.attributes);
                const outputAttributesValues = Object.values(
                  outputNode.attributes
                );
                let smalAttrName = attributeName
                  .replace(/\s+/g, "_")
                  .toLowerCase();
                if (outputAttributes.includes(smalAttrName)) {
                  to = `${outputNode.name},${smalAttrName}`;
                } else if (outputAttributesValues.includes(attributeName)) {
                  const index = outputAttributesValues.indexOf(attributeName);
                  const keyAtIndex = outputAttributes[index];
                  to = `${outputNode.name},${keyAtIndex}`;
                }
              } else {
                // Handle this case if needed
              }
            }

            const node = {
              id: generateUniqueId(),
              from: fromValue,
              with_formula: formulaValue,
              to: to || "",
            };

            mappingnodes.push(node);
          });
        }
      } else {
        inputArray.nodes.forEach((node, index) => {
          if (mappingnodes.length >= 300) return;

          const attributes = node.attributes;
          const familyName = node.name;

          if (attributes && typeof attributes === "object") {
            Object.entries(attributes).forEach(
              ([attributeName, attributeValue]) => {
                if (mappingnodes.length >= 300) return;

                const formulaValue = "assign";
                const fromValue = `${familyName},${attributeName}`;
                const withValue = attributeValue.with
                  ? attributeValue.with
                  : "";
                const replaceValue = attributeValue.replace
                  ? attributeValue.replace
                  : "";
                const toValue = attributeValue;

                const newNode = {
                  id: generateUniqueId(),
                  from: fromValue,
                  with_formula: formulaValue,
                  to: toValue || "",
                };

                if (withValue) newNode.with = withValue;
                if (replaceValue) newNode.replace = replaceValue;

                mappingnodes.push(newNode);
              }
            );
          }
        });
      }
    } else {
      if (data?.data_required?.template_method_type === "import") {
        const mappingnodesWithtemplate =
          data.template_attributes.template.data.map(
            ({ from, with_formula, to, with: withValue, replace }) => {
              if (mappingnodes.length >= 300) return;

              const node = {
                id: generateUniqueId(),
                from: from || "",
                with_formula,
                to: to || "",
              };
              if (withValue) node.with = withValue;
              if (replace) node.replace = replace;

              return node;
            }
          );

        mappingnodes = mappingnodesWithtemplate.filter(
          (node) => node !== undefined
        );

        // Other logic remains the same
        // ...
      } else {
        const mappingnodesWithtemplate =
          data.template_attributes.template.data.map(
            ({ from, with_formula, to, with: withValue, replace }) => {
              if (mappingnodes.length >= 300) return;

              const node = {
                id: generateUniqueId(),
                from: from || "",
                with_formula,
                to: to || "",
              };
              if (withValue) node.with = withValue;
              if (replace) node.replace = replace;

              return node;
            }
          );

        mappingnodes = mappingnodesWithtemplate.filter(
          (node) => node !== undefined
        );
        mappingnodes = mappingnodes.map((node) => ({
          ...node,
          from: node.from && node.from[0] !== null ? node.from : "",
          to: node.to && node.to[0] !== null ? node.to : "",
        }));
      }
    }

    setNodes(mappingnodes);
    setDisplayedNodes(mappingnodes.slice(0, chunkSize));
    setResetedNodes(mappingnodes);
  };
  useEffect(() => {
    createMappingNodes();
    dispatch(setNodesRedux(nodes)); // Initialize nodes with the passed data
    console.log(nodesRedux);
    dispatch(calculateCounts());
    setTimeout(function () {
      $(".short_code_hidden_field").each(function (index, element) {
        var hidden_field_short_code = $(element);
        var base64EncodedString = hidden_field_short_code.val();

        if (
          (base64EncodedString && base64EncodedString.trim() !== "") ||
          (base64EncodedString && base64EncodedString.trim() !== null)
        ) {
          try {
            // Decrypt the base64-encoded string
            var decryptedString = atob(base64EncodedString);

            // Find the corresponding .short_code_div
            var corresponding_div =
              hidden_field_short_code.next(".short_code_div");

            // Update the content of the corresponding .short_code_div
            corresponding_div.html(decryptedString);
          } catch (e) {
            // Check if the error is due to incorrect encoding
            if (
              e instanceof DOMException &&
              e.name === "InvalidCharacterError"
            ) {
              console.warn(
                "String is not base64 encoded:",
                base64EncodedString
              );
            } else {
              console.error("Failed to decode Base64 string:", e);
            }
            // Set decryptedString to empty string if decoding fails
            var decryptedString = "";
          }
        } else {
          console.warn("Invalid or empty Base64 string:", base64EncodedString);
        }

        // If decryptedString is empty or null, set hidden_field_short_code value to empty
        if (!decryptedString || decryptedString.trim() === "") {
          hidden_field_short_code.val("");
        }
      });
    }, 200);
    setIsLoading(false);
  }, [data.nodesRedux, dispatch]);
  const fetchMoreData = () => {
    if (displayedNodes.length >= nodes.length) {
      setHasMore(false);
      return;
    }

    setTimeout(() => {
      const nextChunk = nodes.slice(
        displayedNodes.length,
        displayedNodes.length + chunkSize
      );
      setDisplayedNodes((prevNodes) => [...prevNodes, ...nextChunk]);
    }, 1000); // Simulate a delay for loading more data
  };

  const [submitData, setSubmitData] = useState({});

  const handleResetClick = () => {
    setIsLoading(true);
    const slicedNodes = resetedNodes.slice(0, 4);
    setNodes(slicedNodes);
    setIsLoading(false);
  };
  const handleNextClick = () => {
    setDisplayedNodes(nodes);
    setTimeout(() => {
      const selectedValuesfrom = [];

      setTimeout(function () {
        var templaName = $("input[name='temp_name']").val();
        if (templaName !== "") {
          $("#customSwitch1").prop("checked", true);
          $(".template-name").removeClass("hidden");
          $(".template-name input").prop("readonly", true);
          $(".template-save").removeClass("hidden");
        } else {
          $("#customSwitch1").prop("checked", false);
          $(".template-name").addClass("hidden");
          $(".template-save").addClass("hidden");
        }
        $("#pro_imp_btn").removeAttr("disabled", "disabled");
      }, 200);

      const selectedValuesto = [];
      const selectedValuesWith = [];
      const selectedValuesReplace = [];
      // Iterate through each mapping item
      $(".mapping-item").each(function () {
        const $formulaSelect = $(this).find(".formula_field");
        const formulaValue = $formulaSelect.val();

        // Check if there's a formula and collect "from" and "to" values
        if (formulaValue) {
          const $toSelects = $(this).find(".mapping-to-select");
          // Check if there are multiple "to" selects
          const to = [];
          $toSelects.each(function () {
            const toValue = $(this).val();

            to.push(toValue);
          });
          if (to.length > 0) {
            selectedValuesto.push({
              to,
            });
          }
          const $fromSelects = $(this).find(".mapping-from-select");
          const from = [];
          $fromSelects.each(function () {
            const fromValue = $(this).val();

            from.push(fromValue);
          });
          if (from.length > 0) {
            selectedValuesfrom.push({
              from,
            });
          }
        }
        const $withInput = $(this).find(".mapping-with-input");
        if ($withInput.length > 0) {
          var withValue = $withInput.val();
          selectedValuesWith.push($withInput.val());
        } else {
          selectedValuesWith.push(null);
        }
        const $replaceInput = $(this).find(".mapping-replace-input");
        if ($replaceInput.length > 0) {
          const replace = $replaceInput.val();
          selectedValuesReplace.push($replaceInput.val());
        } else {
          selectedValuesReplace.push(null);
        }
      });

      const selectedValuesformula = [];
      $(".formula_field ").each(function () {
        $(this)
          .find("option:selected")
          .each(function () {
            selectedValuesformula.push($(this).val());
          });
      });
      const newArray = {
        nodes: {
          data: [],
        },
      };

      for (let i = 0; i < selectedValuesfrom.length; i++) {
        if (
          selectedValuesfrom[i] &&
          selectedValuesformula[i] &&
          selectedValuesto[i]
        ) {
          const fromField = [selectedValuesfrom[i].from];
          const withFormula = selectedValuesformula[i];
          const withValue = selectedValuesWith[i];
          const replace = selectedValuesReplace[i];
          const to = [selectedValuesto[i].to];

          const dataObject = {
            from: selectedValuesfrom[i].from,
            with_formula: selectedValuesformula[i],
            to: selectedValuesto[i].to,
          };

          if (withValue !== null && replace !== null) {
            dataObject["with"] = withValue;
            dataObject["replace"] = replace;
          } else if (replace !== null) {
            dataObject["replace"] = replace;
          } else if (withValue !== null) {
            dataObject["with"] = withValue;
          }

          newArray.nodes.data.push(dataObject);
        }
      }

      if ($("#customSwitch1").prop("checked")) {
        var tempStatus = "on";
        newArray.temp_status = tempStatus; // Add "temp_status" to newArray
      }
      setSubmitData(newArray);

      setSubmitModal(1);
    }, 200);
  };

  // Inside your component or function

  $(document).ready(function () {
    var handleWarning = "";

    if (data.import_action == 1) {
      handleWarning = "Product Identifier and SKU column is not mapped.";
    } else if (data.import_action == 2) {
      handleWarning = "SKU column is not mapped.";
    } else if (data.import_action == 3) {
      handleWarning = "Product Identifier column is not mapped.";
    }
    $("#handle_warningtext").text(handleWarning);

    var anyDefaultHandle = false;
    $(".mapping-to-select").each(function () {
      var toValue = $(this).val();
      var fromSelectValue = $(this)
        .closest(".to")
        .siblings(".from")
        .find(".mapping-from-select")
        .val();
      if (data.import_action == 1) {
        if (fromSelectValue !== "") {
          if (toValue === "Default,handle") {
            // Check if any other row has toValue === "Variant,sku"
            if (
              $(".mapping-to-select")
                .not(this)
                .find(":selected[value='Variant,sku']").length > 0
            ) {
              anyDefaultHandle = true;
              return false; // exit the loop since we found a match
            } else {
              handleWarning = "SKU column is not mapped.";
              $("#handle_warningtext").text(handleWarning);
              return false;
            }
          } else if (toValue === "Variant,sku") {
            // Check if any other row has toValue === "Default,handle"
            if (
              $(".mapping-to-select")
                .not(this)
                .find(":selected[value='Default,handle']").length > 0
            ) {
              anyDefaultHandle = true;
              return false; // exit the loop since we found a match
            } else {
              handleWarning = "Product Identifier column is not mapped.";
              $("#handle_warningtext").text(handleWarning);
              return false;
            }
          } else {
            handleWarning = "Product Identifier and SKU column is not mapped.";
            $("#handle_warningtext").text(handleWarning);
          }
        }
      } else if (data.import_action == 2) {
        if (fromSelectValue !== "" && toValue === "Variant,sku") {
          anyDefaultHandle = true;
          return false; // exit the loop since we found a match
        }
      } else if (data.import_action == 3) {
        if (fromSelectValue !== "" && toValue === "Default,handle") {
          anyDefaultHandle = true;
          return false; // exit the loop since we found a match
        }
      }
    });

    if (anyDefaultHandle) {
      $("#handle_warning").addClass("hidden");
      if ($("#ignore_unmapped").is(":checked")) {
        $("#pro_imp_btn").removeAttr("disabled", "disabled");
      } else {
        $("#pro_imp_btn").attr("disabled", "disabled");
      }
    } else {
      $("#handle_warning").removeClass("hidden");
      $("#pro_imp_btn").attr("disabled", "disabled");
    }
    $("#ignore_unmapped").on("change", function () {
      if (data.data_required.template_method_type === "import") {
        if ($(this).is(":checked")) {
          $(".ignore_unchecked").addClass("hidden");
          $(".ignore_checked").removeClass("hidden");
          if ($("#handle_warning").hasClass("hidden")) {
            $("#pro_imp_btn").removeAttr("disabled", "disabled");
          } else {
            $("#pro_imp_btn").attr("disabled", "disabled");
          }
        } else {
          $(".ignore_unchecked").removeClass("hidden");
          $(".ignore_checked").addClass("hidden");
          $("#pro_imp_btn").attr("disabled", "disabled");
        }
      } else {
        if ($(this).is(":checked")) {
          $(".ignore_unchecked").addClass("hidden");
          $(".ignore_checked").removeClass("hidden");

          $("#pro_imp_btn").removeAttr("disabled", "disabled");
        } else {
          $(".ignore_unchecked").removeClass("hidden");
          $(".ignore_checked").addClass("hidden");
          $("#pro_imp_btn").attr("disabled", "disabled");
        }
      }
    });
    var rowCount = $(".warning-msg:not(.hidden)").length;
    $("#invalid_row_count").text(rowCount);
    var totalRowCount = $(".mapping-item").length;
    $("#total_row_count").text(totalRowCount);

    if (rowCount < 1) {
      if ($("#handle_warning").hasClass("hidden")) {
        $("#pro_imp_btn").removeAttr("disabled", "disabled");
      } else {
        $("#pro_imp_btn").attr("disabled", "disabled");
      }
      $(".proceed-data-switch").addClass("hidden");
      $(".proceed-data-switch").removeClass("d-flex");
      $(".row-count").addClass("hidden");
      $(".all-mapped").removeClass("hidden");
    } else {
      $(".row-count").removeClass("hidden");
      $(".all-mapped").addClass("hidden");
      // $("#pro_imp_btn").attr("disabled", "disabled");
      $(".proceed-data-switch").removeClass("hidden");
      $(".proceed-data-switch").addClass("d-flex");
    }
    if (data.data_required.template_method_type === "export") {
      var rowCount = $(".warning-msg:not(.hidden)").length;
      $("#invalid_row_count").text(rowCount);
      var totalRowCount = $(".mapping-item").length;
      $("#total_row_count").text(totalRowCount);

      if (rowCount < 1) {
        $("#pro_imp_btn").removeAttr("disabled", "disabled");

        $(".proceed-data-switch").addClass("hidden");
        $(".proceed-data-switch").removeClass("d-flex");
        $(".row-count").addClass("hidden");
        $(".all-mapped").removeClass("hidden");
      } else {
        $(".row-count").removeClass("hidden");
        $(".all-mapped").addClass("hidden");
        $("#pro_imp_btn").attr("disabled", "disabled");
        $(".proceed-data-switch").removeClass("hidden");
        $(".proceed-data-switch").addClass("d-flex");
      }
      $(document).on("input", ".mapping-to-select", function (event) {
        let keypressed = event.which || event.keyCode;

        var toValue = $(this).val();
        var fromSelectValue = $(this)
          .closest(".to")
          .siblings(".from")
          .find(".mapping-from-select")
          .val();
        if (toValue !== "" && fromSelectValue !== "") {
          $("#pro_imp_btn").removeAttr("disabled", "disabled");
        } else {
          $("#pro_imp_btn").attr("disabled", "disabled");
          var rowCount = $(".warning-msg:not(.hidden)").length;
          $("#invalid_row_count").text(rowCount);
          var totalRowCount = $(".mapping-item").length;
          $("#total_row_count").text(totalRowCount);

          if (rowCount < 1) {
            if ($("#handle_warning").hasClass("hidden")) {
              $("#pro_imp_btn").removeAttr("disabled", "disabled");
            } else {
              $("#pro_imp_btn").attr("disabled", "disabled");
            }
            $(".proceed-data-switch").addClass("hidden");
            $(".proceed-data-switch").removeClass("d-flex");
            $(".row-count").addClass("hidden");
            $(".all-mapped").removeClass("hidden");
          } else {
            $(".row-count").removeClass("hidden");
            $(".all-mapped").addClass("hidden");
            $("#pro_imp_btn").attr("disabled", "disabled");
            $(".proceed-data-switch").removeClass("hidden");
            $(".proceed-data-switch").addClass("d-flex");
          }
        }
      });
      $(document).on("input", ".short_code_div", function (event) {
        let keypressed = event.which || event.keyCode;

        var toValue = $(this).val();
        var fromSelectValue = $(this)
          .closest(".to")
          .siblings(".from")
          .find(".mapping-from-select")
          .val();
        if (toValue !== "" && fromSelectValue !== "") {
          $("#pro_imp_btn").removeAttr("disabled", "disabled");
        } else {
          $("#pro_imp_btn").attr("disabled", "disabled");
          var rowCount = $(".warning-msg:not(.hidden)").length;
          $("#invalid_row_count").text(rowCount);
          var totalRowCount = $(".mapping-item").length;
          $("#total_row_count").text(totalRowCount);

          if (rowCount < 1) {
            $("#pro_imp_btn").removeAttr("disabled", "disabled");

            $(".proceed-data-switch").addClass("hidden");
            $(".proceed-data-switch").removeClass("d-flex");
            $(".row-count").addClass("hidden");
            $(".all-mapped").removeClass("hidden");
          } else {
            $(".row-count").removeClass("hidden");
            $(".all-mapped").addClass("hidden");
            $("#pro_imp_btn").attr("disabled", "disabled");
            $(".proceed-data-switch").removeClass("hidden");
            $(".proceed-data-switch").addClass("d-flex");
          }
        }
      });
    } else {
      $(document).on("change", ".mapping-to-select", function () {
        var anyDefaultHandle = false;
        setTimeout(function () {
          var rowCount = $(".warning-msg:not(.hidden)").length;
          $("#invalid_row_count").text(rowCount);

          if (rowCount < 1) {
            $(".proceed-data-switch").addClass("hidden");
            $(".proceed-data-switch").removeClass("d-flex");
            $(".row-count").addClass("hidden");
            $(".all-mapped").removeClass("hidden");
            $("#ignore_unmapped").prop("checked", true);
            if (
              $("#handle_warning").hasClass("hidden") &&
              $("#ignore_unmapped").is(":checked")
            ) {
              $("#pro_imp_btn").removeAttr("disabled", "disabled");
            } else {
              $("#pro_imp_btn").attr("disabled", "disabled");
            }
          } else {
            $(".row-count").removeClass("hidden");
            $(".all-mapped").addClass("hidden");
            if (
              $("#handle_warning").hasClass("hidden") &&
              $("#ignore_unmapped").is(":checked")
            ) {
              $("#pro_imp_btn").removeAttr("disabled", "disabled");
            } else {
              $("#pro_imp_btn").attr("disabled", "disabled");
              $("#ignore_unmapped").prop("checked", false);
            }
            $(".proceed-data-switch").removeClass("hidden");
            $(".proceed-data-switch").addClass("d-flex");
          }
          $(".mapping-to-select").each(function () {
            var toValue = $(this).val();
            var fromSelectValue = $(this)
              .closest(".to")
              .siblings(".from")
              .find(".mapping-from-select")
              .val();
            if (data.import_action == 1) {
              if (fromSelectValue !== "") {
                if (toValue === "Default,handle") {
                  // Check if any other row has toValue === "Variant,sku"
                  if (
                    $(".mapping-to-select")
                      .not(this)
                      .find(":selected[value='Variant,sku']").length > 0
                  ) {
                    anyDefaultHandle = true;
                    return false; // exit the loop since we found a match
                  } else {
                    handleWarning = "SKU column is not mapped.";
                    $("#handle_warningtext").text(handleWarning);
                    return false;
                  }
                } else if (toValue === "Variant,sku") {
                  // Check if any other row has toValue === "Default,handle"
                  if (
                    $(".mapping-to-select")
                      .not(this)
                      .find(":selected[value='Default,handle']").length > 0
                  ) {
                    anyDefaultHandle = true;
                    return false; // exit the loop since we found a match
                  } else {
                    handleWarning = "Product Identifier column is not mapped.";
                    $("#handle_warningtext").text(handleWarning);
                    return false;
                  }
                } else {
                  handleWarning =
                    "Product Identifier and SKU column is not mapped.";
                  $("#handle_warningtext").text(handleWarning);
                }
              }
            } else if (data.import_action == 2) {
              if (fromSelectValue !== "" && toValue === "Variant,sku") {
                anyDefaultHandle = true;
                return false; // exit the loop since we found a match
              }
            } else if (data.import_action == 3) {
              if (fromSelectValue !== "" && toValue === "Default,handle") {
                anyDefaultHandle = true;
                return false; // exit the loop since we found a match
              }
            }
          });

          if (anyDefaultHandle) {
            $("#handle_warning").addClass("hidden");
            if ($("#ignore_unmapped").is(":checked")) {
              $("#pro_imp_btn").removeAttr("disabled", "disabled");
            }
          } else {
            $("#handle_warning").removeClass("hidden");
          }
        }, 500);
      });
    }
    $(document).on("input", ".mapping-with-input ", function (e) {
      var anyDefaultHandle = false;
      setTimeout(function () {
        var rowCount = $(".warning-msg:not(.hidden)").length;
        $("#invalid_row_count").text(rowCount);
        if (data.data_required.template_method_type === "import") {
          if (rowCount < 1) {
            $(".proceed-data-switch").addClass("hidden");
            $(".proceed-data-switch").removeClass("d-flex");
            $(".row-count").addClass("hidden");
            $(".all-mapped").removeClass("hidden");
            if ($("#handle_warning").hasClass("hidden")) {
              $("#pro_imp_btn").removeAttr("disabled", "disabled");
            } else {
            }
          } else {
            $(".row-count").removeClass("hidden");
            $(".all-mapped").addClass("hidden");
            if (
              $("#handle_warning").hasClass("hidden") &&
              $("#ignore_unmapped").is(":checked")
            ) {
              $("#pro_imp_btn").removeAttr("disabled", "disabled");
            } else {
              $("#pro_imp_btn").attr("disabled", "disabled");
            }
            $(".proceed-data-switch").removeClass("hidden");
            $(".proceed-data-switch").addClass("d-flex");
          }
        } else {
          if (rowCount < 1) {
            $(".proceed-data-switch").addClass("hidden");
            $(".proceed-data-switch").removeClass("d-flex");
            $(".row-count").addClass("hidden");
            $(".all-mapped").removeClass("hidden");
            $("#pro_imp_btn").removeAttr("disabled", "disabled");
          } else {
            $(".row-count").removeClass("hidden");
            $(".all-mapped").addClass("hidden");
            if (
              $("#handle_warning").hasClass("hidden") &&
              $("#ignore_unmapped").is(":checked")
            ) {
              $("#pro_imp_btn").removeAttr("disabled", "disabled");
            } else {
              $("#pro_imp_btn").attr("disabled", "disabled");
            }
            $(".proceed-data-switch").removeClass("hidden");
            $(".proceed-data-switch").addClass("d-flex");
          }
        }
        $(".mapping-to-select").each(function () {
          var toValue = $(this).val();
          var fromSelectValue = $(this)
            .closest(".to")
            .siblings(".from")
            .find(".mapping-from-select")
            .val();

          if (data.import_action == 1) {
            if (fromSelectValue !== "") {
              if (toValue === "Default,handle") {
                // Check if any other row has toValue === "Variant,sku"
                if (
                  $(".mapping-to-select")
                    .not(this)
                    .find(":selected[value='Variant,sku']").length > 0
                ) {
                  anyDefaultHandle = true;
                  return false; // exit the loop since we found a match
                } else {
                  handleWarning = "SKU column is not mapped.";
                  $("#handle_warningtext").text(handleWarning);
                  return false;
                }
              } else if (toValue === "Variant,sku") {
                // Check if any other row has toValue === "Default,handle"
                if (
                  $(".mapping-to-select")
                    .not(this)
                    .find(":selected[value='Default,handle']").length > 0
                ) {
                  anyDefaultHandle = true;
                  return false; // exit the loop since we found a match
                } else {
                  handleWarning = "Product Identifier column is not mapped.";
                  $("#handle_warningtext").text(handleWarning);
                  return false;
                }
              } else {
                handleWarning =
                  "Product Identifier and SKU column is not mapped.";
                $("#handle_warningtext").text(handleWarning);
              }
            }
          } else if (data.import_action == 2) {
            if (fromSelectValue !== "" && toValue === "Variant,sku") {
              anyDefaultHandle = true;
              return false; // exit the loop since we found a match
            }
          } else if (data.import_action == 3) {
            if (fromSelectValue !== "" && toValue === "Default,handle") {
              anyDefaultHandle = true;
              return false; // exit the loop since we found a match
            }
          }
        });

        if (anyDefaultHandle) {
          $("#handle_warning").addClass("hidden");
          // if ($("#ignore_unmapped").is(":checked")) {
          //   $("#pro_imp_btn").removeAttr("disabled", "disabled");
          // }
        } else {
          $("#handle_warning").removeClass("hidden");
        }
      }, 200);
    });
    $(document).on("change", ".mapping-from-select", function () {
      var anyDefaultHandle = false;
      setTimeout(function () {
        var rowCount = $(".warning-msg:not(.hidden)").length;
        $("#invalid_row_count").text(rowCount);

        if (rowCount < 1) {
          $(".proceed-data-switch").addClass("hidden");
          $(".proceed-data-switch").removeClass("d-flex");
          $(".row-count").addClass("hidden");
          $(".all-mapped").removeClass("hidden");
          if ($("#handle_warning").hasClass("hidden")) {
            $("#pro_imp_btn").removeAttr("disabled", "disabled");
          } else {
            $("#pro_imp_btn").attr("disabled", "disabled");
          }
        } else {
          $(".row-count").removeClass("hidden");
          $(".all-mapped").addClass("hidden");
          if (
            $("#handle_warning").hasClass("hidden") &&
            $("#ignore_unmapped").is(":checked")
          ) {
            $("#pro_imp_btn").removeAttr("disabled", "disabled");
          } else {
            $("#pro_imp_btn").attr("disabled", "disabled");
          }
          $(".proceed-data-switch").removeClass("hidden");
          $(".proceed-data-switch").addClass("d-flex");
        }
        $(".mapping-to-select").each(function () {
          var toValue = $(this).val();
          var fromSelectValue = $(this)
            .closest(".to")
            .siblings(".from")
            .find(".mapping-from-select")
            .val();

          if (data.import_action == 1) {
            if (fromSelectValue !== "") {
              if (toValue === "Default,handle") {
                // Check if any other row has toValue === "Variant,sku"
                if (
                  $(".mapping-to-select")
                    .not(this)
                    .find(":selected[value='Variant,sku']").length > 0
                ) {
                  anyDefaultHandle = true;
                  return false; // exit the loop since we found a match
                } else {
                  handleWarning = "SKU column is not mapped.";
                  $("#handle_warningtext").text(handleWarning);
                  return false;
                }
              } else if (toValue === "Variant,sku") {
                // Check if any other row has toValue === "Default,handle"
                if (
                  $(".mapping-to-select")
                    .not(this)
                    .find(":selected[value='Default,handle']").length > 0
                ) {
                  anyDefaultHandle = true;
                  return false; // exit the loop since we found a match
                } else {
                  handleWarning = "Product Identifier column is not mapped.";
                  $("#handle_warningtext").text(handleWarning);
                  return false;
                }
              } else {
                handleWarning =
                  "Product Identifier and SKU column is not mapped.";
                $("#handle_warningtext").text(handleWarning);
              }
            }
          } else if (data.import_action == 2) {
            if (fromSelectValue !== "" && toValue === "Variant,sku") {
              anyDefaultHandle = true;
              return false; // exit the loop since we found a match
            }
          } else if (data.import_action == 3) {
            if (fromSelectValue !== "" && toValue === "Default,handle") {
              anyDefaultHandle = true;
              return false; // exit the loop since we found a match
            }
          }
        });

        if (anyDefaultHandle) {
          $("#handle_warning").addClass("hidden");
          // if ($("#ignore_unmapped").is(":checked")) {
          //   $("#pro_imp_btn").removeAttr("disabled", "disabled");
          // }
        } else {
          $("#handle_warning").removeClass("hidden");
        }
      }, 200);
    });
  });

  const handleSaveTemplateClick = () => {
    setDisplayedNodes(nodes);
    setTimeout(() => {
      console.log(displayedNodes, "displayed nodes");
      const selectedValuesfrom = [];

      const selectedValuesto = [];
      const selectedValuesWith = [];
      const selectedValuesReplace = [];
      // Iterate through each mapping item
      $(".mapping-item").each(function () {
        const $formulaSelect = $(this).find(".formula_field");
        const formulaValue = $formulaSelect.val();

        // Check if there's a formula and collect "from" and "to" values
        if (formulaValue) {
          const $toSelects = $(this).find(".mapping-to-select");
          // Check if there are multiple "to" selects
          const to = [];
          $toSelects.each(function () {
            const toValue = $(this).val();

            to.push(toValue);
          });
          if (to.length > 0) {
            selectedValuesto.push({
              to,
            });
          }
          const $fromSelects = $(this).find(".mapping-from-select");
          const from = [];
          $fromSelects.each(function () {
            const fromValue = $(this).val();

            from.push(fromValue);
          });
          if (from.length > 0) {
            selectedValuesfrom.push({
              from,
            });
          }
        }
        const $withInput = $(this).find(".mapping-with-input");
        if ($withInput.length > 0) {
          var withValue = $withInput.val();
          selectedValuesWith.push($withInput.val());
        } else {
          selectedValuesWith.push(null);
        }
        const $replaceInput = $(this).find(".mapping-replace-input");
        if ($replaceInput.length > 0) {
          const replace = $replaceInput.val();
          selectedValuesReplace.push($replaceInput.val());
        } else {
          selectedValuesReplace.push(null);
        }
      });

      const selectedValuesformula = [];
      $(".formula_field ").each(function () {
        $(this)
          .find("option:selected")
          .each(function () {
            selectedValuesformula.push($(this).val());
          });
      });
      const ignoreUnmapped = $("#ignore_unmapped").prop("checked")
        ? "on"
        : "off";
      const newArray = {
        nodes: {
          data: [],
        },
      };

      for (let i = 0; i < selectedValuesfrom.length; i++) {
        if (
          selectedValuesfrom[i] &&
          selectedValuesformula[i] &&
          selectedValuesto[i]
        ) {
          const fromField = [selectedValuesfrom[i].from];
          const withFormula = selectedValuesformula[i];
          const withValue = selectedValuesWith[i];
          const replace = selectedValuesReplace[i];
          const to = [selectedValuesto[i].to];

          const dataObject = {
            from: selectedValuesfrom[i].from,
            with_formula: selectedValuesformula[i],
            to: selectedValuesto[i].to,
          };

          if (withValue !== null && replace !== null) {
            dataObject["with"] = withValue;
            dataObject["replace"] = replace;
          } else if (replace !== null) {
            dataObject["replace"] = replace;
          } else if (withValue !== null) {
            dataObject["with"] = withValue;
          }

          newArray.nodes.data.push(dataObject);
        }
      }
      if ($("#customSwitch1").prop("checked")) {
        var tempStatus = "on";
        newArray.temp_status = tempStatus; // Add "temp_status" to newArray
      }
      setSubmitData(newArray);
      setSubmitModal(1);
      setTimeout(function () {
        $("#customSwitch1").prop("checked", true);
        $(".template-name").removeClass("hidden");
        $("#template_checkbox_div").addClass("hidden");
        $("#pro_imp_start_btn").addClass("hidden");
        $(".template-save").removeClass("hidden");
        //$("#pro_imp_btn").removeAttr("disabled", "disabled");
      }, 200);
    }, 200);
  };
  const hideModal = () => {
    setDisplayedNodes(nodes.slice(0, chunkSize));
    setSubmitModal(0);
  };

  const [tempName, setTempName] = useState("");
  const [tempId, setTempId] = useState("");
  const [selectedCatalog, setSelectedCatalog] = useState([]);

  const handleSaveTemplate = (templateData) => {
    setTempName(templateData.name);
    setTempId(templateData.id);
    const newSelectedCatalogs = {};
    if (Array.isArray(templateData.channel_id)) {
      // It's an array
      templateData.channel_id.forEach((channelId) => {
        // iterate through channel IDs
        const channelOption = Object.entries(data.data_required.catalogs).find(
          ([key]) => key === channelId
        ); // find the channel option based on ID
        if (channelOption) {
          // if a channel option is found
          const [channelId, channelLabel] = channelOption; // destructure the channel option
          newSelectedCatalogs[channelId] = {
            // save the channel ID and label
            value: channelId,
            label: channelLabel,
          };
        }
      });
    } else {
      // It's a string
      const channelId = templateData.channel_id;
      const channelLabel = data.data_required.catalogs[channelId];
      if (channelLabel) {
        newSelectedCatalogs[channelId] = {
          value: channelId,
          label: channelLabel,
        };
      }
    }
    setSelectedCatalog(Object.values(newSelectedCatalogs));
  };
  return (
    <div id="add_row" className="relative">
      {isLoading ? (
        <div className="loader fixed left-0 top-0 bg-[#fff] flex justify-center items-center w-full h-full z-10"></div>
      ) : (
        ""
      )}

      <ToastContainer
        position="top-right"
        autoClose={2000}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme="light"
      />
      <div className="product-header pb-3">
        <div className="flex formStyle justify-between items-center flex-wrap">
          <div className="tab-list mr-5 pr-5 items-center">
            <button
              className=" btn btn-outline-secondary mr-4 reset_feilds"
              onClick={handleResetClick}
            >
              Reset
            </button>
            <button
              className="btn btn-outline-danger "
              onClick={handleSaveTemplateClick}
            >
              Save Template
            </button>
          </div>

          <div className="heading flex items-center">
            <div className="page-title text-center">
              <h2 className="font-24 block">Mapping Products</h2>
              <p className="block">Mapping convertion of all your products</p>
            </div>
          </div>
          <div className="flex items-center">
            <div className="warning-block">
              {data.data_required.template_method_type === "import" ? (
                <p id="handle_warning" className="text-danger mb-0">
                  <i className="fa fa-exclamation-triangle mr-2"></i>
                  <span id="handle_warningtext"></span>
                </p>
              ) : (
                ""
              )}
              <p className="mb-0 clr-grey row-count">
                You have {rowCount} /{totalRowCount} unmapped columns
              </p>
              <p className="mb-0 text-green-700 all-mapped hidden font-bold">
                All rows are Mapped now!
              </p>
              <div className="mb-0 d-flex align-items-center clr-grey proceed-data-switch">
                <div className="form-check form-switch d-inline" id="draft">
                  <input
                    name="ignore_unmapped"
                    id="ignore_unmapped"
                    type="checkbox"
                    className="custom-control-input form-check-input mt-1"
                  />
                  <label
                    className="custom-control-label custom-label ml-2 mt-1"
                    htmlFor="ignore_unmapped"
                  >
                    <span className="ignore_unchecked">
                      Don't Skip Unmatched columns
                    </span>
                    <span className="ignore_checked hidden">
                      Skip Unmatched columns
                    </span>
                  </label>
                </div>
              </div>
            </div>
            <div className="ml-4 items-center">
              <button
                id="pro_imp_btn"
                className="form-control ripplelink px-5 btn btn-primary mr-4 "
                title="If this button is disabled then please make sure all your unmapped fields are mapped or you can mark the side check box."
                data-bs-toggle="tooltip"
                data-bs-placement="top"
                onClick={handleNextClick}
              >
                Next
              </button>
            </div>
          </div>
        </div>
      </div>
      <hr className="mb-3 block" />

      <List
        height={600} // Adjust height based on your needs
        itemCount={nodes.length}
        itemSize={35} // Adjust item height based on your component's height
        width={"100%"}
      >
        {({ index, style }) => (
          <div className="mapping-container" style={style}>
            <MappingForm
              //key={nodesRedux.id}
              data={data}
              index={index}
              nodes={nodesRedux}
            />
          </div>
        )}
      </List>

      {submitModal === 0 ? (
        ""
      ) : (
        <SubmitModal
          hideModal={hideModal}
          data={data}
          submitData={submitData}
          tempName={tempName}
          setTempName={setTempName}
          tempId={tempId}
          setTempId={setTempId}
          selectedCatalog={selectedCatalog}
          setSelectedCatalog={setSelectedCatalog}
          onSaveTemplate={handleSaveTemplate}
        />
      )}

      <button
        className="add_new_row cursor-pointer px-5 btn btn-outline-primary"
        onClick={handleAddNewNode}
        disabled={disableAddNewRow}
      >
        <i className="fa fa-plus-circle mr-2"></i>
        Add New Row
      </button>
      {disableAddNewRow ? (
        <div className="row-limit-reached w-[220px] mt-2">
          <p className="text-danger">
            You have reached the maximum limit of 300 rows. Please delete some
            rows to add new rows.
          </p>
        </div>
      ) : (
        ""
      )}
      <div
        className="modal"
        id="delete-item"
        data-bs-backdrop="static"
        data-bs-keyboard="false"
        tabIndex="-1"
        aria-labelledby="exampleModalLabel"
        aria-modal="true"
        role="dialog"
      >
        <div className="modal-dialog modal-dialog-centered">
          <div className="modal-content">
            <div className="modal-body">
              <div className="text-4xl text-center text-danger">
                Deleting.....
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MappingModule;
const container = document.getElementById("importmapping");
if (container) {
  const root = createRoot(container);

  root.render(
    <React.StrictMode>
      <Provider store={store}>
        <MappingModule data={data} />
      </Provider>
    </React.StrictMode>
  );
} else {
  console.error("Container element with id 'importmapping' not found.");
}
