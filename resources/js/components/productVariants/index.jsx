import ReactDOM from 'react-dom/client';
import Step1 from './step-1';
import { useState, useEffect, useCallback  } from 'react';
import axios from 'axios';
import { ToastContainer, toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
export default function Variants({data}) {
    const [editableData, setEditableData] = useState(data);
    const [variantData, setVariantData] = useState(data);
    const [variantOptions, setVariantOptions] = useState([]);
    const [seeMoreClicked, setSeeMoreClicked] = useState(false);
    const [bulkDeleteActive, setBulkDeleteActive] = useState(false);
    const [checkedItems, setCheckedItems] = useState(new Set());
    const [variantToDisplay, setVariantToDisplay] = useState(3)
    const [changedRows, setChangedRows] = useState([]);
    const [refreshFlag, setRefreshFlag] = useState(false);

    const totalVariants = variantData.length;
       const urlString = window.location.href;

// Create a new URL object from the urlString
const url = new URL(urlString);

// Split the pathname part of the URL by "/" and filter out empty strings
const pathSegments = url.pathname.split('/').filter(Boolean);

// Assuming the structure is always /products/{product_id}/edit/{version_id}
const prodId = pathSegments[1];
const versionId = pathSegments[3];


      const handleInputChange = (e, index, field) => {
        const updatedEditableData = [...editableData];
        updatedEditableData[index][field] = e.target.value;
          setEditableData(updatedEditableData);
          setVariantData(updatedEditableData);
        //   console.log(editableData);


          const changedRow = { ...updatedEditableData[index], index };
        setChangedRows([...changedRows.filter(row => row.index !== index), changedRow]);
    };
    var jsonData = JSON.stringify(editableData);
    console.log(jsonData);

     const handleToggleSeeMore = (e) => {
         setSeeMoreClicked(!seeMoreClicked);
         if (seeMoreClicked) {
             setVariantToDisplay(3);
         } else {
             setVariantToDisplay(totalVariants);
         }
    };
     const handleCheckbox = (event, id) => {
        const newCheckedItems = new Set(checkedItems);
        if (event.target.checked) {
            newCheckedItems.add(id);
        } else {
            newCheckedItems.delete(id);
        }
        setCheckedItems(newCheckedItems);
        setBulkDeleteActive(newCheckedItems.size > 0);
    };
    const handleSelectAll = (event) => {
        if (event.target.checked) {
            const allItems = variantData.slice(0, seeMoreClicked ? variantData.length : 3).map(item => item.id);
            setCheckedItems(new Set(allItems));
            setBulkDeleteActive(true);
        } else {
            setCheckedItems(new Set());
            setBulkDeleteActive(false);
        }
    };


    useEffect(() => {
        // Set up the CSRF token for Axios
        axios.defaults.headers.common["X-CSRF-TOKEN"] = document.querySelector('meta[name="csrf-token"]').getAttribute("content");

        // Axios request
        axios.get(`/api/product/${prodId}/versions/${versionId}/variants`)
            .then(response => {
                setVariantData(response.data.data.variantsData);
                console.log('variant', variantData);
                // console.log( "data",data)
            })
            .catch(error => console.log(error));

        // Dependency array includes prodId and versionId
    }, [prodId, versionId, refreshFlag]);

 const handleDeleteSelected = async () => {
    // Convert the Set of checked items to an array
    const idsToDelete = Array.from(checkedItems);

    try {
        // Send DELETE request to your backend endpoint with the IDs
        // Adjust the URL to match your API endpoint structure

        axios.delete('/api/product/variants/delete',{ data: { variantIds: idsToDelete } })
            .then(response => {
                    // Handle response (e.g., refresh the list, show a success message)
                toast.success('Variants Deleted Succesfully!'); // Log or process the response as needed
     window.location.reload();
        // Optionally, refresh the variantData from the server or remove the deleted items from the state directly
        setVariantData(variantData.filter(item => !checkedItems.has(item.id)));
        setCheckedItems(new Set()); // Clear the selection
        setBulkDeleteActive(false); // Disable the bulk delete mode
            })
            .catch(error => console.log(error));

    } catch (error) {
        toast.error("Failed to delete variants");
        // Handle error (e.g., show an error message)
    }
};

    const handleDeleteSingle = async (variantId) => {
    try {
        // Wrap the variantId in an array
        const dataToSend = { variantIds: [variantId] };

        // Use Axios config to send DELETE request with the body
        const response = await axios.delete(`/api/product/variants/delete`, {
            data: dataToSend,
            headers: {
                'Content-Type': 'application/json'
            }
        });
            setVariantData(variantData.filter(item => !checkedItems.has(item.id)));
        // Remove the deleted variant from the variantData state
        toast.success('Variant Deleted Succesfully!');
        window.location.reload();
    } catch (error) {
        console.error("Failed to delete variant:", error);
        // Handle error
    }
};


    const refreshVariants = useCallback(() => {
  console.log('asdasd');
  setRefreshFlag(prev => !prev);
}, [setRefreshFlag]);



    return (
        <div className="position-relative" style={{ paddingBottom: variantData.length > 0 ? "50px" : "0px" }}>
            <ToastContainer
                position="top-right"
                autoClose={2000}
                hideProgressBar={false}
                newestOnTop={false}
                closeOnClick
                rtl={false}
                pauseOnFocusLoss
                draggable
                pauseOnHover
                theme="light"
            />

            {variantData.length === 1 ? (
                <>

                    <label className="d-flex align-items-end">Variants</label>
                    <div className=" col-8" style={{ height: "200px" }}>
                        <div className="border rounded col-12 h-50 d-flex align-items-center">
                            <div className="p-3">
                                {/* <p className='mb-1'>Already added variants:<b>{totalVariants}</b></p> */}
                                <a type="button" className="mb-2" data-bs-toggle="modal" data-bs-target="#exampleModal">
                                    + Create Variants
                                </a>
                                <small className="mb-0 d-block text-danger">
                                    You can add max <b>100</b> variants for each product.
                                </small>
                            </div>
                        </div>
                    </div>

                </>
            ) : (
                <>
                    <label className="d-flex align-items-end">Variants</label>
                    <div className="d-flex justify-content-between align-items-center">
                        <div>
                            <p className="mb-0">
                                You can add max <b>100</b> variants for each products.
                            </p>
                            <p className="mb-0">
                                Already added variants:<b>{totalVariants}</b>
                            </p>
                        </div>

                        <div className="d-flex align-items-center gap-2">
                            {bulkDeleteActive ? (
                                <button type="button" className="btn btn-outline-danger" onClick={handleDeleteSelected}>
                                    Delete
                                </button>
                            ) : (
                                ""
                            )}

                            <button
                                type="button"
                                className="btn btn-outline-primary ms-2"
                                data-bs-toggle="modal"
                                data-bs-target="#exampleModal"
                            >
                                Select Options
                            </button>
                        </div>
                    </div>


                    <div className="table-responsive mt-2" style={{ maxHeight: seeMoreClicked ? "auto" : "400px", overflowY: "auto" }}>
                        <table className="table table-responsive">
                            <thead>
                                <tr>
                                    <th>
                                        <input
                                            type="checkbox"
                                            onChange={handleSelectAll}
                                            checked={checkedItems.size === variantToDisplay}
                                        />
                                    </th>
                                    <th>Image</th>
                                    <th>SKU</th>
                                    <th>Name</th>
                                    <th>UPC/EAN/ISBN</th>
                                    <th>Price</th>
                                    <th>Compare at Price</th>
                                    <th>Cost Price</th>
                                    <th>Weight</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                {variantData.slice(0, seeMoreClicked ? variantData.length : 3).map((item, index) => (
                                    <tr key={item.id}>
                                        <td>
                                            <input
                                                type="checkbox"
                                                id="item-id"
                                                checked={checkedItems.has(item.id)}
                                                onChange={(e) => handleCheckbox(e, item.id)}
                                            />
                                        </td>

                                        <td>
                                            <img
                                                src={item.file && item.file.link ? item.file.link : "/img/apimio_default.jpg"}
                                                alt=""
                                                style={{ width: "40px" }}
                                            />
                                        </td>
                                        <td>
                                            <input
                                                type="text"
                                                style={{ width: "100px" }}
                                                value={item.sku}
                                                name="variant_sku[]"
                                                onChange={(e) => handleInputChange(e, index, "sku")}
                                            />
                                        </td>
                                        <td>
                                            <input
                                                type="text"
                                                style={{ width: "100px" }}
                                                value={item.name}
                                                name="name[]"
                                                onChange={(e) => handleInputChange(e, index, "name")}
                                            />
                                        </td>


                                        <td>
                                            <input
                                                type="text"
                                                style={{ width: "100px" }}
                                                value={item.barcode}
                                                placeholder="e.g 5555"
                                                name="barcode[]"
                                                onChange={(e) => handleInputChange(e, index, "barcode")}
                                            />
                                        </td>
                                        <td>
                                            <input
                                                type="text"
                                                style={{ width: "100px" }}
                                                value={item.price}
                                                placeholder="e.g $42"
                                                name="price[]"
                                                onChange={(e) => handleInputChange(e, index, "price")}
                                            />
                                        </td>
                                        <td>
                                            <input
                                                type="text"
                                                style={{ width: "100px" }}
                                                value={item.compare_at_price}
                                                placeholder="e.g $42"
                                                name="cost_price[]"
                                                onChange={(e) => handleInputChange(e, index, "compare_at_price")}
                                            />
                                        </td>
                                        <td>
                                            <input
                                                type="text"
                                                style={{ width: "100px" }}
                                                value={item.cost_price}
                                                placeholder="e.g $42"
                                                name="cost_price[]"
                                                onChange={(e) => handleInputChange(e, index, "cost_price")}
                                            />
                                        </td>
                                        <td>
                                            <input
                                                type="text"
                                                style={{ width: "100px" }}
                                                value={item.weight}
                                                name="weight[]"
                                                onChange={(e) => handleInputChange(e, index, "weight")}
                                            />
                                        </td>
                                        <td className="d-flex align-items-center justify-content-center">
                                            <a
                                                href=""
                                                onClick={(e) => {
                                                    e.preventDefault();
                                                    handleDeleteSingle([item.id]);
                                                }}
                                            >
                                                <i className="fa-regular fa-trash-can fs-20 text-danger" aria-hidden="true"></i>
                                            </a>
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>

                        <div className="d-flex justify-content-center">
                            {variantData.length > 3 && (
                                <div
                                    className="position-absolute bottom-0 w-100 text-center"
                                    style={{ background: "rgba(255, 255, 255, 0.8)", paddingBottom: "10px" }}
                                >
                                    <a onClick={handleToggleSeeMore} className="btn btn-link">
                                        {seeMoreClicked ? "See Less" : `See (${totalVariants - 3}) More`}
                                    </a>
                                </div>
                            )}
                        </div>
                        <input type="hidden" value={jsonData} name="variants" />
                    </div>
                </>
            )}


            <Step1
                variantOptions={variantOptions}
                setVariantOptions={setVariantOptions}
                versionId={versionId}
                prodId={prodId}
                refreshVariants={refreshVariants}
            />
            {/* <button onClick={(e) => {
    e.preventDefault(); // This will prevent the default button click action
    setRefreshFlag(prev => !prev);
}}>Test Refresh</button> */}
        </div>
    );
}
const rootElement = document.getElementById('product-variants');

ReactDOM.createRoot(rootElement).render(
    <Variants  data = {data}/>
);
