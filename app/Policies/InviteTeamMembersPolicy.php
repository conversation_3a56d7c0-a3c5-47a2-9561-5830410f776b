<?php

namespace App\Policies;

use App\Models\Invite\Invite;
use App\Models\Organization\Organization;
use App\Models\Organization\OrganizationUser;
use App\Models\Organization\Plan;
use App\Models\Organization\TeamInvite;
use App\Models\Product\Version;
use App\User;
use Illuminate\Auth\Access\HandlesAuthorization;
use Illuminate\Support\Facades\Auth;

class InviteTeamMembersPolicy
{
    use HandlesAuthorization;

    /**
     * Create a new policy instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }


    /**
     * @param  \App\User  $user
     * @return mixed
     */
    public function createTeamMember(User $user)
    {
        $org = Organization::where('id',$user->organization_id)->first();
        if ($org)
        {
            //check if subscription exists
            if ($org->is_subscribed()) {
                //if subscription is from stripe
                if ($org->subscription_type() == 'stripe') {
                    $invites = TeamInvite::where('organization_id',$org->id)->count();
                    return $invites < $org->team_limit() ? $this->allow() : $this->deny("Limit reached, please upgrade billing");
                } else {
                    //if subscription from shopify
                    $invites = TeamInvite::where('organization_id',$org->id)->count();
                    return $invites < $org->team_limit() ? $this->allow() : $this->deny("Limit reached, please upgrade billing");
                }
            }
            else {
                $invite = new Invite();
                if ($invite->IsInvited(Auth::user()->email)) { //if user is invited and on free plan
                    $team_invites = TeamInvite::where('organization_id', $org->id)->count();
                    $no_of_teammates = Plan::where('handle', 'free_plan')->value('no_of_team_members');
                    return $team_invites < $no_of_teammates ? $this->allow() : $this->deny("Limit reached, please upgrade billing");
                } else { //if user is on trial
                    $invites = TeamInvite::where('organization_id', $org->id)->count();
                    $plan = Plan::where('handle', 'standard_plan')->value('no_of_team_members');
                    return $invites < $plan ? $this->allow() : $this->deny("Limit reached, please upgrade billing");
                }
            }
        }
        else
        {

            return $this->deny("Please add organization first.");
        }
    }
}
