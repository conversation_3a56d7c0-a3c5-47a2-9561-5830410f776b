<?php

namespace App\Policies;

use App\Models\Billing\ShopifySubscription;
use App\Models\Invite\Invite;
use App\Models\Organization\Organization;
use App\Models\Organization\Plan;
use App\Models\Product\Product;
use App\User;
use Illuminate\Auth\Access\HandlesAuthorization;
use Illuminate\Auth\Access\Response;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ProductPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any models.
     *
     * @param \App\User $user
     * @return mixed
     */
    public function viewAny(User $user)
    {
        //
    }

    /**
     * Determine whether the user can view the model.
     *
     * @param \App\User $user
     * @param \App\Models\Product\Product $product
     * @return mixed
     */
    public function view(User $user, Product $product)
    {
        //
    }

    /**
     * Determine whether the user can create models.
     *
     * @param \App\User $user
     * @return mixed
     */
    public function create(User $user)
    {
//        if (!App::environment('local')) {
        $org = Organization::where('id',$user->organization_id)->first();
        $invite = new Invite();
        if ($org) {
            //implementing check for strip
            if ($org->subscribed('default')) {
                $plan_id = DB::table('subscriptions')->where('organization_id',$org->id)->value('stripe_price');
                $no_of_prods = Plan::where('stripe_monthly_id',$plan_id)->orWhere('stripe_yearly_id',$plan_id)->value('no_of_products');
                return  Product::count_variant() < $no_of_prods ? Response::allow() : Response::deny('Product limit reached. Please upgrade your billing plan to start add more products.');
            }
            //implementing check for the shopify subscription
            elseif (Auth::user()->shopify_shop_id) {
                $price = ShopifySubscription::where('organization_id',Auth::user()->organization_id)->value('price');
                if ($price) {
                    $plan = Plan::where('price_per_month',$price)->orWhere('price_per_year',$price)->value('no_of_products');
                    return  Product::count_variant() < $plan ? Response::allow() : Response::deny('Product limit reached. Please upgrade your billing plan to start add more products.');
                    //if user have subscribed community plan while coming from shopify
                } else {
                    $prod_count = DB::table('plans')->where('handle', 'community_plan')->value('no_of_products');
                    return Product::count_variant() < $prod_count ? Response::allow() : Response::deny('Product limit reached. Please upgrade your billing plan to start add more products.');
                }
            }
            elseif($invite->IsInvited(Auth::user()->email)) { //if user is invited
                $prod_count = DB::table('plans')->where('handle', 'free_plan')->value('no_of_products');
                return Product::count_variant() < $prod_count ? Response::allow() : Response::deny('Product limit reached. Please upgrade your billing plan to start add more products.');
            }
            //if user haven't subscribed any plan yet
            else {
                $prod_count = DB::table('plans')->where('handle', 'standard_plan')->value('no_of_products');
                return Product::count_variant() < $prod_count ? Response::allow() : Response::deny('Product limit reached. Please upgrade your billing plan to start add more products.');
            }
        }
        else {
            return Response::deny('Please add organization first.');
        }

//        } else {
//            return Response::allow();
//        }
    }
    /**
     * Determine whether the user can update the model.
     *
     * @param \App\User $user
     * @param \App\Models\Product\Product $product
     * @return mixed
     */
    public function update(User $user, Product $product)
    {
        //
    }

    /**
     * Determine whether the user can delete the model.
     *
     * @param \App\User $user
     * @param \App\Models\Product\Product $product
     * @return mixed
     */
    public function delete(User $user, Product $product)
    {
        //
    }

    /**
     * Determine whether the user can restore the model.
     *
     * @param \App\User $user
     * @param \App\Models\Product\Product $product
     * @return mixed
     */
    public function restore(User $user, Product $product)
    {
    }

    /**
     * Determine whether the user can permanently delete the model.
     *
     * @param \App\User $user
     * @param \App\Models\Product\Product $product
     * @return mixed
     */
    public function forceDelete(User $user, Product $product)
    {
        //
    }

}
