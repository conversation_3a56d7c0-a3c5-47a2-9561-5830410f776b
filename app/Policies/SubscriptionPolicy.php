<?php

namespace App\Policies;

use App\User;
use App\Models\Product\Brand;
use App\Classes\Plan\PlanClass;
use App\Models\Channel\Channel;
use App\Models\Product\Variant;
use App\Models\Cashier\Subscription;
use Illuminate\Support\Facades\Auth;
use App\Models\Organization\Organization;
use App\Models\Billing\ShopifySubscription;

class SubscriptionPolicy
{
    protected $plan;

    protected $old_user = false;

    protected $organization;
    /**
     * Create a new policy instance.
     */
    public function __construct()
    {
        $user = Auth::user();
        $this->organization = Organization::firstWhere("id", $user->organization_id);
        $plans = PlanClass::getPlans();
        if(isset($this->organization->stripe_id)){
            if ($this->organization->plan_handle) {
                $this->plan = $plans->firstWhere('handle', $this->organization?->plan_handle);
            } else {
                $subscription = Subscription::with('items')->where('organization_id', $this->organization->id)->first();
                if ($subscription) {
                    if($subscription->stripe_price){
                        $this->plan = $plans->firstWhere('stripe_monthly_id', $subscription?->stripe_price);
                        if ($this->plan) {
                            $this->old_user = true;
                        }
                    }else{
                        foreach ($subscription->items as $key => $item) {
                            $this->plan = PlanClass::$plans->where('stripe_monthly_id', $item->stripe_price)->first();
                            if($this->plan){
                                 break;
                            }
                        }
                    }
                }
            }
        }else if(isset($user->shopify_shop_id)){
            $subscription = ShopifySubscription::where("organization_id",$this->organization->id)->latest('id')->first();
            if ($subscription) {
                $this->plan = $plans->firstWhere('handle', $subscription?->plan_handle);
            }
        }
    }

    /**
     * Determine if the given module is accessible by the organization.
     *
     * @param User $user
     * @param string $module
     * @return bool
     */
    public function access(User $user, string $module)
    {
        if(env('APP_ENV') == 'local') {
            return true;
        }

        if ($this->organization) {
            if ($this->organization->onTrial()) {
                return true;
            }
            if ($this->old_user && $this->organization->is_subscribed()) {
                return true;
            } else if ($this->plan && isset($this->plan->modules[$module]) && $this->organization->is_subscribed()) {
                $check = $this->plan->modules[$module] ?? false;
                if ($check || $this->old_user) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * Determine if the organization can create a product.
     *
     * @param User $user
     * @return bool
     */
    public function canCreateProduct(User $user, $model )
    {
        if(env('APP_ENV') == 'local') {
            return true;
        }
        $model = $model->whereHas("product",function($query){
            $query->where("organization_id",$this->organization->id);
        });
        if ($this->old_user && $this->organization->is_subscribed()) {
            return $this->checkLimit($model, 10000);
        } else if ($this->plan && $this->organization->is_subscribed()) {
            return $this->checkLimit($model, $this->plan->no_of_lang);
        }
        return $this->checkLimit($model, 1000);
    }

    /**
     * Determine if the organization can create a channel.
     *
     * @param User $user
     * @return bool
     */
    public function canCreateChannel(User $user, $model)
    {
        if(env('APP_ENV') == 'local') {
            return true;
        }
        if ($this->old_user && $this->organization->is_subscribed()) {
            return $this->checkLimit($model, 3);
        } else if ($this->plan && $this->organization->is_subscribed()) {
            return $this->checkLimit($model, $this->plan->no_of_channel);
        }
        return $this->checkLimit($model, 10000);
    }

    /**
     * Determine if the organization can create a brand.
     *
     * @param User $user
     * @return bool
     */
    public function canCreateBrand(User $user, $model)
    {
        if(env('APP_ENV') == 'local') {
            return true;
        }
        if ($this->old_user && $this->organization->is_subscribed()) {
            return $this->checkLimit($model, 1);
        } else if ($this->plan && $this->organization->is_subscribed()) {
            return $this->checkLimit($model, $this->plan->no_of_brand);
        }
        return $this->checkLimit($model, 10000);
    }

    /**
     * Determine if the organization can create a brand.
     *
     * @param User $user
     * @return bool
     */
    public function canCreateLanguage(User $user,  $model)
    {
        if(env('APP_ENV') == 'local') {
            return true;
        }
        if ($this->old_user && $this->organization->is_subscribed()) {
            return $this->checkLimit($model, 10000);
        } else if ($this->plan && $this->organization->is_subscribed()) {
            return $this->checkLimit($model, $this->plan->no_of_lang);
        }
        return $this->checkLimit($model, 10000);
    }

    /**
     * Check the limit for the given type.
     *
     * @param User $user
     * @param string $type
     * @param int $limit
     * @return bool
     */
    protected function checkLimit($model, int $limit)
    {
        if ($this->organization) {
            $currentCount = $model->count();
            return $currentCount < $limit;
        }
        return false;
    }
}
