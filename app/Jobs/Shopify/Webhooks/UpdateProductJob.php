<?php

namespace App\Jobs\Shopify\Webhooks;

use Illuminate\Bus\Queueable;
use App\Models\Product\Variant;
use App\Classes\Shopify\Metafield;
use Illuminate\Support\Facades\Log;
use App\Models\Channel\ChannelVariant;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use App\Models\Channel\ChannelFileProduct;
use App\Classes\Shopify\RetrieveCollection;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Ghazniali95\ShopifyConnector\App\Classes\Rest\Admin2023_10\Product as ShopifyProduct;

class UpdateProductJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $channel_id;

    public $organization_id;

    public $request;

    /**
     * Create a new job instance.
     */
    public function __construct($channel_id, $organization_id, $request)
    {
        $this->channel_id = $channel_id;
        $this->organization_id = $organization_id;
        $this->request = $request;

    }


    /**
     * Execute the job.
     */
    public function handle(): void
    {
        usleep(500000);
        $product = new ShopifyProduct();
        $product->initializeSession(["channel_id" => $this->channel_id]);
        $response = $product::find($product->session, $this->request['id']);

        if ($response) {
            foreach ($response->images as $key => $image) {
                $productChannel = ChannelFileProduct::where([
                    'channel_id' => $this->channel_id,
                    'store_connect_id' => $image->id,
                    'store_connect_type' => 'shopify'
                ])->first();
                if ($productChannel) {
                    $productChannel->delete();
                }
                $VariantChannel = ChannelVariant::where([
                    'channel_id' => $this->channel_id,
                    'store_connect_image_id' => $image->id,
                    'store_connect_type' => 'shopify'
                ])->first();
                if (isset($VariantChannel)) {
                    $VariantChannel->store_connect_image_id = null;
                    $VariantChannel->save();
                    $variant = Variant::find($VariantChannel->variant_id);
                    if ($variant) {
                        $variant->file_id = null;
                        $variant->save();
                    }
                }
            }
            // Directly assign the returned value from the store method to $product_id
            $product_id = (new \App\Classes\Shopify\StoreProduct($response, $this->channel_id, $this->organization_id))
                ->store(function ($obj) {
                    return $obj->id; // Ensure this is the ID you want
                });
        }

        // Ensure product_id is used after being potentially set
        if ($product_id !== null) {
            // for meta-fields and collection saving process
            $ids = [
                'shopify_product_id' => $this->request['id'],
                'product_id' => $product_id
            ];

            // Proceed with operations that depend on $product_id
            (new Metafield($this->channel_id, $this->organization_id))->getMetaFields($ids);
            $product_ids[] = $ids; // Assuming this is declared elsewhere
            (new RetrieveCollection($this->channel_id, $this->organization_id))->getCollectProduct($product_ids);
        } else {
            Log::channel('webhook')->error('Failed to create or retrieve product ID.');
        }
    }
}
