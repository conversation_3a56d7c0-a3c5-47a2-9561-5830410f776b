<?php

namespace App\Jobs\Shopify;

use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use App\Models\Product\Inventory;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use App\Models\Notification\ErrorLog;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Support\Facades\Log;

class UpdateInventoryChunksJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels , Batchable;

    public $location;
    public $payload_chunk;
    public $organization_id;
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($payload_chunk , $location , $organization_id  )
    {
        $this->payload_chunk = $payload_chunk;
        $this->location = $location;
        $this->organization_id = $organization_id;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
            if ($this->payload_chunk && is_array($this->payload_chunk)) {
                foreach ($this->payload_chunk as $inventory) {
                    $obj = Inventory::where('store_connect_id', $inventory->inventory_item_id)
                        ->where('organization_id', $this->organization_id);
                    if ($obj->where('location_id', null)->first()) {
                        $obj->update([
                            'location_id' => $this->location->id,
                            'available_quantity' => $inventory->available
                        ]);
                    }else if($inv_obj = Inventory::where('store_connect_id', $inventory->inventory_item_id)
                        ->where('organization_id', $this->organization_id)
                        ->where('location_id', $this->location->id)
                        ->first()){
                        $inv_obj->update([
                            'available_quantity' => $inventory->available
                        ]);
                    }
                    else {
                        $inventory_obj = Inventory::where('store_connect_id', $inventory->inventory_item_id)
                            ->where('organization_id', $this->organization_id)
                            ->first();
                        if ($inventory_obj) {
                            Inventory::create([
                                'product_id' => $inventory_obj->product_id,
                                'organization_id' => $inventory_obj->organization_id,
                                'variant_id' => $inventory_obj->variant_id,
                                'location_id' => $this->location->id,
                                'store_type' => "shopify",
                                'store_connect_id' => $inventory->inventory_item_id,
                                'available_quantity' => $inventory->available,
                            ]);
                        }
                    }
                }
            }
        } catch (\Exception $e) {
            Log::channel('shopify')->info('There is an error while updating inventories.<br> '.$e->getMessage());

        }
    }
}
