<?php

namespace App\Jobs\PreviousData;

use App\Models\Channel\ChannelFileProduct;
use App\Models\Product\FileProduct;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class ImagesData implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $file_products =  FileProduct::query()
            ->with('channelProduct.shopify_status')
            ->whereHas('channelProduct.shopify_status', function ($query) {
                $query->whereColumn('file_product.updated_at', '<=', 'channel_product_statuses.updated_at');
            })
            ->get();
       // dd($file_products);


        foreach($file_products as $fp){

            $cfp = ChannelFileProduct::query()->where([
                'file_product_id'=>$fp->id,
                'store_connect_type'=>'shopify',
                'channel_id'=>$fp->channelProduct->channel_id
            ])
                ->first();
            if(!$cfp){
                $cfp = new ChannelFileProduct();
                $cfp->channel_id =$fp->channelProduct->channel_id;
                $cfp->file_product_id  =$fp->id;
                $cfp->store_connect_type ='shopify';
                $cfp->save();
            }
        }
        return 'done';
    }
}
