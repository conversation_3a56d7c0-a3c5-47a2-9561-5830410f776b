<?php

namespace App\Jobs\PreviousData;

use App\Models\Channel\AttributeChannel;
use App\Models\Channel\Channel;
use App\Models\Organization\Organization;
use App\Models\Product\Attribute;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class SingleStoreOrganizationMetafields implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $organization_ids = Organization::query()
            ->withoutGlobalScopes()
            ->has('channels','=',1)
            ->get()
            ->pluck('id')
            ->toArray() ;

        $channels = Channel::has('shopify_channels')->withoutGlobalScopes()->whereIn('organization_id',$organization_ids)->get();

        foreach($channels as $channel){
            $attributes = Attribute::withoutGlobalScopes()
                ->where('organization_id',$channel->organization_id)
                ->whereHas('families',function($q){
                    $q->where('is_default',0);
                })
                ->get();

            foreach ($attributes as $attribute) {

                $rules = json_decode($attribute->rules, true);

                if(!empty($rules)){
                    $attr_channel = new AttributeChannel();
                    $attr_channel->channel_id = $channel->id;
                    $attr_channel->attribute_id = $attribute->id;
                    $attr_channel->store_connect_type = 'shopify';
                    $attr_channel->store_connect_id = $rules['shopify_id']??null;
                    $attr_channel->save();
                }

            }
        }
    }
}
