<?php

namespace App\View\Components\Graphs;

use App\Models\Organization\File;
use Illuminate\View\Component;

class MediaStats extends Component
{

    public $total_files = 0;
    public $file_stats = 0;

    /**
     * Create a new component instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Get the view / contents that represent the component.
     *
     * @return \Illuminate\View\View|string
     */
    public function render()
    {
        $files = new File();
        $this->file_stats =  $files->get_quality_stats();
        if($this->file_stats["approve"] == 0 && $this->file_stats["warning"] == 0 && $this->file_stats["error"] == 0) {
            $this->file_stats["approve"] = 0;
            $this->file_stats["warning"] = 0;
            $this->file_stats["error"] = 0;
        }

        $this->total_files = $files->count();

        return view('components.graphs.media-stats');
    }
}
