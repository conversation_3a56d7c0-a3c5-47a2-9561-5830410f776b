<?php

namespace App\View\Components\Products;

use App\Models\Product\AttributeFamily;
use App\Models\Product\AttributeFamilyProductVersion;
use App\Models\Product\Family;
use App\Models\Product\ProductVersion;
use Illuminate\View\Component;

class EditProductAttributeSetWidget extends Component
{
    public $version;
    public $family;
    public $product;
    public $attributes_data;

    /**
     * Create a new component instance.
     *
     * @return void
     */
    public function __construct($version, $family, $product = null)
    {
        $this->family = $family;
        $this->version = $version;
        $this->product = $product;

        foreach ($this->family->attributes as  $attribute) {
            if ($this->product) {
                $product_version_id = ProductVersion::where('version_id',$this->version->id)->where('product_id',$product->id)->pluck('id')->first();
                $attribute_family_id = AttributeFamily::where('family_id',$this->family->id)->where('attribute_id',$attribute->id)->pluck('id')->first();
                $attribute->value = AttributeFamilyProductVersion::where('product_version_id', $product_version_id)->where('attribute_family_id', $attribute_family_id)->get()->toArray();

            } else {
                $attribute->value = [
                    "value" => $attribute->value,
                    "unit" => $attribute->unit,
                ];
            }
        }

    }

    /**
     * Get the view / contents that represent the component.
     *
     * @return \Illuminate\View\View|string
     */
    public function render()
    {
        return view('components.products.edit-product-attribute-set-widget');
    }
}
