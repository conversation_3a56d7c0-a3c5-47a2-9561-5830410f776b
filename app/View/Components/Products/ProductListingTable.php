<?php

namespace App\View\Components\products;

use Apimio\Completeness\Classes\CompletenessMethods;
use App\Models\Channel\Channel;
use App\Models\Invite\Invite;
use App\Models\Product\Attribute;
use App\Models\Product\AttributeFamily;
use App\Models\Product\AttributeFamilyProductVersion;
use App\Models\Product\Brand;
use App\Models\Product\Category;
use App\Models\Product\Family;
use App\Models\Product\Product;
use App\Models\Product\Vendor;
use App\View\Components\CompletenessScore;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\View\Component;
use Illuminate\View\View;

class ProductListingTable extends Component
{
    public $products;
    public
        $request,
        $families,
        $vendors,
        $brands,
        $categories,
        $filter,
        $limit,
        $sync,
        $isdelete,
        $isaction,
        $total_products,
        $multiselect,
        $orgId,
        $isbulkable,
        $channels;

    /**
     * Create a new component instance.
     *
     * @return void
     */
    public function __construct(Request $request, $products = null, $filter = true, $limit = null , $sync=false ,$isdelete = false ,$isaction = true , $multiselect = false, $orgId = null , $isbulkable = false)
    {
        $this->limit = $limit;
        $this->products = $products;
        $this->request = $request;
        $this->families = Family::all();
        $this->vendors = Invite::where('type','vendor')->get();
        $this->brands = Brand::all();
        $this->categories = Category::all();
        $this->filter = $filter;
        $this->multiselect = $multiselect;
        $this->sync = $sync;
        $this->isdelete = $isdelete;
        $this->isaction = (bool) $isaction;
        $this->orgId = $orgId ?? auth()->user()->organization_id;
        $this->isbulkable = $isbulkable;
        $this->channels = Channel::where('organization_id', $this->orgId)->get();

    }

    /**
     * Get the view / contents that represent the component.
     *
     * @return View|string
     */
    public function render(): string|View
    {
        $this->products = Product::query();

        // Search product with channel
        if($this->request->filled("channels")) {
            $this->products = $this->products->whereHas("channels", function($q) {
                return $q->whereIn('channels.id', $this->request->get("channels"));
            });
        }

        // Search product with vendors
        if($this->request->filled("vendors")) {
            $this->products = $this->products->whereHas("invites", function ($query) {
                $query->whereIn("invites.id", $this->request->get("vendors"));
            });
        }

        // Search product with brands
        if($this->request->filled("brands")) {
            $this->products = $this->products->whereHas("brands", function ($query) {
                $query->whereIn("brands.id", $this->request->get("brands"));
            });
        }

        // Search product with categories
        if($this->request->filled("categories")) {
            $this->products = $this->products->whereHas("categories", function ($query) {
                $query->whereIn("categories.id", $this->request->get("categories"));
            });
        }

        // Search product with sku or name
        if($this->request->filled("q")) {
            $searchTerm = $this->request->get("q");
            $this->products = $this->products->where(function ($query) use ($searchTerm) {
                $query->where("sku", "LIKE", "%" . $searchTerm . "%")
                    ->orWhereHas("variants", function ($query) use ($searchTerm) {
                        $query->where("sku", "LIKE", "%" . $searchTerm . "%")
                            ->orWhere("name", "LIKE", "%" . $searchTerm . "%");
                    })
                    ->orWhereHas("values", function ($query) use ($searchTerm) {
                        $query->where("value", "LIKE", "%" . $searchTerm . "%");
                    });
            });
        }

        // search with tags
        if($this->request->filled("tags")) {
            $this->products = $this->products->whereHas("values", function ($query) {
                $tags = explode(',',$this->request->get("tags"));
                $query->where("value", "LIKE", "%".implode('%',$tags)."%");
            });
        }

        // Search according to product status
        if($this->request->filled("status")) {
            $this->products->where("status", $this->request->get("status"));
        }

        // search according to shopify status
        if($this->request->filled('sync_status')) {
            if($this->request->get("sync_status") == 'update_available') {
                $this->products->updateAvailable($this->request->get('channels'));
            }
            else {
                $this->products->synced($this->request->get('channels'));
            }
        }

        // Search according to product score
        if($this->request->filled("score")) {
            if($this->request->get("score") == "good") {
                $this->products->whereHas("versions", function ($model) {
                    $model->where('product_version.score', '>', 90);
                });
            }

            if($this->request->get("score") == "fair") {
                $this->products->whereHas("versions", function ($model) {
                    $model->whereBetween('product_version.score', [50,90]);
                });
            }

            if($this->request->get("score") == "bad") {
                $this->products->whereHas("versions", function ($model) {
                    $model->where('product_version.score', '<', 50);
                });
            }
            if($this->request->get("score") == "highest") {
                $this->products = $this->products->join('product_version', 'products.id', '=', 'product_version.product_id')
                ->orderBy('product_version.score', 'desc')
                ->select('products.*');
            }
            if($this->request->get("score") == "lowest") {
                $this->products->join('product_version', 'products.id', '=', 'product_version.product_id')
                ->orderBy('product_version.score', 'asc')
                ->select('products.*');
            }
        }

        // Order result to get latest at top
        $this->products = $this->products->orderByDesc("id");

        // Paginate total products
        $this->products = $this->products->paginate($this->limit ?? ($this->request->filled('product_list')?$this->request->product_list:32));

        return view('components.products.product-listing-table');
    }
}
