<?php

namespace App\View\Components\Products;

use Illuminate\View\Component;

class EditProductAttributeStatusDot extends Component
{
    public $id;

    /**
     * Create a new component instance.
     *
     * @return void
     */
    public function __construct($id = null)
    {
        $this->id = $id;
    }

    /**
     * Get the view / contents that represent the component.
     *
     * @return \Illuminate\View\View|string
     */
    public function render()
    {
        return view('components.products.edit-product-attribute-status-dot');
    }
}
