<?php

namespace App\View\Components\Products\bulk;

use App\Models\Channel\Channel;
use App\Models\Invite\Invite;
use App\Models\Product\Brand;
use App\Models\Product\Category;
use App\Models\Product\Family;
use App\Models\Product\Version;
use Illuminate\Support\Facades\DB;
use Illuminate\View\Component;

class BulkAssignModal extends Component
{

    /**
     * Create a new component instance.
     *
     * @return void
     */
    public function __construct()
    {

    }


    /**
     * Get the view / contents that represent the component.
     *
     * @return \Illuminate\Contracts\View\View|\Closure|string
     */
    public function render()
    {


        return view('components.products.bulk.bulk-assign-modal');
    }
}
