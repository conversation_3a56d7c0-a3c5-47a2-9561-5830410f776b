<?php

namespace App\View\Components\General;

use Illuminate\View\Component;

class EmptyTable extends Component
{
    public $btnName;
    public $descriptions;
    /**
     * Create a new component instance.
     *
     * @return void
     */
    public function __construct($btnName, $descriptions)
    {
        $this->btnName = $btnName;
        $this->descriptions = $descriptions;
    }

    /**
     * Get the view / contents that represent the component.
     *
     * @return \Illuminate\View\View|string
     */
    public function render()
    {
        return view('components.general.empty-table');
    }
}
