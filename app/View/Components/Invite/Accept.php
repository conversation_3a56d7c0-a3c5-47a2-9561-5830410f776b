<?php

namespace App\View\Components\invite;

use App\Models\Channel\Channel;
use Illuminate\View\Component;

class accept extends Component
{
    public $text;
    public $title;
    public $button;
    public $account;
    public $channels;
    /**
     * Create a new component instance.
     *
     * @return void
     */
    public function __construct($text , $title , $button,$account)
    {
        $this->channels = Channel::orderBy("id", "DESC")->get();
        $this->text = $text;
        $this->title = $title;
        $this->button = $button;
        $this->account = $account;
    }

    /**
     * Get the view / contents that represent the component.
     *
     * @return \Illuminate\View\View|string
     */
    public function render()
    {
        return view('components.invite.accept');
    }
}
