<?php

namespace App\View\Components\Attribute;

use Illuminate\View\Component;

class Measurement extends Component
{
    public $type;
    public $unit;
    /**
     * Create a new component instance.
     *
     * @return void
     */
    public function __construct($type, $unit = null)
    {
        $this->type = $type;
        $this->unit = $unit;
    }

    /**
     * Get the view / contents that represent the component.
     *
     * @return \Illuminate\View\View|string
     */
    public function render()
    {
        return view('components.attribute.measurement');
    }
}
