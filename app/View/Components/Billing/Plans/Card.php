<?php

namespace App\View\Components\Billing\Plans;

use Closure;
use App\Models\BrandsPortal;
use App\Models\Product\Brand;
use Illuminate\View\Component;
use App\Classes\Plan\PlanClass;
use App\Models\Channel\Channel;
use App\Models\Product\Version;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Facades\Auth;
use App\Models\Channel\ShopifyChannel;
use App\Models\Organization\Organization;
use App\Models\Billing\ShopifySubscription;
use Apimio\MappingConnectorPackage\models\Template;

class Card extends Component
{

    public $plans;
    public $organization_id;
    public $subscribedPlan;
    public $user;
    public $channel;
    public $isBasicRange;
    /**
     * Create a new component instance.
     */
    public function __construct( $plans = null,$organization_id = null, $subscribedPlan = null, $user = null ,$channel = null, $isBasicRange = true)
    {
        $this->plans = $plans ?? PlanClass::$plans;

        $this->user = Auth::user();

        $this->isBasicRange = $isBasicRange;

        $organization = Organization::where('id',Auth::user()->organization_id)->firstOrFail();
        if($organization){
            $this->organization_id = $organization_id ?? $organization->id;
            $this->subscribedPlan = $subscribedPlan ?? PlanClass::$plans->where('handle', $organization->plan_handle)->first();
        }else{
            $this->organization_id = $organization_id;
            $this->subscribedPlan = $subscribedPlan;
        }

        if(isset($this->user->shopify_shop_id)){
            $this->channel = ShopifyChannel::where('shop_id', $organization->shop_id)->first();
            if(!$this->channel){
                $this->channel = Channel::has("shopify_channels")->first();
                if($this->channel){
                    $this->channel = ShopifyChannel::where('channel_id', $this->channel->id)->first();
                }
            }
            $plan_handle = ShopifySubscription::where('organization_id',  $this->user->organization_id)->latest('id')->value('plan_handle');
            $this->subscribedPlan = $subscribedPlan ?? PlanClass::$plans->where('handle', $plan_handle)->first();
         }

         $channelCount = Channel::count();
         $brandlCount = BrandsPortal::count();
         $languagelCount = Version::count();
         $templatelCount = Template::count();

         if($channelCount > 1 || $brandlCount > 0 || $languagelCount > 1 || $templatelCount > 0){
            $this->isBasicRange = false;
         }


    }
    /**
     * Get the view / contents that represent the component.
     */
    public function render(): View|Closure|string
    {
        return view('components.billing.plans.card');
    }
}
