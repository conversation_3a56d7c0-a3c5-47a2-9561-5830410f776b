<?php

namespace App\Providers;

use App\Events\BatchProgressEvent;
use Illuminate\Bus\Events\BatchDispatched;
use Illuminate\Queue\Events\JobProcessed;
use Illuminate\Queue\Events\JobProcessing;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\ServiceProvider;
use mysql_xdevapi\Warning;

class JobEventServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     *
     * @return void
     */
    public function register()
    {
        //
    }

    /**
     * Bootstrap services.
     *
     * @return void
     */
    public function boot()
    {
/*        Event::listen(BatchDispatched::class, function ($event) {
           // Log::error(($event));
        });*/

/*        Event::listen(JobProcessing::class, function ($event) {
            $payload = $event->job->payload();
            $serializedCommand = $payload['data']['command'] ?? null;

            if ($serializedCommand) {
                $command = unserialize($serializedCommand);
            } else {
                $command = null;
            }

            if ($command) {
                $batch_id = $command->batchId ?? null;
            } else {
                $batch_id = null;
            }

            if ($batch_id) {
                event(new BatchProgressEvent($batch_id));
            } else {
            }
          //  Log::error(($event));
        });

        Event::listen(JobProcessed::class, function ($event) {
            // Handle job processed event
            $payload = $event->job->payload();
            $serializedCommand = $payload['data']['command'] ?? null;

            if ($serializedCommand) {
                $command = unserialize($serializedCommand);
            } else {
                $command = null;
            }

            if ($command) {
                $batch_id = $command->batchId ?? null;
            } else {
                $batch_id = null;
            }

            if ($batch_id) {
                event(new BatchProgressEvent($batch_id));
            } else {
                // Handle cases where the batch ID is not available
            }
        });*/

//        Event::listen(JobFailed::class, function ($event) {
//            // Handle job failed event
//            $job = $event->job;
//            $exception = $event->exception;
//        });
    }
}
