<?php

namespace App\Traits\Billing;

use Stripe\Subscription;
use App\Models\Invite\Invite;
use App\Classes\Plan\PlanClass;
use App\Models\Channel\Channel;
use App\Models\Product\Product;
use App\Models\Organization\Plan;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use App\Models\Organization\Organization;
use App\Models\Billing\ShopifySubscription;
use phpDocumentor\Reflection\Types\Integer;

Trait LimitTrait {

    private $plan;
    private $shopify;
    private $product;
    private $catalog;

    public function as_subscription() {
        $stripe_id = DB::table('subscriptions')->where('organization_id',$this->id)->value('stripe_price');
        if (!$stripe_id) { //if user is on shopify
            $plan_id = ShopifySubscription::where('organization_id',$this->id)->value('plan_handle');
            $this->plan = PlanClass::$plans->where('handle',$plan_id)->first();
        } else { //if user is on stripe
            $this->plan = PlanClass::$plans->where('stripe_monthly_id',$stripe_id)->first();
        }
        if (!$this->plan) {
            $invite = new Invite();
            if (Auth::check() && $invite->isInvited(Auth::user()->email)) { //if user is invited
                $this->plan = PlanClass::$plans->where('handle','free_plan')->first();
            } else { //if user is on trial
                $this->plan = PlanClass::$plans->where('handle','standard_plan')->first();
            }
        }
        return $this;
    }

    public function as_product() {
        $this->product = PlanClass::$plans->where('name',$this->plan->name)->first();
        return $this;
    }


    public function as_catalog() {
        $this->catalog = PlanClass::$plans->where('name',$this->plan->name)->first();;
        return $this;
    }

    public function limit_restriction() {
        if ($this->product) {
            return $this->product->no_of_sku;
        }elseif($this->catalog) {
            return $this->catalog->no_of_channel;
        }
    }

    //return remaining products limit
    public function limit_remaining_products() {
        //for testing
        return 10000000000000;
    }

    //return number of remaining catalogues in the plan
    public function limit_remaining_catalog() {
        $cat = Channel::where('organization_id',$this->id)->count();
        return $this->plan->no_of_channel - $cat;
    }
}
