<?php

namespace App\Traits\Product;

use App\Models\Product\Attribute;
use App\Models\Product\AttributeFamily;
use App\Models\Product\AttributeFamilyProductVersion;
use App\Models\Product\Product;
use App\Models\Product\ProductVersion;
use Illuminate\Support\Str;

trait ProductTrait{

    public function __construct()
    {

    }
    public function get_product_version($id, $version_ids) {
        $products = new Product();
        $product = $products->with(["brands",
            "categories",
            "vendors",
            "channels",
            "variants",
            "files",
            'versions'=>function($q) use ($version_ids){
                if(is_iterable($version_ids))
                    $q->whereIn('version_id',$version_ids);
                else
                    $q->where('version_id',$version_ids);
            }])
            ->findOrFail($id);
        return $products->product_fetch($product);
    }

    /**
     * @return string
     *
     * Get product current description for the first version you will provide.
     * If version or family is not available then function will return null.
     */
    public function get_description()
    {
        try {
            $version = $this->versions->first();

            if(!$version) {
                throw new \Exception("version not found.");
            }

            $family = $this->versions[0]->families->where("name", "General")->first();

            if(!$family) {
                throw new \Exception("family not found.");
            }

            $attribute = $family->attributes->where("handle", "description")->first();

            if(!$attribute) {
                throw new \Exception("attribute not found.");
            }

            $value = $attribute->value->pluck('value')->first();

            if(!$value) {
                throw new \Exception("attribute not found.");
            }

            return $value;

        }
        catch (\Exception $e) {
            return null;
        }
    }

    public function get_seo_title()
    {
        try {
            $version = $this->versions->first();

            if(!$version) {
                throw new \Exception("version not found.");
            }

            $family = $this->versions[0]->families->where("name", "SEO")->first();

            if(!$family) {
                throw new \Exception("family not found.");
            }

            $attribute = $family->attributes->where("handle", "seo_title")->first();

            if(!$attribute) {
                throw new \Exception("attribute not found.");
            }

            $value = $attribute->value->pluck('value')->first();

            if (empty($value)) {
                throw new \Exception("attribute not found.");
            }

            return $value;
        }
        catch (\Exception $e) {
            return null;
        }
    }

    public function get_seo_description()
    {
        try {
            $version = $this->versions->first();

            if(!$version) {
                throw new \Exception("version not found.");
            }

            $family = $version->families->where("name", "SEO")->first();

            if(!$family) {
                throw new \Exception("family not found.");
            }

            $attribute = $family->attributes->where("handle", "seo_description")->first();

            if(!$attribute) {
                throw new \Exception("attribute not found.");
            }

            $value = $attribute->value->pluck('value')->first();

            if (empty($value)) {
                throw new \Exception("attribute not found.");
            }

            return $value;

        }
        catch (\Exception $e) {
            return null;
        }
    }

    public function get_seo_keyword()
    {
        try {
            $version = $this->versions->first();

            if (!$version) {
                throw new \Exception("Version not found.");
            }

            $family = $version->families->where("name", "SEO")->first();

            if (!$family) {
                throw new \Exception("Family not found.");
            }

            $attribute = $family->attributes->where("handle", "seo_keyword")->first();

            if (!$attribute) {
                throw new \Exception("SEO keyword attribute not found.");
            }

            $value = $attribute->value->pluck('value')->first();

            if (empty($value)) {
                throw new \Exception("SEO keyword value not found.");
            }

            return $value;
        } catch (\Exception $e) {
            return null; // Optionally, you could log this error or handle it differently.
        }
    }

    public function set_seo_keyword($value)
    {
        // Locate the 'seo_keyword' attribute ID within the specified organization.
        $seo_keyword_attr_id = Attribute::where(['handle' => 'seo_keyword', 'organization_id' => $this->organization_id])->value('id');

        // Find the attribute family associated with the 'seo_keyword' attribute.
        $attr_family = AttributeFamily::where('attribute_id', $seo_keyword_attr_id)->first();

        // Retrieve the first version of the product.
        $version = $this->versions->first();

        if (!$version) {
            throw new \Exception("Version not found.");
        }

        if (!$attr_family) {
            throw new \Exception("Attribute family for SEO keyword not found.");
        }

        // Locate the existing attribute value or create a new instance for the 'seo_keyword'.
        $attribute_value = AttributeFamilyProductVersion::where('attribute_family_id', $attr_family->id)
            ->where('product_version_id', $version->pivotId)
            ->firstOrNew([
                'attribute_family_id' => $attr_family->id,
                'product_version_id' => $version->pivotId,
                'attribute_id' => $attr_family->attribute_id,
                'family_id' => $attr_family->family_id,
                'version_id' => $version->id,
                'product_id' => $this->id,
            ]);

        // Check and encode the keyword value to UTF-8 if necessary.
        $encoding = mb_detect_encoding($value, mb_list_encodings(), true);
        if ($encoding != 'UTF-8') {
            $value = utf8_encode($value);
        }

        // Update the attribute value with the new or encoded 'seo_keyword' value.
        $attribute_value->value = $value;
        $attribute_value->save();
    }


    public function set_seo_description($value)
    {

        $seo_description_attr_id = Attribute::where(['handle' => 'seo_description', 'organization_id' => $this->organization_id])->value('id');
        $attr_family = AttributeFamily::where('attribute_id', $seo_description_attr_id)->first();

        $version = $this->versions->first();

        if(!$version) {
            throw new \Exception("version not found.");
        }

        if(!$attr_family && !$version->pivotId) {
            throw new \Exception("version not found.");
        }

        $attribute_value = AttributeFamilyProductVersion::where('attribute_family_id',$attr_family->id)->where('product_version_id', $version->pivotId)->first();
        if(!$attribute_value) {
            $attribute_value = new AttributeFamilyProductVersion();

            // if attribute value not assigned then we need to save attribute family id and product version id also
            $attribute_value->attribute_family_id = $attr_family->id;
            $attribute_value->product_version_id = $version->pivotId;
            $attribute_value->attribute_id = $attr_family->attribute_id;
            $attribute_value->family_id = $attr_family->family_id;
            $attribute_value->version_id = $version->id;
            $attribute_value->product_id = $this->id;
        }

        $description_without_tags = strip_tags($value); //removing html tags
        $description_without_tags = substr($description_without_tags,0,300);

        $encoding = mb_detect_encoding($description_without_tags, mb_list_encodings(), true);
        if ($encoding != 'UTF-8') {
            $description_without_tags = utf8_encode($description_without_tags);
        }
        $attribute_value->value = $description_without_tags; //cutting description upto 300 characters
        $attribute_value->save();

    }

    public function set_seo_title($value) {

        $seo_title_attr_id = Attribute::where(['handle'=>'seo_title','organization_id' => $this->organization_id])->value('id');
        $attr_family = AttributeFamily::where('attribute_id',$seo_title_attr_id)->first();

        $version = $this->versions->first();

        if(!$version) {
            throw new \Exception("version not found.");
        }

        if(!$attr_family && !$version->pivotId) {
            throw new \Exception("version not found.");
        }

        $attribute_value = AttributeFamilyProductVersion::where('attribute_family_id', $attr_family->id)->where('product_version_id', $version->pivotId)->first();
        if(!$attribute_value) {
            $attribute_value = new AttributeFamilyProductVersion();
            // if attribute value not assigned then we need to save attribute family id and product version id also
            $attribute_value->attribute_family_id = $attr_family->id;
            $attribute_value->product_version_id = $version->pivotId;
            $attribute_value->attribute_id = $attr_family->attribute_id;
            $attribute_value->family_id = $attr_family->family_id;
            $attribute_value->version_id = $version->id;
            $attribute_value->product_id = $this->id;
        }
        $encoding = mb_detect_encoding($value, mb_list_encodings(), true);
        if ($encoding != 'UTF-8') {
            $value = utf8_encode($value);
        }

        $attribute_value->value = $value;
        $attribute_value->save();
    }

    public function get_seo_url() {
        try {
            $version = $this->versions->first();

            if(!$version) {
                throw new \Exception("version not found.");
            }

            $family = $version->families->where("name", "SEO")->first();

            if(!$family) {
                throw new \Exception("family not found.");
            }

            $attribute = $family->attributes->where("handle", "seo_url")->first();

            if(!$attribute) {
                throw new \Exception("attribute not found.");
            }

            $value = $attribute->value->pluck('value')->first();

            if (empty($value)) {
                throw new \Exception("attribute not found.");
            }

            return $value;
        }
        catch (\Exception $e) {
            return null;
        }
    }

    public function set_seo_url($value) {
        $seo_title_attr_id = Attribute::where(['handle'=>'seo_url','organization_id' => $this->organization_id])->value('id');
        $attr_family = AttributeFamily::where('attribute_id',$seo_title_attr_id)->first();

        $version = $this->versions->first();

        if(!$version) {
            throw new \Exception("version not found.");
        }

        if(!$attr_family && !$version->pivotId) {
            throw new \Exception("version not found.");
        }

        $attribute_value = AttributeFamilyProductVersion::where('attribute_family_id', $attr_family->id)->where('product_version_id', $version->pivotId)->first();
        if(!$attribute_value) {
            $attribute_value = new AttributeFamilyProductVersion();

            // if attribute value not assigned then we need to save attribute family id and product version id also
            $attribute_value->attribute_family_id = $attr_family->id;
            $attribute_value->product_version_id = $version->pivotId;
            $attribute_value->attribute_id = $attr_family->attribute_id;
            $attribute_value->family_id = $attr_family->family_id;
            $attribute_value->version_id = $version->id;
            $attribute_value->product_id = $this->id;
        }

        $encoding = mb_detect_encoding($value, mb_list_encodings(), true);
        if ($encoding != 'UTF-8') {
            $value = utf8_encode($value);
        }

        $attribute_value->value = Str::slug($value);
        $attribute_value->save();
    }

}
