<?php

namespace App\Traits\ImportExport;

use App\Models\Formula\Vlookup;
use phpDocumentor\Reflection\Types\Integer;
use Psr\Log\InvalidArgumentException;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

/**
 * Apply formulas to different models.
 *
 * @test php vendor/bin/phpunit tests/Unit/FormulasTraitTest.php
 * @deprecated This class will be removed in the future.
 * */
trait Formulas {

    private $VALUE_NOT_DEFINED = "Value is not defined in from list to apply formula";
    private $from       = [];
    private $with       = null;
    private $remove     = null;
    private $formula    = null;

    /**
     * Sets csv keys and values.
     *
     * @param array $from
     *
     * @return self
     * */
    public function from(array $from) : self {
        $this->from = $from;

        return $this;
    }

    /**
     * Pass the formula name to apply that formula.
     *
     * @param string $formula possible values assign, split, merge, replace, slug
     *
     * @return self
     */
    public function withFormula(string $formula) : self {
        $this->formula = $formula;

        return $this;
    }

    /**
     * Helping elements pass to apply formula.
     *
     * @param string $remove
     *
     * @return self
     * */
    public function remove(string $remove) : self {
        $this->remove = $remove;

        return $this;
    }

    /**
     * Helping elements pass to apply formula.
     *
     * @param string $with
     *
     * @return self
     * */
    public function with(string $with) : self {
        $this->with = $with;

        return $this;
    }

    /**
     * Apply assign formula.
     *
     * @return array
     * */
    private function assign() : array {
        try {
            $res = [];

            foreach ($this->from as $from) {
                if(isset($from["value"])) {

//                    throw new InvalidArgumentException($this->VALUE_NOT_DEFINED);

                    $res[] = $from["value"];

                }
            }

            return $res;
        }
        catch(\InvalidArgumentException $e) {
            Log::error($e);
            return [];
        }
    }

    /**
     * Apply split formula.
     *
     * @return array
     * */
    private function split() : array {
        try {
            $res = [];

            foreach ($this->from as $from) {
                if(isset($from["value"]) && !empty($this->with)) {
//                    throw new InvalidArgumentException($this->VALUE_NOT_DEFINED);
                    $res = explode($this->with, $from["value"]);
                }

//                if(empty($this->with)) {
//                    throw new InvalidArgumentException("Empty delimiter, Can't use explode here.");
//                }

            }

            return $res;
        }
        catch(\InvalidArgumentException $e) {
            Log::error($e);
            return [];
        }
    }

    /**
     * Apply merge formula.
     *
     * @return array
     * */
    private function merge() : array {
        try {

            $res = array();

                $res = $this->from[0]["value"].$this->with.$this->from[1]["value"];

            return [$res];
        }
        catch (\InvalidArgumentException $e) {
            Log::error($e);
            return [];
        }
    }

    /**
     * Apply replace formula.
     *
     * @return array
     * */
    private function replace() : array {
        try {
            $res = [];

            foreach ($this->from as $from) {
                if(isset($from["value"])) {
                    $res[] = str_replace($this->remove, $this->with, $from["value"]);
//                    throw new InvalidArgumentException($this->VALUE_NOT_DEFINED);
                }
            }

            return $res;
        }
        catch(\InvalidArgumentException $e) {
            Log::error($e);
            return [];
        }
    }

    /**
     * Apply slug formula.
     *
     * @return array
     * */
    private function slug() : array {
        try {
            $res = [];

            foreach ($this->from as $from) {
                if(isset($from["value"])) {
//                    throw new InvalidArgumentException($this->VALUE_NOT_DEFINED);
                    $res[] =  Str::slug($from["value"]);
                }

            }

            return $res;
        }
        catch(\InvalidArgumentException $e) {
            Log::error($e);
            return [];
        }
    }




    /**
     * Apply vlookup formula.
     *
     * @return array
     * */
    private function vlookup() : array {
        try {
            $res = [];

            foreach ($this->from as $from) {
                if(isset($from["value"])) {
                    $res[] = $this->find_vlookup_values($this->with,$from["value"]);
                }
            }

            return $res;
        }
        catch(\InvalidArgumentException $e) {
            Log::error($e);
            return [];
        }
    }



    /**
     * Apply calculate formula.
     *
     * @return array
     * */
    private function calculate() : array {
        try {
            $res = [];

            foreach ($this->from as $from) {
                    $res[] = $this->find_calculate_value($this->with,$from["value"]);
            }

            return $res;
        }
        catch(\InvalidArgumentException $e) {
            Log::error($e);
            return [];
        }
    }


    /**
     * Apply expand formula.
     *
     * @return array
     * */
    private function expand() : array {
        try {
            $res = [];
            foreach ($this->from as $from) {
                if(isset($from["value"])) {
                    $res[] = $this->expanding_value($this->remove,$this->with,$from["value"]);
                }
            }

            return $res;
        }
        catch(\InvalidArgumentException $e) {
            Log::error($e);
            return [];
        }
    }







    /**
     * Apply formulas.
     *
     * @return array
     * */
    private function applyFormula() : array {
        try {
            $result = array();
            if(count($this->from) && !empty($this->formula)) {
//                throw new InvalidArgumentException("From key and values are empty.");
                switch ($this->formula) {
                    case("assign") :
                        $result = $this->assign();
                        break;
                    case("split") :
                        $result = $this->split();
                        break;
                    case("merge") :
                        $result = $this->merge();
                        break;
                    case("replace") :
                        $result = $this->replace();
                        break;
                    case("slug") :
                        $result = $this->slug();
                        break;
                    case("vlookup") :
                        $result = $this->vlookup();
                        break;
                    case("calculate") :
                        $result = $this->calculate();
                        break;
                    case("expand") :
                        $result = $this->expand();
                        break;
                    default:
                        throw new InvalidArgumentException("Invalid formula value provided.");
                }
            }

//            if(empty($this->formula)) {
//                throw new InvalidArgumentException("Formula is empty.");
//            }


            return $result;
        }
        catch (\InvalidArgumentException $e) {
            Log::error($e);
            return [];
        }
    }

    /**
     * The values which should be assigned results.
     *
     * @param array $keys
     *
     * @return array
     * */
    public function to(array $keys) : array {
        try {
            $final_result = [];
            $result = $this->applyFormula();

            foreach ($keys as $key => $value) {
                if(isset($result[$key])) {
                    $final_result[$value] = $result[$key];
                }
                else {
                    $final_result[$value] = null;
                }
            }

            return $final_result;
        }
        catch (\InvalidArgumentException $e) {
            Log::error($e);
            return [];
        }
    }




    /**
     * The values which should be assigned results.
     *
     * @param string $id
     * @param string $value
     *
     * @return string
     * */
    public function find_vlookup_values(string $id, string $value) : string {
        $vlookup_obj = Vlookup::withoutGlobalScopes()->where('id',$id)->get()->first();
        $vlookup_values = json_decode($vlookup_obj->payload,true);
        $lookup_val = $value;
        foreach ($vlookup_values as $key_lookup => $value_vlookup){
            $lookup_val = str_replace ($key_lookup, $value_vlookup, $lookup_val);
        }

        return $lookup_val;
    }



    /**
     * The values which should be assigned results.
     *
     * @param string $expresion
     * @param string $value
     *
     * @return string
     * */
    public function find_calculate_value(string $expresion, $value) : string
    {
        try {
            if (!is_numeric($value)) {
                $value = 0;
            }


            $cal_value = (int)$value;
            if (isset($expresion)) {
                if ($expresion[0] == '+' || $expresion[0] == '-') {
                    $cal_value = $value . $expresion;
                } else {
                    $cal_value = $value . "*" . $expresion;
                }
            }
            if (strpos($cal_value, '%') !== false) {
                $percent_cal_value = "*".(int)$value."/100*";
                $cal_value = str_replace("%", $percent_cal_value, $cal_value) . "1";
            }

            if (strpos($cal_value, '++') !== false || strpos($cal_value, '--') !== false) {
                $cal_value = $value;
            }
            else{
                $cal_value = eval('return ' . $cal_value . ';');
                if ($cal_value < 0) {
                    $cal_value = 0;
                }
            }

        } catch (\Exception $e) {
            $cal_value = $value;
            Log::error($e->getMessage());
        }

        return $cal_value;

    }




    /**
     * The values which should be assigned results.
     *
     * @param string $position
     * @param  $with
     * @param string $value
     *
     * @return string
     * */
    public function expanding_value(string $position, $with, string $value) : string {
        if ($position == 'start'){
            $expand_val = $with. $value;
        }
        elseif ($position == 'end'){
            $expand_val = $value.$with;
        }
        else{
            $expand_val = $value;
        }

        return $expand_val;
    }


}
