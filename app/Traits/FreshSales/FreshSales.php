<?php

namespace App\Traits\FreshSales;

use App\User;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

Trait FreshSales{

    public function __construct()
    {

    }

    public function createFreshSalesContact($status = null)
    {
      if (App::environment() == 'production') {
            try {

                //getting user registration method
                if ($this->google_id)
                {
                    $lead_source_id = 22000270811;
                }
                elseif($this->shopify_shop_id)
                {
                    $lead_source_id = 22000270809;
                }
                else
                {
                    $lead_source_id = 22000270810;
                }

                $url = "https://" . env('FRESH_WORKS_DOMAIN_NAME_URL') .'/api/contacts';

                $data['contact'] = [
                    'first_name' => $this->fname,
                    'last_name' =>  $this->lname,
                    'display_name' => $this->fname,
                    'email' => $this->email,
                    'mobile_number' => $this->phone,
                    'owner_id' => 22000016798,
                    'lead_source_id' => $lead_source_id,
                    'custom_field' =>  ['cf_verified' => isset($status) ? 0 : 1],
                ];

                $response = Http::retry(2,60)->withHeaders([
                    'Content-Type' => 'application/json',
                    'Authorization' =>'Token token='. env('FRESH_WORKS_API_KEY')
                ])->post($url,$data);

                //hitting deals api
                if ($response->successful()) {
                    $contact_data = $response->json();

                    //saving freshworks contact id in users table
                    $user = User::find($this->id);
                    $user->freshworks_id = $contact_data['contact']['id'];
                    $user->save();

                    $url = "https://" . env('FRESH_WORKS_DOMAIN_NAME_URL') .'/api/deals';
                    $data['deal'] = [
                        "name" => $this->fname,
                        "amount" =>  159,
                        'owner_id' => 22000016798,
                        'lead_source_id' => $lead_source_id,
                        "contacts_added_list" => [$contact_data['contact']['id']]
                    ];

                    $deal_response = Http::retry(2,60)->withHeaders([
                        'Content-Type' => 'application/json',
                        'Authorization' =>'Token token='. env('FRESH_WORKS_API_KEY')
                    ])->post($url,$data);
                }
            }
            catch(\Exception $e) {
                Log::error('Fresh sales contact creation error');
                Log::debug($e->getMessage());
            }
       }
    }

    public function updateFreshSalesContact ($custom_field)
    {
       if (App::environment() == 'production') {
            try {
                $url = "https://" . env('FRESH_WORKS_DOMAIN_NAME_URL') .'/api/contacts/'.$this->freshworks_id;

                $data['contact'] = [
                    'custom_field' =>  $custom_field,
                ];

                $response = Http::retry(2,60)->withHeaders([
                    'Content-Type' => 'application/json',
                    'Authorization' =>'Token token='. env('FRESH_WORKS_API_KEY')
                ])->put($url,$data);

                if ($response->successful()) {
                    return true;
                }
            }
            catch(\Exception $e) {
                Log::error('Fresh sales contact update error');
                Log::debug($e->getMessage());
                return false;
            }
        }
   }
}
