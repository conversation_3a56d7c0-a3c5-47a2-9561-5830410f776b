<?php

namespace App\Listeners\Product;

use App\Models\Product\Category;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class ManageCategoryListener
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {

    }

    /**
     * Handle the event.
     *
     * @param  object  $event
     * @return void
     */
    public function handle($event)
    {
        $category_ids = [];
        if (!$event->data) {
            if($event->refresh) {
                $event->product->categories()->detach();
            }
            return;
        }

        if (!is_array($event->data)) {
            $event->data = explode(',', $event->data);
            $event->data = $this->mapArrayWithSameKeyName($event->data, 'id');
        }

        foreach ($event->data as $data) {
            if (array_key_exists('id',$data)) {
                $category_ids[] = $data['id'];
            } elseif(array_key_exists('name',$data)) {
                if (!empty($data['name'])) {
                    $category_ids[] = Category::findOrCreateForProduct($data['name'], $event->product)->id;
                }
            } else {
                Log::channel('events')->warning('Both "id", "name" keys and data are required.');
            }
        }
        if ($event->refresh) {
            $event->product->categories()->sync(array_filter($category_ids));
        } else {
            $event->product->categories()->syncWithoutDetaching(array_filter($category_ids));
        }
    }

    function mapArrayWithSameKeyName($array, $keyName) {
        $result = array();
        foreach ($array as $value) {
            $result[] = array($keyName => $value);
        }
        return $result;
    }
}
