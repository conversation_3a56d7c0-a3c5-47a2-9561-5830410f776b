<?php

namespace App\Listeners\Product;

use App\Events\Product\ManageAttributes;
use App\Models\Product\Attribute;
use App\Models\Product\AttributeFamilyProductVersion;
use App\Models\Product\Family;
use App\Models\Product\Product;
use App\Models\Product\Version;
use Exception;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class ManageVersionListener
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param object $event
     * @return void
     * @throws Exception
     */
    public function handle($event)
    {
        if (!$event->data) {
            $event->product->attach_default_version();
        }
        else {
            $versionIds = [];
            foreach ($event->data as $version) {
                if (array_key_exists('id', $version)) {
                    $versionIds[] = $version["id"];
                } elseif (array_key_exists('name', $version)) {
                    if (!empty($version['name'])) {
                        $versionIds[] = Version::findOrCreateForProduct($version['name'], $event->product)->id;
                    }else{
                        throw new Exception('Version name is required.');
                    }
                } else {
                    throw new Exception('Both "id", "name" keys and data are required.');
                }
            }
            if($event->refresh)
                $event->product->versions()->sync(array_filter($versionIds));
            else
                $event->product->versions()->syncWithoutDetaching(array_filter($versionIds));

        }

        // create default variants
        foreach ($event->product->versions()->get() as $version) {
            if($version->variants()->where("product_id", $event->product->id)->count() === 0) {
                Product::create_default_variant_with_version($event->product, $version);
            }
        }

        // create product name when version is attached
        // if(isset($version_id)){
        //     $firstVersion = $event->product->versions()->where('version_id',$version_id)->first();
        // }else{
        //     $firstVersion = $event->product->versions()->first();
        // }

        // if ($firstVersion) {
        //     $productName = AttributeFamilyProductVersion::where("product_id", $event->product->id)
        //         ->where("version_id", $firstVersion->id)
        //         ->where("attribute_id", Attribute::where("handle", "product_name")->pluck('id')->first())
        //         ->first(); // Retrieve the first matching record

        //     if ($productName) {
        //         $productName->update(["value" => $event->product->sku]);
        //     } else {
        //         // Create a new record
        //         AttributeFamilyProductVersion::create([
        //             "product_id" => $event->product->id,
        //             "version_id" => $firstVersion->id,
        //             "attribute_id" => Attribute::where("handle", "product_name")->pluck('id')->first(),
        //             'family_id' => Family::where("is_default", 1)->where("name", "General")->pluck("id")->first(),
        //             "value" => (isset($product_name) ? $product_name : $event->product->sku), // TODO: Make it a title again
        //         ]);
        //     }
        // }
    }
}
