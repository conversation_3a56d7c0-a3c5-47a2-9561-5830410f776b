<?php

namespace App\Listeners\Product;

use Apimio\Gallery\Models\File;
use App\Models\Channel\ChannelFileProduct;
use App\Models\Product\FileProduct;
use App\Models\Product\Product;
use Carbon\Carbon;
use DB;
use Illuminate\Database\QueryException;
use Illuminate\Support\Facades\Log;


class ManageFilesListener
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param object $event
     * @return void
     * event data example
     * product object
     * Files array
     * [
     * {
     * "id": 1,
     * "link": "link url"
     *  shopify_image_id: 123
     * },
     * {
     * "id": 2,
     * "link": "link url"
     *  shopify_image_id: 1232
     * }
     * ]
     * channel_id is optional null or int
     */
    public function handle(object $event): void
    {
        $fileId = null;
        if (!$event->data) {
            return;
        }

        if($event->detach){
            $event->product->files()->detach();
        }

        foreach ($event->data as $data) {
            $data = (array)$data;
            if (array_key_exists('id', $data)) {
                $fileId = $data['id'];
                $event->product->files()->syncWithoutDetaching([$fileId]);
            } elseif (array_key_exists('link', $data)) {
               // $file = new File();
                $fileId=$this->assignUniqueUrlToProduct1($event->product, [$data]);
            } else {
                Log::channel('events')->warning('"id" or link keys and data is required in saving File.');
            }

            if($fileId && isset($data['shopify_image_id'])){
                $fileProduct = FileProduct::query()->where([
                    'file_id'=>$fileId,
                    'product_id'=>$event->product->id
                ])->first();

                if($fileProduct){
                    ChannelFileProduct::updateOrCreate([
                        'file_product_id'=>$fileProduct->id,
                        'channel_id'=>$event->channel_id,
                        'store_connect_type'=>'shopify'
                    ],[
                        'store_connect_id'=>$data['shopify_image_id']
                    ]);
                }
            }
        }
    }

    public function assignUniqueUrlToProduct1(Product $product, array $data)
    {
        $fileIds = [];
        foreach ($data as $link) {
            if (array_key_exists("link", $link)) {
                $file = File::query()
                    ->where("organization_id", $product->organization_id)
                    ->where("link", $link["link"])
                    ->first();

                if ($file && $file->id) {
                    $fileIds[] = $file->id;   //returning Product_ids of links that already exits in table
                        /*$fileProd = new FileProduct();
                        $fileProd->product_id = $product->id;
                        $fileProd->file_id = $file->id;
                        $fileProd->save();*/


                } else {

                    $file = new File();
                    $file->organization_id = 1;
                    $file->link = $link["link"];
                    $file->width = $link["width"] ?? null;
                    $file->height = $link["height"] ?? null;
                    $file->type = 'img';
                    $file->should_sync = 1;
                    $file->save();
                    //dd(1,$file);
                    $fileIds[] = $file->id;

                   /* if ($file && $file->id) {
                        $fileProd = new FileProduct();
                        $fileProd->product_id = $product->id;
                        $fileProd->file_id = $file->id;
                        $fileProd->save();
                    }*/
                }
            }
        }


         $product->files()->syncWithoutDetaching($fileIds);


        return $fileIds;

    }
}
