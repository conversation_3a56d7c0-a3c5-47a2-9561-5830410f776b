<?php

namespace App\Models\Channel;

use App\Models\Product\Variant;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class ChannelVariant extends Model
{
    use HasFactory;

    protected $fillable = [
        'channel_id', 'variant_id', 'store_connect_type', 'store_connect_id', 'store_connect_image_id'
    ];

    public function variant()
    {
        return $this->belongsTo(Variant::class, 'variant_id', 'id');
    }
}
