<?php

namespace App\Models;

use App\Models\Channel\Channel;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Models\Organization\File;
use App\Models\Organization\Organization;
use App\Models\Product\Template;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Auth;

class BrandsPortal extends Model
{
    protected $table = 'brands_portals';

    use HasFactory ,SoftDeletes;

    protected $fillable = ['id','organization_id','name','url','primary_color','logo_url','file_id'];

    public static function boot()
    {
        parent::boot();

        static::addGlobalScope('organization_id', function (Builder $builder) {
            if (Auth::check()) {
                $builder->where('organization_id', '=', Auth::user()->organization_id
                // Auth::user()->organization_id
            );
            } 
        });
        
    }

    public function set_data(array $data)
    {
        $this->data = $data;
        return $this;
    }


    /**
     * Relationships
     */

     public function file()
     {
         return $this->belongsTo(File::class, 'file_id')->withoutGlobalScopes();
     }

     public function organization()
     {
         return $this->belongsTo(Organization::class, 'organization_id')->withoutGlobalScopes();
     }

     public function channels()
     {
        return $this->belongsToMany(Channel::class, 'brands_portal_channel', 'brands_portal_id', 'channel_id')->withoutGlobalScopes();
     }
     
     public function templates()
     {
        return $this->belongsToMany(Template::class, 'brands_portal_template', 'brands_portal_id', 'template_id')->withoutGlobalScopes();
     }
}
