<?php

namespace App\Models\Product;

use App\Traits\Product\ProductTrait;
use App\Traits\Version\VersionScoreTrait;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ProductVersion extends Model
{

    protected $guarded = [];
    protected $table = 'product_version';

    public function version(): BelongsTo
    {
        return $this->belongsTo(Version::class)->withoutGlobalScopes();
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }
}
