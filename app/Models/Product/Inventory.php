<?php

namespace App\Models\Product;

use App\Models\Location\Location;
use Illuminate\Support\Facades\Log;
use App\Models\Channel\ChannelLocation;
use Illuminate\Database\Eloquent\Model;
use App\Http\Controllers\Channel\ChannelController;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Inventory extends Model
{
    use HasFactory;

    protected $fillable = [
        'product_id', 'location_id', 'variant_id', 'available_quantity', 'store_connect_id', 'store_type','organization_id'
    ];

    protected $appends = ['location_name','main_location_id'];

    public function getLocationNameAttribute()
    {
        // This will return the name of the location or null if not loaded
        return $this->location->name ?? null;
    }

    public function getMainLocationIdAttribute()
    {
        // This will return the name of the location or null if not loaded
        return $this->location->id ?? null;
    }

    public static function boot()
    {
            parent::boot();

            static::created(function ($model) {
                $channelLocation = ChannelLocation::find($model->location_id);
                Log::info('channel location not found in inventory created event  location id = '. $model->location_id);
                if(isset($channelLocation)){
                    $channelLocations = ChannelLocation::where('location_id',$channelLocation->location_id)
                    ->get();
                    foreach ($channelLocations as $key => $location) {
                        $inventories= Inventory::query()
                        ->where(['variant_id' => $model->variant_id, 'location_id' => $location->id])
                        ->get();
                            foreach($inventories as $inventory){
                                if($inventory->location_id !== $model->location_id){
                                    if ($model->available_quantity !== null) {
                                            $inventory->available_quantity = $model->available_quantity;
                                            $inventory->save();
                                    }elseif($model->available_quantity == null){
                                        $qty = $inventory->available_quantity;
                                        $model->available_quantity = $qty;
                                        $model->save();
                                    }
                                }
                            }
                        }
                    }

                });

                static::updated(function ($model) {
                $channelLocation = ChannelLocation::find($model->location_id);
                Log::info('channel location not found in inventory updated event  location id = '. $model->location_id);
                if($channelLocation){
                $channelLocations = ChannelLocation::where('location_id',$channelLocation->location_id)
                    ->get();
                if(isset($channelLocation)){
                    foreach ( $channelLocations as $key => $location) {
                $inventories= Inventory::query()
                ->where(['variant_id' => $model->variant_id, 'location_id' => $location->id])
                ->get();
                foreach($inventories as $inventory){
                    if($inventory && $inventory->location_id !== $model->location_id){
                        if ($model->available_quantity !== null) {
                            $inventory->available_quantity = $model->available_quantity;
                            $inventory->save();
                        }elseif($model->available_quantity == null)
                        {
                            $qty = $inventory->available_quantity;
                            $model->available_quantity = $qty;
                            $model->save();
                        }

                    }
                }
            }
        }
    }else{
        Log::info('inventory update observer');
        Log::info('channel location id not found');
    }
    });
}

    public function product() : BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    public function variant() : BelongsTo
    {
        return $this->belongsTo(Variant::class);
    }


    public function channelLocation()
    {
        return $this->belongsTo(ChannelLocation::class, 'location_id', 'id');
    }


    //    public function location() : BelongsTo
//    {
//        return $this->belongsTo(Location::class);
//    }

    // hasManyThrough relationship
    public function location()
    {
        return $this->hasOneThrough(
            Location::class,
            ChannelLocation::class,
            'id',          // Foreign key on ChannelLocation table
            'id',          // Foreign key on Location table
            'location_id', // Local key on Inventory table
            'location_id'  // Local key on ChannelLocation table
        );
    }
}
