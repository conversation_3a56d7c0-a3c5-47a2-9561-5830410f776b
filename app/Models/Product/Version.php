<?php

namespace App\Models\Product;

use App\Models\Channel\ChannelVersion;
use App\Models\Organization\Organization;
use App\Rules\UniqueManyToMany;
use App\Traits\Version\VersionScoreTrait;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class Version extends Model
{
    use VersionScoreTrait;
    private $data, $filter;

    protected $fillable = ['name','currency','separator','organization_id'];

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);
    }

    /**
     * The "booted" method of the model.
     *
     * @return void
     */
    public static function boot()
    {
        parent::boot();
        static::creating(function ($model) {
            if(!isset($model->organization_id)) {
                if (Auth::check()) {
                    $model->organization_id = Auth::user()->organization_id;
                }
            }
        });

        static::addGlobalScope('organization_id', function (Builder $builder) {
            if (Auth::check()) {
                $builder->where('organization_id', '=', Auth::user()->organization_id);
            }
        });
    }

    public function fetch()
    {
        $version = $this;
        if ($this->filter) {
            if (isset($this->filter["name"]))
                $version = $version->where("name", "LIKE", "%" . $this->filter["name"] . "%");
        }
        return $version->orderBy("id", "DESC")->paginate(8);
    }

    public function get_all(){
        return $this->all();
    }

    public function filter($filter)
    {
        $this->filter = $filter;
        return $this;
    }

    public function set_data($data)
    {
        if (isset($data['_token']))
            unset($data['_token']);

        $this->data = $data;
        return $this;
    }

    public function set_id($id)
    {
        $this->id = $id;
        return $this->findOrFail($this->id);
    }

    public function rules()
    {
        $attributes = [];

        if(isset($this->data["organization_id"])) {
            $attributes["organization_id"] = $this->data["organization_id"];
        }

        if(isset($this->data["id"])) {
            $attributes["id"] = $this->data["id"];
        }

        return [
            'name' => ['required', 'max:255',  new UniqueManyToMany(new Version(), $attributes)],
            'currency' => "required"
        ];
    }

    public function validation()
    {
        $validator = Validator::make($this->data, $this->rules());
        if (!$validator->fails()) {
            $this->attributes = $this->data;
        }
        return $validator;
    }

    public function store($error_callback, $success_callback)
    {
        $validation = $this->validation();
        if ($validation->fails()) {
            return $error_callback($validation->errors());
        }
        if (isset($this->data['id'])) {
            $version = $this->find($this->data['id']);
        } else {
            $version = $this;
        }
        if(isset($this->data['organization_id'])) {
            $version->organization_id = $this->data['organization_id'];
        }
        $version->name = $this->data['name'];
        $version->currency = $this->data['currency'];
        $version->separator = $this->data['separator'];
        $version->save();
        return $success_callback($version);
    }

    public static function findOrCreateForProduct($name, $product) {
        // Find the version with the given name for the specified product
        $version = static::where('name', $name)
            ->where('organization_id', $product->organization_id)
            ->first();

        // If the brand exists, return it
        if ($version) {
            return $version;
        }

        // Otherwise, create a new brand and associate it with the product
        $version = new static();
        $version->name = $name;
        $version->organization_id = $product->organization_id;
        $version->save();
        return $version;
    }

    public function delete_by_id($id)
    {
        $this->set_id($id)->delete();
    }


    /**
     * Relationships
     */

    public function products() {
        return $this->belongsToMany(Product::class);
    }

    public function channel(){
        return $this->hasOne(ChannelVersion::class,'version_id','id');
    }

    public function families() {
        return $this->hasMany(AttributeFamilyProductVersion::class)->distinct("family_id");
    }

    public function organization() {
        return $this->belongsTo(Organization::class)->distinct("organization_id");
    }

    public function variants()
    {
        return $this->hasMany(Variant::class);
    }

    public function hasVariants()
    {
        return $this->variants()->exists();
    }
}
