<?php

namespace App\Models\Notification;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Support\Facades\Bus;

class Notification extends Model
{
    public static function boot()
    {
        parent::boot();

        static::addGlobalScope('organization_id', function (Builder $builder) {
            if (Auth::check()) {
                $builder->where('organization_id', '=', Auth::user()->organization_id);
            }
        });
    }


    protected function data(): Attribute
    {
        return Attribute::make(
            get: function ($value) {
                $jsonData = json_decode($value, true);
                $data = $this->flatten_json($jsonData);
                if (isset($data['batch'])){
                    $data['batch'] = Bus::findBatch($data['batch']);

                    if (isset($data['batch'])){
                        $data['batch'] = [
                            'id' => $data['batch']->id,
                            'batch_progress' => ($data['batch']->pendingJobs == 0) ? 100 : $data['batch']->progress(),
                        ];
                    }
                }
                return $data;
            }
        );
    }


    /**
     * @param $data
     * @param string $prefix
     * @return array
     */
    function flatten_json($data, string $prefix = ''): array
    {
        $result = [];
        foreach ($data as $key => $value) {
            $new_key = $prefix ? $prefix . '_' . $key : $key;
            if (is_array($value)) {
                $result = array_merge($result, $this->flatten_json($value, $new_key));
            } else {
                $result[$new_key] = $value;
            }
        }
        return $result;
    }


    /**
     * Cast variables to specified data types
     *
     * @var array
     */
    protected $casts = [
        'data' => 'array',
        'id' => 'string'
    ];

}
