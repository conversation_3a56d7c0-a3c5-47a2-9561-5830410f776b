<?php

namespace App\Http\Requests\invite;

use Illuminate\Foundation\Http\FormRequest;

class invitesRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'emails' => 'required|array|min:1|max:30',
            'emails.*' => 'required|email|distinct|unique:team_invites,email,' . $this->route('invite') . ',id,organization_id,' . auth()->user()->id,
        ];
    }

    public function message(){
        return [
        'emails.unique' => 'Email is already invited',
        ];
    }
}
