<?php

namespace App\Http\Controllers\Product;

use App\Http\Controllers\Controller;
use App\Models\Product\Vendor;
use App\Models\Product\Version;
use Illuminate\Support\Facades\Gate;
use Illuminate\Http\Request;

class VersionController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'verified', 'activeOrganization']);
    }


    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        if (Gate::denies('SubscriptionAccess', 'language')) {
            return redirect(route('billing'))->withErrors(['main' => "upgrade your plan to access all modules"]);
        }
        $version = new Version();
        if ($request->has('q')) {
            $version->filter(["name" => $request->get('q')]);
        }
        $data['version'] = $version->fetch();
        return view('products.versions.view', compact('data', 'request'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        if (Gate::denies('create-lang', \App\Models\Product\Version::query())) {
            return redirect(route('billing'))->withErrors(['main' => "upgrade your plan to access all modules"]);
        }
        return view('products.versions.add');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $response = Gate::inspect('createLanguage',Version::class );
        if ($response->allowed()) {
            $version = new Version();
            return $version->set_data($request->all())->store(function ($error) {
                return back()->withInput()->withErrors($error);
            }, function () {
                return redirect(route("versions.index"))->withSuccess("Language created successfully.");
            });
        } else {
            return \redirect(route('dashboard'))->withErrors(['main' => $response->message()]);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $version = Version::findOrFail($id);
        return view('products.versions.add', compact('version'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $version = new Version();
        return $version->set_id($id)->set_data($request->all())->store(function ($error) {
            return back()->withInput()->withErrors($error);
        }, function () {
            return redirect(route("versions.index"))->withSuccess("Language updated successfully.");
        });
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $version = Version::findOrFail($id);
        if ($version->is_default) {
            return back()->withErrors(['main' => 'Default language cannot be deleted']);
        }
        $version->delete();
        return redirect()->back()->withSuccess('Language deleted Successfully');
    }
}
