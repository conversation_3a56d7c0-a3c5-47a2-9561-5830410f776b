<?php

namespace App\Http\Controllers\Organization;

use App\Http\Controllers\Controller;
use App\Models\Channel\Channel;
use App\Models\Organization\Organization;
use App\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Validator;
use Illuminate\View\View;
use Inertia\Inertia;

class OrganizationController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'verified']);
        $this->middleware(["activeOrganization"])->only("checkpoint");
    }

    /**
     * Display a listing of the resource.
     *
     */
    public function index()
    {
        $organization = new Organization();

        if ($organization->only_one()) {
            $organization->set_active_organization_automatically();
            return redirect(route("dashboard"));
        } else if ($organization->more_than_one()) {
            return Inertia::render('organizationselect/OrganizationSelect', [
                'title' => 'Select Organization'
            ]);
            // return view("organization.index", ["organizations" => $organization->orderBy("id", "DESC")->get()]);
        } else {
            return Inertia::render('organizationselect/OrganizationSelect', [
                'title' => 'Select Organization'
            ]);
        }
    }



    /**
     * Show the form for creating a new resource.
     *
     * @return View
     */
    public function create()
    {
        $phone = 1;
        if (auth()->user()->phone) {
            $phone = 0;
        }
        return view("organization.create", compact('phone'));
    }

    protected function validator(array $data)
    {
        $validator = Validator::make($data, [
            'phone' => 'nullable|regex:/^([0-9\s\-\+\(\)]*)$/|max:20',
        ], [
            'phone.max' => 'Phone number should not be greater than 20 digits',
            'phone.min' => 'Phone number should not be less than 7 digits'
        ]);
        if ($validator->fails()) {
            return $validator;
        }
        return $validator;
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     */
    public function store(Request $request)
    {
        if ($request->has('phone')) {
            $this->validator($request->all())->validate();
        }
        $organization = new Organization();
        return $organization->set_user(Auth::user())
            ->set_data($request->all())
            ->store(
                // when error
                function ($errors) {
                    return back()->withInput()->withErrors($errors);
                },

                // when success
                function ($obj) {
                    $data = $obj->get_data();
                    $isCreatingOrganization = 1;
                    return redirect(route("dashboard", ['onBoarding' => $isCreatingOrganization]));
                }
            );
    }

    /**
     * Display the specified resource.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        return redirect(route("dashboard"));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $organization = new Organization();
        return $organization->set_data($request->all())
            ->set_id($id)
            ->store(
                // when error
                function ($errors) {
                    return back()->withInput()->withErrors($errors);
                },

                // when success
                function ($obj) {
                    return redirect(route("dashboard"));
                }
            );
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }

    public function checkpoint()
    {
        //         if((new Channel())->get_cat() >= 1) {
        //             return redirect('dashboard');
        //         }
        return \view('organization.checkpoint');
    }

    public function set_active_organization($id)
    {
        $organization = new Organization();
        $organization->set_active_organization_by_id($id);
        return redirect(route("organization.edit", $id));
    }
}
