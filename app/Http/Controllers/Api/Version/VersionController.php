<?php

namespace App\Http\Controllers\Api\Version;

use App\Http\Controllers\Controller;
use App\Http\Requests\Version\VersionRequest;
use App\Http\Resources\VersionResource;
use App\Models\Product\Version;
use Illuminate\Support\Facades\Gate;
use App\Traits\Api\ApiResponseTrait;
use Illuminate\Http\Request;

class VersionController extends Controller
{
    use ApiResponseTrait;
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware("auth:sanctum");
    }
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        try {
            $user = $request->user();
            // Validate the request parameters
            $validatedData = $request->validate([
                'id' => 'integer|exists:versions,id',
                'organization_id' => 'integer|exists:organizations,id',
                'name' => 'string|max:50',
                'is_default' => 'boolean',
                'currency' => 'string|size:3', // Assuming currency is a 3-character code like 'USD'
                'paginate' => 'integer|min:1|max:255',
            ]);

            // Query to get versions without global scopes
            $versions = $user->versions();

            // Apply filters if provided
            if ($request->filled("id")) {
                $versions->where("id", $request->get("id"));
            }

            if ($request->filled("organization_id")) {
                $versions->where("organization_id", $request->get("organization_id"));
            }

            if ($request->filled("name")) {
                $versions->where("name", "LIKE", "%" . $request->get("name") . "%");
            }

            if ($request->filled("is_default")) {
                $versions->where("is_default", $request->get("is_default"));
            }

            if ($request->filled("currency")) {
                $versions->where("currency", $request->get("currency"));
            }

            // Determine the number of items per page
            $paginate = $request->filled("paginate") ? $request->get("paginate") : 255;

            // Get the paginated data
            $paginatedVersions = $versions->paginate($paginate);

            // Prepare pagination details
            $pagination = [
                'current_page' => $paginatedVersions->currentPage(),
                'last_page' => $paginatedVersions->lastPage(),
                'per_page' => $paginatedVersions->perPage(),
                'total' => $paginatedVersions->total(),
            ];

            // Return the response with pagination details
            return $this->successResponse(
                'Versions retrieved successfully',
                VersionResource::collection($paginatedVersions),
                200,
                $pagination
            );
        } catch (\Exception $e) {
            return $this->errorResponse('Failed to retrieve versions', $e->getCode() != 0 ? $e->getCode() : 500, $e->getMessage());
        }
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(VersionRequest $request)
    {
       $version = $request->user()->versions()->create($request->all());
       return response([
        'message' => "Version created successfully",
        'version' => $version
       ]);
    }

    /**
     * Display the specified resource.
     */
    public function show(Request $request,string $id)
    {
        $version = $request->user()->versions()->findOrFail($id);
        return response([
         'message' => "Version retrieved successfully",
         'version' => $version
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(VersionRequest $request, string $id)
    {
       $version = $request->user()->versions()->findOrFail($id);
       $version->update($request->all());
         return response([
          'message' => "Version updated successfully",
          'version' => $version
         ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Request $request,string $id)
    {
       $request->user()->versions()->findOrFail($id)->delete();
         return response([
          'message' => "Version deleted successfully"
         ]);
    }
}
