<?php

namespace App\Http\Controllers\Api\Channel;

use App\Http\Controllers\Controller;
use App\Http\Requests\Channel\ChannelRequest;
use App\Http\Resources\ChannelResource;
use App\Models\Channel\Channel;
use App\Traits\Api\ApiResponseTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use App\Services\LocationService;

class ChannelController extends Controller
{
    use ApiResponseTrait;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware("auth:sanctum");
    }


    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        try {
            $user = $request->user();

            if(get_class($user) != 'App\Models\Organization\Organization'){
                $this->check_user_organization($user, $request);
            }



            // Validate the request parameters
            $validatedData = $request->validate([
                'id' => 'integer|exists:channels,id',
                'name' => 'string|max:255',
                'product_update' => 'string|max:100',
                'inventory' => 'integer',
                'export_status' => 'string|in:publish,draft,archive',
                'category_empty' => 'boolean',
                'type' => 'string|in:shopify,magento,woocommerce',
                'paginate' => 'integer|min:1|max:255',
            ]);

            // Query to get channels without global scopes
            $channels = $user->channels();

            // Apply filters if provided
            if ($request->filled("id")) {
                $channels->where("id", $request->get("id"));
            }

            if ($request->filled("name")) {
                $channels->where("name", "LIKE", "%" . $request->get("name") . "%");
            }

            if ($request->filled("product_update")) {
                $channels->where("product_update", $request->get("product_update"));
            }

            if ($request->filled("inventory")) {
                $channels->where("inventory", $request->get("inventory"));
            }

            if ($request->filled("export_status")) {
                $channels->where("export_status", $request->get("export_status"));
            }

            if ($request->filled("category_empty")) {
                $channels->where("category_empty", $request->get("category_empty"));
            }

            if ($request->filled("type")) {
                $channels->where("type", $request->get("type"));
            }

            // Determine the number of items per page
            $paginate = $request->filled("paginate") ? $request->get("paginate") : 255;

            // Get the paginated data
            $paginatedChannels = $channels->orderBy('updated_at', 'desc')->paginate($paginate);

            // Prepare pagination details
            $pagination = [
                'current_page' => $paginatedChannels->currentPage(),
                'last_page' => $paginatedChannels->lastPage(),
                'per_page' => $paginatedChannels->perPage(),
                'total' => $paginatedChannels->total(),
            ];

            return $this->successResponse(
                'Channels retrieved successfully',
                ChannelResource::collection($paginatedChannels),
                200,
                $pagination
            );
        } catch (\Exception $e) {
            return $this->errorResponse('Failed to retrieve channels', $e->getCode() != 0 ? $e->getCode() : 500, $e->getMessage());
        }
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(ChannelRequest $request)
    {
            $channel =  $request->user()->channels()->create($request->validated());

            return response([
                'message' => 'Channel created successfully',
                'channel' => new ChannelResource($channel)
            ]);

    }



    /**
     * Display the specified resource.
     */
    public function show(Request $request,string $id)
    {
        $channel =  $request->user()->channels()->findOrFail($id);

            return response([
                'message' => 'Channel retrieved successfully',
                'channel' => new ChannelResource($channel)
            ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(ChannelRequest $request, string $id)
    {
          $channel =  $request->user()->channels()->findOrFail($id);
          $channel->update($request->validated());

            return response([
                'message' => 'Channel updated successfully',
                'channel' => new ChannelResource($channel)
            ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Request $request ,string $id)
    {
        $request->user()->channels()->findOrFail($id)->delete();
        return response([
            'message' => 'Channel deleted successfully'
        ]);
    }
}
