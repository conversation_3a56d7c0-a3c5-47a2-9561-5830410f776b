<?php

namespace App\Http\Controllers\Api\Vendor;

use App\Http\Controllers\Controller;
use App\Http\Requests\Vendor\VendorRequest;
use App\Http\Resources\VendorResource;
use Illuminate\Http\Request;

class VendorController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware("auth:sanctum");
    }
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        try {
            $user = $request->user();
            $authToken = $request->header('Authorization');
            if(get_class($user) != 'App\Models\Organization\Organization'){
                $this->check_user_organization($user, $request);
            }


            // Validate the request parameters
            $validatedData = $request->validate([
                'id' => 'integer|exists:invites,id',
                'email' => 'string|max:76',
                'token' => 'string|max:35',
                'is_accepted' => 'boolean',
                'is_declined' => 'boolean',
                'type' => 'string|in:vendor,retailer',
                'paginate' => 'integer|min:1|max:255',
            ]);

            // Query to get invites without global scopes
            $invites = $user->invites();

            // Apply filters if provided
            if ($request->filled("id")) {
                $invites->where("id", $request->get("id"));
            }

            if ($request->filled("email")) {
                $invites->where("email", "LIKE", "%" . $request->get("email") . "%");
            }
            if ($request->filled("fname")) {
                $invites->where("fname", "LIKE", "%" . $request->get("fname") . "%");
            }

            if ($request->filled("token")) {
                $invites->where("token", $request->get("token"));
            }

            if ($request->filled("is_accepted")) {
                $invites->where("is_accepted", $request->get("is_accepted"));
            }

            if ($request->filled("is_declined")) {
                $invites->where("is_declined", $request->get("is_declined"));
            }

            if ($request->filled("type")) {
                $invites->where("type", $request->get("type"));
            }

            // Determine the number of items per page
            $paginate = $request->filled("paginate") ? $request->get("paginate") : 255;

            // Get the paginated data
            $paginatedInvites = $invites->orderBy('updated_at', 'desc')->paginate($paginate);

            // Prepare pagination details
            $pagination = [
                'current_page' => $paginatedInvites->currentPage(),
                'last_page' => $paginatedInvites->lastPage(),
                'per_page' => $paginatedInvites->perPage(),
                'total' => $paginatedInvites->total(),
            ];

            return $this->successResponse(
                'Invites retrieved successfully',
                VendorResource::collection($paginatedInvites),
                200,
                $pagination
            );
        } catch (\Exception $e) {
            return $this->errorResponse('Failed to retrieve invites', 500, $e->getMessage());
        }
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(VendorRequest $request)
    {
        // Create vendor using validated data from the request
        $vendor = $request->user()->invites()->create($request->validated());

        // Sync channels with the vendor (assuming channel_ids is validated)
        if ($request->has('channel_ids')) {
            $vendor->channels()->sync($request->get('channel_ids'));
        }

        return response([
            'message' => 'Vendor created successfully',
            'vendor' => $vendor,
        ]);
    }

    /**
     * Display the specified resource.
     */
    public function show(Request $request, string $id)
    {
        $vendor =  $request->user()->invites()->find($id);
        return response([
            'message' => 'Vendor retrieved successfully',
            'vendor' => $vendor,
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(VendorRequest $request, string $id)
    {
        $vendor = $request->user()->invites()->findOrFail($id);
        $vendor->update($request->validated());

        // Sync channels with the vendor (assuming channel_ids is validated)
        if ($request->has('channel_ids')) {
            $vendor->channels()->sync($request->get('channel_ids'));
        }

        return response([
            'message' => 'Vendor updated successfully',
            'vendor' => $vendor,
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Request $request, string $id)
    {
        $request->user()->invites()->findOrFail($id)->delete();
        return response([
            'message' => 'Vendor deleted successfully',
        ]);
    }
}
