<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class VendorResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'value' => $this->id,
            'title' => isset($this->lname) ? $this->fname . " " . $this->lname : $this->fname,
            'email' => $this->email,
            'organization_id_sender' => $this->organization_id_sender,
            'organization_id_receiver' => $this->organization_id_receiver,
            'token' => $this->token,
            'is_accepted' => $this->is_accepted,
            'fname' => $this->fname,
            'lname' => $this->lname,
            'designation' => $this->designation,
            'phone' => $this->phone,
            'is_declined' => $this->is_declined,
            'type' => $this->type,
            'created_at' => $this->created_at->toDateTimeString(),
            'updated_at' => $this->updated_at->toDateTimeString(),
        ];
    }
}
