<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Newsletter;

class CreateUserNotification extends Notification
{
    use Queueable;
    /*** user variable to get user data */
    public $user;
    /*** $type variable to get $type of user */
    public $type;
    /*** $ip variable to get ip of user */
    public $ip;
    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct($user ,$ip = null)
    {
        $this->user = $user;
        $this->ip = $ip;

        //get user type
        $this->type=  $this->user->getUserType();

        //mailchimp subscription

//        if(App::environment('production')) {
//            $this->mailchimpSubscription();
//        }

        //slack notifications
        $this->slackNotification();
    }


    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return [];
    }

    public function mailchimpSubscription(){
        if($this->user->phone) {
            $data['fields'] = [
                'FNAME' => $this->user ? $this->user->fname : '',
                'LNAME' => $this->user ? $this->user->lname : '',
                'PHONE' => $this->user ? $this->user->phone : '',
                "TYPE" => $this->type
            ];
        }
        else{
            $data['fields'] = [
                'FNAME' => $this->user ? $this->user->fname : '',
                'LNAME' => $this->user ? $this->user->lname : '',
                "TYPE" => $this->type
            ];
        }
        $data['list']='apimio';
        Newsletter::subscribeOrUpdate($this->user->email, $data['fields'],$data['list']);
    }

    public function slackNotification(){
        $details['content']='New User has been registered :tada: :+1:';
        $details['fields'] = [
            'Name'=>$this->user->fname.' '.$this->user->lname,
            'Email'=>$this->user->email,
            'Type'=>$this->type,
            'Phone'=>$this->user->phone,
            'IP'=>$this->ip,
            'Date & Time'=>$this->user->created_at->format('d M Y H:i'),

        ];
        $details['title'] = 'User Registration';
        if(App::environment('production')) {
            $details['channel'] = '#apimio-notifications';
        }
        else if(App::environment(['staging', "development"])){
            $details['channel'] = '#apimio-notifications-staging';
        }

        if(App::environment(['production', 'staging', "development"])) {
            \Illuminate\Support\Facades\Notification::route('slack', env('LOG_SLACK_WEBHOOK_URL'))
                ->notify(new SlackNotification($details));
        }

    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        return (new MailMessage)
            ->line('The introduction to the notification.')
            ->action('Notification Action', url('/'))
            ->line('Thank you for using our application!');
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            //
        ];
    }
}
