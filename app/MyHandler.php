<?php

namespace App;
use Artisan;
use Aws\Exception\AwsException;
use Bref\Context\Context;
use Bref\Event\Sqs\SqsEvent;
use Bref\Event\Sqs\SqsHandler;
use Illuminate\Queue\CallQueuedHandler;
use Illuminate\Support\Facades\Log;

class MyHandler extends SqsHandler
{
    public function handleSqs(SqsEvent $event, Context $context): void
    {


        foreach ($event->getRecords() as $record) {
            // Get the SQS message body
            $messageBody = $record->getBody();
           info("SQS Message Body: " . $messageBody);

            $data = json_decode($messageBody);

            if(is_object($data)){
                $data_job = $data->job_data;
            }else{
                $data_job = $data['job_data'];
            }
            if (isset($data_job)) {
                $job = unserialize($data_job);
                if (is_object($job) && method_exists($job, 'handle')) {
                    try {
                        info('Processing the job...');
                        $job->handle();
                        info('The job has been processed successfully.');

                    } catch (\Exception $e) {
                        Log::channel('shopify')->info('Error processing job: ' . $e->getMessage());
                    }
                } else {
                    info('The job does not have a handle method.');
                }
            } else {
                info('The SQS message does not contain a valid job.');
            }
        }


        /*        foreach ($event->getRecords() as $record) {
            // Get the SQS message body
            $messageBody = $record->getBody();
            info("SQS Message Body: " . $messageBody);

            // Decode the SQS message (it's JSON)
            $data = json_decode($messageBody, true);

            if (isset($data['data']['command'])) {
                // Unserialize the command to get the job object
                $job = unserialize($data['data']['command']);

                // Run the job manually by invoking its handle method
                if (method_exists($job, 'handle')) {
                    $job->handle();
                    info('The job has been processed successfully.');
                } else {
                    info('The job does not have a handle method.');
                }
            } else {
                info('The SQS message does not contain a valid job.');
            }
        }*/
    }
}
