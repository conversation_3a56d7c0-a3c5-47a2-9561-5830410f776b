<?php

namespace App\Console\Commands;

use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class CheckDatabaseForTask extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'check:database-for-task';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check database for tasks to be run.';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        // Get a semaphore lock for the command
        $semaphore = Cache::store('file')->get('check-database-for-task', null);

        if (!is_null($semaphore)) {
            $this->error('Another instance of this command is already running.');
            return;
        }

        Cache::store('file')->put('check-database-for-task', true, 1);

        // Get the current date and time in UTC timezone
        $nowUTC = Carbon::now('UTC');

        // Get tasks for the current day
        $tasks = DB::table('tasks')
            ->where('date', $nowUTC->format('Y-m-d'))
            ->get();

        foreach ($tasks as $task) {
            // Create a Carbon instance of the task date and time in the task timezone
            $taskDateTime = Carbon::createFromFormat('Y-m-d H:i:s', $task->date.' '.$task->time, $task->timezone);

            // Check if the task date and time is greater than or equal to the current date and time in the task timezone
            if ($taskDateTime->gte(Carbon::now($task->timezone))) {
                Artisan::call('your:command', [
                    '--timezone' => $task->timezone,
                    '--datetime' => $task->date.' '.$task->time,
                    '--specific-data' => $task->specific_data,
                ]);
            }
        }


        // Release the semaphore lock
        Cache::store('file')->forget('check-database-for-task');

    }
}
