<?php

namespace App\Console\Commands;

use Stripe\Stripe;
use App\Models\BrandsPortal;
use App\Models\Product\Brand;
use App\Classes\Plan\PlanClass;
use App\Models\Channel\Channel;
use App\Models\Product\Product;
use App\Models\Product\Variant;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use App\Models\Organization\Organization;

class SubscriptionSchedular extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'organization:subscription';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Daily basis schedular to update subscription quantities';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        \Log::info("schedule start");
        try {
            Stripe::setApiKey(env('STRIPE_SECRET'));

            $organizations = Organization::with("subscriptions")->whereHas("subscriptions", function($query){
                $query->whereHas("items", function($query1){
                    $query1->whereIn("stripe_product", PlanClass::$items->pluck("stripe_product_id"));
                });
            })->get();

            foreach($organizations as $org) {
                $subscription = $org->subscriptions->first();
                if($subscription) {
                    foreach($subscription->items as $item) {
                        $planItem = PlanClass::$items->where("stripe_price_id", $item->stripe_price)->first();
                        if($planItem) {
                            if($planItem->handle === 'sku'){
                                $count = Variant::whereHas("product",function($query) use($subscription){
                                    $query->where("organization_id",$subscription->organization_id);
                                })->count();
                                $quantity = ($count > 0 ? $count : 1);
                                if($quantity > 0){
                                    $item->updateQuantity($quantity);
                                } 
                            } elseif ($planItem->handle == "channel") {
                                $count = Channel::where("organization_id",$subscription->organization_id)->count();
                                $quantity = ($count > 0 ? $count : 1);
                                if($quantity > 0){
                                    $item->updateQuantity($quantity);
                                } 
                            } elseif ($planItem->handle == "brand") {
                                $count = BrandsPortal::where("organization_id",$subscription->organization_id)->count();
                                $quantity = ($count > 0 ? $count : 1);
                                if($quantity > 0){
                                    $item->updateQuantity($quantity);
                                } 
                            }
                        }
                    }
                }
            }

            $this->info('Subscription items quantities have been updated successfully.');
        } catch (\Exception $e) {
            $this->error('An error occurred: ' . $e->getMessage());
        }
    }
}
