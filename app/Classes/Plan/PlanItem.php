<?php

namespace App\Classes\Plan;

class PlanItem
{
    public $stripe_price_id;
    public $stripe_product_id;
    public $name;
    public $handle;
    public $price;

    public function __construct($stripe_price_id = null, $stripe_product_id = null, $name = null, $handle = null, $price = null)
    {
        $this->stripe_price_id = $stripe_price_id;
        $this->stripe_product_id = $stripe_product_id;
        $this->name = $name;
        $this->handle = $handle;
        $this->price = $price;
    }
}
