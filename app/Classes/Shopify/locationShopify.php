<?php

namespace App\Classes\Shopify;

use App\Traits\AWSSetting;
use App\User;
use Illuminate\Bus\Batch;
use App\Models\Channel\Channel;
use App\Models\Location\Location;
use App\Models\Product\Inventory;
use App\Traits\Shopify\ShopifyAPI;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use Psr\Log\InvalidArgumentException;
use App\Models\Channel\ShopifyChannel;
use App\Models\Notification\ErrorLog;
use App\Models\Channel\ChannelLocation;
use App\Models\Organization\Organization;
use App\Notifications\ApimioNotification;
use App\Jobs\Shopify\UpdateInventoryChunksJob;
use Ghazniali95\ShopifyConnector\App\Classes\Services\LocationService;
use Ghazniali95\ShopifyConnector\App\Classes\Services\InventoryLevelService;

class locationShopify
{
    use ShopifyAPI , AWSSetting;

    protected $organization_id;

    protected $channel_id;

    protected $batch;

    public function __construct(int $channel_id, int $organization_id,  $batch = null)
    {
        //set base url
        $shopify_channel = ShopifyChannel::query()->where('channel_id', $channel_id)->first();
        $this->setupCredentialsManually($shopify_channel->access_token, $shopify_channel->shop)->createBaseUrl();
        $this->organization_id = $organization_id;
        $this->channel_id = $channel_id;
        $this->batch = $batch;
    }

    public function saveLocation(){
        $locations = (new FetchDataByGQL($this->channel_id))->locations();
    }

    function save_shopify_location()
    {
        try {
            $LocationService = new LocationService($this->channel_id);
            // Get all products from shopify
            $LocationService->getAll(function($response) {
                if ($response) {
                    foreach ($response as $loc) {
                        $location = Location::query()
                        ->where(['organization_id' => $this->organization_id])
                        ->whereHas("channelLocation", function($query) use ($loc){
                            $query->where(["channel_id" => $this->channel_id , "store_connect_id" => $loc->id]);
                        })
                        ->first();
                        if(!$location){
                            $this->store($loc);
                        }
                    }
                }
            },['limit' => 250]);

        } catch (\Exception $e) {
            Log::channel('shopify')->info('GET META FIELDS DEFINITIONS ERROR.');
            Log::channel('shopify')->error($e);
            return null;
        }
    }

    function store($loc){
        try {
            $data = array();
            $data['name'] = $loc->name;
            $data['address'] = $loc->address1;
            $data['apartment'] = $loc->address2;
            $data['postal_code'] = $loc->zip;
            $data['city'] = $loc->city;
            $data['fulfill_online_orders'] = 1;
            $data['phone_number'] = $loc->phone;
            $data['default_location'] = false;
            $data['store_type'] = 'shopify';
            $data['store_connect_id'] = $loc->id;
            $data['organization_id'] = $this->organization_id;
            $channel = Channel::findOrFail($this->channel_id);
            $organization = Organization::findOrFail($this->organization_id);
            $location = Location::query()
                ->where(['organization_id' => $this->organization_id])
                ->where(["name" => $channel->name . ' Warehouse'])
                ->first();

            if ($organization && $channel) {
                if ($location) {
                    $data['id'] = $location->id;
                    if ($location->default_location == 1) {
                        $data['default_location'] = true;
                    }
                    event(new \App\Events\LocationEvent($channel, [$data], true));
                } else {
                    event(new \App\Events\LocationEvent($channel, [$data]));
                }
            }
        } catch (\Exception $e) {
            Log::channel('shopify')->error($e);
        }
    }

    function get_shopify_inventory_levels(int $limit = null, $loc = null)
    {
        $location_id = $loc ? $loc->store_connect_id : null;
        try {
            $InventoryLevelService = new InventoryLevelService($this->channel_id);
            $InventoryLevelService->getAll(function($response) use($loc){
                if ($response) {
                    if(env('CUSTOM_QUEUE_WORKER') == 'local'){
                        $this->batch->add(new UpdateInventoryChunksJob($response, $loc, $this->organization_id));
                    }else{
                        $job = [new UpdateInventoryChunksJob($response, $loc, $this->organization_id)];
                        $this->dispatchJobsToFifoQueue($job, $this->organization_id,$this->organization_id);
                    }
                } else {
                    throw new InvalidArgumentException("Inventory array not found from Shopify payload.");
                }
            }, ['location_ids' => $location_id,'limit' => $limit]);
            return true;
        } catch (\InvalidArgumentException | \Exception $e) {
            Log::channel('shopify')->info('There is an error while updating inventories.<br> '.$e->getMessage());
            Log::error($e);
            return false;
        }
    }

    function update_inventory_location()
    {
        try {
                $locations = ChannelLocation::query()
                    ->where('channel_id', $this->channel_id)
                    ->where('store_type', 'shopify')
                    ->get();
                if ($locations) {
                    foreach ($locations as $loc) {
                        $this->get_shopify_inventory_levels(self::$INVENTORY_LIMIT ? self::$INVENTORY_LIMIT : 250, $loc);
                    }
                }

        } catch (\Exception $e) {

            Log::channel('shopify')->error($e);
        }
    }
}
