<?php

namespace App\Classes\Shopify;

use App\Models\Billing\ShopifySubscription;
use App\Models\Channel\Channel;
use App\Models\Channel\ShopifyChannel;
use App\Models\Invite\Invite;
use App\Models\Organization\Organization;
use App\Models\Organization\Plan;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class ShopifyUsageCharge {

    private $data,$shopify_base_url;
    private static $API_VERSION = "2021-04";    // api version of shopify currently used in app.

    public function __construct()
    {
        $this->shopify_api_key = env("SHOPIFY_API_KEY");
    }

    //free or paid plan check
    public function plan_check() {
        $plan_id = ShopifySubscription::where('organization_id',Auth::user()->organization_id)->value('plan_id');
        $plan = Plan::where('id',$plan_id)->value('handle');
        if ($plan == "free_plan" || !$plan) {
            return "free plan";
        } else {
            return "paid plan";
        }
    }

    //getting subscribed plan details
    public function subscribed_plan() {
        $plan_id = ShopifySubscription::where('organization_id',Auth::user()->organization_id)->value('plan_id');
        return Plan::where('id',$plan_id)->first();
    }

    /**
     * return @boolean
     */
    public function check_capped_amount() {
        $capped_amount = ShopifySubscription::where('organization_id',Auth::user()->organization_id)->first();
        if ($capped_amount) {
            $catalog_price = Plan::where('id',$capped_amount->plan_id)->value('next_price');
            if ($capped_amount->balance_remaining > $catalog_price) {
                return true;
            } else {
                return false;
            }
        }
    }

    //getting access_token etc
    public function get_store_details() {
        $this->data['subscription_details'] = ShopifySubscription::where('organization_id',Auth::user()->organization_id)->latest('id')->first();
        $channel_details = ShopifyChannel::where('id',$this->data['subscription_details']->shopify_channel_id)->first();
        $this->data['shop'] = $channel_details->shop;
        $this->data['access_token'] = $channel_details->access_token;
        $this->data['charge_id'] = $this->data['subscription_details']->recurring_application_charge_id;
        return $this->data;
    }

    public function catalog_charge($user) {
        $org = Organization::where('id',$user->organization_id)->first();

        $shopify_subscribed_plan_id = ShopifySubscription::where('organization_id',$user->organization_id)->latest('id')->value('plan_id'); //getting shopify subscribed plan id
        if ($shopify_subscribed_plan_id) {
            //creating logic for applying limit on channels according shopify grow plan
            $plan = Plan::where('id',$shopify_subscribed_plan_id)->first();
            $added_channels = Channel::where('organization_id',$user->organization_id)->count();
        }

        if($org) {
            if ($org->is_subscribed()) {
                //if user comes from stripe
                if ($org->subscription_type() == 'stripe') {
                    $current_plan_id = $org->plans()->first();
                    if (Plan::where('handle', 'community_plan')->where(function ($q) use($current_plan_id) {
                        $q->where('stripe_monthly_id', $current_plan_id->current_stripe_plan)->orWhere('stripe_yearly_id', $current_plan_id->current_stripe_plan);
                    })->first()) {
                        //if user has subscribed grow plan
                        return false;
                    } else {
                        $no_of_channels = Plan::where('handle', $org->subscribed_plan_handle())->value('no_of_channels');
                        $channels_count = Channel::where('organization_id',$org->id)->count();
                        if ($channels_count >= $no_of_channels) {
                            return $org->subscription('default')->incrementQuantity(1, $current_plan_id->current_stripe_plan);
                        } else {
                            return true;
                        }
                    }
                } //if user comes from shopify
                else {

                    if ($this->plan_check() == 'free plan') //if user is on free plan
                    {
                        return false;
                    }

                    if (($plan->number_of_channels <= $added_channels) && ($plan->handle == 'community_plan')) // if limit of allowed channels is greater then added channels
                    {
                        return false;
                    }

                    if ($this->check_capped_amount()) { //if user is on paid plan
                        $this->shopify_base_url = "https://" . $this->shopify_api_key . ":" . $this->get_store_details()["access_token"] . "@" . $this->get_store_details()["shop"] . "/admin/api/" . self::$API_VERSION . "/";

                        $response = Http::retry(1, 50)->post($this->shopify_base_url . "recurring_application_charges/" . $this->get_store_details()["charge_id"] . "/usage_charges.json", [
                            "usage_charge" => [
                                "description" => "$100 for each catalog",
                                "price" => $this->subscribed_plan()->next_price,
                            ]
                        ]);

                        if ($response->successful()) {
                            $usage_charge_response = $response->json();
                            $current_plan = $this->get_store_details()['subscription_details'];
                            $current_plan->balance_remaining = $usage_charge_response['usage_charge']['balance_remaining'];
                            $current_plan->balance_used = $usage_charge_response['usage_charge']['balance_used'];
                            $current_plan->save();
                            return true;
                        }
                    } else {
                        return false;
                    }
                }
            }
            else {
                $invite = new Invite();
                if ($invite->IsInvited(Auth::user()->email)) { //if user is on invited
                    $channels = Channel::where('organization_id',$org->id)->count();
                    $allowed_channels = Plan::where('handle','free_plan')->value('no_of_channels');
                    return $channels < $allowed_channels ? true : false;
                } else { //if user is on trial
                    $channels = Channel::where('organization_id',$org->id)->count();
                    $allowed_channels = Plan::where('handle','standard_plan')->value('no_of_channels');
                    return $channels < $allowed_channels ? true : false;
                }
            }
        } else {
            return false;
        }


    }
}
