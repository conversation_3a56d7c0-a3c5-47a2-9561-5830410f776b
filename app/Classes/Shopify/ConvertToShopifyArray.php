<?php

namespace App\Classes\Shopify;

use Exception;
use InvalidArgumentException;
use Apimio\Gallery\Models\File;
use App\Models\Channel\Channel;
use App\Models\Product\Variant;
use App\Models\Product\Category;
use App\Models\Product\Attribute;
use App\Models\Product\Inventory;
use Illuminate\Support\Facades\DB;
use App\Models\Product\FileProduct;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\Client\Response;
use Illuminate\Support\Facades\Http;
use App\Models\Notification\ErrorLog;
use App\Models\Channel\ChannelProduct;
use App\Models\Channel\ChannelVariant;
use App\Models\Channel\ShopifyChannel;
use App\Models\Channel\CategoryChannel;
use App\Models\Product\CategoryProduct;
use Illuminate\Validation\Rules\Unique;
use App\Models\Channel\ChannelFileProduct;
use App\Models\Channel\ChannelProductStatus;

class ConvertToShopifyArray
{
    public static $API_VERSION = "2024-10";
    public $shopify_array;
    public $shopify_base_url;
    public $shopify_api_key;
    public $product;
    public $product_image_id;
    public $channel_id;
    public $version_id;
    public $product_images = [];
    public $shopify_product_id;
    public $shopify_product_image_id;
    public $organization_id;

    public function __construct(public array $data)
    {
        $this->shopify_api_key = env("SHOPIFY_API_KEY");
        $this->product = $this->data['product']->first();
        $this->product_image_id = (isset($this->data['image_id']) ? $this->data['image_id'] : null);
        $this->organization_id = $this->data['organization_id'];
        $this->channel_id = $this->data['channel_id'];
        $this->shopify_base_url = $this->setBaseUrl();
        $this->version_id = $this->getVersionId();
        $this->shopify_product_id = $this->getProductShopifyId();
        $this->shopify_product_image_id = $this->getProductShopifyImageId();
    }

    /**
     * @return string
     */
    public function setBaseUrl(): string
    {
        $shopify_channel=ShopifyChannel::query()->where('channel_id',$this->channel_id)->first();
        return "https://".$this->shopify_api_key.":".$shopify_channel->access_token."@".$shopify_channel->shop."/admin/api/".self::$API_VERSION."/";
    }

    /**
     * @return $this
     */
    public function convert(): ConvertToShopifyArray
    {
        $shopify_array = $this->defaultValues();

        if($shopify_array){
            unset(
                $shopify_array['price'],
                $shopify_array['weight'],
                $shopify_array['barcode'],
                $shopify_array['compare_at_price'],
                $shopify_array['sku']
            );

            $shopify_array['status'] = $this->status($shopify_array['status']);
            $shopify_array['options'] = $this->options();
            $shopify_array['optionValues'] = $shopify_array['options'];
            //    dd($shopify_array['optionValues']);
            // dd($this->variantCount());
            if($this->variantCount() == 0)
            {
                $shopify_array['variants'][] =  $this->defaultVariant();
            }
            else{
                $shopify_array['variants'] = $this->variants();
                // dd($shopify_array['variants']);
            }
            if(isset($shopify_array['image_src'])){
               $shopify_array['images'] = $this->convertImageStringArray();
            }

            unset(
                $shopify_array['image_src']
            );
                $collections = [];
                if(count($this->product['categories']) > 0) {
                    $sync_collections = new SyncCollection(
                        $this->product['categories'],
                        $this->shopify_product_id,
                        $this->shopify_base_url,
                        $this->organization_id,
                        $this->channel_id
                    );
                    $sync_collections->createCollectionArray();
                    $collections = $sync_collections->main_category['new_categories'];
                 }
               $sync = new Sync([
                'collections' => $collections,
                'organization_id' => $this->organization_id,
                'channel_id' => $this->channel_id,
                'shopify_base_url' => $this->shopify_base_url,
                'media' => $shopify_array['images'] ?? [],
                'product_id' => $this->product->id ?? null,
                'shopify_product_id' => $this->shopify_product_id ?? null,
                ]);

               $sync->sync();
               $this->product->load([
                'categories.channel' => function($q){
                    $q->where('channel_id', $this->channel_id)
                      ->where('store_connect_type', 'shopify');
                }
            ]);
                $collectionsToJoin = [];
                $collectionsToLeave = [];
                $categoriesToLeave = DB::table('category_channels')
                    ->join('category_product', 'category_product.category_id', '=', 'category_channels.category_id')
                    ->where('category_channels.channel_id', $this->channel_id)
                    ->where('category_product.product_id', $this->product->id)
                    ->whereNot('category_product.deleted_at', null)
                    ->select('category_channels.*') // Ensuring only category_channels fields are selected
                    ->get();
// dd($this->product['categories']);
                foreach ($this->product['categories'] as $value) {
                    if(isset($value->channel)){
                        $collectionsToJoin[] =   "gid://shopify/Collection/".$value->channel->store_connect_id;
                    }
                }

                foreach ($categoriesToLeave as $value) {
                    $collectionsToLeave[] =   "gid://shopify/Collection/".$value->store_connect_id;
                }
                // save metafields for new product
                $sync_metafield = new SyncMetafield();

                //get attribute list array
                $attributes = $sync_metafield->createAttributeArray($this->product);

                $inventories= Inventory::with('channelLocation')->where("product_id",$this->product->id)
                ->whereNotNull("location_id")
                ->whereNotNull("store_connect_id")
                ->whereRelation("channelLocation" , "channel_id" , $this->channel_id)
                ->get();
                $adjustments = [];
                foreach ($inventories as $quantity) {
                    if (isset($quantity->channelLocation) && isset($quantity->store_connect_id)) {
                        $adjustments[] = [
                            'quantity'           => isset($quantity->available_quantity) ? $quantity->available_quantity : 0,
                            'inventoryItemId' => "gid://shopify/InventoryItem/" . $quantity->store_connect_id,
                            'locationId'      => "gid://shopify/Location/" . $quantity->channelLocation->store_connect_id,
                        ];
                    }
                }
                $inventoryQuantitiesPayload = [
                    'reason'  => 'correction',
                    'setQuantities' => $adjustments,
                ];


// dd($shopify_array['variants']);

// dd($attributes);
// dd($this->shopify_product_id);
   $this->shopify_array = [
        "productId" => "gid://shopify/Product/".$this->shopify_product_id,
        "productInput" => [
            "id" => "gid://shopify/Product/".$this->shopify_product_id,
            "title" => isset($shopify_array['title'])? ( is_array($shopify_array['title']) ? reset($shopify_array['title']) : $shopify_array['title'] ) :'',
            "descriptionHtml" => isset($shopify_array['body_html']) ? ( is_array($shopify_array['body_html']) ? reset($shopify_array['body_html']): $shopify_array['body_html'] ):'',
            "handle" => isset($shopify_array['handle']) ? $shopify_array['handle'] : '',
            "status" => strtoupper($this->status($shopify_array['status'])),
            "vendor" => isset($shopify_array['vendor'])? $shopify_array['vendor'] : '',
            "tags" => isset($shopify_array['tags'])? ( is_array($shopify_array['tags']) ?  reset($shopify_array['tags']) : $shopify_array['tags']) :'',
            "seo" => [
                "title" => isset($shopify_array['metafields_global_title_tag'])? ( is_array($shopify_array['metafields_global_title_tag']) ? reset($shopify_array['metafields_global_title_tag']) : $shopify_array['metafields_global_title_tag'] ) :'',
                "description" => isset($shopify_array['metafields_global_description_tag'])? ( is_array($shopify_array['metafields_global_description_tag']) ? reset($shopify_array['metafields_global_description_tag']) : $shopify_array['metafields_global_description_tag'] ) :''
            ],
            "collectionsToLeave" => $collectionsToLeave,
            "collectionsToJoin" => $collectionsToJoin ,
        ],
        "metafields"=> array_values($attributes),
        "updateVariants" => isset($shopify_array['variants']['updateVariants']) ? array_values($shopify_array['variants']['updateVariants']) : [],
        "createVariants" => isset($shopify_array['variants']['createVariants']) ? array_values($shopify_array['variants']['createVariants']) : [],
        'input' => $inventoryQuantitiesPayload
    ];
    // dd($this->shopify_array);
      return $this;

        }
        else{
            //when error occurs the status of the product change to not syncing
            (new ChannelProduct())->changeProductSyncStatus(['channel_id'=>$this->channel_id,'product_ids'=>[$this->product['id']],'status'=>0]);

            throw new InvalidArgumentException('Something went wrong');
        }
    }

    /**
     * @return mixed
     */
    public function defaultValues(): mixed
    {
        return !empty($this->data['shopify_array'])
            ?(!empty($this->data['shopify_array'][0]['parent'])
                ?(!empty($this->data['shopify_array'][0]['parent']['Shopify'])
                    ?$this->data['shopify_array'][0]['parent']['Shopify']
                    :null)
                :null)
            :null;
    }

    /**
     * @return mixed|null
     */
    public function getWeightUnit(): mixed
    {

        if (isset($this->product['versions'][0]) && isset($this->product['versions'][0]['families'])){
            $general_family =  $this->product['versions'][0]['families']->where('name','General')->first()->toArray();
            $attributes = array_map(function($q){
                if($q['handle'] == 'weight'){
                    if($q['value']->first()){
                        return $q['value']->first()->unit;
                    }
                }
                return "";
            },$general_family['attributes']);
            $unit = array_values(array_filter($attributes));
        }

        return $unit[0] ?? null;
    }

    /**
     * @param $value
     * @return string
     */
    public function status($value): string
    {
        if($value){
            return $value;
        }else{
            return "draft";
        }
    }


    /**
     * convert string to array
     * @return array
     */
    public function convertImageStringArray(): array {
        $file_list = [];

        foreach ($this->product->files as $key => $file) {
            if(isset($file->channelFile) && isset($file->channelFile->store_connect_id)){
                // $file_list[] = ['id'=> $file->channelFile->store_connect_id];
            }else{
                $file_list[] =  [
                    "mediaContentType" => "IMAGE",
                    "originalSource"=> $file->bucket_url ?? $file->link,
                    "alt"=>'product-'.$file->id.'-1'
                    ];
            }
       }

       if(isset($this->product->variants) && count($this->product->variants) > 0){

        foreach ($this->product->variants as $item) {
            if(isset($item->ChannelVariants) && isset($item->ChannelVariants->store_connect_image_id)){
                // return  ['id'=> $item->store_connect_image_id];
            }else{
                if(isset($item->file) && !isset($item->file)){
                    $file_list[] = [
                        "mediaContentType" => "IMAGE",
                        "originalSource"=> $item->file->bucket_url ?? $item->file->link,
                        "alt"=> 'variant-'.$item->file->id.'-'.$item->id
                    ];
                }
            }
       }
        // $file_list[] = $this->product->variants->unique()->values()->transform(function ($item) {

        //     if(isset($item->ChannelVariants) && isset($item->ChannelVariants->store_connect_image_id)){
        //         // return  ['id'=> $item->store_connect_image_id];
        //     }else{
        //         if(isset($item->file)){
        //             return [
        //                 "mediaContentType" => "IMAGE",
        //                 "originalSource"=> $item->file->bucket_url ?? $item->file->link,
        //                 "alt"=> $item->file->bucket_url.'-'.$item->file->id ?? $item->file->link.'-'.$item->file->id
        //             ];
        //         }
        //     }
        //    })->toArray();

        // $file_list = array_merge($file_list, (isset($shopify_image_ids) ? $shopify_image_ids : []));
       }

        // $src_file_list = array_filter($file_list, function ($item) {
        //     return isset($item['src']);
        // });
        // $src_file_list = array_values($src_file_list);

        // $file_list = array_filter($file_list, function ($item) {
        //     return !isset($item['src']);
        // });

        // $file_list = array_values($file_list);

        // $src_file_list = array_chunk($src_file_list , 35);

        // $first_chunk = array_shift($src_file_list);

        // $file_list = array_merge($file_list, (isset($first_chunk) ? $first_chunk : []));

        // $this->product_images =  array_values($src_file_list);

        // $file_list = $this->excludeDuplicatesByKey($file_list);

        return $file_list;

    }

    function excludeDuplicatesByKey($array) {
        $tempArray = [];
        $resultArray = [];

        foreach ($array as $subArray) {
            if(isset($subArray['id'])){
                if (!in_array($subArray['id'], $tempArray) ) {
                        $tempArray[] = $subArray['id'];
                        $resultArray[] = $subArray;
                }
            }else{
                if(isset($subArray['src'])){
                if (!in_array($subArray['src'], $tempArray) ) {
                    $tempArray[] = $subArray['src'];
                    $resultArray[] = $subArray;
                  }
                }
            }
        }

        return $resultArray;
    }
    /**
     * @param $files
     * @return array
     */
    public function images($files): array
    {
        $count=1;
        $images=array();

        // foreach ( $this->product->files as $file)
        foreach ( $files as $file)
        {
            $images[] = [
                "src"=> $file,
                // "src"=> $file->link,
                //"width"=> $file->width,
                // "height"=> $file->height,
                "position"=> $count,
                // "created_at"=> $file->created_at,
                // "updated_at"=> $file->updated_at,
            ];
            $count++;
        }

        return $images;
    }


    /**
     * @return string
     */
    public function image(): string
    {
        if(isset($this->product['files'][0])){
            return $this->product['files'][0]['link'];
        }
        return "";
    }

        /**
     * @return array
     */
    public function SingleVariantOptions($variant): array
    {
                    $options = array();
                    if(!is_array($variant->option)){
                        $variant_options = [$variant->option];
                    }else{
                        $variant_options =$variant->option;
                    }

                    $variantModel = new Variant();
                    $selected_attributes_id = $variantModel->getVariantAttributeIds($variant_options);

                    $attributes = Attribute::where('attribute_type_id', 13)
                    ->with('attribute_options','attributeChannel')
                    ->whereIn('id', $selected_attributes_id)
                    ->get();

                    foreach ($attributes as $attribute) {
                        foreach ($attribute->attribute_options as $option) {
                            foreach ($variant_options as $variant) {
                                $variantData = json_decode($variant, true);
                                foreach ($variantData['attributes'] as $key => $variantAttribute) {
                                    if ($variantAttribute['id'] == $attribute->id && in_array($option->name, $variantData['options'])) {
                                        $options[$key]['optionId'] = "gid://shopify/ProductOption/".$attribute?->attributeChannel->first()->store_connect_id;
                                        $options[$key]['name'] = $option->name;
                                        break 2;
                                    }
                                }
                            }
                        }
                    }

        if(count($options) > 0){
            return $options;
        }
        else{
            $defaultAttribute = Attribute::where('attribute_type_id', 13)
            ->where('is_default', 1)
            ->join('attribute_channels', 'attributes.id', '=', 'attribute_channels.attribute_id')
            ->whereNotNull('attribute_channels.store_connect_id')
            ->where('attribute_channels.channel_id', $this->channel_id)
            ->select('attributes.id', 'attribute_channels.store_connect_id')
            ->first();
            $options[0]['optionId']="gid://shopify/ProductOption/".$defaultAttribute->store_connect_id ?? null;
            $options[0]['name']="Title";
            // $options[0]['position']=1;
            // $options[0]['values']=["Default Title"];
            return $options;
        }
        return [];
    }

    /**
     * @return array
     */
    public function options(): array
    {
        $options = array();
        $count = 1;
        $product =  $this->product;
        if(sizeof($product->variants) > 0)
        {
            $variant_options = $product->variants->pluck('option');
                    $variantModel = new Variant();
                    $selected_attributes_id = $variantModel->filter_already_assigned_variants($variant_options);
                    $attributes = Attribute::where('attribute_type_id', 13)
                    ->where('is_default', '!=', 1)
                    ->with('attribute_options','attributeChannel')
                    ->whereIn('id', $selected_attributes_id)
                    // ->where(function ($query) {
                    //      $query->doesntHave('attributeChannel')
                    //            ->orWhereHas('attributeChannel', function ($q) {
                    //                $q->whereNull('store_connect_id')
                    //                  ->where('channel_id', $this->channel_id);
                    //            });
                    // })
                    ->get();

                    foreach ($attributes as $key => $attribute) {
                            $count = $key;
                            $options[$key]['optionId'] = $attribute->name;
                            $options[$key]['name'] = $attribute->name;
                            // $options[$key]['position'] = ++$count;

                        foreach ($attribute->attribute_options as $option) {
                            foreach ($variant_options as $variant) {
                                $variantData = json_decode($variant, true);
                                foreach ($variantData['attributes'] as $variantAttribute) {
                                    if ($variantAttribute['id'] == $attribute->id && in_array($option->name, $variantData['options'])) {
                                        $options[$key]['values'][] = $option->name;
                                        break 2;
                                    }
                                }
                            }
                        }
                    }
        }
        else
        {
            $defaultAttribute = Attribute::where('attribute_type_id', 13)
            ->where('is_default', 1)
            ->join('attribute_channels', 'attributes.id', '=', 'attribute_channels.attribute_id')
            ->whereNotNull('attribute_channels.store_connect_id')
            ->where('attribute_channels.channel_id', $this->channel_id)
            ->select('attributes.id', 'attribute_channels.store_connect_id')
            ->first();
            $options[0]['optionId']=$defaultAttribute->store_connect_id ?? null;
            $options[0]['name']="Title";
            // $options[0]['position']=1;
            // $options[0]['values']=["Default Title"];
        }
        if(count($options) > 0){
            return $options;
        }
        else{
            $defaultAttribute = Attribute::where('attribute_type_id', 13)
            ->where('is_default', 1)
            ->join('attribute_channels', 'attributes.id', '=', 'attribute_channels.attribute_id')
            ->whereNotNull('attribute_channels.store_connect_id')
            ->where('attribute_channels.channel_id', $this->channel_id)
            ->select('attributes.id', 'attribute_channels.store_connect_id')
            ->first();
            $options[0]['optionId']=$defaultAttribute->store_connect_id ?? null;
            $options[0]['name']="Title";
            // $options[0]['position']=1;
            // $options[0]['values']=["Default Title"];
            return $options;
        }
        return [];
    }


    /**
     * @return int
     */
    public function variantCount(): int
    {
        return isset($this->data['shopify_array'][0]['variants']) ? count($this->data['shopify_array'][0]['variants']) : 0;
    }

    /**
     * @return array
     */
    public function defaultVariant(): array
    {

        $values = $this->defaultValues();
        // dd($values);
       // $weight = UnitFacade::convert_value(['from_unit'=>'g','to_unit'=>$this->getWeightUnit(),'value'=>$values['weight']]);
        $array = [
            'title'=>$values['title'],
            'sku'=> $values['sku'] ?? null,
            'barcode'=>$values['barcode'] ?? null,
            'price'=>$values['price'] ?? null,
            'weight'=>$values['weight'] ?? null,
          //  'inventory_quantity'=>$values['inventory_quantity']?? null,
            'compare_at_price'=>$values['compare_at_price'] ?? null,
            // 'cost_price'=>$values['cost_price'] ?? 1,
            'weight_unit'=>$this->getWeightUnit(),
        ];

        if(isset($values['track_quantity'])){
            $array["inventory_management"] = isset($values['track_quantity']) && $values['track_quantity'] == 1 ? "shopify" : null;
            $array["inventory_policy"] = (isset($values['track_quantity']) && $values['track_quantity'] == 0 ? "deny" : ( isset($values['continue_selling']) && $values['continue_selling'] == 1 ? "continue" : "deny"));
        }

        return $array;
    }

    /**
     * @return array
     */
    public function variants(): array
    {
        $attribute_array = array();
        $variant = $this->data['shopify_array'][0]['variants'] ?? false;

        if($variant){
            $count = 1;

            for ($i = 0;$i< sizeOf($this->product['variants']); $i++)
            {
                $status = 'createVariants';
                $count1 = 1;
                if(isset($variant[$i]['store_connect_id'])){
                    $status = 'updateVariants';
                    $attribute_array[$status][$i]['id'] = "gid://shopify/ProductVariant/". $variant[$i]['store_connect_id'];
                }
                // if(isset($variant[$i]['title']))
                //     $attribute_array[$i]['title'] = $variant[$i]['title'];
                if(isset($variant[$i]['sku']))
                    $attribute_array[$status][$i]['inventoryItem']['sku'] = $variant[$i]['sku'];
                if(isset($variant[$i]['barcode']))

                    $attribute_array[$status][$i]['barcode'] = $variant[$i]['barcode'];
                if(isset($variant[$i]['price']) && $variant[$i]['price'] != "")
                    $attribute_array[$status][$i]['price'] = $variant[$i]['price'];
                if(isset($variant[$i]['compare_at_price']) && $variant[$i]['compare_at_price'] != "") {
                    $attribute_array[$status][$i]['compareAtPrice'] = $variant[$i]['compare_at_price'];


                // if(isset($variant[$i]['cost_price'])) {
                    // $attribute_array[$i]['cost_price'] = $variant[$i]['cost_price'] ?? 1;
                // }
                if(isset($variant[$i]['weight'])) {
                   // $attribute_array[$i]['weight'] = UnitFacade::convert_value(['from_unit'=>'g','to_unit'=>$variant[$i]['weight_unit'],'value'=>$variant[$i]['weight']]);
                     $attribute_array[$i]['weight'] = $variant[$i]['weight'];

                }
                // $attribute_array[$status][$i]['mediaId'] = "gid://shopify/MediaImage/33809798398176";
                // if(isset($variant[$i]['store_connect_image_id'])){
                //     $attribute_array[$status][$i]['mediaId'] = "gid://shopify/MediaImage/".$variant[$i]['store_connect_image_id'];
                // }else{
                //     if(isset($variant[$i]['image']) && $variant[$i]['image'] != ""){
                //         $attribute_array[$status][$i]['mediaSrc'] = $variant[$i]['image'];
                //     }
                // }
                if(isset($variant[$i]['continue_selling'])) {
                    $attribute_array[$status][$i]['inventoryPolicy'] = ($variant[$i]['track_quantity'] == 0 ? "CONTINUE" : (  $variant[$i]['continue_selling']  == 1 ? "CONTINUE" : "DENY"));
                }
                // $attribute_array[$status][$i]['position'] = $count;
                if(isset($variant[$i]['track_quantity'])) {
                    $attribute_array[$status][$i]['inventoryItem']['tracked'] = $variant[$i]['track_quantity'] == 1 ? true : false;
                }
                if(isset($variant[$i]['cost_price']) && $variant[$i]['cost_price'] != "") {
                    $attribute_array[$status][$i]['inventoryItem']['cost'] = (float)$variant[$i]['cost_price'];
                }
                $unit = "KILOGRAMS";
                if(isset($variant[$i]['weight_unit'])) {
                    if($variant[$i]['weight_unit'] == "oz"){
                        $unit = "OUNCES";
                    }else if($variant[$i]['weight_unit'] == "lb"){
                        $unit = "POUNDS";
                    }else if($variant[$i]['weight_unit'] == "g"){
                        $unit = "GRAMS";
                    }
                    $attribute_array[$status][$i]['inventoryItem']['measurement']['weight'] = [
                        'unit' => $unit,
                        'value' => isset($variant[$i]['weight']) ? (float)$variant[$i]['weight'] : 0.00,
                    ];
                }

                if($status == "createVariants"){
                    $inventories= Inventory::with('channelLocation')->where("product_id",$this->product->id)
                    ->whereNotNull("location_id")
                    ->where("variant_id" , $this->product['variants'][$i]['id'] )
                    ->get();
                    foreach ($inventories as $key => $quantity) {
                        if (isset($quantity->channelLocation)) {
                            $attribute_array[$status][$i]['inventoryQuantities'][$key]['availableQuantity'] = isset($quantity->available_quantity) ? $quantity->available_quantity : 0;
                            $attribute_array[$status][$i]['inventoryQuantities'][$key]['locationId'] = "gid://shopify/Location/" . $quantity->channelLocation->store_connect_id;
                        }
                    }
                }

                $attribute_array[$status][$i]['optionValues'] =  $this->SingleVariantOptions($this->product['variants'][$i]);

                $count++;
            }
        }
        return ($attribute_array);
    }

    /**
     * @param $url
     * @return string
     */
    private function file_url($url): string
    {
        $parts = parse_url($url);
        $path_parts = array_map('rawurldecode', explode('/', $parts['path']));
        return $parts['scheme'] . '://' .
            $parts['host'] .
            implode('/', array_map('rawurlencode', $path_parts));


    }
    private function getAndSaveImageId($image_id, $shopify_id){

            $file_product = FileProduct::query()
                ->where('file_id',$image_id)
                ->where('product_id',$this->product['id'])
                ->first();
            if($file_product){
                $channel_file_product = ChannelFileProduct::query()
                ->where([
                    'channel_id'=>$this->channel_id,
                    'store_connect_type'=>'shopify',
                    'file_product_id'=>$file_product->id
                ])
                ->first();
                if(!$channel_file_product){
                    $channel_file_product =  new ChannelFileProduct();
                }
                $channel_file_product->channel_id = $this->channel_id;
                $channel_file_product->file_product_id = $file_product->id;
                $channel_file_product->store_connect_type = 'shopify';
                $channel_file_product->store_connect_id = $shopify_id;
                $channel_file_product->save();
            }
    }

    private function getImageIdusingImageUrl($url){

        $imageName = basename(parse_url($url, PHP_URL_PATH));
        $file = File::where("name", "LIKE", "%".$imageName."%")->first();
        if($file){
            return $file->id;
        }
        $baseImageNameArray = explode('_', $imageName);
        foreach($baseImageNameArray as $name){
            $obj = new File();
            $obj = $obj->where("name", "LIKE", "%".$name."%")->get();
            if(count($obj) == 1){
                $file =  $obj[0];
                break;
            }
        }
        if($file){
            return $file->id;
        }
        return null;
    }

     private function SaveImageId($shopify){
        $image_id = $this->getImageIdusingImageUrl($shopify["src"]);
        $file_product = FileProduct::query()
            ->where('file_id',$image_id)
            ->where('product_id',$this->product['id'])
            ->first();
        if($file_product){
            $channel_file_product = ChannelFileProduct::query()
            ->where([
                'channel_id'=>$this->channel_id,
                'store_connect_type'=>'shopify',
                'file_product_id'=>$file_product->id
            ])
            ->first();
            if(!$channel_file_product){
                $channel_file_product =  new ChannelFileProduct();
            }
            $channel_file_product->channel_id = $this->channel_id;
            $channel_file_product->file_product_id = $file_product->id;
            $channel_file_product->store_connect_type = 'shopify';
            $channel_file_product->store_connect_id = $shopify["id"];
            $channel_file_product->save();
        }
}

    /**
     * @param $image
     * @return mixed
     */
    public function saveImage($image_data): mixed  // TODO
    {
        if(isset($image_data['src'])){
            $image = $image_data['src'];
            $image_id = null;
           }else{
            $image = $image_data['link'];
            $image_id = $image_data['id'];
           }
        // get file name from url
        $image = ltrim($image,' ');
        $image = strtok($image, '?');

        $array=explode("/",$image);
        $file_name =  end($array);

        if(isset($image) && $image != '' && $image != null ){

            $image_name = $this->file_url($image);
            //
        }else{
            $image_name = null;
        }

        //covert url to base64 image
        try{
            if(isset($image_name) && $image_name != '' && $image_name != null ){

                $b64image = base64_encode(file_get_contents($image_name));

            }else{
                $b64image = null;
            }
        }
        catch(Exception $e){
            Log::channel('shopify')->error($e);
            return null;
        }


        // save the image of the variant in product of shopify
        $image_response = Http::retry(1, 1000)
            ->post($this->shopify_base_url."products/".$this->shopify_product_id."/images.json", [
            "image" => [
                'attachment'=>$b64image,
                'filename'=> $file_name
            ]
        ]);

        if($image_response->successful()){
            $image_data = $image_response->json();

            if(!isset($image_id)){
                $image_id = $this->getImageIdusingImageUrl($image_data["image"]["src"]);
            }

            $this->getAndSaveImageId($image_id,$image_data['image']['id']);
            return $image_data;
        }
        elseif($image_response->failed()){
            $json = $image_response->json();

            $error = "";
            if (!is_array($json["errors"])) {
                $error .= implode("<br>", $json["errors"]);
            }else{
                foreach ($json["errors"] as $key => $error_row) {
                    if (is_array($error_row)) {
                        $error .= '<b>'.$file_name.' '. $key . "</b> " . implode("<br>", $error_row);
                    } else {
                        $error .= '<b>'.$file_name.' '. $key . "</b> " . $error_row;
                    }
                }
            }
            // $data = [
            //     'organization_id' => $this->organization_id,
            //     'description' => "There is an error in image while syncing product back to shopify with <b> sku : {$this->product['sku']} </b>.<br>  {$error}",
            //     'type' => 'shopify',
            //     'link' => route('products.edit', $this->product['id']),
            //     'link_text' => 'View Product',
            //     'status' => 'error',
            // ];

            // $error = new ErrorLog();
            // $error->setData($data)->store(function ($error) {
            //     Log::channel('shopify')->info('Error in saving ErrorLogs.');
            //     Log::channel('shopify')->error($error);
            // }, function () {
            // });

            Log::channel('shopify')->error($image_response->json());
            return null;
        }
        return null;
    }

    /**
     * @return void
     */
    public function saveImageWithVariant(): void
    {

        $sync_id=$this->shopify_product_id;
        $product_id=$this->product['id'];
        $variants = Variant::query()
            ->with(['file','channelVariant'=>function($q){
                $q->where('channel_id',$this->channel_id);
            }])
            ->where(['product_id'=>$product_id, 'version_id' => $this->version_id])
            ->get();

        if(sizeof($variants) > 0)
        {
            foreach ($variants as $variant){
                $image_response_data = null;

                if(isset($variant->channelVariant) && isset($variant->channelVariant->store_connect_image_id)){
                    continue;
                }
                else{
                    // check file present or not
                    if($variant->file_id){
                        //image name
                        if($variant->file) {
                            $variant_image = [
                                'link' => $variant->file->link,
                                'id' => $variant->file_id,
                            ];
                        }else{
                            continue;
                        }

                        // SAE VARIANT IMAGE IN SHOPIFY AND RETURN RESPONSE OF SHOPIFY
                        $image_response_data = $this->saveImage($variant_image);

                        if(!$image_response_data){
                            continue;
                        }
                    }
                }

                //update the shopify variant and add image id in it
                if(isset($image_response_data)){

                    //check if the shopify variant id is present in database or not
                    $variant_id = $variant->channelVariant->store_connect_id;

                    try{
                        $variant_response = Http::retry(1, 1000)->put($this->shopify_base_url . "variants/" . $variant_id . ".json", [
                            'variant' => [
                                'image_id'=>$image_response_data['image']['id'],
                            ],
                        ]);
                        if($variant_response->successful()){

                             $variant_response->json();

                            // save new image id in the variant table
                            $channel_variant = ChannelVariant::query()->find($variant->channelVariant->id);
                            $channel_variant->store_connect_image_id = $image_response_data['image']['id'];
                            $channel_variant->save();

                        }else{
                            $variant_response_error = $variant_response->json();
                            Log::channel('shopify')->info($variant_response_error);
                        }
                    }catch(Exception $e){
                        Log::channel('shopify')->error($e->getMessage());
                    }
                }

            }
        }
    }

    public function getChannelProduct()
    {
        return ChannelProduct::query()
            ->where('channel_id',$this->channel_id)
            ->where('product_id',$this->product['id'])->with(['status'=>function($q){
                $q->where('type','shopify');
            }])->first();
    }

    public function getChannelProductImage()
    {
        if(isset($this->channel_id) && isset($this->product_image_id) && isset($this->product['id'])){
            $file_product = FileProduct::query()
            ->where('file_id',$this->product_image_id)
            ->where('product_id',$this->product['id'])
            ->first();
            if(isset($this->shopify_product_id)){
                return ChannelFileProduct::query()
                ->where('channel_id',$this->channel_id)
                ->where('file_product_id',$file_product->id)
                ->where('store_connect_type',"shopify")
                ->first();
            }else{
              return null;
            }
        }else{
            return null;
        }

    }

    public function getChannelDetail(): \Illuminate\Database\Eloquent\Model|\Illuminate\Database\Eloquent\Collection|\Illuminate\Database\Eloquent\Builder|array|null
    {
        return Channel::query()->find($this->channel_id);
    }

    public function getProductShopifyId(){

        $product_sync = $this->getChannelProduct();
        if($product_sync && count($product_sync->status) > 0) {

            $status_response = $product_sync->status->first()->response;

            return !empty($status_response->sync_id)?$status_response->sync_id:null;
        }
        return null;
    }

    public function getProductShopifyImageId(){

        if($this->product_image_id){
            $product_file_sync = $this->getChannelProductImage();
            if($product_file_sync) {
                return !empty($product_file_sync->store_connect_id)?$product_file_sync->store_connect_id:null;
            }
        }
        return null;
    }


    /**
     * @param $syncing_method
     * @return string
     */
    public function getProductShopifyIdByHandle($syncing_method): string
    {

        $query =
            <<<QUERY
            query getProductIdFromHandle(\$handle: String!) {
              productByHandle(handle: \$handle) {
                id
              }
            }
            QUERY;

        $response = Http::withHeaders([
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
            ])->post($this->shopify_base_url.'graphql.json', [
                'query' => $query,
                'variables' => [
                    'handle' => $this->shopify_array['handle'] ,
                ],
            ]);

        if ($response->successful()) {

            $data = $response->json();

            $idString = $data['data']['productByHandle']['id'] ?? null;

            if ($idString) {
                // Extract the numeric product ID using a regular expression
                preg_match('/\d+/', $idString, $matches);
                $shopify_product_id =  $matches[0] ?? null;

                //if syncing method is to skip the product
                if ($syncing_method == "skip") {
                    $data = [
                        'organization_id' => $this->organization_id,
                        'description' => "The Product with <b> Handle : {$this->shopify_array['handle']} </b>  is already exists in shopify, so we are skiping it to sync in shopify.",
                        'type' => 'shopify',
                        'link' => route('products.edit', $this->product['id']),
                        'link_text' => 'View Product',
                        'status' => 'info',
                    ];

                    $error = new ErrorLog();
                    $error->setData($data)->store(function ($error) {
                        Log::channel('shopify')->info('Error in saving Error Logs.');
                        Log::channel('shopify')->error($error);
                    }, function () {
                    });
                    return "skip";

                } else if ($syncing_method == "update") {

                    $this->saveProductStatus($shopify_product_id);
                    $this->shopify_product_id =  $shopify_product_id;
                }

            }
        }

        return "";

    }

    /**
     * @param $shopify_id
     * @return void
     */
    public function saveProductStatus($shopify_id): void
    {
        $product_status = ChannelProductStatus::query()
            ->where([
                'channel_product_id'=>$this->getChannelProduct()->id,
                'type'=>'shopify'
            ])
            ->first();
        if(!$product_status){
            $product_status = new ChannelProductStatus();
            $product_status->channel_product_id = $this->getChannelProduct()->id;
            $product_status->organization_id = $this->organization_id;
            $product_status->type = 'shopify';
        }
        $product_status->response =  ['sync_id' => $shopify_id];
        $product_status->save();
    }

    /**
     * @return mixed
     */
    public function getChannelProductStatusId(): mixed
    {
        $product_sync = $this->getChannelProduct();
        if($product_sync){
            $prod_status = $product_sync->status->first();
            if($prod_status){
                return $prod_status->id;
            }else{
                return null;
            }
        }else{
            return null;
        }
    }

    /**
     * @return bool|int
     */
    public function getVersionId(): bool|int
    {
        $version_id =  (new ShopifyChannel())->get_product_channel_version_ids($this->product['id'],$this->channel_id);
        return !empty($version_id) ? $version_id[0] : 0;
    }

    public function changeStatusWithoutUpdateTime(): void
    {
        $product_status = ChannelProductStatus::query()->find($this->getChannelProductStatusId());

        $product_status->timestamps = false;

        $updated_at= $product_status->updated_at;
        $product_status->status = 0;
        $product_status->updated_at = $updated_at ;
        $product_status->save();

        $product_status->timestamps = true;
    }

    /**
     * @param $error_func
     * @param $success_func
     * @return mixed|void
     */
    public function create($error_func, $success_func){
        try{
            //get the syncing method
            $syncing_method = $this->getChannelDetail()->syncing_method??'skip';

           //check if shopify id exists or not
            if(!$this->shopify_product_id) {

                // if product should not  create
                if($syncing_method != "new") {
                    //check by handle name and get the shopify product id
                    if (isset($this->shopify_array['handle']) && $this->shopify_array['handle'] != "") {


                            $handle_syncing_response = $this->getProductShopifyIdByHandle($syncing_method);
                            if ($handle_syncing_response == "skip") {
                                $this->changeStatusWithoutUpdateTime();
                                return $success_func();

                            }
                    }
                }
            }

            if($this->shopify_product_id){
                //  $this->shopify_array['id'] = $this->shopify_product_id;

                $response = $this->updateProduct();
                // CategoryProduct::withTrashed()
                //     ->where('category_product.product_id', $this->product->id)
                //     ->whereNotNull('category_product.deleted_at')
                //     ->forceDelete();
dd($response->json());
                if($response->successful())
                {
                    $data = $response->json()['data'];

                    // if(isset($data["product"]["images"])){
                    //    foreach($data["product"]["images"] as $key => $image){
                    //       $this->SaveImageId($image);
                    //    }
                    // }

                    // //save main Images of the product
                    // if(!empty($this->product_images)){
                    //     //set the check if the product images are less than 15 then it will sync with product
                    //     foreach($this->product_images as $images){
                    //         foreach($images as $image){
                    //             $this->saveImage($image);
                    //         }
                    //     }
                    // }
                    if(isset($data['productVariantsBulkCreate'])){
                    //save the id of the shopify variants in database
                    $this->saveVariantIds($data['productVariantsBulkCreate']['productVariants']);
                    }


                    // // to save the image and then assign the image id to variant
                    // $this->saveImageWithVariant();


                    // // save metafields for new product
                    // $sync_metafield = new SyncMetafield();

                    // //get attribute list array
                    // $attributes = $sync_metafield->createAttributeArray($this->product);

                    // save attributes in shopify

                    // try{

                    //    $sync_metafield->store($attributes, $this->shopify_base_url, $this->shopify_product_id, $this->product, $this->organization_id);

                    // }
                    // catch (Exception $e){
                    //     // $this->changeStatusWithoutUpdateTime();

                    //     return $error_func(["main" =>$e->getMessage()]);
                    // }

                    // save collections for new product
                    // if(count($this->product['categories']) > 0) {

                    //         $sync_collections = new SyncCollection(
                    //             $this->product['categories'],
                    //             $this->shopify_product_id,
                    //             $this->shopify_base_url,
                    //             $this->organization_id,
                    //             $this->channel_id
                    //         );

                    //         $sync_collections->createCollectionArray()->sync();
                    // }

                    // $product_status = ChannelProductStatus::query()
                    //     ->find($this->getChannelProductStatusId());
                    // $product_status->status = 0;
                    // $product_status->save();

                    return $success_func();
                }
                else{

                    $json = $response->json();
                    if($json) {
                        $error = "";
                        if (!is_array($json["errors"])) {
                            $error .= $json["errors"];
                        }else{
                            foreach ($json["errors"] as $key => $error_row) {
                                if (is_array($error_row)) {
                                    $error .= $key . " " . implode("<br>", $error_row);
                                } else {
                                    $error .= $key . " " . $error_row;
                                }
                            }
                        }

                        return $error_func(["main" => $error]);
                    }
                }
            }
            else{
                unset($this->shopify_array['productInput']['id']);
                unset($this->shopify_array['productInput']['collectionsToLeave']);
                $response =  $this->createProduct();
                if($response->successful()) {
                    $data = $response->json();
                    $this->shopify_product_id = $data['product']['id'];
                    //save main Images of the product

                    if(isset($data["product"]["images"])){
                    foreach($data["product"]["images"] as $key => $image){
                        $this->SaveImageId($image);
                    }
                    }
                    //save the shopify product id to the database table
                    $this->saveProductStatus($data['product']['id']);

                     //save main Images of the product
                     if(!empty($this->product_images)){
                         //set the check if the product images are less than 15 then it will sync with product
                         foreach($this->product_images as $images){
                             foreach($images as $image){
                                 $this->saveImage($image);
                             }
                         }
                     }

                    //save the id of the shopify variants in database
                    $this->saveVariantIds($data['product']);

                    // send images of variants in shopify and assign image ids to variants
                    $this->saveImageWithVariant();

                    // save metafields for new product
                    $sync_metafield = new SyncMetafield();

                    //get attribute list array
                    $attributes = $sync_metafield->createAttributeArray($this->product);

                    try{
                        $sync_metafield->store($attributes, $this->shopify_base_url, $this->shopify_product_id, $this->product, $this->organization_id);
                    } catch (Exception $e){
                        return $error_func(["main" =>$e->getMessage()]);
                    }

                    // save collections for new product
                    if(count($this->product['categories']) > 0) {

                        $sync_collections = new SyncCollection(
                            $this->product['categories'],
                            $this->shopify_product_id,
                            $this->shopify_base_url,
                            $this->organization_id,
                            $this->channel_id
                        );
                        $sync_collections->createCollectionArray()->sync();
                    }

                    // $product_status = ChannelProductStatus::query()->find($this->getChannelProductStatusId());
                    // $product_status->status = 0;
                    // $product_status->save();

                    return $success_func();

                }
                else{
                    $json = $response->json();
                    $error = "";
                    if (!is_array($json["errors"])) {
                        $error .= $json["errors"];
                    }else {
                        foreach ($json["errors"] as $key => $error_row) {
                            $error .= $key . " " . implode("<br>", $error_row);
                        }
                    }
                    return $error_func(["main" =>$error]);
                }
            }
        }
        catch(Exception $exception){
            Log::channel('shopify')->error('main error');
            Log::channel('shopify')->error($exception);
            $error_func(["main" =>$exception->getMessage()]);
        }

    }

    public function updateProduct(): Response
{
    $query = <<<GQL
    mutation productUpdateAndMetafieldsSet(\$productId: ID!, \$productInput: ProductInput!, \$metafields: [MetafieldsSetInput!]!, \$updateVariants: [ProductVariantsBulkInput!]!, \$createVariants: [ProductVariantsBulkInput!]!, \$input: InventorySetOnHandQuantitiesInput!) {
        productUpdate(input: \$productInput) {
            product {
                id
            }
            userErrors {
                field
                message
            }
        }
        metafieldsSet(metafields: \$metafields) {
            metafields {
                key
                namespace
                value
            }
            userErrors {
                field
                message
                code
            }
        }
        productVariantsBulkUpdate(productId: \$productId, variants: \$updateVariants) {
            productVariants {
                id
                title
                selectedOptions {
                    name
                    value
                }
            }
            userErrors {
                field
                message
            }
        }
        productVariantsBulkCreate(productId: \$productId, variants: \$createVariants) {
            productVariants {
                id
                inventoryItem{
                    sku
                    id
                }
                selectedOptions {
                    name
                    optionValue{
                       id
                       name
                    }
                }
            }
            userErrors {
                field
                message
            }
        }
        inventorySetOnHandQuantities(input: \$input) {
            inventoryAdjustmentGroup {
                id
                reason
                referenceDocumentUri
                changes {
                    name
                    delta
                }
                }
            userErrors {
            field
            message
            }
        }
    }
    GQL;

    return  $response = Http::retry(1, 1000)
    ->withHeaders([
    'Content-Type' => 'application/json',
    'Accept' => 'application/json',
    ])
    ->timeout(180)
        ->post($this->shopify_base_url . "graphql.json", [
            'query' => $query,
            'variables' => $this->shopify_array
        ]);
        // dd($response->json());
}

    /**
     * @return Response
     */
    public function createProduct(): Response  // TODO
    {
        $query = <<<GQL
        mutation productCreateAndMetafieldsSet(\$productId: ID!, \$productInput: ProductInput!, \$metafields: [MetafieldsSetInput!]!, \$updateVariants: [ProductVariantsBulkInput!]!, \$input: InventorySetOnHandQuantitiesInput!) {
            productUpdate(input: \$productInput) {
                product {
                    id
                }
                userErrors {
                    field
                    message
                }
            }
            metafieldsSet(metafields: \$metafields) {
                metafields {
                    key
                    namespace
                    value
                }
                userErrors {
                    field
                    message
                    code
                }
            }
            productVariantsBulkUpdate(productId: \$productId, variants: \$updateVariants) {
                productVariants {
                    id
                    title
                    selectedOptions {
                        name
                        value
                    }
                }
                userErrors {
                    field
                    message
                }
            }
            inventorySetOnHandQuantities(input: \$input) {
                inventoryAdjustmentGroup {
                    createdAt
                    reason
                    referenceDocumentUri
                    changes {
                        name
                        delta
                    }
                    }
                userErrors {
                field
                message
                }
            }
        }
        GQL;
    $response = Http::retry(1, 1000)
    ->withHeaders([
    'Content-Type' => 'application/json',
    'Accept' => 'application/json',
    ])
    ->timeout(180)
        ->post($this->shopify_base_url . "graphql.json", [
            'query' => $query,
            'variables' => $this->shopify_array
        ]);
        dd($response->json());
    }

   public function saveInventoryId($variant, $v = null){
           $inventories = Inventory::query()
                ->where('organization_id',$this->organization_id)
                ->where('product_id',$this->product['id'])
                ->whereRelation("channelLocation" , "channel_id" , $this->channel_id);

           if($v){
               $inventories =  $inventories->where('variant_id',$v->id)
                                            ->whereNull('store_connect_id');
           }

           $inventories = $inventories->get();

           foreach ($inventories as $inventory){

                $inventory->store_connect_id = $variant['inventory_item_id'];
                $inventory->store_type = 'shopify';
                $inventory->save();

                // connect inventory id with location id in shopify
               (new SyncInventories($inventory, $this->channel_id))->connect();

           }
            // NEW: If this variant has cost_price, update the InventoryItem cost
            if (isset($v->cost_price) && !empty($v->cost_price)) {
            $this->updateInventoryCost($variant['inventory_item_id'], $v->cost_price);
        }
   }

        /****
         * Update Shopify InventoryItem cost via REST
         * Requires plan + scopes to see/use cost
         ****/
        private function updateInventoryCost($inventoryItemId, $costPrice)
        {
            Http::retry(1, 1000)
                ->withHeaders([
                    'Content-Type' => 'application/json',
                    // If you use OAuth tokens, also set:
                    // 'X-Shopify-Access-Token' => $this->yourAccessToken,
                ])
                ->put($this->shopify_base_url . "inventory_items/" . $inventoryItemId . ".json", [
                    'inventory_item' => [
                        'cost' => $costPrice
                    ]
                ]);
            usleep(600000);
        }

    /**
     *
     * Save the variant id of newly created variants in variant table
     *
     * @param $product
     * @return void
     */

    public function saveVariantIds($variants): void
    {
        foreach($variants as $variant){
            // $options = [];
            // if($variant['option1'])
            //     $options[] = $variant['option1'];
            // if($variant['option2'])
            //     $options[] = $variant['option2'];
            // if($variant['option3'])
            //     $options[] = $variant['option3'];

            $v =  Variant::query()->where('sku',$variant['inventoryItem']['sku'])->where('product_id',$this->product['id'])->first();
            // if($variant['title'] == "Default Title"){
            //     $this->saveInventoryId($variant,$v);
            //     return ;
            // }
            $shopifyId = $variant['id'] ?? null;
            $parts = explode('/', $shopifyId);
            $shopifyId = end($parts);
            if($v && isset($shopifyId)) {
                $channel_variant = ChannelVariant::query()
                    ->where('variant_id',$v->id)
                    ->where('channel_id',$this->channel_id)
                    ->first();
                if(!$channel_variant){
                    $channel_variant = new ChannelVariant();
                }
                $channel_variant->variant_id = $v->id;
                $channel_variant->channel_id = $this->channel_id;
                $channel_variant->store_connect_id = $shopifyId;
                // $channel_variant->store_connect_image_id = $variant["image_id"]?? ($channel_variant->store_connect_image_id??null);
                $channel_variant->store_connect_type = $variant_required_data['type'] ?? 'shopify';
                $channel_variant->save();
                //save inventory id
                // $this->saveInventoryId($variant , $v);
            }

        }
    }
}
