<?php

namespace App\Classes\Shopify;

use App\Models\Product\Product;
use App\Models\Product\Category;
use App\Traits\Shopify\ShopifyAPI;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use App\Jobs\Shopify\StoreCollectJob;
use App\Models\Notification\ErrorLog;
use App\Models\Channel\ShopifyChannel;
use App\Models\Product\CategoryProduct;
use App\Jobs\Shopify\StoreCustomCollectionJob;
use Ghazniali95\ShopifyConnector\App\Classes\Services\CollectService;
use Ghazniali95\ShopifyConnector\App\Classes\Services\CustomCollectionService;

class RetrieveCollection
{
    use ShopifyAPI;

    /**
     * @var array
     */
    private $products_id;

    public $batch;


    function __construct(public int $channel_id,public int $organization_id, $batch = null)
    {
        //set base url
        $shopify_channel=ShopifyChannel::query()->where('channel_id',$channel_id)->first();
        $this->setupCredentialsManually($shopify_channel->access_token, $shopify_channel->shop)->createBaseUrl() ;

        //get the ids of products which are saved in shopify
        $this->products_id = $this->getProductsId($channel_id);

        $this->organization_id = $organization_id;

        $this->batch = $batch;
    }

    public function getCollectionOld(): void
    {
        try {
            $CustomCollectionService = new CustomCollectionService($this->channel_id);
            // Get all products from shopify
            $CustomCollectionService->getAll(function($response) {
                if($response){
                    $collectionChunks = array_chunk($response , 125);
                    foreach ($collectionChunks as $key => $collections) {
                        if($this->batch){
                            $this->batch->add(new StoreCustomCollectionJob([
                                "collections" => $collections,
                                "organization_id" => $this->organization_id,
                                "channel_id" => $this->channel_id,
                            ]));
                        }else{
                          $this->store($response);
                        }

                    }

                }
            },['limit' => 250]);
         } catch (\Exception $e) {
            $data = [
                'organization_id' => $this->organization_id,
                'description' => "There is an error while fetching collection.<br>  {$e->getMessage()}",
                'type' => 'shopify',
                'link' => "#",
                'link_text' => 'View Product',
                'status' => 'error',
            ];

            $error = new ErrorLog();
            $error->setData($data)->store(function ($error) {
                Log::channel('shopify')->info('Error in saving ErrorLogs.');
                Log::channel('shopify')->error($error);
            }, function () {
            });
            Log::channel('shopify')->info('Collection get from shopify error');
            Log::channel('shopify')->info($e->getMessage());
         }
    }
    public function getCollection(): void
    {
        try {
            $shopifyService = new \App\Classes\Shopify\FetchDataByGQL($this->channel_id);
            $collectionsList =  $shopifyService->collections();
            // dump($collectionsList);
            $this->store($collectionsList['nodes']);
        } catch (\Exception $e) {
            // dd($e->getMessage());
            $data = [
                'organization_id' => $this->organization_id,
                'description' => "There is an error while fetching collection.<br>  {$e->getMessage()}",
                'type' => 'shopify',
                'link' => "#",
                'link_text' => 'View Product',
                'status' => 'error',
            ];

            $error = new ErrorLog();
            $error->setData($data)->store(function ($error) {
                Log::channel('shopify')->info('Error in saving ErrorLogs.');
                Log::channel('shopify')->error($error);
            }, function () {
            });
            Log::channel('shopify')->info('Collection get from shopify error');
            Log::channel('shopify')->info($e->getMessage());
        }
    }

    public function store($data): void
    {
        foreach($data as $collection){

            $array = [];
            $collection = (object) $collection;
            $cat = Category::where('organization_id',$this->organization_id)
                        ->where('name',$collection->title)
                        ->first();

            if($cat){
                $array['id'] =$cat->id;
            }
            $collection_array = explode('/', $collection->id);
            $collection_id = end($collection_array);
            $array['name'] = $collection->title;
            $array['description'] = $collection->descriptionHtml;
            $array['organization_id'] = $this->organization_id;
            $array['response'] = ['type'=>'shopify','collection_id'=>$collection_id,'channel_id'=>$this->channel_id];

            $category = new Category();
            $category->set_data($array)->store(function () {
                Log::channel('shopify')->info('error in saving category in db.');
            }, function ($obj) {
                // dump($obj->id);
                Log::channel('shopify')->info('success in saving cat.');
            });
        }
    }

    /**
     * @param $products
     * @return void
     */
    public function getCollectProduct($products_id = [] ): void
    {

        if(!empty($products_id)){
            $this->products_id = $products_id;
        }

        $CollectService = new CollectService($this->channel_id);
        foreach($this->products_id as $ids){
            $shopify_product_id = $ids['shopify_product_id'];
            try {
            // Get all products from shopify
            $CollectService->getAll(function($response) use($ids) {
                $collectChunks = array_chunk($response , 125);
                foreach ($collectChunks as $key => $collects) {
                    if($this->batch){
                        $this->batch->add(new StoreCollectJob([
                            "collects" => $collects,
                            "organization_id" => $this->organization_id,
                            "channel_id" => $this->channel_id,
                            "product_id" => $ids['product_id'],
                        ]));
                    }else{
                 $this->accessToProduct($response,$ids['product_id']);
                    }

                }

            },['limit' => 250,'product_id' => $shopify_product_id]);
        } catch (\Exception $e) {
            // $data = [
            //     'organization_id' => $this->organization_id,
            //     'description' => "There is an error while saving collection.<br>  {$e->getMessage()}",
            //     'type' => 'shopify',
            //     'link' => "#",
            //     'link_text' => 'View Product',
            //     'status' => 'error',
            // ];

            // $error = new ErrorLog();
            // $error->setData($data)->store(function ($error) {
            //     Log::channel('shopify')->info('Error in saving ErrorLogs.');
            //     Log::channel('shopify')->error($error);
            // }, function () {
            // });
            Log::channel('shopify')->info('Collection get from shopify error');
            Log::channel('shopify')->info($e->getMessage());
         }
        }
    }

    public function accessToProduct($data , $product_id): void
    {
        $categoryIds = [];
        foreach($data as $collect){
          if(isset($collect->collection_id)){
              $cat = Category::query()
                        ->where('organization_id',$this->organization_id)
                        ->with('channel')
                        ->whereHas('channel',function ($q) use ($collect) {
                            $q->where('channel_id',$this->channel_id)
                                ->where('store_connect_type','shopify')
                                ->where('store_connect_id',$collect->collection_id);
                        })
                        ->first();
              if($cat){
                    $categoryIds[] = $cat->id;
              }else{
                  Log::channel('shopify')->info('Collection not link with product');
              }

          }
        }

        $product = Product::find($product_id);

        if ($product) {
            $product->categories()->sync($categoryIds);
        }
    }
}
